# 室外天气功能测试文档

## 测试环境
- Flutter版本: 3.16.5+
- Dart SDK版本: >=3.2.3 <4.0.0

## 测试用例结构

```
test/
  ├── whole_house/
  │   └── weather/
  │       ├── outdoor_weather_model_test.dart   # 室外天气模型测试
  │       └── outdoor_weather_action_test.dart  # 室外天气Action测试
  └── mocks/
      └── mock_weather_data.dart                # 模拟测试数据
```

## 运行测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/whole_house/weather/outdoor_weather_model_test.dart

# 带覆盖率报告
flutter test --coverage
```

## 模拟数据说明
`mock_weather_data.dart` 文件提供了以下模拟数据:
1. 天气API响应模拟数据
2. 多种天气现象数据(用于UI测试)
3. 多种位置数据(家庭位置、手机定位、IP定位)

## 测试范围
- 数据模型: 确保模型可以正确地从JSON解析和转换为JSON
- Redux Actions: 验证室外天气相关Action能正确更新Redux状态
- 位置策略: 测试家庭位置>手机定位>IP定位的优先级逻辑
- UI组件: 验证天气信息在UI中正确显示

## 注意事项
- 确保测试前安装了所有依赖: `flutter pub get`
- 所有测试应该是独立的，不依赖于外部网络资源
- UI测试时会模拟多种天气现象，以确保不同状态下的显示效果 