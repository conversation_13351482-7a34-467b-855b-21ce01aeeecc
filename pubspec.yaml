name: smart_home
description: 智家tab-框架
version: 1.0.0
author: tan<PERSON><PERSON><PERSON>@haier.com
homepage: http://**************:8083
publish_to: http://**************:8083

flutterVersion: 3

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter

  flutter_svg: 2.0.9

  redux: 5.0.0
  flutter_redux: 0.8.2
  reselect: 0.5.0
  dio: 5.3.2
  flutter_screenutil: 5.0.0+2
  gif: 2.3.0
  auto_size_text: 3.0.0
  http: 1.1.0
  path_provider: 2.1.2
  url_launcher: ">=6.0.0"
  reorderable_grid_view: 2.2.8
  retrofit: 4.1.0
  lottie: 2.6.0
  visibility_detector: 0.4.0+2
  keframe: 3.0.0

  easy_refresh: 3.4.0

  extended_nested_scroll_view: 6.2.1
  scrollable_positioned_list: 0.3.8
  scroll_to_index: 3.0.1
  json_annotation: 4.8.1

  reorderable_plus: 0.0.2
  sliver_tools: 0.2.12

  gradient_borders: 1.0.1
  freezed_annotation: ^2.4.1

  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"

  eshop_widgets:
    hosted:
      name: eshop_widgets
      url: http://**************:8083
    version: ">=0.3.0+**********"

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.2"
  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.1.1"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=1.0.3"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=0.0.12"
  uplustrace:
    hosted:
      name: uplustrace
      url: http://**************:8083
    version: ">=1.0.0"

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: ">=0.0.12"
  family:
    hosted:
      name: family
      url: http://**************:8083
    version: ">=0.0.9+2"
  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.0.4"

  plugin_device:
    hosted:
      name: plugin_device
      url: http://**************:8083
    version: ">=5.16.0 <5.17.0"
  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.2"

  library_widgets:
    hosted:
      name: library_widgets
      url: http://**************:8083
    version: ">=2.0.6"

  uppermission:
    hosted:
      name: uppermission
      url: http://**************:8083
    version: ">=0.0.1"

  uimessage:
    hosted:
      name: uimessage
      url: http://**************:8083
    version: ">=0.0.1"

  device_utils:
    hosted:
      name: device_utils
      url: http://**************:8083
    version: ">=2.0.0"

  wash_device_manager:
    hosted:
      name: wash_device_manager
      url: http://**************:8083
    version: ">=0.0.1"

  upsystem:
    hosted:
      name: upsystem
      url: http://**************:8083
    version: ">=0.1.1"

  flutter_common_ui:
    version: ">=9.2.1"
    hosted:
      name: flutter_common_ui
      url: http://**************:8083

  upservice:
    hosted:
      name: upservice
      url: http://**************:8083
    version: ">=0.0.1"

  location:
    hosted:
      name: location
      url: http://**************:8083
    version: ">=0.1.2"

  videoview:
    hosted:
      name: videoview
      url: http://**************:8083
    version: ">=0.0.1"

  share:
    hosted:
      name: share
      url: http://**************:8083
    version: ">=0.0.11"
  whole_house_music:
    hosted:
      name: whole_house_music
      url: http://**************:8083
    version: ">=0.0.1"
  function_toggle:
    hosted:
      name: function_toggle
      url: http://**************:8083
    version: ">=0.0.1"

  main_business:
    hosted:
      name: main_business
      url: http://**************:8083
    version: ">=0.0.1"

  plugin_usdk:
    hosted:
      name: plugin_usdk
      url: http://**************:8083
    version: ">=0.0.1 <=999.999.999"

  cx_player:
    version: ">=0.0.1"
    hosted:
      name: cx_player
      url: http://**************:8083

  auto_orientation: ">=2.3.1"

dev_dependencies:
  retrofit_generator: 8.1.0
  freezed: ^2.4.6
  build_runner: 2.4.6
  json_serializable: 6.7.1
  mockito: 5.4.4
  flutter_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/edit/
    - assets/images/env_device/
    - assets/icons/
    - assets/components/
    - assets/theme/
    - assets/components/mode/
    - assets/on_off_animation/
    - assets/on_off_animation/on_animation/
    - assets/on_off_animation/off_animation/
    - assets/new_fan/
    - assets/images/running_device/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
