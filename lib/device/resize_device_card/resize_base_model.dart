/*
 * 描述：卡片大小基类
 * 作者：fancunshuo
 * 建立时间: 2025/2/5
 */

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/resize_device_card/resize_overlay.dart';
import 'package:upsystem/upsystem.dart';

import '../reorderable_staggered_grid_view/reorderable_staggered_grid_view.dart';

enum DeviceCardType { largeCard, middleCard, smallCard }

class ResizeBaseModel extends ReorderableStaggeredScrollViewGridExtentItem {
  DeviceCardType size;

  ResizeBaseModel(Key key, this.size) : super(key: key);

  // TODO(fcs): 后续处理UI细节
  int get row {
    switch (deviceCardType) {
      case DeviceCardType.largeCard:
        return 2;
      case DeviceCardType.middleCard:
        return 2;
      case DeviceCardType.smallCard:
        return 1;
    }
  }

  // TODO(fcs): 后续处理UI细节
  int get col {
    switch (deviceCardType) {
      case DeviceCardType.largeCard:
        return 4;
      case DeviceCardType.middleCard:
        return 2;
      case DeviceCardType.smallCard:
        return 2;
    }
  }

  // TODO(fcs): 后续处理UI细节
  double get height {
    switch (deviceCardType) {
      case DeviceCardType.largeCard:
        return 140;
      case DeviceCardType.middleCard:
        return 140;
      case DeviceCardType.smallCard:
        return 64;
    }
  }

  void onResizeComplete(num width, num height, DeviceCardType? initSize,
      BuildContext context, CardSide side) {
    double screenw = ScreenUtil().screenWidth;
    if (context.mounted) {
      final Size screenSize = MediaQuery.of(context).size;
      screenw = screenSize.width;
    }

    if (side == CardSide.right) {
      final bool inA = height < 64 + (140 - 64) / 2 && width < screenw / 4;
      final bool inB = height < 64 + (140 - 64) / 2 && width >= screenw / 4;
      final bool inD = height >= 64 + (140 - 64) / 2 && width < screenw / 4;
      final bool inC = height >= 64 + (140 - 64) / 2 && width >= screenw / 4;

      if (initSize == DeviceCardType.middleCard) {
        if (inB && deviceCardType != DeviceCardType.smallCard) {
          deviceCardType = DeviceCardType.smallCard;
          UpSystem.impactFeedBack();
        }
        if (inD &&
            supportLargeCard &&
            deviceCardType != DeviceCardType.largeCard) {
          deviceCardType = DeviceCardType.largeCard;
          UpSystem.impactFeedBack();
        }
        if (inC && deviceCardType != DeviceCardType.middleCard) {
          deviceCardType = DeviceCardType.middleCard;
          UpSystem.impactFeedBack();
        }
      }
      if (initSize == DeviceCardType.smallCard) {
        if (inB && deviceCardType != DeviceCardType.smallCard) {
          deviceCardType = DeviceCardType.smallCard;
          UpSystem.impactFeedBack();
        }
        if (inC && deviceCardType != DeviceCardType.middleCard) {
          deviceCardType = DeviceCardType.middleCard;
          UpSystem.impactFeedBack();
        }
        if (inD &&
            supportLargeCard &&
            deviceCardType != DeviceCardType.largeCard) {
          deviceCardType = DeviceCardType.largeCard;
          UpSystem.impactFeedBack();
        }
      }
    }
    if (side == CardSide.left) {
      final bool inA = height < 64 + (140 - 64) / 2 && width < screenw / 4 * 3;
      final bool inB = height < 64 + (140 - 64) / 2 && width >= screenw / 4 * 3;
      final bool inD = height >= 64 + (140 - 64) / 2 && width < screenw / 4 * 3;
      final bool inC =
          height >= 64 + (140 - 64) / 2 && width >= screenw / 4 * 3;

      if (size == DeviceCardType.largeCard) {
        if (inA || (inB && initSize == DeviceCardType.largeCard)) {
          deviceCardType = DeviceCardType.smallCard;
          UpSystem.impactFeedBack();
        } else if (inD) {
          deviceCardType = DeviceCardType.middleCard;
          UpSystem.impactFeedBack();
        }
      } else if (size == DeviceCardType.middleCard) {
        if (inA) {
          deviceCardType = DeviceCardType.smallCard;
          UpSystem.impactFeedBack();
        } else if (inB || inC) {
          deviceCardType = DeviceCardType.largeCard;
          UpSystem.impactFeedBack();
        }
      } else if (size == DeviceCardType.smallCard) {
        if ((inB && initSize == DeviceCardType.smallCard) || inC) {
          deviceCardType = DeviceCardType.largeCard;
          UpSystem.impactFeedBack();
        }
        if (inD) {
          deviceCardType = DeviceCardType.middleCard;
          UpSystem.impactFeedBack();
        }
      }
    }
  }

  set deviceCardType(DeviceCardType value) {
    size = value;
  }

  DeviceCardType get deviceCardType => size;

  bool get supportLargeCard => true;

  @override
  int get crossAxisCellCount => col;

  @override
  double get mainAxisExtent => height;

  int get maxCol {
    if (supportLargeCard) {
      return 4;
    }
    return 2;
  }

  double get maxHeight => 140;

  int get minCol => 2;

  double get minHeight => 64;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ResizeBaseModel &&
          runtimeType == other.runtimeType &&
          size == other.size;

  @override
  int get hashCode => super.hashCode ^ size.hashCode;
}
