/*
 * 描述：拖动调整卡片大小组件
 * 作者：fancunshuo
 * 建立时间: 2024/12/24
 */

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/resize_device_card/resize_base_model.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:upsystem/upsystem.dart';

import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';
import '../factory/device_card_factory.dart';

void removeOverlay() {
  smartHomeStore.dispatch(DragResizeAction(dragging: false));

  dragResizing = false;
  _entry?.remove();
  _entry = null;
}

bool dragResizing = false;

OverlayEntry? _entry;

class ResizeOverlay extends StatefulWidget {
  ResizeOverlay({
    super.key,
    required this.child,
    this.onResizeStart,
    this.onResizeComplete,
    this.enable = false,
    required this.model,
    this.onResizeEnd,
  });

  final Widget child;

  final ResizeBaseModel model;

  final Function? onResizeStart;

  final Offset Function(num, num, DeviceCardType?, CardSide)? onResizeComplete;

  final void Function()? onResizeEnd;

  bool enable;

  @override
  State<ResizeOverlay> createState() => _ResizeOverlayState();
}

class _ResizeOverlayState extends State<ResizeOverlay> {
  Size size = Size.zero;
  Offset offset = Offset.zero;

  Offset handleOffset = Offset.zero;

  DeviceCardType? cardType;

  void Function(double width, double height, Offset offset)? _overlaySetState;

  void Function(double width, double height, Offset offset)?
      _overlayUpdateOffset;

  void Function(bool dragResizing)? _overlayUpdateResizing;

  double width = 0;
  double height = 0;

  HandleStatus handleStatus = HandleStatus.allowBoth;

  CardSide side = CardSide.left;

  Offset tOffset = Offset.zero;

  double tWidth = 0, tHeight = 0;

  double screenWidth = ScreenUtil().screenWidth;

  HandleStatus _calCardStatus() {
    HandleStatus handleStatus = HandleStatus.allowBoth;
    switch (widget.model.size) {
      case DeviceCardType.largeCard:
        handleStatus = HandleStatus.allowIn;
      case DeviceCardType.smallCard:
        handleStatus = HandleStatus.allowOut;
      case DeviceCardType.middleCard:
        handleStatus = HandleStatus.allowBoth;
    }
    if (!widget.model.supportLargeCard &&
        widget.model.size == DeviceCardType.middleCard) {
      handleStatus = HandleStatus.allowIn;
    }
    return handleStatus;
  }

  @override
  void initState() {
    super.initState();
    setState(() {
      handleStatus = _calCardStatus();
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && context.mounted) {
        screenWidth = MediaQuery.of(context).size.width;
      } else {
        screenWidth = ScreenUtil().screenWidth;
      }
    });
  }

  @override
  void didUpdateWidget(covariant ResizeOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.enable != widget.enable) {
      setState(() {
        handleStatus = _calCardStatus();
        if (!widget.enable) {
          _removeOverlay();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (BuildContext context) {
      return Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          if (size == Size.zero)
            StoreConnector<SmartHomeState, bool>(
              key: UniqueKey(),
              distinct: true,
              converter: (Store<SmartHomeState> store) {
                return store.state.editState.dragResizing;
              },
              builder: (BuildContext context, bool dragResizing) {
                return Opacity(
                    opacity: dragResizing ? 0.3 : 1, child: widget.child);
              },
            )
          else
            // TODO(fcs): 与UE和UI确定后修改
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFDDDDDD), width: 2),
                borderRadius: const BorderRadius.all(
                  Radius.circular(22),
                ),
              ),
            ),
          if (widget.enable)
            Positioned(
              bottom: 0,
              right: 0,
              child: RawGestureDetector(
                gestures: <Type, GestureRecognizerFactory>{
                  CustomPanGestureRecognizer:
                      GestureRecognizerFactoryWithHandlers<
                          CustomPanGestureRecognizer>(
                    () => CustomPanGestureRecognizer(onPanDown: (_) {
                      _showDragOverlay();
                      return true;
                    }, onPanUpdate: (Offset localPosition) {
                      _onDragUpdate(localPosition);
                    }, onPanEnd: (_) {
                      _overlayUpdateResizing?.call(false);
                      final double width = widget.model.deviceCardType ==
                                  DeviceCardType.largeCard
                              ? screenWidth - 32
                              : (screenWidth - 32) / 2 - 6,
                          height = widget.model.height;
                      if (offset == tOffset &&
                          width == this.width &&
                          height == this.height) {
                        _removeOverlay();
                        return;
                      }
                      _overlayUpdateOffset?.call(width, height, tOffset);
                    }),
                    (CustomPanGestureRecognizer instance) {},
                  ),
                },
                behavior: HitTestBehavior.translucent,
                child: AnimatedOpacity(
                  opacity: size == Size.zero ? 1 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: Container(
                    width: 30,
                    height: 30,
                    color: Colors.transparent,
                    alignment: Alignment.bottomRight,
                    child: StoreConnector<SmartHomeState, bool>(
                      key: UniqueKey(),
                      distinct: true,
                      converter: (Store<SmartHomeState> store) {
                        return store.state.editState.dragResizing;
                      },
                      builder: (BuildContext context, bool dragResizing) {
                        if (dragResizing) {
                          return Container();
                        }
                        return Image.asset(
                          _convertIconFromHandleStatus(handleStatus),
                          package: SmartHomeConstant.package,
                          fit: BoxFit.fill,
                          width: 24,
                          height: 24,
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }

  Widget _overlayPosition(Widget child) {
    return AnimatedPositioned(
      top: offset.dy,
      left: side == CardSide.left ? offset.dx : null,
      right: side == CardSide.right ? offset.dx : null,
      duration: const Duration(milliseconds: 200),
      onEnd: () {
        if (!dragResizing) {
          _removeOverlay();
        }
      },
      child: child,
    );
  }

  void _showDragOverlay() {
    smartHomeStore.dispatch(DragResizeAction(dragging: true));
    dragResizing = true;
    UpSystem.impactFeedBack();
    setState(() {
      cardType = widget.model.size;
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      size = renderBox?.size ?? Size.zero;
      offset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;
      side = offset.dx > MediaQuery.of(context).size.width / 2
          ? CardSide.right
          : CardSide.left;

      handleOffset = Offset(
          side == CardSide.left ? size.width : size.width + offset.dx - 22,
          size.height);

      if (offset.dx > MediaQuery.of(context).size.width / 2) {
        offset = Offset(
            MediaQuery.of(context).size.width - (offset.dx + size.width),
            offset.dy);
      }
    });
    width = size.width;
    height = size.height;
    tOffset = offset;

    _entry?.remove();

    _entry = OverlayEntry(
      builder: (_) => StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
        _overlaySetState = (double w, double h, Offset offset) {
          if (context.mounted) {
            setState(() {
              width = w;
              height = h;
              handleStatus = _calCardStatus();
              handleOffset = offset;
            });
          }
        };
        _overlayUpdateOffset = (double w, double h, Offset o) {
          if (context.mounted) {
            setState(() {
              width = w;
              height = h;
              offset = o;
            });
          }
        };
        _overlayUpdateResizing = (bool resizing) {
          setState(() {
            dragResizing = resizing;
          });
        };
        return Material(
          color: Colors.transparent,
          child: Stack(
            children: <Widget>[
              _overlayPosition(AnimatedContainer(
                width: width,
                height: height,
                duration: Duration(milliseconds: dragResizing ? 0 : 200),
                onEnd: () {
                  if (!dragResizing) {
                    _removeOverlay();
                  }
                },
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(22)),
                ),
                child: StoreProvider<SmartHomeState>(
                    store: smartHomeStore,
                    child: DeviceCardFactory.deviceCardWidget(
                        widget.model as DeviceCardViewModel)),
              )),
              Positioned(
                left: handleOffset.dx - 8,
                top: handleOffset.dy + offset.dy - 24,
                child: Offstage(
                  offstage: !dragResizing,
                  child: Image.asset(
                    _convertIconFromHandleStatus(handleStatus),
                    package: SmartHomeConstant.package,
                    fit: BoxFit.fill,
                    width: 30,
                    height: 30,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
    final OverlayState overlay = Overlay.of(this.context);
    overlay.insert(_entry!);
  }

  void _onDragUpdate(Offset delta) {
    final double maxHeight = widget.model.maxHeight;
    final double minHeight = widget.model.minHeight;

    final double maxWidth = widget.model.supportLargeCard
        ? screenWidth - 16 * 2
        : (screenWidth - 16 * 2 - 12) / 2;

    if (side == CardSide.right) {
      final double twidth = delta.dx - offset.dx - size.width;
      height = delta.dy - offset.dy + 15;
      if (height < minHeight * 0.9) {
        height = minHeight * 0.9;
      }
      if (height > maxHeight * 1.05) {
        height = maxHeight * 1.05;
      }
      tOffset = widget.onResizeComplete?.call(twidth, height, cardType, side) ??
          Offset.zero;
      double cardWidth = 0; // 大卡片不补偿
      if (widget.model.crossAxisCellCount == 2) {
        cardWidth = (screenWidth - 16 * 2) / 2 - 6;
      }
      if (tOffset.dx > screenWidth / 2) {
        tOffset = Offset(16, tOffset.dy);
      } else {
        tOffset = Offset(tOffset.dx + cardWidth, tOffset.dy);
      }
      width = size.width - twidth + size.width - 5;
      if (width > maxWidth * 1.05) {
        width = maxWidth * 1.05;
      }
      if (width > maxWidth * (widget.model.supportLargeCard ? 1.02 : 1.05)) {
        width = maxWidth * (widget.model.supportLargeCard ? 1.02 : 1.05);
      }

      if (width < (screenWidth - 16 * 2 - 12) / 2 * 0.96) {
        width = (screenWidth - 16 * 2 - 12) / 2 * 0.96;
      }

      // 支持大卡的设备卡片在列表右侧时，拖拽按钮最多只能拖拽到中间
      final double offsetx = twidth + size.width;
      Offset tempOffset = Offset(twidth + size.width, height);
      if (offsetx < screenWidth / 2) {
        tempOffset = Offset(screenWidth / 2, height);
      } else if (offsetx > screenWidth - 16 - 20) {
        tempOffset = Offset(screenWidth - 16 - 20, height);
      }
      if (!widget.model.supportLargeCard) {
        // 不支持大卡的设备卡片在列表右侧时，拖拽按钮不能向左拖拽
        tempOffset = Offset(screenWidth - 16 - 22, height - 5);
      }
      _overlaySetState?.call(width, height, tempOffset);
    }
    if (side == CardSide.left) {
      width = delta.dx - offset.dx + 15;
      height = delta.dy - offset.dy + 15;

      if (width < (screenWidth - 16 * 2 - 12) / 2 * 0.96) {
        width = (screenWidth - 16 * 2 - 12) / 2 * 0.96;
      }
      if (height < minHeight * 0.9) {
        height = minHeight * 0.9;
      }
      if (width > maxWidth * (widget.model.supportLargeCard ? 1.02 : 1.05)) {
        width = maxWidth * (widget.model.supportLargeCard ? 1.02 : 1.05);
      }
      if (height > maxHeight * 1.05) {
        height = maxHeight * 1.05;
      }

      _overlaySetState?.call(width, height, Offset(width, height));
      tOffset = widget.onResizeComplete?.call(width, height, cardType, side) ??
          Offset.zero;
    }
  }

  void _removeOverlay() {
    _entry?.remove();
    _entry = null;

    widget.onResizeEnd?.call();
    smartHomeStore.dispatch(DragResizeAction(dragging: false));

    cardType = null;
    size = Size.zero;
    if (mounted) {
      setState(() {});
    }
  }
}

String _convertIconFromHandleStatus(HandleStatus handleStatus) {
  switch (handleStatus) {
    case HandleStatus.allowOut:
      return 'assets/icons/card_resize_out.webp';
    case HandleStatus.allowIn:
      return 'assets/icons/card_resize_in.webp';
    case HandleStatus.allowBoth:
      return 'assets/icons/card_resize.webp';
  }
}

enum HandleStatus { allowOut, allowIn, allowBoth }

enum CardSide { left, right }

class CustomPanGestureRecognizer extends OneSequenceGestureRecognizer {
  final bool Function(Offset) onPanDown;
  final void Function(Offset) onPanUpdate;
  final void Function(Offset) onPanEnd;

  CustomPanGestureRecognizer(
      {required this.onPanDown,
      required this.onPanUpdate,
      required this.onPanEnd});

  @override
  void addAllowedPointer(PointerEvent event) {
    if (onPanDown(event.position)) {
      startTrackingPointer(event.pointer);
      resolve(GestureDisposition.accepted);
    } else {
      stopTrackingPointer(event.pointer);
    }
  }

  @override
  void handleEvent(PointerEvent event) {
    if (event is PointerMoveEvent) {
      onPanUpdate(event.position);
    }
    if (event is PointerUpEvent) {
      onPanEnd(event.position);
      stopTrackingPointer(event.pointer);
    }
    if (event is PointerCancelEvent) {
      onPanEnd(event.position);
      stopTrackingPointer(event.pointer);
    }
  }

  @override
  String get debugDescription => 'customPan';

  @override
  void didStopTrackingLastPointer(int pointer) {}

  @override
  void resolve(GestureDisposition disposition) {
    if (disposition == GestureDisposition.rejected) {
      onPanEnd(Offset.zero);
    }
    super.resolve(disposition);
  }
}
