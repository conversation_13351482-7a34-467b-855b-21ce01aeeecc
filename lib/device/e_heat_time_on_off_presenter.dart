/*
 * 描述：电热定时开关机
 * 作者：songFJ
 * 创建时间：2024/7/22
 */

import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/e_heat_time_of_off_model.dart';
import 'package:smart_home/service/http_service.dart';

typedef EHeatTimeOnOffCallback = void Function(
    String deviceId, SmartHomeDeviceAttribute attribute);

class EHeatTimeOnOffPresenter {
// 单例
  static EHeatTimeOnOffPresenter? _instance;

  List<String> get notSupportTimeOnOffTypeId => <String>[
        '201c120000118674061800418006590000000000000000000000000000000040',
        '201c120000118674061800418007514600000000000000000000000000000040',
        '201c120000118674061900418007515100000000000000000000000000000040',
        '201c120000118674061b00418013730000000000000000000000000000000040',
        '201c120000118674061b00418013750000000000000000000000000000000040',
        '201c120000118674061b00418008324300000000000000000000000000000040'
      ];

// 内部构造方法
  EHeatTimeOnOffPresenter._internal();

  factory EHeatTimeOnOffPresenter.getInstance() => _getInstance();

  static EHeatTimeOnOffPresenter _getInstance() {
    _instance ??= EHeatTimeOnOffPresenter._internal();
    return _instance!;
  }

  bool needUpdateDevice(SmartHomeDevice device) {
    if ((device.basicInfo.bigClass == '6' &&
            isSupportTimeOnOff(device.basicInfo.typeId)) ||
        device.basicInfo.bigClass == '18') {
      return true;
    }
    return false;
  }

  Future<void> updateTimeOnOffStatus(
      SmartHomeDevice scDevice, EHeatTimeOnOffCallback callback) async {
    if (scDevice.basicInfo.deviceId.isEmpty) {
      return;
    }

    doRequest(scDevice, callback);
  }

  bool isSupportTimeOnOff(String typeId) {
    return !notSupportTimeOnOffTypeId.contains(typeId);
  }

  void doRequest(SmartHomeDevice device, EHeatTimeOnOffCallback callback) {
    HttpService.timeOnOffStatus(device.basicInfo.deviceId)
        .then((EHeatTimeOnOffResponseModel? responseModel) {
      final EHeatTimeOnOffDataModel? data = responseModel?.data;
      if (data is EHeatTimeOnOffDataModel) {
        final bool value = data.onOffStatus ?? false;
        final String onOffValue = value.toString();
        final SmartHomeDeviceAttribute attribute =
            SmartHomeDeviceAttribute(name: 'autoTimeOnOff', value: onOffValue);
        callback(device.basicInfo.deviceId, attribute);
      }
    });
  }
}
