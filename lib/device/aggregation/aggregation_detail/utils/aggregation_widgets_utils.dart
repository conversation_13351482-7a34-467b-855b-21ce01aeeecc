import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/agg_env_info.dart';

class AggregationWidgetsUtils {
  static const String levelperfect = '优';
  static const String levelGood = '良';
  static const String levelBad = '差';

  static const int pm25PerfectThreshold = 35;
  static const int pm25GoodThreshold = 50;

  static bool isPM25(String paramName) {
    return paramName.trim() == AggregationDeviceConstant.aggEnvLabelDescPm25;
  }

  static String _convertUnit(AggEnvModel envModel) {
    return isPM25(envModel.name)
        ? AggregationDeviceConstant.aggEnvUnitPm25
        : envModel.unit;
  }

  static PropertyBadgeModel buildPropertyBadge(AggEnvModel envModel) {
    if (!AggregationWidgetsUtils.isPM25(envModel.name)) {
      return PropertyBadgeModel(text: '', color: 0);
    }

    final int propertyValue = int.tryParse(envModel.value) ?? 0;
    if (propertyValue < pm25PerfectThreshold) {
      return PropertyBadgeModel(text: levelperfect, color: 0xff44BE3B);
    }
    if (propertyValue >= pm25PerfectThreshold &&
        propertyValue <= pm25GoodThreshold) {
      return PropertyBadgeModel(text: levelGood, color: 0xffF5A623);
    }

    return PropertyBadgeModel(text: levelBad, color: 0xffED2856);
  }

  static void showQuestionDialog(BuildContext context, AggEnvModel envModel) {
    final EvaluationModel evaluationModel = _buildEvaluation(envModel.name);
    showDialog<dynamic>(
      context: context,
      builder: (BuildContext context) {
        return Center(
          child: SingleChildScrollView(
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              insetPadding: const EdgeInsets.all(16),
              child: Column(
                children: <Widget>[
                  _buildDialogTitle(),
                  const SizedBox(height: 18),
                  _buildDividerLine(),
                  _buildDialogDesc(),
                  const SizedBox(height: 15),
                  _buildCurrentEnvProperty(envModel),
                  const SizedBox(height: 10),
                  _buildLevelImage(evaluationModel.image),
                  _buildLevelTitle(evaluationModel),
                  _buildLevelDesc(evaluationModel),
                  const SizedBox(height: 17),
                  _buildDividerLine(),
                  _buildConfirmButton(context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static EvaluationModel _buildEvaluation(String envPropertyName) {
    final List<EvaluationLevelModel> levelList = <EvaluationLevelModel>[
      EvaluationLevelModel(name: levelperfect, desc: '<35ug/m³'),
      EvaluationLevelModel(name: levelGood, desc: '35-50ug/m³'),
      EvaluationLevelModel(name: levelBad, desc: '＞50ug/m³'),
    ];

    return EvaluationModel(
      image: 'assets/images/green_yellow_red.png',
      levelList: levelList,
    );
  }

  static Widget _buildDialogTitle() {
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
      child: Text(
        '评价标准',
        style:
            TextStyle(fontSize: 17, color: AppSemanticColors.container.toast),
      ),
    );
  }

  static Widget _buildDialogDesc() {
    return Container(
      margin: const EdgeInsets.only(top: 18),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        '空气参数及评价由房间下空调、新风等设备传感器检测上报数据综合计算得出，不同设备上报数据存在差异，结果仅供参考；',
        style: TextStyle(
            fontSize: 14,
            color: AppSemanticColors.container.toast,
            height: 1.3),
      ),
    );
  }

  static Widget _buildCurrentEnvProperty(AggEnvModel envModel) {
    return Text(
      ' ${envModel.name} ${envModel.value} (${_convertUnit(envModel)})',
      style: TextStyle(fontSize: 14, color: AppSemanticColors.container.toast),
    );
  }

  static Widget _buildLevelImage(String imageUrl) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Image.asset(
        imageUrl,
        package: SmartHomeConstant.package,
      ),
    );
  }

  static Widget _buildLevelTitle(EvaluationModel evaluationModel) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          ..._buildLevelTitleList(evaluationModel.levelList),
        ],
      ),
    );
  }

  static Widget _buildLevelDesc(EvaluationModel evaluationModel) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 22),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          ..._buildLevelDescList(evaluationModel.levelList),
        ],
      ),
    );
  }

  static List<Widget> _buildLevelTitleList(
      List<EvaluationLevelModel> levelList) {
    return levelList.map((EvaluationLevelModel levelModel) {
      return Flexible(
        child: Text(
          levelModel.name,
          style:
              TextStyle(fontSize: 14, color: AppSemanticColors.container.toast),
        ),
      );
    }).toList();
  }

  static List<Widget> _buildLevelDescList(
      List<EvaluationLevelModel> levelList) {
    return levelList.map((EvaluationLevelModel levelModel) {
      return Flexible(
        child: Text(
          levelModel.desc,
          style:
              TextStyle(fontSize: 12, color: AppSemanticColors.container.cover),
        ),
      );
    }).toList();
  }

  static Widget _buildConfirmButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
      },
      child: Container(
        height: 50,
        alignment: Alignment.center,
        width: double.infinity,
        color: Colors.transparent,
        child: Text(
          '确定',
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w400,
            color: AppSemanticColors.component.information.on,
          ),
        ),
      ),
    );
  }

  static Widget _buildDividerLine() {
    return Container(
      height: 1,
      width: double.infinity,
      color: const Color.fromRGBO(0, 0, 0, 0.07),
    );
  }
}

class EvaluationModel {
  final String image;
  final List<EvaluationLevelModel> levelList;

  EvaluationModel({required this.image, required this.levelList});
}

class EvaluationLevelModel {
  String name = '';
  String desc = '';

  EvaluationLevelModel({required this.name, required this.desc});

  @override
  String toString() {
    return '{name: $name, desc: $desc}';
  }
}

class PropertyBadgeModel {
  String text;
  int color;

  PropertyBadgeModel({required this.text, required this.color});
}
