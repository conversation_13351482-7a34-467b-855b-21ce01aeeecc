import 'dart:io';

import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';

import 'package:family/family_card_model.dart';
import 'package:family/family_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/detail_room_list_item.dart';
import 'package:upservice/model/uhome_response_model.dart';

import '../../../../common/constant.dart';
import '../../../../common/loading_helper.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../service/http_service.dart';
import '../../../../store/smart_home_store.dart';
import '../../../device_view_model/card_base_view_model.dart';
import '../../agg_store/aggregation_action.dart';
import '../../aggregation_card/util/aggregation_device_util.dart';
import '../../aggregation_card/view_model/aggregation_base_view_model.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';
import '../../utils/agg_utils.dart';
import '../model/supportDeviceModel.dart';

enum AggLoadingStatus {
  loading,
  normal,
}

class AggregationPresenter {
  // 单例实例
  static final AggregationPresenter _instance =
      AggregationPresenter._internal();

  // 工厂构造函数，返回单例实例
  factory AggregationPresenter() {
    return _instance;
  }

  // 私有构造函数，防止外部直接实例化
  AggregationPresenter._internal();

  static String tag = 'AggregationPresenter';

  static Map<String, String> aggTypeMapping = <String, String>{
    AggregationSettingConstant.agg_light_id: 'lightAgg',
    AggregationSettingConstant.agg_curtain_id: 'curtainAgg',
    AggregationSettingConstant.env_id: 'envAgg',
  };

  /// 当前加载状态
  AggLoadingStatus _loadingStatus = AggLoadingStatus.normal;

  /// 获取当前加载状态
  AggLoadingStatus get loadingStatus => _loadingStatus;

  /// 初始化加载状态
  void initLoadingStatus() {
    _loadingStatus = AggLoadingStatus.normal;
  }

  /// 显示加载指示器
  void showLoading(BuildContext context) {
    LoadingHelper.showLoading(context);
    _loadingStatus = AggLoadingStatus.loading;
  }

  /// 隐藏加载指示器
  void hideLoading() {
    LoadingHelper.hideLoading();
    _loadingStatus = AggLoadingStatus.normal;
  }

  /// 添加/删除聚合设备
  static Future<bool> saveAggregationIdList(
    String aggregationId,
    List<String> deviceList,
  ) async {
    DevLogger.debug(tag: tag, msg: '$tag saveAggregationIdList start.');
    // 获取familyId
    final FamilyModel? familyModel = Family.getCurrentFamilySync();
    final String familyId = familyModel?.familyId ?? '';

    String _aggType = '-1';
    if (isDeviceLightAggregation(aggregationId)) {
      _aggType = '0';
    }
    if (isDeviceCurtainAggregation(aggregationId)) {
      _aggType = '1';
    }
    if (isEnvAgg(aggregationId)) {
      _aggType = '2';
    }

    final DeviceCardAggregationModel request = DeviceCardAggregationModel(
      familyId: familyId,
      aggCard: <AggCard>[
        AggCard(
          aggType: _aggType,
          sortList: deviceList,
        )
      ],
    );
    try {
      final DeviceCardResult responseModel =
          await Family.operateDeviceCardAggregation(request);
      DevLogger.info(
          tag: tag,
          msg: '$tag saveAggregationIdList '
              'response = $responseModel');
      return true;
    } catch (e) {
      DevLogger.error(
          tag: tag,
          msg: '$tag saveAggregationIdList failed '
              'e = $e');
      return false;
    }
  }

  /// 查询未聚合设备
  static Future<SupportDeviceResModel?> getDevicesByRoom(String id) async {
    final SupportDeviceReqModel request = SupportDeviceReqModel(
        familyId: Family.getCurrentFamilySync()?.familyId ?? '',
        aggType: switchAggrParentId(id));

    final SupportDeviceResModel? response =
        await HttpService.querySupportedAggDevices(request);
    return response;
  }

  /// 查询灯光、窗帘、环境聚合排序
  static Future<AggSortResModel?> queryAggSortData(
      List<String> aggParentIds) async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final List<String> aggTypeList = <String>[];

    aggParentIds.forEach((String id) {
      final MapEntry<String, String> entry = aggTypeMapping.entries.firstWhere(
          (MapEntry<String, String> element) => id.contains(element.key),
          orElse: () => const MapEntry<String, String>('', ''));
      if (entry.key.isNotEmpty) {
        aggTypeList.add(entry.value);
      }
    });
    final AggSortReqModel request = AggSortReqModel(
      familyId: familyId,
      types: aggTypeList,
    );

    final AggSortResModel? response = await HttpService.queryAggSort(request);
    if (response is AggSortResModel) {
      if (response.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        DevLogger.debug(
            tag: tag, msg: 'AggregationPresenter updateAggSortData success');
      } else {
        DevLogger.debug(
            tag: tag,
            msg:
                'AggregationPresenter updateAggSortData error: ${response?.retInfo}');
      }
    }
    return response;
  }

  static Future<void> queryAndUpdateAllAggSort () async {
    final AggSortResModel? sortResModel =
    await AggregationPresenter.queryAggSortData(_aggSortParams());
    smartHomeStore.dispatch(
        UpdateAggDeviceVmSortAction(sortResModel?.data ?? AggDeviceData()));
  }

  static List<String> _aggSortParams() {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final Map<EditPageType, String> queryMap = <EditPageType, String>{
      EditPageType.light: '${AggregationSettingConstant.agg_light_id}$familyId',
      EditPageType.curtain:
      '${AggregationSettingConstant.agg_curtain_id}$familyId',
      EditPageType.env: '${AggregationSettingConstant.env_id}$familyId'
    };
    final List<String> param = <String>[];
    queryMap.entries.forEach((MapEntry<EditPageType, String> element) {
      if (element.key != EditPresenterManager.currentPageForEdit) {
        final CardBaseViewModel? viewModel = getCardVm(element.value);
        if (viewModel is AggregationBaseViewModel) {
          param.add(element.value);
        }
      }
    });
    return param;
  }

  static AggDeviceData createAggDeviceData(
      String deviceId, Map<String, List<String>> sortMap) {
    final AggDeviceData aggDeviceData = AggDeviceData();
    final Map<String, void Function(Map<String, List<String>> p1)>
        deviceTypeMap = <String, void Function(Map<String, List<String>>)>{
      AggregationSettingConstant.agg_light_id:
          (Map<String, List<String>> map) => aggDeviceData.lightAgg = map,
      AggregationSettingConstant.agg_curtain_id:
          (Map<String, List<String>> map) => aggDeviceData.curtainAgg = map,
      AggregationSettingConstant.env_id: (Map<String, List<String>> map) =>
          aggDeviceData.envAgg = map,
    };

    for (final MapEntry<String,
            void Function(Map<String, List<String>> p1)> entry
        in deviceTypeMap.entries) {
      if (deviceId.contains(entry.key)) {
        entry.value(sortMap);
      }
    }

    return aggDeviceData;
  }

  /// 保存灯光、窗帘、环境聚合排序
  static Future<void> saveAggSort({AggDeviceData? sortData}) async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final AggSortSaveReqModel request = AggSortSaveReqModel(
      familyId: familyId,
      sortData: sortData ?? AggDeviceData(),
    );
    if (sortData == null) {
      aggTypeMapping.entries.forEach((MapEntry<String, String> entry) {
        final CardBaseViewModel? viewModel = getCardVm('${entry.key}$familyId');
        if (viewModel is AggregationBaseViewModel &&
            viewModel.detailRoomsModel.roomList.isNotEmpty) {
          final Map<String, List<String>> roomMap = <String, List<String>>{};
          viewModel.detailRoomsModel.roomList
              .forEach((DetailRoomListItem element) {
            roomMap[element.roomId] = element.deviceList;
          });
          if (entry.value == 'lightAgg') {
            request.sortData.lightAgg = roomMap;
          }
          if (entry.value == 'curtainAgg') {
            request.sortData.curtainAgg = roomMap;
          }
          if (entry.value == 'envAgg') {
            request.sortData.envAgg = roomMap;
          }
        }
      });
    }

    final UhomeResponseModel? response = await HttpService.saveAggSort(request);
    if (response is UhomeResponseModel) {
      if (response.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        DevLogger.debug(
            tag: tag, msg: 'AggregationPresenter saveAggSort success');
      } else {
        DevLogger.debug(
            tag: tag,
            msg: 'AggregationPresenter saveAggSort error: ${response.retInfo}');
      }
    }
  }

  static bool canPopPage() {
    return !(smartHomeStore.state.isEditState && Platform.isIOS);
  }
}
