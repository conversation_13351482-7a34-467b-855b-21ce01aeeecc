import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_card/env_card/model/agg_env_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/utils/aggregation_detail_constant.dart';
import 'package:smart_home/store/smart_home_state.dart';

class WhoseHouseAirWidget extends StatelessWidget {
  const WhoseHouseAirWidget({super.key, required this.viewModel});

  final AggregationBaseViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    if (viewModel is! AggEnvViewModel ||
        viewModel.device.basicInfo.deviceId.isEmpty) {
      return const SizedBox.shrink();
    }
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return !store.state.isEditState && viewModel is AggEnvViewModel;
        },
        builder: (BuildContext context, bool isShow) {
          if (!isShow) {
            return const SizedBox();
          }
          return Container(
            height: 20,
            margin: const EdgeInsets.only(top: 20, bottom: 12),
            alignment: Alignment.center,
            child: GestureDetector(
              onTap: () {
                goToPageWithDebounce(SmartHomeConstant.airPage);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(AggregationDetailConstant.whoseHouseAirText,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppSemanticColors.item.secWeaken,
                        fontWeight: FontWeight.w400,
                      )),
                  Image.asset(
                    'assets/icons/wash_select_more.webp',
                    package: SmartHomeConstant.package,
                    width: 12,
                    height: 12,
                    color: AppSemanticColors.item.secWeaken,
                  ),
                ],
              ),
            ),
          );
        });
  }
}
