import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/utils/aggregation_widgets_utils.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/detail_room_list_item.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_edit_button.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_select_all.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_whose_house_air.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/safe_bottom_area_sliver.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/edit/edit_widget_overlay.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/widget_common/over_scroll_behavior.dart';

import '../../../../common/constant_gio.dart';
import '../../../../common/smart_home_route_manager.dart';
import '../../../../common/smart_home_text_widget.dart';
import '../../../../common/smart_home_util.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../edit/util/edit_manager.dart';
import '../../../../navigator/sliver_header_delegate.dart';
import '../../../../store/smart_home_store.dart';
import '../../../../whole_house/device_env/pages/env_device_setting_page.dart';
import '../../../../whole_house/device_env/services/env_devices_response_model.dart';
import '../../../../widget_common/card_text_style.dart';
import '../../../device_info_model/smart_home_device.dart';
import '../../../device_list_widget.dart';
import '../../agg_store/agg_selectors.dart';
import '../../agg_store/aggregation_action.dart';
import '../../aggregation_card/curtain_card/model/aggregation_curtain_view_model.dart';
import '../../aggregation_card/env_card/model/agg_env_view_model.dart';
import '../../aggregation_card/light_card/model/aggregation_light_view_model.dart';
import '../../aggregation_card/util/aggregation_device_constant.dart';
import '../../aggregation_card/util/aggregation_device_util.dart';
import '../../aggregation_card/view_model/aggregation_btns_view_model.dart';
import '../../aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import '../../utils/agg_utils.dart';
import '../model/supportDeviceModel.dart';
import '../utils/aggregation_detail_constant.dart';
import '../utils/aggregation_presenter.dart';
import '../view_model/agg_env_info.dart';
import '../view_model/agg_normal_detail.dart';
import '../view_model/detail_room_list.dart';
import '../view_model/home_btn_state.dart';
import 'aggregation_detail_btn_list.dart';
import 'agg_detail_base_state.dart';

final TextStyle _titleTextStyle = TextStyle(
  fontSize: 24,
  fontWeight: FontWeight.w500,
  color: AppSemanticColors.item.primary,
  fontFamilyFallback: fontFamilyFallback(),
);

class AggregationDetailList extends StatefulWidget {
  String aggregationId;

  AggregationDetailList({super.key, required this.aggregationId});

  @override
  State<AggregationDetailList> createState() => AggregationDetailState();
}

class AggregationDetailState extends AggDetailBaseState<AggregationDetailList> {
  final AggSelectors aggSelectors = AggSelectors();
  final ValueKey<String> _customScrollViewKey =
      const ValueKey<String>('aggregation_custom_scroll_view');
  final AutoScrollController _scrollController = AutoScrollController(
    viewportBoundaryGetter: () => Rect.zero,
    axis: Axis.vertical,
    suggestedRowHeight: 200,
  );
  @override
  String get pageName => 'aggregationDetailPage';

  @override
  void onInitState() {
    if (!smartHomeStore.state.isEditState) {
      _queryAggregationSortInfo();
    }
  }

  @override
  void onDispose() {
    _scrollController.dispose();
  }

  Future<void> _queryAggregationSortInfo() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        AggregationPresenter().showLoading(context);
        final AggSortResModel? sortResModel =
            await AggregationPresenter.queryAggSortData(
                <String>[widget.aggregationId]);
        smartHomeStore.dispatch(
            UpdateAggDeviceVmSortAction(sortResModel?.data ?? AggDeviceData()));
      } catch (e) {
        DevLogger.info(tag: 'AggregationDetailList', msg: e);
      } finally {
        AggregationPresenter().hideLoading();
      }
    });
  }

  // 添加滚动到指定房间的方法
  Future<void> _scrollToRoom(int index) async {
    if (!_scrollController.hasClients) {
      Future<void>.delayed(const Duration(milliseconds: 200), () {
        // 递归调用自身，当控制器准备好时
        _scrollToRoom(index);
      });
      return;
    }
    try {
      await _scrollController.scrollToIndex(
        index,
        preferPosition: AutoScrollPosition.begin,
        duration: const Duration(milliseconds: 800),
      );
    } catch (e) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: '_scrollToRoom_scrollToIndex catch error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: AggregationPresenter.canPopPage(),
        child: StoreProvider<SmartHomeState>(
            store: smartHomeStore,
            child: Stack(
              children: <Widget>[
                Scaffold(
                  backgroundColor: AppSemanticColors.background.secondary,
                  body: StoreConnector<SmartHomeState,
                          AggNormalDetailViewModel>(
                      distinct: true,
                      converter: (Store<SmartHomeState> store) {
                        // 获取聚合卡片view model
                        // 灯光：'light_aggregation_id', 窗帘：'curtain_aggregation_id'
                        // 聚合详情列表必然是从卡片点击进来的，因此viewModel必然已经组装好
                        final AggregationBaseViewModel viewModel =
                            _buildAggVm(store);

                        return AggNormalDetailViewModel(
                          aggVm: viewModel,
                          isRoomEmpty: viewModel.devicesByRoom.isEmpty ||
                              store.state.aggregationState.isLoading,
                        );
                      },
                      builder:
                          (BuildContext context, AggNormalDetailViewModel vm) {
                        if (vm.isRoomEmpty) {
                          /// 空页面
                          return EmptyView(
                            vm: vm.aggVm,
                          );
                        }
                        return StoreConnector<SmartHomeState, DetailRoomList>(
                            distinct: true,
                            builder: (BuildContext context,
                                DetailRoomList contentRoomList) {
                              return _buildContentView(vm);
                            },
                            converter: (Store<SmartHomeState> store) {
                              return vm.aggVm.detailRoomsModel;
                            });
                      }),
                ),
                EditWidgetOverlay(
                  enableAnimation: false,
                  showEditWidget: smartHomeStore.state.isEditState,
                )
              ],
            )));
  }

  AggregationBaseViewModel _buildAggVm(Store<SmartHomeState> store) {
    AggregationBaseViewModel? viewModel = store.state.deviceState
        .allCardViewModelMap[widget.aggregationId] as AggregationBaseViewModel?;
    if (viewModel == null) {
      final SmartHomeDevice device = SmartHomeDevice();
      if (isEnvAgg(widget.aggregationId)) {
        viewModel = AggEnvViewModel(device: device);
      } else if (isDeviceLightAggregation(widget.aggregationId)) {
        viewModel = AggregationLightViewModel(device: device);
      } else {
        viewModel = AggregationCurtainViewModel(device: device);
      }
    }
    return viewModel;
  }

  Widget _buildContentView(AggNormalDetailViewModel viewModel) {
    final AggregationBaseViewModel aggregationBaseViewModel = viewModel.aggVm;

    return DefaultTextStyle(
      style: TextStyle(
        color: AppSemanticColors.item.primary,
        fontFamilyFallback: fontFamilyFallback(),
      ),
      child: ScrollConfiguration(
        behavior: NoneOverScrollBehavior(),
        child: CustomScrollView(
          key: _customScrollViewKey,
          physics: const AlwaysScrollableScrollPhysics(),
          controller: _scrollController,
          slivers: <Widget>[
            /// 头部
            HeaderWidget(
              vm: aggregationBaseViewModel,
              itemCallback: (int index) {
                smartHomeStore.dispatch(UpdateAggregationRoomIndexAction(
                    index, aggregationBaseViewModel.deviceId));
                _scrollToRoom(index);
              },
            ),
            _buildWholeHouseEnvCard(aggregationBaseViewModel),
            ..._buildRoomList(aggregationBaseViewModel, _scrollController),
            SliverToBoxAdapter(
              child: WhoseHouseAirWidget(
                viewModel: aggregationBaseViewModel,
              ),
            ),
            SliverToBoxAdapter(
              child: EditButtonWidget(vm: aggregationBaseViewModel),
            ),
            const SafeBottomAreaSliver(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildRoomList(
      AggregationBaseViewModel viewModel, AutoScrollController controller) {
    final List<Widget> roomList = <Widget>[];

    for (int i = 0; i < viewModel.detailRoomsModel.roomList.length; i++) {
      roomList.add(_buildRoomHeader(viewModel, controller, i));
      roomList.add(_buildRoomDeviceList(viewModel, i));
    }
    return roomList;
  }

  Widget _buildRoomDeviceList(
      AggregationBaseViewModel viewModel, int roomIndex) {
    final DetailRoomListItem contentRoomListItem =
        viewModel.detailRoomsModel.roomList[roomIndex];
    final DeviceListWidget deviceList = DeviceListWidget(
      cardSortIdList: contentRoomListItem.deviceList,
      dragEnable: !isFamilyMemberRole(),
      roomId: contentRoomListItem.roomId,
      onReorder: (List<String> sortedIdList, String roomId) {
        gioTrack(GioConst.gioDragFinished, <String, String>{
          'source': SmartHomeEditManager.getSourceForGio(),
        });
        smartHomeStore.dispatch(SmallCardDragInAggFinishedAction(
            sortedDeviceIdList: sortedIdList,
            roomId: roomId,
            deviceId: viewModel.deviceId));
      },
    );
    return deviceList.buildSliver(context);
  }

  Widget _buildRoomHeader(AggregationBaseViewModel viewModel,
      AutoScrollController controller, int roomIndex) {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return store.state.isEditState;
        },
        builder: (BuildContext context, bool isEditState) {
          return RoomHeader(
            vm: viewModel,
            controller: controller,
            roomIndex: roomIndex,
            isEditState: isEditState,
          );
        });
  }

  Widget _buildWholeHouseEnvCard(AggregationBaseViewModel viewModel) {
    if (viewModel is AggEnvViewModel) {
      return StoreConnector<SmartHomeState, AggWholeHouseEnvInfo>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return aggSelectors.wholeHouseEnvSelector(store.state);
          },
          builder: (BuildContext context, AggWholeHouseEnvInfo envVm) {
            return SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: <Widget>[
                    Padding(
                        padding: const EdgeInsets.only(
                            top: 12, left: 4, right: 4, bottom: 20),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            '环境',
                            textAlign: TextAlign.left,
                            style: _titleTextStyle,
                          ),
                        )),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 4, top: 12, bottom: 12),
                          child: Text(
                            '全屋',
                            style: TextStyle(
                              fontSize: 17,
                              color: AppSemanticColors.item.primary,
                              fontFamilyFallback: fontFamilyFallback(),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            if (envVm.isEditState) {
                              return;
                            }
                            if (isFamilyMemberRole()) {
                              ToastHelper.showToast(
                                  SmartHomeConstant.contactFamilyRoleWarning);
                              return;
                            }
                            gioAggDetailBtnClick(
                                GioConst.aggDetailBtn,
                                viewModel.deviceId,
                                AggregationDeviceConstant.gioWholeHouseEnvInfo);
                            Navigator.of(context).push(
                              MaterialPageRoute<void>(
                                builder: (BuildContext context) =>
                                    EnvDeviceSettingPage(
                                  spaceId:
                                      smartHomeStore.state.familyState.familyId,
                                ),
                              ),
                            );
                          },
                          child: Opacity(
                            opacity: envVm.isEditState ? 0.39 : 1,
                            child: Row(
                              children: <Widget>[
                                Padding(
                                  padding: const EdgeInsets.only(right: 2),
                                  child: Text(
                                    '设置',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontFamilyFallback: fontFamilyFallback(),
                                      color: AppSemanticColors.item.secWeaken,
                                    ),
                                  ),
                                ),
                                Image.asset(
                                  'assets/icons/wash_select_more.webp',
                                  package: SmartHomeConstant.package,
                                  width: 12,
                                  height: 12,
                                  color: AppSemanticColors.item.secWeaken,
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                    _buildWholeHouseEnvCardlWidget(envVm, viewModel.deviceId),
                  ],
                ),
              ),
            );
          });
    } else {
      return const SliverToBoxAdapter(
        child: SizedBox(),
      );
    }
  }

  Widget _buildWholeHouseEnvCardlWidget(
      AggWholeHouseEnvInfo envVm, String deviceId) {
    return GestureDetector(
      onTap: () {
        if (envVm.isEditState) {
          return;
        }
        if (isFamilyMemberRole()) {
          ToastHelper.showToast(SmartHomeConstant.contactFamilyRoleWarning);
          return;
        }
        gioTrack(GioConst.wholeHouseEnvSettings,
            <String, String>{'page_type': '全屋环境管理页'});
        Navigator.of(context).push(
          MaterialPageRoute<void>(
            builder: (BuildContext context) => EnvDeviceSettingPage(
              spaceId: smartHomeStore.state.familyState.familyId,
            ),
          ),
        );
      },
      child: Opacity(
        opacity: envVm.isEditState ? 0.39 : 1,
        child: Container(
          height: 84,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: AppSemanticColors.background.primary,
          ),
          child: envVm.envList.isNotEmpty
              ? _buildEnvContent(envVm.envList, context)
              : _buildEnvEmptyContent(),
        ),
      ),
    );
  }

  Widget _buildEnvContent(
      List<AggEnvModel> envModelList, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: <Widget>[
        ...envModelList.map((AggEnvModel envModel) {
          return Stack(
            clipBehavior: Clip.none,
            children: <Widget>[
              Container(
                width: 80,
                color: Colors.transparent,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    RichText(
                      maxLines: 1,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      text: SmartHomeSpan.textSpan(
                        text: envModel.value,
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                        color: AppSemanticColors.item.primary,
                        children: <InlineSpan>[
                          SmartHomeSpan.textSpan(
                            text: envModel.unit,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppSemanticColors.item.primary,
                          ),
                        ],
                      ),
                    ),
                    Text(
                      envModel.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppSemanticColors.item.secWeaken,
                      ),
                    ),
                  ],
                ),
              ),
              _buildEnvPropertyBadge(envModel),
              _buildQuestionIcon(envModel),
            ],
          );
        })
      ],
    );
  }

  Widget _buildEnvPropertyBadge(AggEnvModel envModel) {
    if (!AggregationWidgetsUtils.isPM25(envModel.name)) {
      return const SizedBox();
    }
    final PropertyBadgeModel propertyBadge =
        AggregationWidgetsUtils.buildPropertyBadge(envModel);

    return Positioned(
      top: -5,
      right: 4,
      child: Container(
          width: 18,
          height: 16,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(3)),
            color: Color(propertyBadge.color),
          ),
          child: Center(
            child: Text(
              propertyBadge.text,
              textAlign: TextAlign.center,
              style: TextStyle(
                  height: 1,
                  color: Colors.white,
                  fontSize: 10,
                  fontFamilyFallback: fontFamilyFallback(),
                  fontWeight: FontWeight.w400),
            ),
          )),
    );
  }

  Widget _buildQuestionIcon(AggEnvModel envModel) {
    if (!AggregationWidgetsUtils.isPM25(envModel.name)) {
      return const SizedBox();
    }

    return Positioned(
      bottom: 5,
      right: 7,
      child: GestureDetector(
        onTap: () {
          gioTrack(GioConst.wholeHouseEnvQuestionClick);
          AggregationWidgetsUtils.showQuestionDialog(context, envModel);
        },
        child: Image.asset(
          'assets/icons/question.webp',
          width: 12,
          height: 12,
          package: SmartHomeConstant.package,
        ),
      ),
    );
  }

  Widget _buildEnvEmptyContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            '暂无环境信息',
            style: TextStyle(
              fontSize: 16,
              color: AppSemanticColors.item.primary,
              fontWeight: FontWeight.w500,
              fontFamilyFallback: fontFamilyFallback(),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Image.asset(
            'assets/images/icon_aggregation_no_device.webp',
            package: SmartHomeConstant.package,
            width: 32,
            height: 32,
          ),
        )
      ],
    );
  }
}

class EmptyView extends StatelessWidget {
  const EmptyView({
    super.key,
    required this.vm,
  });

  final AggregationBaseViewModel vm;

  @override
  Widget build(BuildContext context) {
    final double _paddingTop = MediaQuery.of(context).padding.top;
    return Container(
      color: AppSemanticColors.background.secondary,
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: <Widget>[
          Column(children: <Widget>[
            _buildStatusBar(_paddingTop),
            _buildHeaderContent(context),
          ]),
          _buildEmptyContent(context)
        ],
      ),
    );
  }

  Center _buildEmptyContent(BuildContext context) {
    return Center(
      child: SizedBox(
          height: 252,
          width: MediaQuery.of(context).size.width,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const SizedBox(height: 56),
              Image.asset(
                'assets/images/icon_aggregation_detail_no_device.webp',
                package: 'smart_home',
                width: 96,
                height: 96,
              ),
              const SizedBox(
                height: 8,
              ),
              Text(
                vm.manageListEmpty,
                style: TextStyle(
                    fontSize: 14,
                    color: AppSemanticColors.item.secWeaken,
                    fontFamilyFallback: fontFamilyFallback(),
                    fontWeight: FontWeight.w400),
              ),
              const SizedBox(
                height: 20,
              ),
              if (vm.device.basicInfo.deviceId.isNotEmpty)
                EditButtonWidget(vm: vm)
            ],
          )),
    );
  }

  Widget _buildStatusBar(double paddingTop) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
        child: Container(
            color: AppSemanticColors.background.secondary, height: paddingTop),
        value: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ));
  }

  Widget _buildHeaderContent(BuildContext context) {
    return Expanded(
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
        child: Stack(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(top: 46, left: 4),
              child: Container(
                height: 34,
                alignment: Alignment.centerLeft,
                child: Text(vm.deviceName, style: _titleTextStyle),
              ),
            ),
            GestureDetector(
              onTap: () {
                EditPresenterManager.changePageWithHomeAndAgg(
                    EditPageType.home);
                Navigator.pop(context);
              },
              child: Image.asset(
                'assets/images/icon_aggregation_back.webp',
                package: 'smart_home',
                width: 24,
                height: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({
    super.key,
    required this.vm,
    required this.itemCallback,
  });

  final AggregationBaseViewModel vm;
  final void Function(int index) itemCallback;

  Widget _getSliverHeader(BuildContext context) {
    final double _paddingTop = MediaQuery.of(context).padding.top;
    return StoreConnector<SmartHomeState, bool>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return vm.devicesByRoom.keys.toList().isEmpty;
      },
      builder: (BuildContext context, bool isRoomEmpty) {
        const int maxHeightWithoutRoom = 44;
        const int maxHeightWithRoom = 192;
        const int minHeightWithoutRoom = 44;
        const int minHeightWithRoom = 110;
        final double maxHeightValue = ((isRoomEmpty || !vm.isShowRoomList)
                ? maxHeightWithoutRoom
                : maxHeightWithRoom) +
            _paddingTop;
        final double minHeightValue =
            (vm.isShowRoomList ? minHeightWithRoom : minHeightWithoutRoom) +
                _paddingTop;
        return SliverPersistentHeader(
          pinned: true,
          floating: true,
          delegate: SliverHeaderDelegate.builder(
            maxHeight: maxHeightValue,
            minHeight: minHeightValue,
            builder: (BuildContext context, double shrinkOffset,
                bool overlapsContent) {
              final double _shrinkOffset = shrinkOffset;

              return ColoredBox(
                color: AppSemanticColors.background.secondary,
                child: vm.devicesByRoom.keys.toList().isEmpty
                    ? _getHeaderStackWithoutRoom(context, _paddingTop)
                    : _getHeaderStackWithRoomList(
                        context, _paddingTop, _shrinkOffset, vm),
              );
            },
          ),
        );
      },
    );
  }

  Stack _getHeaderStackWithoutRoom(BuildContext context, double _paddingTop) {
    return Stack(
      children: <Widget>[
        Column(children: <Widget>[
          AnnotatedRegion<SystemUiOverlayStyle>(
              child: Container(
                  color: AppSemanticColors.background.secondary,
                  height: _paddingTop),
              value: const SystemUiOverlayStyle(
                systemNavigationBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
              )),
          Expanded(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
              child: Stack(
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.only(top: 46, left: 4),
                    child: Container(
                      height: 34,
                      alignment: Alignment.topLeft,
                      child: Text(vm.deviceName, style: _titleTextStyle),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Image.asset(
                      'assets/images/icon_aggregation_back.webp',
                      package: 'smart_home',
                      width: 24,
                      height: 24,
                    ),
                  ),
                ],
              ),
            ),
          )
        ]),
      ],
    );
  }

  Stack _getHeaderStackWithRoomList(BuildContext context, double _paddingTop,
      double _shrinkOffset, AggregationBaseViewModel vm) {
    return Stack(
      children: <Widget>[
        Column(children: <Widget>[
          AnnotatedRegion<SystemUiOverlayStyle>(
              child: Container(
                  color: AppSemanticColors.background.secondary,
                  height: _paddingTop),
              value: const SystemUiOverlayStyle(
                systemNavigationBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
              )),
          _getHeaderContent(context, _shrinkOffset, vm),
          if (vm.isShowRoomList) _getRoomList(context, vm),
        ]),
      ],
    );
  }

  Expanded _getHeaderContent(
      BuildContext context, double _shrinkOffset, AggregationBaseViewModel vm) {
    // 根据动效要求调整参数
    const double fadeRange = 10.0; // 透明度变化区间10px
    const double backButtonHeight = 24.0; // 返回按钮高度

    // 根据是否有房间列表来计算透明度和触发时机
    double navTitleOpacity = 0.0;
    double mainTitleOpacity = 1.0;
    double statusTextOpacity = 1.0;

    if (vm.isShowRoomList) {
      // 2.5 有房间列表吸顶的情况：标题栏操作按钮和标签的情况
      // 强调型标题被导航栏完全遮住的相对滑动距离
      // 基于实际布局计算：强调型标题top=46，导航栏高度=24，所以Y1=46-24=22
      const double y1 = 22.0;

      // 上滑时：Y1到Y1+10px之间，导航栏标题不透明度由0%变为100%
      // 下滑时：Y1+10px到Y1之间，导航栏标题不透明度由100%变为0%
      if (_shrinkOffset > y1) {
        navTitleOpacity = ((_shrinkOffset - y1) / fadeRange).clamp(0.0, 1.0);
      } else {
        navTitleOpacity = 0.0;
      }

      mainTitleOpacity = 1.0 - navTitleOpacity;
      statusTextOpacity = mainTitleOpacity; // 状态文本与强调型标题同步
    } else {
      // 2.4 没有房间列表吸顶的情况：仅有标题的情况
      // 强调型标题与导航栏接触时的滑动距离
      const double y2 = 22.0; // 强调型标题与导航栏接触时的滑动距离

      // 上滑时：Y2到Y2+10px之间，导航栏标题不透明度由0%变为100%
      // 下滑时：Y2+10px到Y2之间，导航栏标题不透明度由100%变为0%
      if (_shrinkOffset > y2) {
        navTitleOpacity = ((_shrinkOffset - y2) / fadeRange).clamp(0.0, 1.0);
      }
      mainTitleOpacity = 1.0; // 强调型标题始终显示
      statusTextOpacity = 1.0; // 状态文本始终显示，不随滚动变化
    }

    return Expanded(
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
        child: Stack(
          children: <Widget>[
            // 内容区域 - 使用ClipRect确保不覆盖返回按钮区域
            Positioned(
              left: 0,
              right: 0,
              top: backButtonHeight, // 从返回按钮底部开始
              bottom: 0,
              child: ClipRect(
                child: Stack(
                  children: <Widget>[
                    // 强调型标题（展开时显示）
                    Positioned(
                      left: 4,
                      top: (46 - backButtonHeight - _shrinkOffset)
                          .clamp(-46.0, 46.0 - backButtonHeight),
                      child: Opacity(
                        opacity: mainTitleOpacity,
                        child: Container(
                          height: 34,
                          alignment: Alignment.topLeft,
                          child: Text(vm.deviceName, style: _titleTextStyle),
                        ),
                      ),
                    ),
                    // 状态文本
                    Positioned(
                      left: 4,
                      top: vm.isShowRoomList
                          ? (84 - backButtonHeight - _shrinkOffset)
                              .clamp(14.0, 84.0 - backButtonHeight)
                          : 14.0, // 没有房间列表时固定位置
                      child: Opacity(
                        opacity: statusTextOpacity,
                        child: StoreConnector<SmartHomeState, String>(
                          distinct: true,
                          converter: (Store<SmartHomeState> store) {
                            final String homeStatus = vm.getStatus();
                            return homeStatus;
                          },
                          builder: (BuildContext context, String homeStatus) {
                            return Container(
                              height: 20,
                              alignment: Alignment.topLeft,
                              child: Text(homeStatus,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppSemanticColors.item.secWeaken,
                                    fontFamilyFallback: fontFamilyFallback(),
                                    fontWeight: FontWeight.w400,
                                  )),
                            );
                          },
                        ),
                      ),
                    ),
                    // 操作按钮区域（在内容区域内）
                    _buildOperationAllBtn(
                        vm.isOperate, _shrinkOffset, 22.0, backButtonHeight),
                  ],
                ),
              ),
            ),
            // 返回按钮 - 放在最上层，确保始终可见且不被遮挡
            Positioned(
              left: 0,
              top: 0,
              child: SizedBox(
                width: 24,
                height: 24,
                child: GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Image.asset(
                    'assets/images/icon_aggregation_back.webp',
                    package: 'smart_home',
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
            ),
            // 导航栏标题（收缩后显示）- 居中但避开返回按钮区域
            Positioned(
              left: 48, // 给返回按钮留出空间
              right: 48, // 给操作按钮留出空间
              top: 0,
              height: 24,
              child: Opacity(
                opacity: navTitleOpacity,
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    vm.deviceName,
                    style: TextStyle(
                      fontSize: 17,
                      color: AppSemanticColors.item.primary,
                      fontFamilyFallback: fontFamilyFallback(),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationAllBtn(
      bool isOperate, double shrinkOffset, double y1, double backButtonHeight) {
    if (!isOperate) {
      return const SizedBox.shrink();
    }

    // 计算操作按钮的透明度，与强调型标题保持一致
    const double fadeRange = 10.0;
    double buttonOpacity = 1.0;
    if (shrinkOffset > y1) {
      buttonOpacity = 1.0 - ((shrinkOffset - y1) / fadeRange).clamp(0.0, 1.0);
    }

    return Positioned(
      right: 0,
      top: (61 - backButtonHeight - shrinkOffset)
          .clamp(-37.0, 61.0 - backButtonHeight),
      child: Opacity(
        opacity: buttonOpacity,
        child: StoreConnector<SmartHomeState, HomeBtnState>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            final DeviceStateForAggregation homeDeviceState =
                vm.deviceStateForAggregation;
            final HomeBtnState homeBtnState = HomeBtnState(
                homeDeviceState, vm.aggregationOperating,
                isEditState: store.state.isEditState);
            return homeBtnState;
          },
          builder: (BuildContext context, HomeBtnState homeBtnState) {
            return AggregationDetailBtnList(
                vm: AggregationBtnViewModel(
              isEditState: homeBtnState.isEditState,
              allClose: !homeBtnState.homeOperating &&
                  (homeBtnState.homeDeviceState ==
                          DeviceStateForAggregation.ALL_CLOSE ||
                      homeBtnState.homeDeviceState ==
                          DeviceStateForAggregation.ALL_OFFLINE),
              // 离线认为关闭
              allOpen: !homeBtnState.homeOperating &&
                  homeBtnState.homeDeviceState ==
                      DeviceStateForAggregation.ALL_OPEN,
              closeClickCallback: (BuildContext context) {
                gioAggDetailBtnClick(GioConst.aggDetailBtn, vm.deviceId,
                    AggregationDeviceConstant.gioWholeHouseClose);
                vm.turnOffAllDevices(context);
              },
              openClickCallback: (BuildContext context) {
                gioAggDetailBtnClick(GioConst.aggDetailBtn, vm.deviceId,
                    AggregationDeviceConstant.gioWholeHouseOpen);
                vm.turnOnAllDevices(context);
              },
            ));
          },
        ),
      ),
    );
  }

  Container _getRoomList(BuildContext context, AggregationBaseViewModel vm) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 66,
      padding: const EdgeInsets.only(bottom: 8, top: 8, left: 16, right: 16),
      color: AppSemanticColors.background.secondary,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: StoreConnector<SmartHomeState, int>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return vm.roomList.length;
          },
          builder: (BuildContext context, int roomListLength) {
            return Row(
              children: List<Widget>.generate(roomListLength, (int index) {
                return GestureDetector(
                  onTap: () {
                    itemCallback(index);
                  },
                  child: StoreConnector<SmartHomeState, bool>(
                    distinct: true,
                    converter: (Store<SmartHomeState> store) {
                      final AggregationDevicesByRoomViewModel? roomModel =
                          vm.devicesByRoom[vm.roomList[index]];
                      bool isRoomOpen = false;
                      if (roomModel is AggregationDevicesByRoomViewModel) {
                        isRoomOpen = roomModel.isRoomOpen;
                      }
                      return isRoomOpen;
                    },
                    builder: (BuildContext context, bool isRoomOpen) {
                      final bool showFloor =
                          smartHomeStore.state.deviceState.cardShowFloor;
                      final SmartHomeRoomInfo room = vm.roomList[index];
                      String floorName = '', roomName = room.roomName;
                      if (showFloor) {
                        floorName = room.floorName;
                      }
                      final String roomNameIndex = '$floorName$roomName';
                      return IntrinsicWidth(
                        stepWidth: 1,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(
                            minWidth: 80,
                          ),
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
                            decoration: BoxDecoration(
                                color: AppSemanticColors
                                    .component.secondary.invert,
                                borderRadius: BorderRadius.circular(12)),
                            child: Column(
                              children: <Widget>[
                                ConstrainedBox(
                                  constraints: const BoxConstraints(
                                    minWidth: 56,
                                    maxWidth: 126,
                                  ),
                                  child: Center(
                                    child: Text(
                                      roomNameIndex,
                                      style: TextStyle(
                                        color: AppSemanticColors.item.primary,
                                        fontFamilyFallback:
                                            fontFamilyFallback(),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      maxLines: 1,
                                      softWrap: false,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                Container(
                                  width: 12,
                                  height: 2,
                                  margin: const EdgeInsets.only(top: 8),
                                  decoration: isRoomOpen
                                      ? BoxDecoration(
                                          color: AppSemanticColors
                                              .component.primary.fill,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        )
                                      : BoxDecoration(
                                          color:
                                              AppSemanticColors.item.secWeaken,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              }),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _getSliverHeader(context);
  }
}

class RoomHeader extends StatelessWidget {
  const RoomHeader(
      {super.key,
      required this.vm,
      required this.controller,
      this.isEditState = false,
      required this.roomIndex});

  final AggregationBaseViewModel vm;
  final AutoScrollController controller;
  final bool isEditState;
  final int roomIndex;

  String _getSubTitleByRoomInfo(DeviceStateForAggregation deviceState,
      String roomName, int openCount, bool roomOperating) {
    if (roomOperating) {
      return '正在运行中';
    }
    final DeviceStateForAggregation state = deviceState;
    final bool isLight = isDeviceLightAggregation(vm.deviceId);
    final bool isCurtain = isDeviceCurtainAggregation(vm.deviceId);
    if (state == DeviceStateForAggregation.ALL_OPEN) {
      if (isLight) {
        return AggregationDetailConstant.detailLightSubTitleAllOpen;
      }
      if (isCurtain) {
        return AggregationDetailConstant.detailCurtainSubTitleAllOpen;
      }
    } else if (state == DeviceStateForAggregation.ALL_CLOSE ||
        state == DeviceStateForAggregation.ALL_OFFLINE) {
      // 离线算关闭
      if (isLight) {
        return AggregationDetailConstant.detailLightSubTitleAllClose;
      }
      if (isCurtain) {
        return AggregationDetailConstant.detailCurtainSubTitleAllClose;
      }
    } else if (state == DeviceStateForAggregation.PART_OPEN) {
      if (isLight) {
        return '$openCount${AggregationDetailConstant.detailLightSubTitlePartOpen}';
      }
      if (isCurtain) {
        return '$openCount${AggregationDetailConstant.detailCurtainSubTitlePartOpen}';
      }
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: AutoScrollTag(
          key: ValueKey<String>('room_$roomIndex'),
          controller: controller,
          index: roomIndex,
          // 移除高亮效果
          highlightColor: Colors.transparent,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(
                height: roomIndex == 0 ? 12 : 8,
              ),
              _buildRoomHeader(vm.detailRoomsModel.roomList, roomIndex, vm),
            ],
          )),
    );
  }

  Widget _buildRoomHeader(List<DetailRoomListItem> rooms, int roomIndex,
      AggregationBaseViewModel vm) {
    return StoreConnector<SmartHomeState, DetailRoomListItem>(
        distinct: true,
        builder:
            (BuildContext context, DetailRoomListItem contentRoomListItem) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            height: 75,
            child: Stack(
              children: <Widget>[
                // 房间名称
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Text(
                    rooms[roomIndex].roomName,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppSemanticColors.item.primary,
                      fontFamilyFallback: fontFamilyFallback(),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                // 房间状态
                Padding(
                    padding: const EdgeInsets.only(top: 42),
                    child: vm is AggEnvViewModel
                        ? _buildRoomEnvSubTitle(contentRoomListItem)
                        : _buildRoomStatusSubTitle(
                            contentRoomListItem,
                            rooms,
                            roomIndex,
                            vm,
                          )),
                _buildOperationRoomBtn(vm.isOperate, contentRoomListItem),
                AggregationSelectAllWidget(
                  roomInfo: vm.roomList[roomIndex],
                  aggregationId: vm.deviceId,
                ),
              ],
            ),
          );
        },
        converter: (Store<SmartHomeState> store) {
          return vm.detailRoomsModel.roomList[roomIndex];
        });
  }

  Widget _buildRoomStatusSubTitle(
      DetailRoomListItem contentRoomListItem,
      List<DetailRoomListItem> rooms,
      int roomIndex,
      AggregationBaseViewModel vm) {
    return StoreConnector<SmartHomeState, String>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          final bool roomOperating =
              vm.roomIdOperating == contentRoomListItem.roomId &&
                  vm.roomOperating;
          final String text = _getSubTitleByRoomInfo(
              contentRoomListItem.roomStatus,
              rooms[roomIndex].roomName,
              contentRoomListItem.openCount,
              roomOperating);
          return text;
        },
        builder: (BuildContext context, String text) {
          return Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: AppSemanticColors.item.secWeaken,
              fontFamilyFallback: fontFamilyFallback(),
              fontWeight: FontWeight.w400,
            ),
          );
        });
  }

  Widget _buildRoomEnvSubTitle(DetailRoomListItem room) {
    return StoreConnector<SmartHomeState, String>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          final SpaceEnvironmentModel? envInfo =
              store.state.wholeHouseState.envDeviceState.spaces[room.roomId];
          final List<AggEnvModel> _envModelList =
              AggSelectors.getEnvModelList(envInfo);

          if (_envModelList.isNotEmpty) {
            const int maxEnvToDisplay = 3; // 定义环境项目显示最大数量
            final int limit = _envModelList.length < maxEnvToDisplay
                ? _envModelList.length
                : maxEnvToDisplay;
            final List<String> _subTitleList = _envModelList
                .sublist(0, limit)
                .map((AggEnvModel e) => '${e.name}${e.value}${e.unit}')
                .toList();
            return _subTitleList.join(' | ');
          } else {
            return AggregationDeviceConstant.aggNoEnvInfo;
          }
        },
        builder: (BuildContext context, String text) {
          return GestureDetector(
            onTap: () {
              if (isEditState) {
                return;
              }
              if (isFamilyMemberRole()) {
                ToastHelper.showToast(
                    SmartHomeConstant.contactFamilyRoleWarning);
                return;
              }
              gioAggDetailBtnClick(GioConst.aggDetailBtn, vm.deviceId,
                  AggregationDeviceConstant.gioRoomEnvInfo);
              Navigator.of(context).push(
                MaterialPageRoute<void>(
                  builder: (BuildContext context) => EnvDeviceSettingPage(
                    spaceId: room.roomId ?? '',
                    spaceName: room.roomName ?? '',
                  ),
                ),
              );
            },
            child: Opacity(
              opacity: isEditState ? 0.39 : 1,
              child: Row(
                children: <Widget>[
                  Text(text,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppSemanticColors.item.secWeaken,
                        fontFamilyFallback: fontFamilyFallback(),
                        fontWeight: FontWeight.w400,
                      ),
                      maxLines: 1,
                      softWrap: false,
                      overflow: TextOverflow.ellipsis),
                  Image.asset(
                    'assets/icons/wash_select_more.webp',
                    package: SmartHomeConstant.package,
                    width: 12,
                    height: 12,
                    color: AppSemanticColors.item.secWeaken,
                  ),
                ],
              ),
            ),
          );
        });
  }

  Widget _buildOperationRoomBtn(
      bool isOperate, DetailRoomListItem contentRoomListItem) {
    if (!isOperate || isEditState) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Align(
          alignment: Alignment.topRight,
          child: StoreConnector<SmartHomeState, AggregationBtnViewModel>(
            distinct: true,
            converter: (Store<SmartHomeState> store) {
              final bool roomOperating =
                  vm.roomIdOperating == contentRoomListItem.roomId &&
                      vm.roomOperating;
              return AggregationBtnViewModel(
                  isEditState: store.state.isEditState,
                  allClose: !roomOperating &&
                      (contentRoomListItem.roomStatus ==
                              DeviceStateForAggregation.ALL_CLOSE ||
                          contentRoomListItem.roomStatus ==
                              DeviceStateForAggregation.ALL_OFFLINE),
                  // 离线认为关闭
                  allOpen: !roomOperating &&
                      contentRoomListItem.roomStatus ==
                          DeviceStateForAggregation.ALL_OPEN,
                  closeClickCallback: (BuildContext context) {
                    vm.roomClickTurnOffAllForDetail(
                        contentRoomListItem.roomViewModel, context);
                  },
                  openClickCallback: (BuildContext context) {
                    vm.roomClickTurnOnAllForDetail(
                        contentRoomListItem.roomViewModel, context);
                  });
            },
            builder: (BuildContext context, AggregationBtnViewModel vm) {
              return AggregationDetailBtnList(vm: vm);
            },
          )),
    );
  }
}
