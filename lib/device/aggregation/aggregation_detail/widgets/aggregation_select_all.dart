import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_state.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/aggregation_room_select_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

class AggregationSelectAllWidget extends StatelessWidget {
  const AggregationSelectAllWidget({
    super.key,
    required this.roomInfo,
    required this.aggregationId,
  });

  final SmartHomeRoomInfo roomInfo;
  final String aggregationId;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, SelectAllBtnViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return _createViewModel(store);
      },
      builder: (BuildContext context, SelectAllBtnViewModel viewModel) {
        if (!viewModel.visible) {
          return const SizedBox();
        }
        return _buildSelectionButton(viewModel);
      },
    );
  }

  SelectAllBtnViewModel _createViewModel(Store<SmartHomeState> store) {
    final RoomSelectStatus roomSelectStatus = store.state.aggregationState
            .aggregationRoomSelectStatus[aggregationId] ??
        RoomSelectStatus();
    final bool isRoomSelected =
        roomSelectStatus.isRoomSelected(roomInfo.roomId);
    return SelectAllBtnViewModel(
      btnText: isRoomSelected ? '全不选' : '全选',
      btnWidth: isRoomSelected ? 74 : 60,
      visible: store.state.isEditState,
      isSelectAll: isRoomSelected,
    );
  }

  Widget _buildSelectionButton(SelectAllBtnViewModel viewModel) {
    return GestureDetector(
      onTap: () {
        smartHomeStore.dispatch(UpdateRoomSelectAllStatusAction(
          aggregationRoomSelectVM: AggregationRoomSelectViewModel(
              aggregationId: aggregationId,
              roomInfo: roomInfo,
              isSelectAll: !viewModel.isSelectAll),
        ));
      },
      child: Container(
        padding: const EdgeInsets.only(top: 24),
        alignment: Alignment.topRight,
        child: ClickEffectCircularWidget(
          width: viewModel.btnWidth,
          offColor: AppSemanticColors.component.secondary.invert,
          height: 28,
          enable: true,
          isOn: false,
          borderRadius: 16,
          child: Align(
            child: SmartHomeText(
              text: viewModel.btnText,
              fontSize: 14,
              color: AppSemanticColors.component.secondary.on,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}

class SelectAllBtnViewModel {
  final String btnText;
  final double btnWidth;
  final bool visible;
  final bool isSelectAll;

  SelectAllBtnViewModel({
    required this.btnText,
    required this.btnWidth,
    required this.visible,
    required this.isSelectAll,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is SelectAllBtnViewModel &&
          runtimeType == other.runtimeType &&
          btnText == other.btnText &&
          btnWidth == other.btnWidth &&
          visible == other.visible &&
          isSelectAll == other.isSelectAll;

  @override
  int get hashCode =>
      btnText.hashCode ^
      btnWidth.hashCode ^
      visible.hashCode ^
      isSelectAll.hashCode;
}
