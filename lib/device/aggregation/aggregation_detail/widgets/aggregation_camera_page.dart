import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/agg_camera/aggregation_camera_vm.dart';
import 'package:smart_home/device/aggregation/agg_store/agg_selectors.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_action.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/presenter/camera_presenter.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/agg_unsupport_camera.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_camera_reorder_page.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/base_aggregation_widget_state.dart';
import 'package:smart_home/device/aggregation/utils/agg_utils.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/device_widget/camera.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:upsystem/upsystem.dart';

import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../widget_common/card_text_style.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';
import '../utils/aggregation_presenter.dart';

const double _kListItemSpacing = 16.0;

const int _kGridCrossAxisCount = 2;
const double _kGridCrossAxisSpacing = 12.0;
const double _kGridMainAxisSpacing = 12.0;
const double _kGridChildAspectRatio = 173 / 130;

const double marginTop = 46;

String _aggRecordTitle = '排序';

const double _popRadiusSize = 12;
const double _popShadowRadiusSize = 16;

const double _popWidth = 112;
const double _popHeight = 104;

const bool enableEditCard = true;

Color _popShadowColor = const Color.fromRGBO(0, 0, 0, 0.12);

class AggregationCameraPage extends StatefulWidget {
  const AggregationCameraPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return AggregationCameraPageState();
  }
}

class AggregationCameraPageState extends BaseAggregationWidgetState<
    AggregationCameraPage, AggregationCamerasVMWrapper> {
  @override
  Future<void> onBackPress() {
    AggregationCameraPresenterManager.instance
        .dispatchStopAllCamera(smartHomeStore);
    Future<void>.delayed(const Duration(milliseconds: 200), () {
      Navigator.of(context).pop();
    });
    return Future<void>.value();
  }

  @override
  void initState() {
    super.initState();
    CameraAggPresenter.getCameraAggSortedList();
  }

  @override
  String get pageName {
    return 'cameraBaseAggregationDetailWidgetState';
  }

  CardBaseViewModel? _getViewModelForDevice(String deviceId) {
    final CardBaseViewModel? vm =
        smartHomeStore.state.deviceState.allCardViewModelMap[deviceId];
    if (vm == null) {
      DevLogger.debug(
          tag: 'AggregationCameraPage',
          msg: 'ViewModel is null for deviceId: $deviceId');
    }
    return vm;
  }

  Widget _createItem(List<String> cameraList, int index, CameraUIType type) {
    String deviceId = '';
    if (index >= 0 && index < cameraList.length) {
      deviceId = cameraList[index];
    }
    final CardBaseViewModel? vm = _getViewModelForDevice(deviceId);

    if (vm == null) {
      DevLogger.debug(
          tag: 'AggregationCameraPage',
          msg: 'ViewModel is null for deviceId: $deviceId');
      return const SizedBox();
    }

    return wrapEditArea(
        _createCameraItem(deviceId, type, vm), index, cameraList);
  }

  Widget _createCameraItem(
      String deviceId, CameraUIType type, CardBaseViewModel model) {
    if (model is CameraDeviceCardViewModel) {
      return Camera(
        deviceId,
        model.editComponentViewModel,
        -1,
        cameraType: CameraType.AGGREGATION_CARD,
        uiType: type,
      );
    }
    return AggUnSupportCamera(
      deviceId,
      type,
      model is DeviceCardViewModel ? model.editComponentViewModel : null,
    );
  }

  Widget _buildCameraList(AggregationCamerasVMWrapper wrapper) {
    return wrapper.isLargeCard
        ? _buildSliverList(wrapper.cameraList, CameraUIType.big_card)
        : _buildSliverGrid(wrapper.cameraList, CameraUIType.middle_card);
  }

  Widget _buildSliverList(List<String> cameraList, CameraUIType type) {
    return SliverList.separated(
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox(height: _kListItemSpacing);
      },
      itemCount: cameraList.length,
      itemBuilder: (BuildContext context, int index) {
        return _createItem(cameraList, index, type);
      },
    );
  }

  ///该接口扩展 长按编辑
  Widget wrapEditArea(Widget child, int index, List<String> cameraList) {
    if (!enableEditCard) {
      return child;
    }
    return GestureDetector(
      child: child,
      behavior: HitTestBehavior.translucent,
      onLongPress: () {
        final CardBaseViewModel? vm = _getViewModelForDevice(cameraList[index]);
        if (smartHomeStore.state.isEditState) {
          return;
        }
        if (vm != null) {
          if (isFamilyMemberRole()) {
            ToastHelper.showToast(SmartHomeConstant.cardManageWarning);
            return;
          }
          AggregationCameraPresenterManager.instance
              .dispatchStopAllCamera(smartHomeStore);
          UpSystem.impactFeedBack();
          AggregationPresenter.queryAndUpdateAllAggSort();
          smartHomeStore.dispatch(EnterEditStateByCardAction(id: vm.sortId()));
        }
      },
    );
  }

  Widget _buildSliverGrid(List<String> cameraList, CameraUIType type) {
    return SliverGrid.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _kGridCrossAxisCount,
        crossAxisSpacing: _kGridCrossAxisSpacing,
        mainAxisSpacing: _kGridMainAxisSpacing,
        childAspectRatio: _kGridChildAspectRatio,
      ),
      itemCount: cameraList.length,
      itemBuilder: (BuildContext context, int index) {
        return _createItem(cameraList, index, type);
      },
    );
  }

  @override
  Widget? leadingRightWidget(AggregationCamerasVMWrapper wrapper) {
    return GestureDetector(
      child: SizedBox(
        child: Align(
          alignment: Alignment.centerRight,
          child: Image.asset(
            width: 24,
            height: 24,
            'assets/icons/icon_more.webp',
            package: SmartHomeConstant.package,
          ),
        ),
        height: 24,
        width: 24,
      ),
      onTap: () {
        DevLogger.info(tag: 'agg_camera_page', msg: '  user show pup button !');
        showSwitchCameraTypePopDialog(wrapper.isLargeCard, context, () {
          gioAggDetailBtnClick(
              GioConst.aggDetailBtn,
              AggregationDeviceConstant.gioCameraTitle,
              wrapper.isLargeCard
                  ? AggregationDeviceConstant.gioCameraMiddleSize
                  : AggregationDeviceConstant.gioCameraBigSize);
          smartHomeStore.dispatch(
              SwitchAggregationCameraDetailLargeOrSmallAction(
                  !wrapper.isLargeCard));
          Navigator.of(context).pop();
        });
      },
    );
  }

  @override
  Widget mainWidget(AggregationCamerasVMWrapper wrapper) {
    return _buildCameraList(wrapper);
  }

  final AggSelectors aggSelectors = AggSelectors();

  @override
  AggregationCamerasVMWrapper getVmWrapper(Store<SmartHomeState> store) {
    return aggSelectors.aggCameraDetailPageSelector(store.state);
  }
}

Widget buildOption(String text, VoidCallback onTap) {
  return GestureDetector(
    child: Container(
      height: 52,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(left: 16),
      child: Text(
        text,
        textAlign: TextAlign.left,
        style: TextStyle(
            color: AppSemanticColors.item.primary,
            fontSize: 16,
            fontWeight: FontWeight.w400,
            decoration: TextDecoration.none,
            fontFamilyFallback: fontFamilyFallback()),
      ),
      color: AppSemanticColors.background.primary,
    ),
    onTap: onTap,
  );
}

Future<void> showSwitchCameraTypePopDialog(
    bool isLarge, BuildContext context, VoidCallback onSwitch) {
  final String title = isLarge ? '切换中视图' : '切换大视图';

  return showGeneralDialog<void>(
    barrierColor: Colors.transparent,
    barrierDismissible: true,
    barrierLabel: 'family_popup',
    context: context,
    pageBuilder: (BuildContext popContext, Animation<double> animation1,
        Animation<double> animation2) {
      return _buildPopupMenu(context, title, onSwitch);
    },
    transitionBuilder: (BuildContext ctx, Animation<double> a1,
        Animation<double> a2, Widget child) {
      final double curvedValue = Curves.easeOutCubic.transform(a1.value);
      final Offset origin = Offset(MediaQuery.of(context).size.width - 100,
              -MediaQuery.of(context).size.height / 2) +
          Offset(16, 44 + MediaQuery.of(context).padding.top);
      return Transform.scale(
        origin: origin,
        scale: curvedValue,
        child: Opacity(
          opacity: a1.value,
          child: child,
        ),
      );
    },
  );
}

Widget _buildPopupMenu(
    BuildContext context, String title, VoidCallback onSwitch) {
  final double _popLeftMargin = MediaQuery.of(context).size.width - 128;
  return Stack(
    children: <Widget>[
      Positioned(
          top: marginTop + MediaQuery.of(context).padding.top,
          left: _popLeftMargin,
          child: Container(
            decoration: BoxDecoration(
                color: AppSemanticColors.background.primary,
                borderRadius: const BorderRadius.all(
                  Radius.circular(_popRadiusSize),
                ),
                boxShadow: <BoxShadow>[
                  BoxShadow(
                    color: _popShadowColor,
                    blurRadius: _popShadowRadiusSize,
                  ),
                ]),
            child: ClipRRect(
              borderRadius:
                  const BorderRadius.all(Radius.circular(_popShadowRadiusSize)),
              child: SizedBox(
                height: _popHeight,
                width: _popWidth,
                child: Column(
                  children: <Widget>[
                    buildOption(_aggRecordTitle, () {
                      _navigateToSortPage(context);
                    }),
                    buildOption(title, onSwitch),
                  ],
                ),
              ),
            ),
          )),
    ],
  );
}

void _navigateToSortPage(BuildContext context) {
  Navigator.of(context).pushReplacement(
    MaterialPageRoute<void>(
      builder: (BuildContext context) {
        return AggregatioCameraReorderPage();
      },
    ),
  );
}
