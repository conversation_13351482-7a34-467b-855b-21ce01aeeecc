import 'package:flutter/cupertino.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/edit/edit_widget_overlay.dart';
import 'package:smart_home/store/smart_home_state.dart';

class SafeBottomAreaSliver extends StatelessWidget {
  const SafeBottomAreaSliver({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
        child: StoreConnector<SmartHomeState, bool>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.isEditState;
      },
      builder: (BuildContext context, bool inEdit) {
        return SizedBox(
            height: inEdit
                ? getEditWidgetBottomHeight(context)
                : MediaQuery.of(context).padding.bottom);
      },
    ));
  }
}
