import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_btns_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_flex_switch_text_view_model.dart';

import 'aggregation_detail_flex_switch_text.dart';

class AggregationDetailBtnList extends StatelessWidget {
  const AggregationDetailBtnList({super.key, required this.vm});

  final AggregationBtnViewModel vm;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 132,
      height: 28,
      child: Row(
        children: <Widget>[
          AggregationDetailFlexSwitchTextWidget(
              viewModel: AggregationFlexSwitchTextViewModel(
                  text: '全开',
                  enable: _isButtonEnabled(vm),
                  isOn: vm.allOpen,
                  clickCallback: vm.openClickCallback,
                  offColor: AppSemanticColors.component.secondary.invert)),
          const SizedBox(
            width: 12,
          ),
          AggregationDetailFlexSwitchTextWidget(
              viewModel: AggregationFlexSwitchTextViewModel(
                  text: '全关',
                  enable: _isButtonEnabled(vm),
                  isOn: vm.allClose,
                  clickCallback: vm.closeClickCallback,
                  offColor: AppSemanticColors.component.secondary.invert)),
        ],
      ),
    );
  }

  bool _isButtonEnabled(AggregationBtnViewModel vm) =>
      !vm.allOffline && !vm.isEditState;
}
