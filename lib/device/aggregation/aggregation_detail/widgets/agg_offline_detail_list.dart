// Dart: lib/device/aggregation/aggregation_detail/page/aggregation_offline_detail_page.dart

import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/agg_store/agg_selectors.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/agg_offline_detail.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/agg_offline_device_list_widget.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/safe_bottom_area_sliver.dart';
import 'package:smart_home/edit/edit_widget_overlay.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/widget_common/over_scroll_behavior.dart';

import '../../../../common/smart_home_route_manager.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../aggregation_card/util/aggregation_device_util.dart';
import '../utils/aggregation_presenter.dart';
import 'agg_offline_header.dart';
import 'agg_detail_base_state.dart';
import 'no_device_view.dart';

class AggOfflineDetailList extends StatefulWidget {
  final String aggregationId;

  const AggOfflineDetailList({super.key, required this.aggregationId});

  @override
  AggOfflineDetailListState createState() => AggOfflineDetailListState();
}

class AggOfflineDetailListState
    extends AggDetailBaseState<AggOfflineDetailList> {
  final ValueKey<String> scrollViewKey =
      const ValueKey<String>('aggregation_custom_scroll_view');
  final AutoScrollController scrollController = AutoScrollController(
    viewportBoundaryGetter: () => Rect.zero,
    axis: Axis.vertical,
    suggestedRowHeight: 200,
  );
  late final bool isAggOffline;
  final AggSelectors aggSelectors = AggSelectors();

  @override
  String get pageName => isOfflineAgg(widget.aggregationId)
      ? 'offlineAggDetailPage'
      : 'nonNetAggDetailPage';

  @override
  void onInitState() {
    isAggOffline = isOfflineAgg(widget.aggregationId);
  }

  @override
  void onDispose() {
    scrollController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: AggregationPresenter.canPopPage(),
        child: ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: () => StoreProvider<SmartHomeState>(
            store: smartHomeStore,
            child: Scaffold(
              backgroundColor: AppSemanticColors.background.secondary,
              body: StoreConnector<SmartHomeState, AggOfflineDetail>(
                distinct: true,
                converter: (Store<SmartHomeState> store) {
                  return isAggOffline
                      ? aggSelectors.aggOfflineDetailSelector(store.state)
                      : aggSelectors.nonNetDetailSelector(store.state);
                },
                builder:
                    (BuildContext context, AggOfflineDetail offlineDetail) {
                  return Stack(children: <Widget>[
                    if (offlineDetail.deviceCardList.isEmpty)
                      NoDeviceView(aggregationId: widget.aggregationId),
                    ScrollConfiguration(
                      behavior: NoneOverScrollBehavior(),
                      child: CustomScrollView(
                        key: scrollViewKey,
                        physics: const AlwaysScrollableScrollPhysics(),
                        controller: scrollController,
                        slivers: <Widget>[
                          AggOfflineHeader(isAggOffline: isAggOffline),
                          if (offlineDetail.deviceCardList.isEmpty)
                            const SliverToBoxAdapter()
                          else
                            AggOfflineDeviceListWidget(
                                deviceCardList: offlineDetail.deviceCardList),
                          const SafeBottomAreaSliver(),
                        ],
                      ),
                    ),
                    EditWidgetOverlay(
                      enableAnimation: false,
                      showEditWidget: smartHomeStore.state.isEditState,
                    )
                  ]);
                },
              ),
            ),
          ),
        ));
  }
}
