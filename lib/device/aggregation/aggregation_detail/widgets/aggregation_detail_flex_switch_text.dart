import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../../common/smart_home_text_widget.dart';
import '../../../component/widget/click_effect_circular_widget.dart';
import '../../../component/widget/debounce_throttler/throttler_widget.dart';
import '../../aggregation_card/view_model/aggregation_flex_switch_text_view_model.dart';

class AggregationDetailFlexSwitchTextWidget extends StatelessWidget {
  const AggregationDetailFlexSwitchTextWidget(
      {super.key, required this.viewModel});

  final AggregationFlexSwitchTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SizedBox(
        width: 60,
        height: 28,
        child: ThrottlerWidget(
          throttlerCallback: (BuildContext context) {
            viewModel.clickCallback?.call(context);
          },
          child: Opacity(
            opacity: viewModel.enable ? 1.0 : 0.39,
            child: viewModel.offColor != null
                ? ClickEffectCircularWidget(
                    offColor: viewModel.offColor,
                    height: 28,
                    enable: viewModel.enable,
                    isOn: viewModel.isOn,
                    borderRadius: 16,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        if (viewModel.text.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 7),
                            child: SmartHomeText(
                              text: viewModel.text,
                              fontSize: 14,
                              color: viewModel.isOn
                                  ? AppSemanticColors.component.information.on
                                  : AppSemanticColors.component.secondary.on,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                  )
                : ClickEffectCircularWidget(
                    height: 36,
                    enable: viewModel.enable,
                    isOn: viewModel.isOn,
                    borderRadius: 12,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        if (viewModel.text.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            child: SmartHomeText(
                              text: viewModel.text,
                              fontSize: 14,
                              color: viewModel.isOn
                                  ? AppSemanticColors.item.information.primary
                                  : AppSemanticColors.item.primary,
                            ),
                          ),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
