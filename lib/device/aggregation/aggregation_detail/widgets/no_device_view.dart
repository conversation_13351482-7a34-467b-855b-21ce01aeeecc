// Dart: lib/device/aggregation/aggregation_detail/widgets/no_device_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import '../../../../common/constant.dart';
import '../../../../widget_common/card_text_style.dart';
import '../../aggregation_card/util/aggregation_device_util.dart';

class NoDeviceView extends StatelessWidget {
  final String aggregationId;

  const NoDeviceView({super.key, required this.aggregationId});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: 124,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: <Widget>[
            Image.asset(
              'assets/images/icon_aggregation_detail_no_device.webp',
              package: SmartHomeConstant.package,
              width: 96,
              height: 96,
            ),
            const SizedBox(height: 8),
            Text(
              isOfflineAgg(aggregationId)
                  ? SmartHomeConstant.noOffLineDevice
                  : SmartHomeConstant.noNonNetDevice,
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamilyFallback: fontFamilyFallback(),
                  color: AppSemanticColors.item.secWeaken),
            )
          ],
        ),
      ),
    );
  }
}
