import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/src/store.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/agg_camera/aggregation_camera_vm.dart';
import 'package:smart_home/device/aggregation/agg_store/agg_selectors.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/base_aggregation_widget_state.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../../../widget_common/card_text_style.dart';

class AggregatioCameraReorderPage extends StatefulWidget {
  bool isShowConfirm = false;

  AggregatioCameraReorderPage({super.key}) {}

  @override
  State<AggregatioCameraReorderPage> createState() =>
      _AggregatioCameraReorderPageState();
}

class _AggregatioCameraReorderPageState extends BaseAggregationWidgetState<
    AggregatioCameraReorderPage, AggregationCameraSortedVMWrapper> {
  final AggSelectors aggSelectors = AggSelectors();
  @override
  Future<void> onBackPress() {
    Navigator.of(context).pop();
    return Future<void>.value();
  }

  @override
  String title() {
    return '排序';
  }

  Widget _buildText(String text, TextStyle style, {EdgeInsets? padding}) {
    return Text(text, style: style);
  }

  @override
  Widget mainWidget(AggregationCameraSortedVMWrapper wrapper) {
    return SliverToBoxAdapter(
      child: ReorderableListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: wrapper.data.length,
        proxyDecorator: (Widget child, int index, Animation<double> animation) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(16), // 拖动时的圆角
            child: child,
          );
        },
        itemBuilder: (BuildContext context, int index) {
          return Container(
            key: ValueKey<String>(wrapper.devs[index]),
            margin: EdgeInsets.only(top: (index == 0) ? 0 : 12),
            child: AspectRatio(
              aspectRatio: 358 / 75,
              child: ClipRRect(
                borderRadius: const BorderRadius.all(
                  Radius.circular(16),
                ),
                child: Stack(
                  children: <Widget>[
                    Container(
                      width: double.infinity,
                      color: AppSemanticColors.background.primary,
                      child: Stack(
                        children: <Widget>[
                          Positioned(
                            child: _buildText(
                              wrapper.getName(index),
                              TextStyle(
                                  height: 1.0,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: AppSemanticColors.item.primary,
                                  fontFamilyFallback: fontFamilyFallback()),
                            ),
                            top: 16,
                            left: 16,
                          ),
                          Positioned(
                            child: _buildText(
                              wrapper.getDesc(index),
                              TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppSemanticColors.item.secondary,
                                  fontFamilyFallback: fontFamilyFallback()),
                            ),
                            bottom: 16,
                            left: 16,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        right: 16,
                      ),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Image.asset(
                          width: 24,
                          height: 24,
                          'assets/icons/camera_record_item_anchor.webp',
                          package: SmartHomeConstant.package,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        },
        onReorder: (int oldIndex, int newIndex) {
          if (oldIndex == newIndex - 1) {
            return;
          }
          // 更新列表顺序的逻辑
          if (oldIndex < newIndex) {
            newIndex -= 1; // 如果是向下拖动，新索引需要减1
          }
          setState(() {
            widget.isShowConfirm = true;
            final String item = wrapper.devs.removeAt(oldIndex);
            wrapper.devs.insert(newIndex, item);
          });
        },
      ),
    );
  }

  @override
  bool isRecordPage() {
    return true;
  }

  @override
  String get pageName {
    return 'cameraBaseAggregationRecordWidgetState';
  }

  @override
  bool showExpandedTitle() {
    return false;
  }

  @override
  Widget? leadingRightWidget(AggregationCameraSortedVMWrapper wrapper) {
    if (!widget.isShowConfirm) {
      return null;
    }
    return GestureDetector(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: SizedBox(
          child: Align(
            alignment: Alignment.centerRight,
            child: Text('完成',
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 17,
                    color: AppSemanticColors.item.information.primary,
                    fontWeight: FontWeight.w400,
                    fontFamilyFallback: fontFamilyFallback())),
          ),
          height: 24,
          width: 72,
        ),
      ),
      onTap: () {
        DevLogger.info(
            tag: 'agg_camera_sorted_page',
            msg: '  user save agg camera sorted info');
        wrapper.saveAggCameraDetailSortedInfo();
        Navigator.of(context).pop();
      },
    );
  }

  @override
  AggregationCameraSortedVMWrapper getVmWrapper(Store<SmartHomeState> store) {
    return aggSelectors.aggCameraDetailSortedSelector(store.state);
  }
}
