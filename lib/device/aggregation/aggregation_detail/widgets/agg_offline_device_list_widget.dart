import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/device_widget/device_card_middle.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:upsystem/upsystem.dart';

import '../utils/aggregation_presenter.dart';

class AggOfflineDeviceListWidget extends StatelessWidget {
  const AggOfflineDeviceListWidget({super.key, required this.deviceCardList});

  final List<DeviceCardViewModel> deviceCardList;

  static const double _kGridCrossAxisSpacing = 12.0;
  static const double _kGridMainAxisSpacing = 12.0;
  static const double _kGridChildAspectRatio = 173 / 140;

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
        sliver: SliverGrid(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              return _itemBuilder(deviceCardList[index]);
            }, childCount: deviceCardList.length),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: _kGridCrossAxisSpacing,
                crossAxisSpacing: _kGridMainAxisSpacing,
                childAspectRatio: _kGridChildAspectRatio)));
  }

  Widget _itemBuilder(CardBaseViewModel cardViewModel) {
    if (cardViewModel is DeviceCardViewModel) {
      return wrapEditArea(
          DeviceCardMiddle(deviceCardViewModel: cardViewModel), cardViewModel);
    }
    return Container();
  }

  Widget wrapEditArea(Widget child, CardBaseViewModel vm) {
    return GestureDetector(
      child: child,
      behavior: HitTestBehavior.translucent,
      onLongPress: () {
        if (smartHomeStore.state.isEditState) {
          return;
        }
        if (isFamilyMemberRole()) {
          ToastHelper.showToast(SmartHomeConstant.cardManageWarning);
          return;
        }
        UpSystem.impactFeedBack();
        AggregationPresenter.queryAndUpdateAllAggSort();
        smartHomeStore.dispatch(EnterEditStateByCardAction(id: vm.sortId()));
      },
    );
  }
}
