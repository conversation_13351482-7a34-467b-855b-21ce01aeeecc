import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_constant.dart';
import 'package:smart_home/navigator/sliver_header_delegate.dart';

import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../widget_common/card_text_style.dart';

class AggOfflineHeader extends StatelessWidget {
  final bool isAggOffline;

  const AggOfflineHeader({super.key, required this.isAggOffline});

  @override
  Widget build(BuildContext context) {
    final double topPadding = MediaQuery.of(context).padding.top;
    final String title = isAggOffline
        ? AggregationDeviceConstant.aggOfflineTitle
        : AggregationDeviceConstant.aggNonNetTitle;
    final String subTitle = isAggOffline
        ? AggregationSettingConstant.aggOfflineSettingSub
        : AggregationDeviceConstant.aggNonNetTitleSub;

    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverHeaderDelegate.builder(
        maxHeight: 134 + topPadding,
        minHeight: 44 + topPadding,
        builder:
            (BuildContext context, double shrinkOffset, bool overlapsContent) {
          const double shrinkLimit = 30;
          return ColoredBox(
            color: AppSemanticColors.background.secondary,
            child: Column(
              children: <Widget>[
                SizedBox(
                  // 状态栏
                  height: topPadding,
                  child: AnnotatedRegion<SystemUiOverlayStyle>(
                    child: Container(
                        color: AppSemanticColors.background.secondary,
                        height: topPadding),
                    value: const SystemUiOverlayStyle(
                      systemNavigationBarColor: Colors.white,
                      systemNavigationBarIconBrightness: Brightness.dark,
                      statusBarColor: Colors.transparent,
                      statusBarBrightness: Brightness.light,
                      statusBarIconBrightness: Brightness.dark,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    child: Stack(
                      children: <Widget>[
                        AnimatedPadding(
                          padding: EdgeInsets.only(
                              top: shrinkOffset < shrinkLimit ? 46 : 0,
                              left: 4),
                          duration: const Duration(milliseconds: 200),
                          child: AnimatedOpacity(
                            opacity: shrinkOffset < shrinkLimit ? 1 : 0,
                            duration: const Duration(milliseconds: 200),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(title,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        leadingDistribution:
                                            TextLeadingDistribution.even,
                                        fontSize: 24,
                                        fontFamilyFallback:
                                            fontFamilyFallback(),
                                        color: AppSemanticColors.item.primary,
                                        fontWeight: FontWeight.w500)),
                                const SizedBox(
                                  height: 4,
                                ),
                                Expanded(
                                  child: Text(subTitle,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          leadingDistribution:
                                              TextLeadingDistribution.even,
                                          fontSize: 14,
                                          fontFamilyFallback:
                                              fontFamilyFallback(),
                                          color: AppSemanticColors
                                              .item.secWeaken)),
                                )
                              ],
                            ),
                          ),
                        ),
                        Container(
                            width: MediaQuery.of(context).size.width,
                            height: 34,
                            color: AppSemanticColors.background.secondary,
                            child: Stack(
                              children: <Widget>[
                                GestureDetector(
                                  onTap: () {
                                    EditPresenterManager
                                        .changePageWithHomeAndAgg(
                                        EditPageType.home);
                                    Navigator.pop(context);
                                  },
                                  behavior: HitTestBehavior.opaque,
                                  child: Image.asset(
                                    'assets/images/icon_aggregation_back.webp',
                                    package: 'smart_home',
                                    width: 24,
                                    height: 24,
                                  ),
                                ),
                                // 标题
                                AnimatedOpacity(
                                  opacity: shrinkOffset < shrinkLimit ? 0 : 1,
                                  duration: const Duration(milliseconds: 200),
                                  child: Align(
                                    alignment: Alignment.topCenter,
                                    child: Text(title,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontSize: 17,
                                            fontFamilyFallback:
                                                fontFamilyFallback(),
                                            color: AppSemanticColors
                                                .item.primary)),
                                  ),
                                ),
                              ],
                            )),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
