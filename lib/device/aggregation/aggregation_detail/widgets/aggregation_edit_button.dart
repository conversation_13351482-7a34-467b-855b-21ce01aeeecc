import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/utils/aggregation_detail_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_detail_list.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_manage_list.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

class EditButtonWidget extends StatelessWidget {
  const EditButtonWidget({
    super.key,
    required this.vm,
  });

  final AggregationBaseViewModel vm;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return !store.state.isEditState && !isFamilyMemberRole();
        },
        builder: (BuildContext context, bool isShow) {
          if (!isShow) {
            return const SizedBox.shrink();
          }
          return Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.only(bottom: 16),
              child: GestureDetector(
                onTap: () {
                  if (!smartHomeStore.state.aggregationState.isLoading) {
                    _navigateToManageList(context);
                  }
                },
                child: _buildButtonContent(),
              ));
        });
  }

  void _navigateToManageList(BuildContext context) {
    Navigator.push(
        context,
        MaterialPageRoute<AggregationDetailList>(
            builder: (BuildContext context) => AggregationManageList(
                  vm: vm,
                )));
  }

  Widget _buildButtonContent() {
    return Container(
      width: 76,
      height: 36,
      decoration: BoxDecoration(
        color: AppSemanticColors.background.primary,
        borderRadius: BorderRadius.circular(30),
      ),
      child: Center(
        child: Text(
          AggregationDetailConstant.detailEditBtn,
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: 14,
              color: AppSemanticColors.item.primary,
              fontFamilyFallback: fontFamilyFallback(),
              fontWeight: FontWeight.w400),
        ),
      ),
    );
  }
}
