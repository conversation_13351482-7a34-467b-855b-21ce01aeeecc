
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/agg_camera_header.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/safe_bottom_area_sliver.dart';
import 'package:smart_home/edit/edit_widget_overlay.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/widget_common/over_scroll_behavior.dart';

import '../../../../common/smart_home_route_manager.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../widget_common/card_text_style.dart';
import '../utils/aggregation_presenter.dart';
import 'agg_detail_base_state.dart';

double _emptyIconSize = 96;
double _emptyTipSize = 14;
double _emptyContentGap = 8;

abstract class BaseAggregationWidgetState<T extends StatefulWidget,
        U extends BaseAggCameraWrapper> extends AggDetailBaseState<T> {
  final ValueKey<String> scrollViewKey =
      const ValueKey<String>('aggregation_custom_scroll_view');

  AutoScrollController? scrollController;

  @override
  void onInitState() {
    initScrollController();
  }

  void initScrollController() {
    scrollController ??= AutoScrollController(
      viewportBoundaryGetter: () => Rect.zero,
      axis: Axis.vertical,
      suggestedRowHeight: 200,
    );
  }

  @override
  String get pageName {
    return 'cameraBaseAggregationWidgetState';
  }

  Future<void> _onBackPress() {
    Navigator.of(context).pop();
    return Future<void>.value();
  }

  @override
  void onDispose() {
    scrollController?.dispose();
    scrollController = null;
  }

  U getVmWrapper(Store<SmartHomeState> store);

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: AggregationPresenter.canPopPage(),
        child: StoreProvider<SmartHomeState>(
            store: smartHomeStore,
            child: Scaffold(
                backgroundColor: AppSemanticColors.background.secondary,
                body: DefaultTextStyle(
                    style: TextStyle(color: AppSemanticColors.item.primary),
                    child: StoreConnector<SmartHomeState, U>(
                        distinct: true,
                        converter: (Store<SmartHomeState> store) {
                          return getVmWrapper(store);
                        },
                        builder: (BuildContext context, U wrapper) {
                          return Stack(children: <Widget>[
                            if (wrapper.isNotValid()) emtpyContentCenter(),
                            ScrollConfiguration(
                              behavior: NoneOverScrollBehavior(),
                              child: CustomScrollView(
                                key: scrollViewKey,
                                physics: const AlwaysScrollableScrollPhysics(),
                                controller: scrollController,
                                slivers: <Widget>[
                                  AggCameraHeader(
                                    leadingRightButton:
                                        _hideLeadRightBtn(wrapper)
                                            ? Container(
                                                height: 0,
                                              )
                                            : leadingRightWidget(wrapper),
                                    title: title(),
                                    showExpand: showExpandedTitle(),
                                    isRecordPage: isRecordPage(),
                                    onBackpress: () {
                                      _onBackPress();
                                    },
                                  ),
                                  SliverPadding(
                                      padding: EdgeInsets.only(
                                          left: 16,
                                          right: 16,
                                          bottom: 40,
                                          top: isRecordPage() ? 8 : 0),
                                      sliver: wrapper.isNotValid()
                                          ? const SliverToBoxAdapter()
                                          : mainWidget(wrapper)),
                                  const SafeBottomAreaSliver(),
                                ],
                              ),
                            ),
                            EditWidgetOverlay(
                              enableAnimation: false,
                              showEditWidget: smartHomeStore.state.isEditState,
                            )
                          ]);
                        })))));
  }

  // 需要隐藏右上角“...”菜单按钮入口
  bool _hideLeadRightBtn(BaseAggCameraWrapper wrapper) {
    // 需求：管理员和创建者展示编辑按钮，非管理员不展示编辑按钮
    // 判断是否是非管理员，member不展示编辑按钮
    return wrapper.isNotValid() || isFamilyMemberRole();
  }

  String title() {
    return '摄像头';
  }

  bool isRecordPage() {
    return false;
  }

  Widget emtpyContentCenter() {
    return Center(
      child: SizedBox(
        height: 124,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: <Widget>[
            Image.asset(
              height: _emptyIconSize,
              width: _emptyIconSize,
              'assets/images/icon_aggregation_detail_no_device.webp',
              package: SmartHomeConstant.package,
            ),
            SizedBox(
              height: _emptyContentGap,
            ),
            Text('暂无摄像头设备',
                style: TextStyle(
                    fontSize: _emptyTipSize,
                    color: AppSemanticColors.item.secWeaken,
                    fontWeight: FontWeight.w400,
                    fontFamilyFallback: fontFamilyFallback())),
          ],
        ),
      ),
    );
  }

  bool showExpandedTitle() {
    return true;
  }

  Widget mainWidget(U wrapper);

  Widget? leadingRightWidget(U wrapper);
}

class BaseAggCameraWrapper {
  bool isNotValid() {
    return false;
  }
}
