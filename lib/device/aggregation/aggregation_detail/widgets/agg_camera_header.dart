// Dart: lib/device/aggregation/aggregation_detail/widgets/aggregation_offline_header.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../navigator/sliver_header_delegate.dart';

double _defaultPaddingTop = 44;
double _recordPagePaddingTop = 44;

double _expandedTopPadding = 46;
double _expanedHeight = 110;

double offsetPaddingTop = 0;

class AggCameraHeader extends StatefulWidget {
  String title = '摄像头';
  Widget? leadingRightButton;
  bool showExpand = true;
  bool isRecordPage = false;
  VoidCallback? onBackpress;
  AggCameraHeader(
      {super.key,
      this.leadingRightButton,
      this.title = '摄像头',
      this.showExpand = true,
      this.isRecordPage = false,
      this.onBackpress});

  @override
  State<StatefulWidget> createState() {
    return AggCameraHeaderState();
  }
}

double _aggCameraExpendedTitleSize = 24;

class AggCameraHeaderState extends State<AggCameraHeader> {
  @override
  Widget build(BuildContext context) {
    return widget.showExpand ? buildExpended(context) : buildNormal(context);
  }

  Widget buildNormal(BuildContext context) {
    final double topPadding = MediaQuery.of(context).padding.top;

    return SliverPersistentHeader(
      key: UniqueKey(),
      pinned: true,
      delegate: _StickyHeaderDelegate(
        (widget.isRecordPage ? _recordPagePaddingTop : _defaultPaddingTop) +
            topPadding,
        child: Container(
          height: _defaultPaddingTop + topPadding,
          color: AppSemanticColors.background.secondary,
          child: Column(
            children: <Widget>[
              SizedBox(
                height: topPadding,
                child: AnnotatedRegion<SystemUiOverlayStyle>(
                  child: Container(
                      color: AppSemanticColors.background.secondary,
                      height: topPadding),
                  value: const SystemUiOverlayStyle(
                    systemNavigationBarColor: Colors.white,
                    systemNavigationBarIconBrightness: Brightness.dark,
                    statusBarColor: Colors.transparent,
                    statusBarBrightness: Brightness.light,
                    statusBarIconBrightness: Brightness.dark,
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: Stack(
                    children: <Widget>[
                      Align(
                        alignment: Alignment.topCenter,
                        child: AnimatedOpacity(
                          opacity: 1,
                          duration: const Duration(milliseconds: 200),
                          child: Container(
                            height: 24,
                            alignment: Alignment.center,
                            child: Text(widget.title,
                                maxLines: 1,
                                softWrap: false,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 17,
                                    color: AppSemanticColors.item.primary,
                                    fontWeight: FontWeight.w500,
                                    fontFamilyFallback: fontFamilyFallback())),
                          ),
                        ),
                      ),
                      backWidget(),
                      Positioned(
                        right: 0,
                        top: 0,
                        child: widget.leadingRightButton ??
                            Container(
                              height: 0,
                            ),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget backWidget() {
    return Positioned(
      left: 0,
      top: 0,
      child: GestureDetector(
        onTap: () {
          if (widget.onBackpress != null) {
            widget.onBackpress!();
          } else {
            Navigator.pop(context);
          }
        },
        child: Image.asset(
          'assets/images/icon_aggregation_back.webp',
          package: 'smart_home',
          width: 24,
          height: 24,
          color: AppSemanticColors.item.primary,
        ),
      ),
    );
  }

  Widget buildExpended(BuildContext context) {
    final double topPadding = MediaQuery.of(context).padding.top;
    final double maxHeight =
        widget.showExpand ? _expanedHeight : _defaultPaddingTop;
    return SliverPersistentHeader(
      key: UniqueKey(),
      pinned: true,
      floating: true,
      delegate: SliverHeaderDelegate.builder(
        maxHeight: maxHeight + topPadding + offsetPaddingTop,
        minHeight: _defaultPaddingTop + topPadding,
        builder:
            (BuildContext context, double shrinkOffset, bool overlapsContent) {
          const double shrinkLimit = 50;
          return ColoredBox(
            color: AppSemanticColors.background.secondary,
            child: Column(
              children: <Widget>[
                SizedBox(
                  height: topPadding,
                  child: AnnotatedRegion<SystemUiOverlayStyle>(
                    child: Container(
                        color: AppSemanticColors.background.secondary,
                        height: topPadding),
                    value: const SystemUiOverlayStyle(
                      systemNavigationBarColor: Colors.white,
                      systemNavigationBarIconBrightness: Brightness.dark,
                      statusBarColor: Colors.transparent,
                      statusBarBrightness: Brightness.light,
                      statusBarIconBrightness: Brightness.dark,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    child: Stack(
                      children: <Widget>[
                        Align(
                          alignment: Alignment.topCenter,
                          child: AnimatedOpacity(
                            opacity: widget.showExpand
                                ? (shrinkOffset < shrinkLimit ? 0 : 1)
                                : 1,
                            duration: const Duration(milliseconds: 200),
                            child: Container(
                              height: 24,
                              alignment: Alignment.center,
                              child: Text(widget.title,
                                  maxLines: 1,
                                  softWrap: false,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontSize: 17,
                                      color: AppSemanticColors.item.primary,
                                      fontWeight: FontWeight.w500,
                                      fontFamilyFallback:
                                          fontFamilyFallback())),
                            ),
                          ),
                        ),
                        if (widget.showExpand)
                          AnimatedPadding(
                            padding: shrinkOffset < shrinkLimit
                                ? EdgeInsets.only(
                                    top: _expandedTopPadding + offsetPaddingTop,
                                    left: 4,
                                    bottom: 10)
                                : const EdgeInsets.only(top: 0, left: 4),
                            duration: const Duration(milliseconds: 200),
                            child: AnimatedOpacity(
                              opacity: shrinkOffset < shrinkLimit ? 1 : 0,
                              duration: const Duration(milliseconds: 200),
                              child: Text(widget.title,
                                  style: TextStyle(
                                      fontSize: _aggCameraExpendedTitleSize,
                                      color: AppSemanticColors.item.primary,
                                      fontWeight: FontWeight.w500,
                                      fontFamilyFallback:
                                          fontFamilyFallback())),
                            ),
                          )
                        else
                          Container(
                            height: 0,
                          ),
                        backWidget(),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: widget.leadingRightButton ??
                              Container(
                                height: 0,
                              ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}

class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _StickyHeaderDelegate(this.height, {required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
