import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/agg_unsupport_camera_wrapper.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_widget/camera.dart';
import 'package:smart_home/device/device_widget/camera_ui_support.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../../../widget_common/card_text_style.dart';

double defaultHeight = (375.w - 32) / 16 * 9;

class AggUnSupportCamera extends StatelessWidget {
  final String deviceId;

  final CameraUIType cameraType;
  ComponentBaseViewModel? selectedMode;

  AggUnSupportCamera(this.deviceId, this.cameraType, this.selectedMode,
      {super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        child: _buildUnsupportCameraCardWidget(context), height: defaultHeight);
  }

  Widget _buildUnsupportCameraCardWidget(BuildContext context) {
    return ClipRRect(
        borderRadius: const BorderRadius.all(
          Radius.circular(20),
        ),
        child: StoreConnector<SmartHomeState, UnsupportCameraWrapper>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return UnsupportCameraWrapper(
                store.state.deviceState.allCardViewModelMap[deviceId],
                store.state.isEditState);
          },
          builder: (BuildContext context, UnsupportCameraWrapper vm) {
            return Stack(children: <Widget>[
              _backgroundImage(vm, double.infinity, double.infinity),
              _buildCameraHeader(vm),
              _buildTips(vm),
              InkWell(
                child: const SizedBox.expand(),
                onTap: () {
                  vm.gotoDetailPage(deviceId);
                },
              ),
              buildHeaderButtons(context, selectedMode, deviceId,cameraType),
              buildEditOverlay(
                child: GestureDetector(
                  onTap: () {
                    (selectedMode! as DeviceEditComponentViewModel)
                        .selectedClick();
                  },
                  child: Container(
                    width: double.infinity,
                  ),
                ),
              ),
            ]);
          },
        ));
  }

  Widget _buildTips(UnsupportCameraWrapper vm) {
    if (vm.isOffline) {
      return _offlineView();
    }
    return Center(
      child: Container(
          margin: EdgeInsets.only(
              top: cameraType == CameraUIType.middle_card ? 30 : 0),
          padding: const EdgeInsets.only(left: 8, right: 8, top: 2, bottom: 4),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(12)),
            color: Color.fromRGBO(0, 0, 0, 0.4),
          ),
          child: Text(
            CameraConstant.tipsForUnsupportCamera,
            style: TextStyle(
                fontSize: 12,
                color: AppSemanticColors.item.invert,
                fontFamilyFallback: fontFamilyFallback()),
          )),
    );
  }

  Widget _statusViewWrapper({required Widget child}) {
    if (cameraType == CameraUIType.middle_card) {
      return Padding(
        padding: const EdgeInsets.only(top: 36), // 距离顶部 60 像素
        child: Center(child: child), // 水平居中
      );
    }
    return Center(child: child);
  }

  Widget _offlineView() {
    return _statusViewWrapper(
        child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Image.asset(
          CameraConstant.offlineIcon,
          width: 32,
          height: 32,
          package: SmartHomeConstant.package,
          fit: BoxFit.fill,
        ),
        const SizedBox(
          height: 8,
        ),
        Text(
          CameraConstant.offlineTips,
          style: _textStyle,
        ),
      ],
    ));
  }

  Widget _backgroundImage(UnsupportCameraWrapper vm, double w, double h) {
    return Image.asset(
      vm.bgImageUrl,
      package: SmartHomeConstant.package,
      fit: BoxFit.cover,
      alignment: Alignment.topLeft,
      width: w,
      height: h,
    );
  }

  Widget _buildCameraHeader(UnsupportCameraWrapper vm) {
    return SizedBox(
      height: 60,
      child: getCameraHeader(cameraType, _deviceImage(vm), _deviceInfo(vm),
          isEdit: vm.isEdit),
    );
  }

  Widget _deviceInfo(UnsupportCameraWrapper vm) {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) =>
            store.state.deviceState.cardShowFloor,
        builder: (BuildContext context, bool showFloor) {
          return getCameraDeviceInfo(
              vm.title, '${showFloor ? vm.floor : ''}${vm.subTitle}');
        });
  }

  Widget _deviceImage(UnsupportCameraWrapper vm) {
    if (cameraType == CameraUIType.middle_card) {
      return const SizedBox.shrink();
    }
    return getCameraDeviceImage(vm.deviceIcon);
  }
}

TextStyle _textStyle = TextStyle(
    fontSize: 12.0,
    color: AppSemanticColors.item.invert,
    fontWeight: FontWeight.w400,
    fontFamilyFallback: fontFamilyFallback());
