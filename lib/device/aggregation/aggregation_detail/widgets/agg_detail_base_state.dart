import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:smart_home/common/smart_home_route_manager.dart';

import '../../../../common/smart_home_util.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../edit/edit_widget_overlay.dart';

abstract class AggDetailBaseState<T extends StatefulWidget> extends State<T>
    with EditOverlayBackObserver<T>, RouteAware {
  bool _isRouteSubscribed = false;

  String get pageName;

  void onBackPress() {
    if (mounted) {
      Navigator.pop(context);
    }
  }

  @override
  void initState() {
    super.initState();
    InterceptSystemBackUtil.interceptSystemBack(
        pageName: pageName,
        callback: () {
          onBackPress();
        });

    onInitState();
  }

  void onInitState() {}

  @override
  void onEditBackPress() {
    onBackPress();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _subscribeToRouteObserver();
  }

  void _subscribeToRouteObserver() {
    if (mounted && !_isRouteSubscribed) {
      final ModalRoute<Object?>? route = ModalRoute.of(context);
      if (route is PageRoute<Object?>) {
        try {
          SmartHomeRouteManager.observer.subscribe(this, route);
          _isRouteSubscribed = true;
          DevLogger.debug(
              tag: pageName,
              msg: '$pageName successfully subscribed to RouteObserver');
        } catch (e) {
          DevLogger.debug(
              tag: pageName,
              msg: '$pageName failed to subscribe to RouteObserver: $e');
        }
      }
    }
  }

  @override
  void didPop() {
    super.didPop();
    EditPresenterManager.changePageWithHomeAndAgg(EditPageType.home);
    onDidPop();
  }

  void onDidPop() {}

  @override
  void didPush() {
    super.didPush();
    onDidPush();
  }

  void onDidPush() {}

  @override
  void didPushNext() {
    super.didPushNext();
    onDidPushNext();
  }

  void onDidPushNext() {}

  @override
  void didPopNext() {
    super.didPopNext();
    onDidPopNext();
  }

  void onDidPopNext() {}

  @override
  void dispose() {
    InterceptSystemBackUtil.cancelInterceptSystemBack(pageName);

    if (_isRouteSubscribed) {
      SmartHomeRouteManager.observer.unsubscribe(this);
      _isRouteSubscribed = false;
    }
    onDispose();
    super.dispose();
  }

  void onDispose() {}
}
