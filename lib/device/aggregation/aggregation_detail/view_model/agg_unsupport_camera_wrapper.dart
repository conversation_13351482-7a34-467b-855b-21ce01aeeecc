import 'package:device_utils/log/log.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class UnsupportCameraWrapper {
  final int localKey;
  final CardBaseViewModel? vm;
  final bool isOffline;
  final String bgImageUrl;
  final String title;
  final String floor;
  final String subTitle;
  final String deviceIcon;
  final bool isEdit;
  final bool isSelected;

  UnsupportCameraWrapper(this.vm, this.isEdit)
      : localKey = vm?.hashCode ?? 0,
        isOffline = _isDeviceOffline(vm),
        bgImageUrl = CameraConstant.bgImageUrl,
        title = _getTitleFromViewModel(vm),
        floor = _getFloorFromViewModel(vm),
        subTitle = _getRoomFromViewModel(vm),
        deviceIcon = _getDeviceIconFromViewModel(vm),
        isSelected = _getSelectedState(vm);

  void gotoDetailPage(String deviceId) {
    if (deviceId.isEmpty) {
      DevLogger.debug(
          tag: 'UnsupportCameraWrapper',
          msg: 'gotoDetailPage deviceId is empty, localKey = $localKey');
      return;
    }
    goToPageWithDebounce(CameraConstant.getCameraDetailPageUrl(deviceId));
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UnsupportCameraWrapper &&
          localKey == other.localKey &&
          isSelected == other.isSelected &&
          isEdit == other.isEdit;

  @override
  int get hashCode =>
      super.hashCode ^
      localKey.hashCode ^
      isSelected.hashCode ^
      isEdit.hashCode;
}

String _getTitle(String? desc) {
  if (desc == null || desc.isEmpty) {
    return '';
  }
  return desc.length > 12 ? '${desc.substring(0, 11)}…' : desc;
}

bool _isDeviceOffline(CardBaseViewModel? vmo) {
  return (vmo is DeviceCardViewModel) && vmo.deviceOffline;
}

String _getTitleFromViewModel(CardBaseViewModel? vmo) {
  return (vmo is DeviceCardViewModel) ? _getTitle(vmo.deviceName) : '';
}

String _getFloorFromViewModel(CardBaseViewModel? vmo) {
  return (vmo is DeviceCardViewModel) ? vmo.floor : '';
}

String _getRoomFromViewModel(CardBaseViewModel? vmo) {
  return (vmo is DeviceCardViewModel) ? _getTitle(vmo.room) : '';
}

String _getDeviceIconFromViewModel(CardBaseViewModel? vmo) {
  return (vmo is DeviceCardViewModel) ? vmo.deviceIcon : '';
}

bool _getSelectedState(CardBaseViewModel? vmo) {
  if (vmo is DeviceCardViewModel &&
      vmo.editComponentViewModel is DeviceEditComponentViewModel) {
    return (vmo.editComponentViewModel as DeviceEditComponentViewModel)
        .isSelected;
  }
  return false;
}
