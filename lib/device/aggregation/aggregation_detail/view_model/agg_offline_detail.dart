import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class AggOfflineDetail {
  final List<DeviceCardViewModel> deviceCardList;

  AggOfflineDetail(this.deviceCardList);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggOfflineDetail &&
          runtimeType == other.runtimeType &&
          listEquals(deviceCardList, other.deviceCardList);

  @override
  int get hashCode => listHashCode(deviceCardList);
}
