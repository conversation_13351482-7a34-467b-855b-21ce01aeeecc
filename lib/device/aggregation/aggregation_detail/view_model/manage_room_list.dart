import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

import 'detail_room_list.dart';

class ManageRoomList {
  List<AggregationRoomInfo> roomList = <AggregationRoomInfo>[];

  ManageRoomList(this.roomList);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ManageRoomList &&
          runtimeType == other.runtimeType &&
          listEquals(roomList, other.roomList);

  @override
  int get hashCode => listHashCode(roomList);
}
