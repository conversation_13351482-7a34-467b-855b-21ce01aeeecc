class ManageDialogRoom {
  String deviceId = '';
  bool isSelected = false;

  ManageDialogRoom({
    this.deviceId = '',
    this.isSelected = false,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ManageDialogRoom &&
          runtimeType == other.runtimeType &&
          deviceId == other.deviceId &&
          isSelected == other.isSelected;

  @override
  int get hashCode => deviceId.hashCode ^ isSelected.hashCode;
}
