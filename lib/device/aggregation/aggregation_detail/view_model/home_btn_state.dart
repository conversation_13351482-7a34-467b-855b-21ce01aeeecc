import '../../aggregation_card/view_model/aggregation_devices_by_room_model.dart';

class HomeBtnState {
  DeviceStateForAggregation homeDeviceState;
  bool homeOperating;
  final bool isEditState;

  HomeBtnState(this.homeDeviceState, this.homeOperating,
      {this.isEditState = false});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HomeBtnState &&
          runtimeType == other.runtimeType &&
          homeDeviceState == other.homeDeviceState &&
          homeOperating == other.homeOperating &&
          isEditState == other.isEditState;

  @override
  String toString() {
    return 'HomeBtnState{homeDeviceState: $homeDeviceState, homeOperating: $homeOperating, isEditState: $isEditState}';
  }

  @override
  int get hashCode =>
      homeDeviceState.hashCode ^ homeOperating.hashCode ^ isEditState.hashCode;
}
