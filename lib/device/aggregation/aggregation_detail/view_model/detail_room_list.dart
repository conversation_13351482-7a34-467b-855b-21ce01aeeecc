import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

import 'detail_room_list_item.dart';

class DetailRoomList {
  List<DetailRoomListItem> roomList = <DetailRoomListItem>[];

  DetailRoomList(this.roomList);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DetailRoomList &&
          runtimeType == other.runtimeType &&
          listEquals(roomList, other.roomList);

  @override
  int get hashCode => listHashCode(roomList);
}

class AggregationRoomInfo {
  String roomName;
  String roomId;

  AggregationRoomInfo(this.roomId, this.roomName);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggregationRoomInfo &&
          runtimeType == other.runtimeType &&
          roomName == other.roomName &&
          roomId == other.roomId;

  @override
  int get hashCode => roomName.hashCode ^ roomId.hashCode;
}
