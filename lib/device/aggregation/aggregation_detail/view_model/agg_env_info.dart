import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

class AggWholeHouseEnvInfo {
  final bool isEditState;
  final List<AggEnvModel> envList;

  AggWholeHouseEnvInfo({
    required this.envList,
    required this.isEditState,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggWholeHouseEnvInfo &&
          runtimeType == other.runtimeType &&
          listEquals(envList, other.envList) &&
          isEditState == other.isEditState;

  @override
  int get hashCode => listHashCode(envList) ^ isEditState.hashCode;
}

class AggEnvModel {
  AggEnvModel({
    required this.name,
    required this.value,
    required this.unit,
  });

  final String name;
  final String value;
  final String unit;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggEnvModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          value == other.value &&
          unit == other.unit;

  @override
  int get hashCode => name.hashCode ^ value.hashCode ^ unit.hashCode;

  @override
  String toString() {
    return 'AggEnvModel{name: $name, value: $value, unit: $unit,}';
  }
}
