import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';

import '../../../device_info_model/smart_home_device_basic_info.dart';

class DetailRoomListItem {
  DeviceStateForAggregation roomStatus = DeviceStateForAggregation.UNKNOWN;
  bool roomOperating = false;
  String roomId;
  String roomName;
  List<String> deviceList = <String>[];
  int openCount = 0;
  bool isRoomOpen = false;
  AggregationDevicesByRoomViewModel roomViewModel;

  DetailRoomListItem(
      this.roomStatus,
      this.roomOperating,
      this.roomId,
      this.roomName,
      this.deviceList,
      this.openCount,
      this.isRoomOpen,
      this.roomViewModel);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DetailRoomListItem &&
          runtimeType == other.runtimeType &&
          roomStatus == other.roomStatus &&
          roomOperating == other.roomOperating &&
          roomId == other.roomId &&
          roomName == other.roomName &&
          listEquals(deviceList, other.deviceList) &&
          openCount == other.openCount &&
          isRoomOpen == other.isRoomOpen &&
          roomViewModel == other.roomViewModel;

  @override
  int get hashCode =>
      roomStatus.hashCode ^
      roomOperating.hashCode ^
      roomId.hashCode ^
      roomName.hashCode ^
      listHashCode(deviceList) ^
      openCount.hashCode ^
      isRoomOpen.hashCode ^
      roomViewModel.hashCode;
}
