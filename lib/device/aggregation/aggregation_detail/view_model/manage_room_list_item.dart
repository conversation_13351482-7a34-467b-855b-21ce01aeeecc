import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';

class ManageRoomListItem {
  String? roomId;
  List<String> deviceList = <String>[];
  AggregationDevicesByRoomViewModel roomViewModel;

  ManageRoomListItem(this.roomId, this.deviceList, this.roomViewModel);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ManageRoomListItem &&
          runtimeType == other.runtimeType &&
          roomId == other.roomId &&
          listEquals(deviceList, other.deviceList) &&
          roomViewModel == other.roomViewModel;

  @override
  int get hashCode =>
      roomId.hashCode ^ listHashCode(deviceList) ^ roomViewModel.hashCode;
}
