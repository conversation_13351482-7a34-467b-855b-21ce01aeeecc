import '../../aggregation_card/view_model/aggregation_base_view_model.dart';

class AggNormalDetailViewModel {
  final AggregationBaseViewModel aggVm;
  final bool isRoomEmpty;

  AggNormalDetailViewModel({required this.aggVm, this.isRoomEmpty = false});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggNormalDetailViewModel &&
          runtimeType == other.runtimeType &&
          aggVm == other.aggVm &&
          isRoomEmpty == other.isRoomEmpty;

  @override
  int get hashCode => aggVm.hashCode ^ isRoomEmpty.hashCode;
}
