import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';

class AggregationRoomSelectViewModel {
  final String aggregationId;
  final SmartHomeRoomInfo roomInfo;
  final bool isSelectAll;

  AggregationRoomSelectViewModel({
    required this.aggregationId,
    required this.roomInfo,
    required this.isSelectAll,
  });

  @override
  String toString() {
    return 'AggregationRoomSelectViewModel(aggregationId: $aggregationId,'
        ' roomInfo: $roomInfo, isSelectAll: $isSelectAll)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationRoomSelectViewModel &&
          runtimeType == other.runtimeType &&
          aggregationId == other.aggregationId &&
          roomInfo == other.roomInfo &&
          isSelectAll == other.isSelectAll;

  @override
  int get hashCode =>
      aggregationId.hashCode ^ roomInfo.hashCode ^ isSelectAll.hashCode;
}
