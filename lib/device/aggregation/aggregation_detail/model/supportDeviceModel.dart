// 查询家庭下某类未聚合设备接口请求model
// 接口文档详见：https://stp.haier.net/project/79/interface/api/233587
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class SupportDeviceReqModel {
  String familyId = '';
  String aggType = ''; // 聚合类型 0-灯光 1-窗帘 2-环境 3-非网器 4-长期离线 5-摄像头

  SupportDeviceReqModel({
    required this.familyId,
    required this.aggType,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    data['aggType'] = aggType;
    return data;
  }

  @override
  String toString() {
    return 'SupportDeviceReqModel{familyId: $familyId, aggType: $aggType}';
  }
}

// 家庭下某类未聚合设备请求返回model
class SupportDeviceResModel {
  String retCode = '';
  String retInfo = '';
  List<SupportDevModel> data = <SupportDevModel>[];

  SupportDeviceResModel.fromJson(Map<dynamic, dynamic> json) {
    retCode = json.stringValueForKey('retCode', '');
    retInfo = json.stringValueForKey('retInfo', '');
    final List<dynamic> list = json.listValueForKey('data', <dynamic>[]);
    for (final dynamic v in list) {
      if (v is Map<dynamic, dynamic>) {
        data.add(SupportDevModel.fromJson(v));
      }
    }
  }
}

class SupportDevModel {
  String roomId = '';
  String roomName = '';
  String floorId = '';
  String floorName = '';
  List<String> deviceList = <String>[];

  SupportDevModel.fromJson(Map<dynamic, dynamic> json) {
    roomId = json.stringValueForKey('roomId', '');
    roomName = json.stringValueForKey('roomName', '');
    floorId = json.stringValueForKey('floorId', '');
    floorName = json.stringValueForKey('floorName', '');

    final List<dynamic> data = json.listValueForKey('deviceList', <dynamic>[]);
    for (final dynamic v in data) {
      if (v is String) {
        deviceList.add(v);
      }
    }
  }

  @override
  String toString() {
    return 'SupportDevModel{roomId: $roomId, roomName: $roomName, '
        'floorId: $floorId, floorName: $floorName, deviceList: $deviceList}';
  }
}

class AggSortReqModel {
  String familyId = '';
  List<String> types = <String>[];

  AggSortReqModel({
    required this.familyId,
    required this.types,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    data['types'] = types;
    return data;
  }

  @override
  String toString() {
    return 'AggSortReqModel{familyId: $familyId, types: $types}';
  }
}

class AggSortResModel {
  String retCode = '';
  String retInfo = '';
  AggDeviceData data = AggDeviceData();

  AggSortResModel.fromJson(Map<dynamic, dynamic> json) {
    retCode = json.stringValueForKey('retCode', '');
    retInfo = json.stringValueForKey('retInfo', '');
    final dynamic dataJson = json['data'];
    if (dataJson is Map) {
      data = AggDeviceData.fromJson(dataJson);
    }
  }

  @override
  String toString() {
    return 'AggSortResModel{retCode: $retCode, retInfo: $retInfo, data: $data}';
  }
}

class AggDeviceData {
  Map<String, List<String>>? lightAgg = <String, List<String>>{};
  Map<String, List<String>>? curtainAgg = <String, List<String>>{};
  Map<String, List<String>>? envAgg = <String, List<String>>{};

  AggDeviceData({
    this.lightAgg,
    this.curtainAgg,
    this.envAgg,
  });

  AggDeviceData.fromJson(Map<dynamic, dynamic> json) {
    final Map<dynamic, dynamic> light =
        json.mapValueForKey('lightAgg', <dynamic, dynamic>{});
    if (light is Map<String, dynamic>) {
      light.forEach((String key, dynamic value) {
        final List<dynamic> deviceList =
            light.listValueForKey(key, <dynamic>[]);
        lightAgg?[key] = deviceList.map((dynamic e) => e.toString()).toList();
      });
    }

    final Map<dynamic, dynamic> curtain =
        json.mapValueForKey('curtainAgg', <dynamic, dynamic>{});
    if (curtain is Map<String, dynamic>) {
      curtain.forEach((String key, dynamic value) {
        final List<dynamic> deviceList =
            curtain.listValueForKey(key, <dynamic>[]);
        curtainAgg?[key] = deviceList.map((dynamic e) => e.toString()).toList();
      });
    }

    final Map<dynamic, dynamic> env =
        json.mapValueForKey('envAgg', <dynamic, dynamic>{});
    if (env is Map<String, dynamic>) {
      env.forEach((String key, dynamic value) {
        final List<dynamic> deviceList = env.listValueForKey(key, <dynamic>[]);
        envAgg?[key] = deviceList.map((dynamic e) => e.toString()).toList();
      });
    }
  }

  @override
  String toString() {
    return 'AggDeviceData{lightAgg: $lightAgg, curtainAgg: $curtainAgg,'
        ' envAgg: $envAgg}';
  }
}

class AggSortSaveReqModel {
  String familyId = '';
  AggDeviceData sortData = AggDeviceData();

  AggSortSaveReqModel({
    required this.familyId,
    required this.sortData,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    data['lightAgg'] = sortData.lightAgg?.map((String key, List<String> value) {
      return MapEntry<String, List<String>>(
        key,
        value.map((String e) => e).toList(),
      );
    });

    data['curtainAgg'] =
        sortData.curtainAgg?.map((String key, List<String> value) {
      return MapEntry<String, List<String>>(
        key,
        value.map((String e) => e).toList(),
      );
    });

    data['envAgg'] = sortData.envAgg?.map((String key, List<String> value) {
      return MapEntry<String, List<String>>(
        key,
        value.map((String e) => e).toList(),
      );
    });
    return data;
  }

  @override
  String toString() {
    return 'AggSortSaveReqModel{familyId: $familyId, sortData: $sortData}';
  }
}
