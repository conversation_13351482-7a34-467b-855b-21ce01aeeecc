import 'package:smart_home/device/aggregation/agg_camera/model/agg_sort_response_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';

import '../aggregation_detail/model/supportDeviceModel.dart';
import '../aggregation_setting/service/switch_status_response_model.dart';

class AggregationBaseAction {}

/// 更新选中状态
class UpdateAggregationStateAction extends AggregationBaseAction {
  final String id;
  final bool isSelected;

  UpdateAggregationStateAction({required this.id, this.isSelected = true});
}

/// 初始化聚合开关状态
class UpdateAggregationSettingListAction extends AggregationBaseAction {
  final SwitchStatusData switchStatusData;

  UpdateAggregationSettingListAction({required this.switchStatusData});
}

/// 摄像头聚合
class UpdateCameraAggListAction extends AggregationBaseAction {
  bool isLarge = false;
  Set<String> devs;

  UpdateCameraAggListAction(this.isLarge, this.devs);
}

class SwitchAggregationCameraDetailLargeOrSmallAction
    extends AggregationBaseAction {
  bool isLarge;

  SwitchAggregationCameraDetailLargeOrSmallAction(this.isLarge);
}

class SaveAggregationCameraDetailSortedDevsAction
    extends AggregationBaseAction {
  bool isLarge;
  List<String> devs;

  SaveAggregationCameraDetailSortedDevsAction(this.devs, this.isLarge);
}

class UpdateAggDeviceVmSortAction extends AggregationBaseAction {
  AggDeviceData sortData;

  UpdateAggDeviceVmSortAction(this.sortData);
}

class UpdateAggDetailListLoadingAction extends AggregationBaseAction {
  UpdateAggDetailListLoadingAction();
}

class SmallCardDragInAggFinishedAction extends AggregationBaseAction {
  List<String> sortedDeviceIdList;
  String deviceId;
  String roomId;

  SmallCardDragInAggFinishedAction(
      {required this.sortedDeviceIdList,
      required this.roomId,
      required this.deviceId});
}
