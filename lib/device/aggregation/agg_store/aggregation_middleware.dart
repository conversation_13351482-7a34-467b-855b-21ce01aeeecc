import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_card_model.dart' as family_card;
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/presenter/camera_presenter.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_widget/camera.dart';
import 'package:smart_home/store/smart_home_action.dart';
import 'package:smart_home/store/smart_home_store.dart';
import '../../../../store/smart_home_state.dart';
import '../aggregation_setting/util/aggregation_setting_constant.dart';
import 'aggregation_action.dart';

class AggregationMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    if (action is UpdateAggregationStateAction) {
      saveSingleAggregationSwitch(action.id, action.isSelected);
    }
    if (action is SwitchAggregationCameraDetailLargeOrSmallAction) {
      updateSwitchAggregationCameraDetailLargeOrSmallAction(
          store,
          store.state.aggregationState.aggregationCameraViewModel.cameraList,
          store.state.familyState.familyId,
          action.isLarge);
    } else if (action is SaveAggregationCameraDetailSortedDevsAction) {
      dealSaveAggregationCameraDetailSortedDevsAction(
          store,
          store.state.deviceState.allCardViewModelMap,
          store.state.familyState.familyId,
          action.devs,
          action.isLarge);
    } else if (action is UpdateNetworkStateAction) {
      if (action.isNetAvailable && !store.state.familyState.netAvailable) {
        CameraAggPresenter.getCameraAggSortedList();
      }
    }
    next(action);
  }
}

void dealSaveAggregationCameraDetailSortedDevsAction(
    Store<SmartHomeState> store,
    Map<String, CardBaseViewModel> maps,
    String fmID,
    List<String> devs,
    bool isLarge) {
  if (devs.isEmpty) {
    return;
  }
  DevLogger.info(
      tag: 'agg_middleware',
      msg: 'dealSaveAggregationCameraDetailSortedDevsAction fmID = $fmID , '
          'isLarge = $isLarge, devs = $devs');
  final List<String> cameraList = <String>[];
  devs.forEach((String element) {
    if (maps.containsKey(element)) {
      cameraList.add(element);
    }
  });

  CameraAggPresenter.saveAggCameraData2FamilyPlugin(
      store, cameraList, fmID, isLarge);
}

void updateSwitchAggregationCameraDetailLargeOrSmallAction(
    Store<SmartHomeState> store,
    List<String> cameraList,
    String fmId,
    bool isLarge) {
  //调用插件刷数据
  DevLogger.info(
      tag: 'agg_middleware',
      msg:
          'updateSwitchAggregationCameraDetailLargeOrSmallAction fmId = $fmId , isLarge = $isLarge');
  CameraAggPresenter.saveAggCameraData2FamilyPlugin(
      store, cameraList, fmId, isLarge);
}

Future<void> saveSingleAggregationSwitch(
    String aggregationId, bool isSelected) async {
  final String setValue = isSelected ? '1' : '0';
  final family_card.FamilyAgg familyAggModel = family_card.FamilyAgg(
      familyId: smartHomeStore.state.familyState.familyId);

  final Map<String, String Function(String value)> aggregationSetters =
      <String, String Function(String value)>{
    AggregationSettingConstant.agg_light_id: (String value) =>
        familyAggModel.lightAgg = value,
    AggregationSettingConstant.agg_curtain_id: (String value) =>
        familyAggModel.curtainAgg = value,
    AggregationSettingConstant.offline_id: (String value) =>
        familyAggModel.offlineAgg = value,
    AggregationSettingConstant.env_id: (String value) =>
        familyAggModel.envAgg = value,
    AggregationSettingConstant.camera_id: (String value) =>
        familyAggModel.cameraAgg = value,
    AggregationSettingConstant.non_net_id: (String value) =>
        familyAggModel.nonnetAgg = value,
  };

  aggregationSetters[aggregationId]?.call(setValue);

  final family_card.AggregationSwitchModel model =
      family_card.AggregationSwitchModel(
    source: '1',
    familyAgg: <family_card.FamilyAgg>[familyAggModel],
  );

  await Family.operateAggregationSwitch(model)
      .then((family_card.DeviceCardResult value) {
    //根据result成功或者失败,输出log
    DevLogger.info(
        tag: 'AggregationMiddleware',
        msg: 'saveSingleAggregationSwitch success: $value');
  });
}
