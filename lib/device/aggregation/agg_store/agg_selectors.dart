import 'package:reselect/reselect.dart';
import 'package:smart_home/device/aggregation/agg_camera/aggregation_camera_vm.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/agg_offline_detail.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/store/smart_home_state.dart';
import '../../../whole_house/device_env/services/env_devices_response_model.dart';
import '../aggregation_detail/view_model/agg_env_info.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../aggregation_setting/util/aggregation_setting_constant.dart';

class AggSelectors {
  // 长期离线聚合详情VM
  final Selector<SmartHomeState, AggOfflineDetail> aggOfflineDetailSelector =
      createSelector1((SmartHomeState state) {
    final String aggOfflineDeviceId =
        '${AggregationSettingConstant.offline_id}${state.familyState.familyId}';
    return state.aggregationState.aggDeviceIdSetMap[aggOfflineDeviceId] ??
        <String>{};
  }, (Set<String> deviceIds) => _createAggDetailSelector(deviceIds));

  // 非网器聚合详情VM
  final Selector<SmartHomeState, AggOfflineDetail> nonNetDetailSelector =
      createSelector1(
    (SmartHomeState state) {
      final String nonNetDeviceId =
          '${AggregationSettingConstant.non_net_id}${state.familyState.familyId}';
      return state.aggregationState.aggDeviceIdSetMap[nonNetDeviceId] ??
          <String>{};
    },
    (Set<String> deviceIds) {
      return _createAggDetailSelector(deviceIds);
    },
  );

  static AggOfflineDetail _createAggDetailSelector(Set<String> deviceIds) {
    final Map<String, CardBaseViewModel> allCardViewModelMap =
        smartHomeStore.state.deviceState.allCardViewModelMap;
    if (deviceIds.isEmpty || allCardViewModelMap.isEmpty) {
      return AggOfflineDetail(<DeviceCardViewModel>[]);
    }
    final List<DeviceCardViewModel> deviceCardList = deviceIds
        .where((String deviceId) => allCardViewModelMap.containsKey(deviceId))
        .map((String deviceId) =>
            allCardViewModelMap[deviceId]! as DeviceCardViewModel)
        .toList();
    return AggOfflineDetail(deviceCardList);
  }

  final Selector<SmartHomeState, AggregationCameraSortedVMWrapper>
      aggCameraDetailSortedSelector = createSelector3(
    (SmartHomeState state) =>
        state.aggregationState.aggregationCameraViewModel.cameraList,
    (SmartHomeState state) =>
        state.aggregationState.aggregationCameraViewModel.cardType,
    (SmartHomeState state) => state.deviceState.allCardViewModelMap,
    (List<String> cameraList, AggregationCamerasVMCardType cardType,
        Map<String, CardBaseViewModel> allCardViewModelMap) {
      return _createAggCameraDetailSelector(
          cameraList, cardType, allCardViewModelMap);
    },
  );

  static AggregationCameraSortedVMWrapper _createAggCameraDetailSelector(
    List<String> deviceIds,
    AggregationCamerasVMCardType cardType,
    Map<String, CardBaseViewModel> allCardViewModelMap,
  ) {
    if (deviceIds.isEmpty) {
      return AggregationCameraSortedVMWrapper(
          <String, List<String>>{}, <String>[], cardType);
    }
    final Map<String, List<String>> data = <String, List<String>>{};
    final List<String> devIds = <String>[];
    for (final String devId in deviceIds) {
      final CardBaseViewModel? vm = allCardViewModelMap[devId];
      if (vm is DeviceCardViewModel) {
        data[devId] = <String>[
          vm.deviceName,
          '${vm.device.basicInfo.roomInfo.floorName}${vm.device.basicInfo.roomInfo.roomName}'
        ];
        devIds.add(devId);
      }
    }
    return AggregationCameraSortedVMWrapper(data, devIds, cardType);
  }

  final Selector<SmartHomeState, AggregationCamerasVMWrapper>
      aggCameraDetailPageSelector = createSelector3(
    (SmartHomeState state) =>
        state.aggregationState.aggregationCameraViewModel.cameraList,
    (SmartHomeState state) =>
        state.aggregationState.aggregationCameraViewModel.cardType,
    (SmartHomeState state) => state.deviceState.allCardViewModelMap,
    (List<String> cameraList, AggregationCamerasVMCardType cardType,
        Map<String, CardBaseViewModel> allCardViewModelMap) {
      return _createAggCameraDetailPageSelector(
          cameraList, cardType, allCardViewModelMap);
    },
  );

  static AggregationCamerasVMWrapper _createAggCameraDetailPageSelector(
    List<String> deviceIds,
    AggregationCamerasVMCardType cardType,
    Map<String, CardBaseViewModel> allCardViewModelMap,
  ) {
    if (deviceIds.isEmpty) {
      return AggregationCamerasVMWrapper(
          cardType == AggregationCamerasVMCardType.large, <String>[]);
    }
    final List<String> devIds = <String>[];
    for (final String devId in deviceIds) {
      final CardBaseViewModel? vm = allCardViewModelMap[devId];
      if (vm is DeviceCardViewModel) {
        devIds.add(devId);
      }
    }
    return AggregationCamerasVMWrapper(
        cardType == AggregationCamerasVMCardType.large, devIds);
  }

  // 环境聚合详情全屋卡片
  final Selector<SmartHomeState, AggWholeHouseEnvInfo> wholeHouseEnvSelector =
      createSelector3(
    (SmartHomeState state) => state.wholeHouseState.envDeviceState.spaces,
    (SmartHomeState state) => state.familyState.familyId,
    (SmartHomeState state) => state.isEditState,
    (Map<String, SpaceEnvironmentModel> spaces, String familyId,
        bool isEditState) {
      return _createWholeHouseEnvSelector(spaces, familyId, isEditState);
    },
  );

  static AggWholeHouseEnvInfo _createWholeHouseEnvSelector(
    Map<String, SpaceEnvironmentModel> spaces,
    String familyId,
    bool isEditState,
  ) {
    final SpaceEnvironmentModel? envInfo = spaces[familyId];
    final List<AggEnvModel> _envModelList = getEnvModelList(envInfo);

    return AggWholeHouseEnvInfo(
        envList: _envModelList, isEditState: isEditState);
  }

  static List<AggEnvModel> getEnvModelList(SpaceEnvironmentModel? envInfo) {
    if (envInfo is SpaceEnvironmentModel) {
      final EnvDeviceItemModel? temp =
          getEnvDeviceItemModel(envInfo.temperature);
      final EnvDeviceItemModel? humidity =
          getEnvDeviceItemModel(envInfo.humidity);
      final EnvDeviceItemModel? pm25 = getEnvDeviceItemModel(envInfo.pm25);
      final List<AggEnvModel> _envModelList = <AggEnvModel>[];
      if (temp is EnvDeviceItemModel) {
        _envModelList.add(AggEnvModel(
          name: '温度',
          value: temp.reportedValue,
          unit: '℃',
        ));
      }
      if (humidity is EnvDeviceItemModel) {
        _envModelList.add(AggEnvModel(
          name: '湿度',
          value: humidity.reportedValue,
          unit: '%',
        ));
      }
      if (pm25 is EnvDeviceItemModel) {
        _envModelList.add(AggEnvModel(
          name: '${AggregationDeviceConstant.aggEnvLabelDescPm25} ',
          value: pm25.reportedValue,
          unit: '',
        ));
      }
      return _envModelList;
    } else {
      return <AggEnvModel>[];
    }
  }

  // TODO quzhenhao 待调用仪表盘通用方法
  static EnvDeviceItemModel? getEnvDeviceItemModel(
      List<EnvDeviceItemModel> devices) {
    // 过滤出已选择且在线的设备
    final List<EnvDeviceItemModel> filteredDevices = devices
        .where(
            (EnvDeviceItemModel device) => device.selected && device.isOnline)
        .toList();

    if (filteredDevices.isNotEmpty) {
      return filteredDevices.first;
    }
    return null;
  }
}
