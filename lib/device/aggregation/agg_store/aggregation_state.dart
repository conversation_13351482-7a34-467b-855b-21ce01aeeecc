import 'package:smart_home/device/aggregation/agg_camera/aggregation_camera_vm.dart';
import '../aggregation_setting/aggregation_setting_sheet.dart';

class AggregationState {
  Map<String, AggregationSettingBaseViewModel> switchViewModelMap =
      <String, AggregationSettingBaseViewModel>{};

  AggregationCamerasVM aggregationCameraViewModel =
      AggregationCamerasVM(<String>[]);
  List<String> aggDeviceIdList = <String>[];

  // 是否开启长期离线聚合
  bool offlineAggEnabled = true;

  // Key:聚合卡片的deviceId Value:聚合卡片下的设备deviceId Set集合
  Map<String, Set<String>> aggDeviceIdSetMap = <String, Set<String>>{};

  bool isLoading = true;
  // 房间全选状态 Key: aggregationId
  Map<String, RoomSelectStatus> aggregationRoomSelectStatus =
      <String, RoomSelectStatus>{};

  @override
  String toString() {
    return 'AggregationSettingState{switchViewModelMap: $switchViewModelMap}';
  }
}

// 全选状态管理
class RoomSelectStatus {
  final Map<String, bool> roomSelectStatus;

  RoomSelectStatus({Map<String, bool>? roomSelectStatus})
      : roomSelectStatus = roomSelectStatus ?? <String, bool>{};

  bool isRoomSelected(String? roomId) {
    if (roomId == null || roomId.isEmpty) {
      return false;
    }
    return roomSelectStatus[roomId] ?? false;
  }

  RoomSelectStatus copyWith({
    Map<String, bool>? roomSelectStatus,
  }) {
    return RoomSelectStatus(
      roomSelectStatus: roomSelectStatus ?? this.roomSelectStatus,
    );
  }
}
