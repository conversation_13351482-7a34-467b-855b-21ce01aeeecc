import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:family/floor_model.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/plugin/logicengine_plugin.dart';
import 'package:smart_home/device/aggregation/agg_offline/agg_offline_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/curtain_card/model/curtain_devices_by_room_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/detail_room_list.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../../common/device_filter_util.dart';
import '../../../store/smart_home_state.dart';
import '../../../store/smart_home_store.dart';
import '../../device_info_model/smart_home_device_basic_info.dart';
import '../../store/device_middleware.dart';
import '../aggregation_detail/model/supportDeviceModel.dart';
import '../aggregation_detail/utils/aggregation_detail_constant.dart';
import '../aggregation_card/light_card/model/light_devices_by_room_model.dart';
import '../aggregation_card/util/aggregation_device_util.dart';
import '../aggregation_detail/view_model/detail_room_list_item.dart';
import '../aggregation_setting/util/aggregation_setting_constant.dart';
import '../utils/agg_utils.dart';
import '../aggregation_card/light_card/model/light_devices_by_room_model.dart';
import '../aggregation_card/util/aggregation_device_util.dart';

String tag = 'AggregationDeviceSupport';

Map<String, int> _sortMap = <String, int>{};

/// 设备列表变化
void handleDeviceViewModelForAggregation(
  SmartHomeDevice smartHomeDevice,
  Map<String, Map<String, DeviceCardViewModel>> aggregationViewModelMap,
  Map<String, CardBaseViewModel> tmpAllCardViewModelMap,
) {
  if (isNormalAggregation(smartHomeDevice.basicInfo.aggregationParentId) &&
      smartHomeDevice.basicInfo.deviceRole != '3') {
    // 已聚合非附件设备
    DevLogger.debug(
        tag: tag, msg: 'handleDeviceViewModelForAggregation //聚合子设备.');

    /// 父设备id
    final String parentId = smartHomeDevice.basicInfo.aggregationParentId;

    /// 获取 cardViewModel
    final DeviceCardViewModel? cardViewModel =
        tmpAllCardViewModelMap[smartHomeDevice.basicInfo.deviceId]
            as DeviceCardViewModel?;

    if (cardViewModel is DeviceCardViewModel) {
      // 组装已聚合设备map用于更新聚合卡片vm
      final Map<String, DeviceCardViewModel> viewModelMap =
          aggregationViewModelMap[parentId] ?? <String, DeviceCardViewModel>{};

      viewModelMap[cardViewModel.sortId()] = cardViewModel;
      aggregationViewModelMap[parentId] = viewModelMap;

      // 聚合卡片vm
      final CardBaseViewModel? parentViewModel =
          tmpAllCardViewModelMap[parentId];

      // 更新聚合卡片vm
      if (parentViewModel is AggregationBaseViewModel) {
        _updateAggregationDevices(cardViewModel, parentViewModel);
      }
    }
  } else if (isNormalAggregation(smartHomeDevice.basicInfo.deviceId)) {
    // 聚合卡片
    DevLogger.debug(
        tag: tag, msg: 'handleDeviceViewModelForAggregation //聚合卡片设备.');

    // 聚合卡片vm
    final DeviceCardViewModel? cardVm =
        tmpAllCardViewModelMap[smartHomeDevice.basicInfo.deviceId]
            as DeviceCardViewModel?;

    if (cardVm is AggregationBaseViewModel) {
      final Map<String, DeviceCardViewModel>? devicesMap =
          aggregationViewModelMap[cardVm.sortId()];
      if (devicesMap == null) {
        _clearAggCardRooms(cardVm);
      }
      devicesMap?.forEach((String deviceId, DeviceCardViewModel cardViewModel) {
        _updateAggregationDevices(cardViewModel, cardVm);
      });
    }
  }
}

void sortAggregationRoomAddDevice(
  Map<String, CardBaseViewModel> tmpAllCardViewModelMap,
  Map<String, int> sortMap,
  bool showFloor,
) {
  _sortMap.clear();
  _sortMap.addAll(sortMap);
  final String familyId = Family.getCurrentFamilySync()?.familyId ?? '';
  aggregationDeviceIdList.forEach((String element) {
    final AggregationBaseViewModel? viewModel =
        tmpAllCardViewModelMap['$element$familyId']
            as AggregationBaseViewModel?;
    if (viewModel is AggregationBaseViewModel) {
      // 处理聚合设备房间列表
      final List<SmartHomeRoomInfo> tmpDeviceRoomList = _processRoomList(
        viewModel.roomList,
        getFloorOrderList,
        getRoomOrderFromFamily,
      );
      viewModel.roomList = tmpDeviceRoomList;

      // 统一处理设备排序
      _sortDevicesByRoom(viewModel.devicesByRoom, handleCardSortList, sortMap);
      _updateDetailRoomList(viewModel, showFloor);
    }
  });
}

void sortAggDeviceWithoutHome(AggDeviceData sortData) {
  smartHomeStore.state.aggregationState.isLoading = false;
  final String familyId = smartHomeStore.state.familyState.familyId;
  _sortSingleAggDeviceWithoutHome(
      sortData.lightAgg, '${AggregationSettingConstant.agg_light_id}$familyId');
  _sortSingleAggDeviceWithoutHome(sortData.curtainAgg,
      '${AggregationSettingConstant.agg_curtain_id}$familyId');
  _sortSingleAggDeviceWithoutHome(
      sortData.envAgg, '${AggregationSettingConstant.env_id}$familyId');
}

void _sortSingleAggDeviceWithoutHome(
    Map<String, List<String>>? aggData, String aggId) {
  if (aggData == null) {
    return;
  }

  final CardBaseViewModel? viewModel =
      smartHomeStore.state.deviceState.allCardViewModelMap[aggId];

  if (viewModel is AggregationBaseViewModel) {
    final Map<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel>
        tmpDevicesByRoom = viewModel.devicesByRoom;

    tmpDevicesByRoom.entries.forEach(
        (MapEntry<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> room) {
      final List<MapEntry<String, List<String>>> tarRoom = aggData.entries
          .where((MapEntry<String, List<String>> element) =>
              element.key == room.key.roomId)
          .toList();

      if (tarRoom.isNotEmpty) {
        final List<String> homeSortAgg =
            List<String>.from(room.value.deviceList);
        final List<String> newSortAgg = <String>[];

        tarRoom[0].value.forEach((String deviceId) {
          if (homeSortAgg.contains(deviceId)) {
            newSortAgg.add(deviceId);
            homeSortAgg.remove(deviceId);
          }
        });

        if (homeSortAgg.isNotEmpty) {
          newSortAgg.addAll(homeSortAgg);
        }

        room.value.deviceList = newSortAgg;
      }
    });

    viewModel.devicesByRoom = tmpDevicesByRoom;
    final List<SmartHomeRoomInfo> tmpDeviceRoomList = _processRoomList(
      viewModel.devicesByRoom.keys.toList(),
      getFloorOrderList,
      getRoomOrderFromFamily,
    );
    viewModel.roomList = tmpDeviceRoomList;
    _updateDetailRoomList(
        viewModel, smartHomeStore.state.deviceState.cardShowFloor);
  }
}

List<SmartHomeRoomInfo> _processRoomList(
  List<SmartHomeRoomInfo> originalRoomList,
  List<String> Function(Set<String>) getFloorOrderList,
  List<String> Function(Set<String> roomNameListByDevice,
          List<FloorModel> floorListByFamily, String floorNameByDevice)
      getRoomOrderList,
) {
  final Map<String, List<SmartHomeRoomInfo>> floorMap =
      <String, List<SmartHomeRoomInfo>>{};
  final Set<String> floorNameSet = <String>{};

  // 按楼层分组
  for (final SmartHomeRoomInfo room in originalRoomList) {
    final String floorName = room.floorName;
    floorNameSet.add(floorName);
    floorMap.putIfAbsent(floorName, () => <SmartHomeRoomInfo>[]).add(room);
  }

  // 按楼层顺序处理房间
  final List<SmartHomeRoomInfo> resultList = <SmartHomeRoomInfo>[];
  final List<String> orderedFloorNames = getFloorOrderList(floorNameSet);
  final List<FloorModel> floorListByFamily =
      Family.getCurrentFamilySync()?.floorInfos ?? <FloorModel>[];

  for (final String floorName in orderedFloorNames) {
    final List<SmartHomeRoomInfo> rooms =
        floorMap[floorName] ?? <SmartHomeRoomInfo>[];
    final Set<String> roomNames =
        rooms.map((SmartHomeRoomInfo room) => room.roomName).toSet();
    final List<String> orderedRoomNames =
        getRoomOrderList(roomNames, floorListByFamily, floorName);
    final Map<String, int> roomOrderMap = <String, int>{
      for (int i = 0; i < orderedRoomNames.length; i++) orderedRoomNames[i]: i
    };

    rooms.sort((SmartHomeRoomInfo a, SmartHomeRoomInfo b) {
      final int indexA = roomOrderMap[a.roomName] ?? -1;
      final int indexB = roomOrderMap[b.roomName] ?? -1;
      return indexA.compareTo(indexB);
    });

    resultList.addAll(rooms);
  }

  return resultList;
}

void _sortDevicesByRoom(
  Map<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> devicesByRoom,
  void Function(Map<String, int>, List<String>) handleSort,
  Map<String, int> sortMap,
) {
  devicesByRoom.forEach((SmartHomeRoomInfo roomInfo,
      AggregationDevicesByRoomViewModel roomViewModel) {
    handleSort(sortMap, roomViewModel.deviceList);
  });
}

// 属性变化
void updateDeviceViewModelForAggregation(
  SmartHomeDevice smartHomeDevice,
  DeviceCardViewModel deviceCardViewModel,
  bool showFloor,
) {
  if (smartHomeDevice.basicInfo.deviceRole != '3' &&
      isNormalAggregation(smartHomeDevice.basicInfo.aggregationParentId)) {
    /// 父设备id
    final String parentId = smartHomeDevice.basicInfo.aggregationParentId;

    /// 获取父设备
    final CardBaseViewModel? parentViewModel =
        smartHomeStore.state.deviceState.allCardViewModelMap[parentId];
    if (parentViewModel is AggregationBaseViewModel) {
      _updateAggregationDevicesForAttr(
          deviceCardViewModel, parentViewModel, showFloor);
    }
  } else if (!isNormalAggregation(smartHomeDevice.basicInfo.deviceId)) {
    final String parentIdForRole3 = smartHomeDevice.basicInfo.parentId;
    final CardBaseViewModel? parentViewModelForRole3 =
        smartHomeStore.state.deviceState.allCardViewModelMap[parentIdForRole3];
    if (smartHomeDevice.basicInfo.deviceRole == '3' &&
        parentViewModelForRole3 is DeviceCardViewModel &&
        parentViewModelForRole3.device.basicInfo.aggregationParentId != null &&
        parentViewModelForRole3
            .device.basicInfo.aggregationParentId.isNotEmpty) {
      /// 父设备id
      final String parentId =
          parentViewModelForRole3.device.basicInfo.aggregationParentId;

      /// 获取父设备
      final CardBaseViewModel? parentViewModel =
          smartHomeStore.state.deviceState.allCardViewModelMap[parentId];
      if (parentViewModel is AggregationBaseViewModel) {
        _updateAggregationDevicesForAttr(
            parentViewModelForRole3, parentViewModel, showFloor);
      }
    }
  }
}

// 更新聚合卡片操作状态
void updateAggregationCardAction(
    SmartHomeState state, bool aggregationOperating, String aggregationId) {
  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[aggregationId];
  if (viewModel is AggregationBaseViewModel) {
    viewModel.aggregationOperating = aggregationOperating;
    state.deviceState.allCardViewModelMap[aggregationId] = viewModel;
  }
}

// 更新聚合详情页选中房间index状态
void updateAggregationRoomIndexAction(
    SmartHomeState state, int currentIndex, String aggregationId) {
  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[aggregationId];
  if (viewModel is AggregationBaseViewModel) {
    viewModel.currentIndex = currentIndex;
    state.deviceState.allCardViewModelMap[aggregationId] = viewModel;
  }
}

// 更新详情页房间列表中房间的操作状态
void updateAggregationRoomOperateAction(SmartHomeState state, String roomId,
    bool roomOperating, String aggregationId) {
  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[aggregationId];
  if (viewModel is AggregationBaseViewModel) {
    viewModel.roomOperating = roomOperating;
    viewModel.roomIdOperating = roomId;
    state.deviceState.allCardViewModelMap[aggregationId] = viewModel;
  }
}

void addAggDevice({
  required SmartHomeState state,
  required String aggregationId,
  required List<String> allDevices,
  required AggDeviceData sortedAggList,
}) {
  _updateAggDevice(
    state: state,
    aggregationId: aggregationId,
    allDevices: allDevices,
    aggDeviceData: sortedAggList,
  );
}

void removeAggDevice({
  required SmartHomeState state,
  required String aggregationId,
  required List<String> allDevices,
  required AggDeviceData sortedAggList,
  String deleteDevice = '',
}) {
  // 更新移出聚合的设备vm
  if (deleteDevice.isNotEmpty) {
    final CardBaseViewModel? viewModel = getCardVm(deleteDevice);
    if (viewModel is DeviceCardViewModel) {
      viewModel.device.basicInfo.aggregationParentId = '';
      state.deviceState.allCardViewModelMap[deleteDevice] = viewModel;
    }
  }

  _updateAggDevice(
    state: state,
    aggregationId: aggregationId,
    allDevices: allDevices,
    aggDeviceData: sortedAggList,
  );
}

// 更新聚合设备列表
void _updateAggDevice({
  required SmartHomeState state,
  required String aggregationId,
  required List<String> allDevices,
  required AggDeviceData aggDeviceData,
}) {
  // 更新已聚合设备卡片vm
  allDevices.forEach((String id) {
    final CardBaseViewModel? viewModel = getCardVm(id);
    if (viewModel is DeviceCardViewModel) {
      viewModel.device.basicInfo.aggregationParentId =
          assembleAggId(aggregationId);
      state.deviceState.allCardViewModelMap[id] = viewModel;
    }
  });

  // 清空聚合卡片vm的设备数据和房间数据
  _clearAggCardRooms(getCardVm(aggregationId));

  /// 已聚合设备
  final Map<String, Map<String, DeviceCardViewModel>> aggregationViewModelMap =
      <String, Map<String, DeviceCardViewModel>>{};

  final Map<String, int> sortMap = <String, int>{};
  final List<String> tempAggDeviceIdList = <String>[];

  state.deviceState.allCardViewModelMap
      .forEach((String key, CardBaseViewModel value) {
    if (value is DeviceCardViewModel) {
      final SmartHomeDevice device = value.device;
      sortMap[key] = device.basicInfo.cardSort;
      if (device.basicInfo.aggregationParentId.isNotEmpty) {
        tempAggDeviceIdList.add(device.basicInfo.deviceId);
      }
      handleDeviceViewModelForAggregation(device, aggregationViewModelMap,
          state.deviceState.allCardViewModelMap);
    }
  });

  state.aggregationState.aggDeviceIdList = tempAggDeviceIdList;

  sortAggDeviceWithoutHome(aggDeviceData);
}

void _clearAggCardRooms(CardBaseViewModel? aggrCardViewModel) {
  if (aggrCardViewModel is AggregationBaseViewModel) {
    aggrCardViewModel.devicesByRoom.clear();
    aggrCardViewModel.detailRoomsModel.roomList.clear();
  }
}

List<String> sortDeviceInRoom(List<String> list) {
  // 移除附件设备
  list.removeWhere((String element) {
    final DeviceCardViewModel? cardVm = getCardVm(element);
    if (cardVm is DeviceCardViewModel) {
      return cardVm.device.basicInfo.deviceRole == '3';
    }
    return true;
  });
  return handleCardSortList(_sortMap, list);
}

void _updateAggregationDevicesForAttr(DeviceCardViewModel cardViewModel,
    AggregationBaseViewModel aggregationViewModel, bool showFloor) {
  // 更新设备列表
  final SmartHomeRoomInfo roomInfo = cardViewModel.device.basicInfo.roomInfo;
  final AggregationDevicesByRoomViewModel roomViewModel =
      aggregationViewModel.devicesByRoom[roomInfo] ??
          _initAggregationRoomModel(aggregationViewModel, roomInfo);
  // 属性变化不能更新设备列表，如果聚合设备列表中没有，就直接返回
  // 如果设备列表中存在，则更新属性参数
  if (!roomViewModel.deviceList.contains(cardViewModel.deviceId)) {
    return;
  }
  _updateKeyModelTipsForRoom(cardViewModel, roomViewModel);
  _updateRoomViewModel(roomViewModel);
  aggregationViewModel.devicesByRoom[roomInfo] = roomViewModel;

  final int roomNum = aggregationViewModel.devicesByRoom.keys.length;
  int allOpenRomNum = 0;
  int allCloseRoomNum = 0;
  int allOfflineRoomNum = 0;
  int onlineRoomNum = 0;
  int roomOpenNum = 0;
  aggregationViewModel.devicesByRoom.forEach((SmartHomeRoomInfo roomInfo,
      AggregationDevicesByRoomViewModel roomModel) {
    if (roomModel.deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_OFFLINE) {
      allOfflineRoomNum++;
    } else {
      onlineRoomNum++;
      if (roomModel.deviceStateForAggregation ==
          DeviceStateForAggregation.ALL_CLOSE) {
        allCloseRoomNum++;
      } else {
        if (roomModel.deviceStateForAggregation ==
            DeviceStateForAggregation.ALL_OPEN) {
          allOpenRomNum++;
        }
        if (roomModel.isRoomOpen) {
          roomOpenNum++;
        }
      }
    }
  });

  aggregationViewModel.openRoomCount = roomOpenNum;
  aggregationViewModel.onlineRoomCount = onlineRoomNum;
  if (roomNum == allOfflineRoomNum) {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_OFFLINE;
  } else if (onlineRoomNum == allCloseRoomNum) {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_CLOSE;
  } else if (onlineRoomNum == allOpenRomNum) {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_OPEN;
  } else {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.PART_OPEN;
  }
  _updateDetailRoomList(aggregationViewModel, showFloor);
}

void _updateKeyModelTipsForRoom(DeviceCardViewModel cardViewModel,
    AggregationDevicesByRoomViewModel roomViewModel) {
  final String keyModelTip = _getKeyModelTip(cardViewModel);
  DevLogger.debug(
      tag: tag,
      msg: '_updateKeyModelTipsForRoom'
          ' with keyModelTip: $keyModelTip');
  if (cardViewModel != null && cardViewModel.deviceId.isNotEmpty) {
    roomViewModel.keyModelTips[cardViewModel.deviceId] = keyModelTip;
  }
}

String _getKeyModelTip(DeviceCardViewModel? cardViewModel) {
  final String keyModel =
      cardViewModel?.getDeviceCardAttrValueByName('keyModel4') ?? '';
  final String keyModelTip = getTipStrByKeyMode(keyModel);
  DevLogger.debug(
      tag: tag,
      msg: '_getKeyModelTip'
          ' with keyModel4: $keyModel');
  return keyModelTip;
}

void _updateAggregationDevices(DeviceCardViewModel cardViewModel,
    AggregationBaseViewModel aggregationViewModel) {
  // 更新设备列表
  final SmartHomeRoomInfo roomInfo = cardViewModel.device.basicInfo.roomInfo;
  final AggregationDevicesByRoomViewModel roomViewModel =
      aggregationViewModel.devicesByRoom[roomInfo] ??
          _initAggregationRoomModel(aggregationViewModel, roomInfo);
  if (!roomViewModel.deviceList.contains(cardViewModel.deviceId)) {
    roomViewModel.deviceList.add(cardViewModel.deviceId);
  }
  _updateRoomViewModel(roomViewModel);
  aggregationViewModel.devicesByRoom[roomInfo] = roomViewModel;

  final int roomNum = aggregationViewModel.devicesByRoom.keys.length;
  int allOpenRomNum = 0;
  int allCloseRoomNum = 0;
  int allOfflineRoomNum = 0;
  int onlineRoomNum = 0;
  int roomOpenNum = 0;
  aggregationViewModel.devicesByRoom.forEach((SmartHomeRoomInfo roomInfo,
      AggregationDevicesByRoomViewModel roomModel) {
    if (roomModel.deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_OFFLINE) {
      allOfflineRoomNum++;
    } else {
      onlineRoomNum++;
      if (roomModel.deviceStateForAggregation ==
          DeviceStateForAggregation.ALL_CLOSE) {
        allCloseRoomNum++;
      } else if (roomModel.deviceStateForAggregation ==
          DeviceStateForAggregation.ALL_OPEN) {
        allOpenRomNum++;
      }
      if (roomModel.isRoomOpen) {
        roomOpenNum++;
      }
    }
  });

  aggregationViewModel.openRoomCount = roomOpenNum;
  aggregationViewModel.onlineRoomCount = onlineRoomNum;
  if (roomNum == allOfflineRoomNum) {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_OFFLINE;
  } else if (onlineRoomNum == allCloseRoomNum) {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_CLOSE;
  } else if (onlineRoomNum == roomOpenNum) {
    aggregationViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_OPEN;
  } else {
    if (roomOpenNum == 0) {
      aggregationViewModel.deviceStateForAggregation =
          DeviceStateForAggregation.ALL_CLOSE;
    } else {
      aggregationViewModel.deviceStateForAggregation =
          DeviceStateForAggregation.PART_OPEN;
    }
  }

  // 组装房间列表
  aggregationViewModel.roomList =
      aggregationViewModel.devicesByRoom.keys.toList();
}

void _updateDetailRoomList(
    AggregationBaseViewModel baseViewModel, bool showFloor) {
  final List<DetailRoomListItem> roomList = <DetailRoomListItem>[];
  baseViewModel.roomList.forEach((SmartHomeRoomInfo roomInfo) {
    final AggregationDevicesByRoomViewModel? roomViewModel =
        baseViewModel.devicesByRoom[roomInfo];
    if (roomViewModel is AggregationDevicesByRoomViewModel &&
        roomViewModel.deviceList.isNotEmpty) {
      String floorName = '', roomName = roomInfo.roomName;
      if (showFloor) {
        floorName = roomInfo.floorName;
      }
      DeviceStateForAggregation roomStatus = DeviceStateForAggregation.UNKNOWN;
      final String roomId = roomInfo.roomId;
      final String itemRoomName = '$floorName$roomName';
      final bool roomOperating = baseViewModel.roomIdOperating == roomId &&
          baseViewModel.roomOperating;
      final List<String> deviceList = <String>[];
      int openCount = 0;
      bool isRoomOpen = false;
      AggregationDevicesByRoomViewModel? roomViewModel;
      baseViewModel.devicesByRoom.forEach((SmartHomeRoomInfo roomInfo,
          AggregationDevicesByRoomViewModel model) {
        if (roomId == roomInfo.roomId) {
          roomStatus = model.deviceStateForAggregation;
          deviceList.addAll(model.deviceList);
          isRoomOpen = model.isRoomOpen;
          roomViewModel = model;
          openCount = model.openCount;
        }
      });
      final DetailRoomListItem contentRoomListItem = DetailRoomListItem(
          roomStatus,
          roomOperating,
          roomId,
          itemRoomName,
          deviceList,
          openCount,
          isRoomOpen,
          roomViewModel ?? AggregationDevicesByRoomViewModel());
      roomList.add(contentRoomListItem);
    }
  });
  final DetailRoomList detailRoomsModel = DetailRoomList(roomList);
  baseViewModel.detailRoomsModel = detailRoomsModel;
}

AggregationDevicesByRoomViewModel _initAggregationRoomModel(
    DeviceCardViewModel cardViewModel, SmartHomeRoomInfo roomInfo) {
  if (isDeviceCurtainAggregation(cardViewModel.deviceId)) {
    return CurtainDevicesByRoomModel(roomInfo: roomInfo);
  }
  return LightDevicesByRoomModel(roomInfo: roomInfo);
}

String getTipStrByKeyMode(String keyModel) {
  if (keyModel == '1') {
    return '已转为负载分离设备，不可操控';
  } else if (keyModel == '2') {
    return '已转为场景按键，不可操控';
  }
  return '';
}

void _updateRoomViewModel(AggregationDevicesByRoomViewModel roomViewModel) {
  final int roomDeviceNum = roomViewModel.deviceList.length;
  int offlineDeviceNum = 0;
  int onlineDeviceNum = 0;
  int openDeviceNum = 0;
  int closeDeviceNum = 0;
  roomViewModel.deviceList.forEach((String devId) {
    final bool deviceExists =
        smartHomeStore.state.deviceState.allCardViewModelMap.containsKey(devId);
    if (deviceExists) {
      final DeviceCardViewModel? model = smartHomeStore
          .state.deviceState.allCardViewModelMap[devId] as DeviceCardViewModel?;
      if (model is DeviceCardViewModel) {
        final SmartHomeDevice device = model.device;
        if (isDeviceOffLine(device)) {
          offlineDeviceNum++;
        } else {
          onlineDeviceNum++;
          if (isDeviceSmartSwitchOrScreen(model)) {
            // judge device open state by children
            if (isSwitchOpen(model)) {
              openDeviceNum++;
            }
          } else {
            // 原处理
            if (isOpen(device)) {
              openDeviceNum++;
            }
          }
          if (isDeviceSmartSwitchOrScreen(model)) {
            // judge device close state by children
            if (isSwitchClose(model)) {
              closeDeviceNum++;
            }
          } else {
            // 原处理
            if (isClose(device)) {
              closeDeviceNum++;
            }
          }
        }
      }
    }
  });
  roomViewModel.openCount = openDeviceNum;
  roomViewModel.onlineCount = onlineDeviceNum;
  if (roomDeviceNum == offlineDeviceNum) {
    roomViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_OFFLINE;
    roomViewModel.isRoomOpen = false;
  } else if (onlineDeviceNum == closeDeviceNum) {
    roomViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_CLOSE;
    roomViewModel.isRoomOpen = false;
  } else if (onlineDeviceNum == openDeviceNum) {
    roomViewModel.deviceStateForAggregation =
        DeviceStateForAggregation.ALL_OPEN;
    roomViewModel.isRoomOpen = true;
  } else {
    if (openDeviceNum == 0) {
      roomViewModel.deviceStateForAggregation =
          DeviceStateForAggregation.ALL_CLOSE;
      roomViewModel.isRoomOpen = false;
    } else {
      roomViewModel.deviceStateForAggregation =
          DeviceStateForAggregation.PART_OPEN;
      roomViewModel.isRoomOpen = true;
    }
  }
}

void aggregationBatchPowerCommandEngine(
    Map<String, List<Command>> groupCommands) {
  if (groupCommands.isEmpty) {
    DevLogger.debug(
        tag: 'SmartHomeDevice',
        msg:
            'aggregationBatchPowerCommandEngine abort, groupCommands is empty, return');
    return;
  }

  DevLogger.debug(
      tag: 'SmartHomeDevice',
      msg:
          '_batchPowerCommandEngine begin, groupCommands-length:${groupCommands.length}, commands deviceIds:${groupCommands.keys.toList()}');

  groupCommands.forEach((String key, List<Command> value) {
    DevLogger.debug(
        tag: 'SmartHomeDevice',
        msg:
            'aggregationBatchPowerCommandEngine print all params, deviceId:$key, command:$value');
  });

  LogicEnginePlugin.batchOperate(groupCommands).then((void value) {
    DevLogger.debug(
        tag: 'SmartHomeDevice',
        msg: 'aggregationBatchPowerCommandEngine success');
  }).catchError((dynamic e) {
    DevLogger.error(
        tag: 'SmartHomeDevice',
        msg: 'aggregationBatchPowerCommandEngine fail: err: $e');
  });
}

bool isDeviceLightAggregation(String deviceId) {
  return deviceId.contains(AggregationSettingConstant.agg_light_id);
}

bool isDeviceCurtainAggregation(String deviceId) {
  return deviceId.contains(AggregationSettingConstant.agg_curtain_id);
}

/// 是否是已聚合设备或聚合卡片【灯光、窗帘、环境】
/// 灯光、窗帘、环境继承同一个vm AggregationBaseViewModel
/// id 传 deviceId时判断是否是聚合卡片
/// id 传 aggregationParentId时判断是否是已聚合设备
bool isNormalAggregation(String id) {
  return isDeviceLightAggregation(id) ||
      isDeviceCurtainAggregation(id) ||
      isEnvAgg(id);
}

List<String> aggregationDeviceIdList = <String>[
  AggregationSettingConstant.agg_light_id,
  AggregationSettingConstant.agg_curtain_id,
  AggregationSettingConstant.env_id
];

String getLightAggregationKey() {
  final FamilyModel? familyModel = Family.getCurrentFamilySync();
  final String lightKey =
      '${AggregationSettingConstant.agg_light_id}${familyModel?.familyId}';
  return lightKey;
}

String getCurtainAggregationKey() {
  final FamilyModel? familyModel = Family.getCurrentFamilySync();
  final String curtainKey =
      '${AggregationSettingConstant.agg_curtain_id}${familyModel?.familyId}';
  return curtainKey;
}

String getAggregationKey(String aggHardKey) {
  final FamilyModel? familyModel = Family.getCurrentFamilySync();
  final String aggregationKey = '$aggHardKey${familyModel?.familyId}';
  return aggregationKey;
}

// 判断是否是灯组
bool isGroup(SmartHomeDevice smartHomeDevice) {
  final bool isDeviceGroup =
      smartHomeDevice.basicInfo.deviceGroupType == 'group';
  DevLogger.debug(
      tag: 'SmartHomeDevice', msg: 'is device group: $isDeviceGroup');
  return isDeviceGroup;
}

// 判断设备是否隐藏，根据产品编码
bool shouldDeviceHideForAgg(SmartHomeDevice smartHomeDevice) {
  final bool isDeviceCurtainAgg = isDeviceCurtainAggregation(
      smartHomeDevice.basicInfo.aggregationParentId); // 聚合窗帘，全部隐藏
  final bool isDeviceLightAgg =
      isDeviceLightAggregation(smartHomeDevice.basicInfo.aggregationParentId);
  final bool _isNotGroup = !isGroup(smartHomeDevice); // 聚合灯表格中需要隐藏的开关
  return isDeviceCurtainAgg ||
      (isDeviceLightAgg && _isNotGroup) ||
      isOfflineAggDevice(smartHomeDevice.basicInfo.deviceId) ||
      isNonNetAgg(smartHomeDevice.basicInfo.aggregationParentId) ||
      isEnvAgg(
          smartHomeDevice.basicInfo.aggregationParentId); // 聚合环境设备全屋tab下全部隐藏
}

// 获取卡片Vm
DeviceCardViewModel? getCardVm(String id) {
  final CardBaseViewModel? viewModel =
      smartHomeStore.state.deviceState.allCardViewModelMap[id];
  if (viewModel is DeviceCardViewModel) {
    return viewModel;
  }
  return null;
}

bool _isLightGroup(SmartHomeDevice smartHomeDevice) {
  final bool isDeviceLightAgg =
      isDeviceLightAggregation(smartHomeDevice.basicInfo.aggregationParentId);
  final bool _isGroup = isGroup(smartHomeDevice);
  return isDeviceLightAgg && _isGroup;
}

// 判断是否需要通过附件设备判断（聚合设备中只有灯组，智能开关，大脑屏有附件设备）
bool isDeviceSmartSwitchOrScreen(DeviceCardViewModel model) {
  // 灯组按照默认逻辑处理
  final bool _isNotLightGroup = !_isLightGroup(model.device);
  // 附件设备非空说明是智能开关、大脑屏、灯组
  final bool _isChildrenNotEmpty = model.childViewModelMap?.isNotEmpty ?? false;
  // 附件设备非空，并且不是灯组的情况下，需要通过附件设备判断设备是否开启/关闭
  return _isNotLightGroup && _isChildrenNotEmpty;
}

typedef AggregationFilter = bool Function(String parentId);

void filterDevicesByAggregation(SmartHomeDevice smartHomeDevice,
    Set<String> deviceIds, AggregationFilter filter) {
  final String deviceId = smartHomeDevice.basicInfo.deviceId;
  final String parentId = smartHomeDevice.basicInfo.aggregationParentId;
  if (filter(parentId)) {
    deviceIds.add(deviceId);
  }
}

// 更新长期离线设备数据(如果设备离线数据刷新，则更新state以及长期离线聚合卡片viewModel中存储的长期离线设备数据)
void updateAggOfflineDevices(
    SmartHomeDevice smartHomeDevice, SmartHomeState state) {
  if (!state.aggregationState.offlineAggEnabled ||
      smartHomeDevice.basicInfo.isSharedDevice) {
    return;
  }
  final String deviceId = smartHomeDevice.basicInfo.deviceId;
  final String aggOfflineDeviceId =
      '${AggregationSettingConstant.offline_id}${state.familyState.familyId}';
  final AggOfflineViewModel? aggOfflineViewModel = state.deviceState
      .allCardViewModelMap[aggOfflineDeviceId] as AggOfflineViewModel?;
  final Set<String> offlineDeviceSet =
      state.aggregationState.aggDeviceIdSetMap[aggOfflineDeviceId] ??
          <String>{};
  if (isLongOfflineDevice(smartHomeDevice)) {
    aggOfflineViewModel?.longOfflineDeviceIds.add(deviceId);
    offlineDeviceSet.add(deviceId);
    state.aggregationState.aggDeviceIdList.add(deviceId);
  } else if (offlineDeviceSet.contains(deviceId) &&
      smartHomeDevice.onlineState != SmartHomeDeviceOnlineState.offline) {
    aggOfflineViewModel?.longOfflineDeviceIds.remove(deviceId);
    offlineDeviceSet.remove(deviceId);
    state.aggregationState.aggDeviceIdList.remove(deviceId);
  }
  state.aggregationState.aggDeviceIdSetMap[aggOfflineDeviceId] =
      offlineDeviceSet;
}
