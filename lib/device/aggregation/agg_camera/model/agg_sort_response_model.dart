import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class AggSortResponseModel extends UhomeResponseModel {
  AggSortResponseModel.fromJson(super.data) : super.fromJson() {
    data = AggDetailSortModel.fromJson(super.retData);
  }

  AggDetailSortModel data = AggDetailSortModel.fromJson(<dynamic, dynamic>{});
}

class AggDetailSortModel {
  List<AggSortModel> aggSortList = <AggSortModel>[];

  AggDetailSortModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> data = json.listValueForKey('aggSortList', <dynamic>[]);
    for (final dynamic v in data) {
      if (v is Map<dynamic, dynamic>) {
        aggSortList.add(AggSortModel.fromJson(v));
      }
    }
  }

  @override
  String toString() {
    return 'CameraAggSortModel{aggSortList: $aggSortList}';
  }
}

class AggSortModel {
  String deviceId = '';
  String cardStatus = '';

  AggSortModel.fromJson(Map<dynamic, dynamic> json) {
    deviceId = json.stringValueForKey('deviceId', '');
    cardStatus = json.stringValueForKey('cardStatus', '');
  }

  @override
  String toString() {
    return '{deviceId: $deviceId, cardStatus: $cardStatus}';
  }
}
