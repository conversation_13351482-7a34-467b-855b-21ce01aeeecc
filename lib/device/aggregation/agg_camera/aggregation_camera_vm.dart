import 'package:flutter/foundation.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_action.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/base_aggregation_widget_state.dart';
import 'package:smart_home/device/aggregation/utils/agg_utils.dart';
import 'package:smart_home/store/smart_home_store.dart';

enum AggregationCamerasVMCardType {
  large,
  small,
}

class AggregationCameraSortedVMWrapper extends BaseAggCameraWrapper {
  Map<String, List<String>> data;

  List<String> devs;

  AggregationCamerasVMCardType cardType;

  AggregationCameraSortedVMWrapper(this.data, this.devs, this.cardType);

  String getName(int index) {
    return data[devs[index]]?[0] ?? '';
  }

  String getDesc(int index) {
    return data[devs[index]]?[1] ?? '';
  }

  void saveAggCameraDetailSortedInfo() {
    gioAggDetailBtnClick(
        GioConst.aggDetailBtn,
        AggregationDeviceConstant.gioCameraTitle,
        AggregationDeviceConstant.gioCameraSort);
    smartHomeStore.dispatch(SaveAggregationCameraDetailSortedDevsAction(
        devs, cardType == AggregationCamerasVMCardType.large));
  }

  @override
  bool isNotValid() {
    return devs.isEmpty;
  }
}

class AggregationCamerasVMWrapper extends BaseAggCameraWrapper {
  List<String> cameraList = <String>[];

  AggregationCamerasVMWrapper(this.isLargeCard, this.cameraList);

  bool isLargeCard = false;
  @override
  bool operator ==(Object other) {
    bool flag = other is AggregationCamerasVMWrapper &&
        listEquals(cameraList, other.cameraList) &&
        isLargeCard == other.isLargeCard;
    return flag;
  }

  @override
  bool isNotValid() {
    return cameraList.isEmpty;
  }

  @override
  int get hashCode => listHashCode(cameraList) ^ isLargeCard.hashCode;
}

int listHashCode(List<String> list) {
  return Object.hashAll(list.asMap().entries.map((MapEntry<int, String> entry) {
    return Object.hash(entry.key, entry.value);
  }));
}

class AggregationCamerasVM {
  List<String> cameraList = <String>[];

  void updateDevs(Set<String> devs, bool isLarge) {
    cameraList.clear();
    cameraList.addAll(devs);
    cardType = isLarge
        ? AggregationCamerasVMCardType.large
        : AggregationCamerasVMCardType.small;
  }

  AggregationCamerasVMCardType cardType = AggregationCamerasVMCardType.small;
  bool get isLargeCard => cardType == AggregationCamerasVMCardType.large;
  AggregationCamerasVM(this.cameraList);

  void switchLargeCard() {
    if (isLargeCard) {
      cardType = AggregationCamerasVMCardType.small;
    } else {
      cardType = AggregationCamerasVMCardType.large;
    }
  }

  @override
  bool operator ==(Object other) {
    bool flag = other is AggregationCamerasVM &&
        listEquals(cameraList, other.cameraList) &&
        cardType == other.cardType;
    return flag;
  }

  @override
  int get hashCode => listHashCode(cameraList) ^ cardType.hashCode;
}
