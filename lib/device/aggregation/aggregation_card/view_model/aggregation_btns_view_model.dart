import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class AggregationBtnViewModel extends ComponentBaseViewModel {
  final bool allOpen, allClose, allOffline, isEditState;
  final void Function(BuildContext context)? openClickCallback;
  final void Function(BuildContext context)? closeClickCallback;
  final Color offColor;

  AggregationBtnViewModel(
      {this.allOpen = false,
      this.allClose = false,
      this.allOffline = false,
      required this.openClickCallback,
      required this.closeClickCallback,
      this.offColor = const Color(0xfff5f5f5),
      this.isEditState = false});

  @override
  ComponentType componentType() {
    return ComponentType.aggregation_btn;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationBtnViewModel &&
          runtimeType == other.runtimeType &&
          allOffline == other.allOffline &&
          allOpen == other.allOpen &&
          allClose == other.allClose;

  @override
  int get hashCode =>
      super.hashCode ^
      allOpen.hashCode ^
      allClose.hashCode ^
      allOffline.hashCode;
}
