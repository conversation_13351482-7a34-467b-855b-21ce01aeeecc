import 'package:smart_home/device/component_view_model/component_view_model.dart';

class AggregationEmptyViewModel extends ComponentBaseViewModel {
  final String emptyTitle;
  AggregationEmptyViewModel({this.emptyTitle = '无设备'});

  @override
  ComponentType componentType() {
    return ComponentType.aggregationEmpty;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationEmptyViewModel &&
          runtimeType == other.runtimeType &&
          emptyTitle == other.emptyTitle;

  @override
  int get hashCode => super.hashCode ^ emptyTitle.hashCode;
}
