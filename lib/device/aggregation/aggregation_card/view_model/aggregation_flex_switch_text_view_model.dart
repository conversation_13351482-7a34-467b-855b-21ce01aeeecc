import 'package:flutter/cupertino.dart';

class AggregationFlexSwitchTextViewModel {
  final String packageName;
  final String text;
  final bool enable;
  final bool isOn;
  final void Function(BuildContext context)? clickCallback;
  final Color offColor;

  AggregationFlexSwitchTextViewModel(
      {this.packageName = 'smart_home',
      required this.text,
      required this.enable,
      required this.isOn,
      this.clickCallback,
      this.offColor = const Color(0xfff5f5f5)});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationFlexSwitchTextViewModel &&
          runtimeType == other.runtimeType &&
          packageName == other.packageName &&
          text == other.text &&
          enable == other.enable &&
          isOn == other.isOn;

  @override
  int get hashCode =>
      super.hashCode ^
      packageName.hashCode ^
      text.hashCode ^
      enable.hashCode ^
      isOn.hashCode;
}
