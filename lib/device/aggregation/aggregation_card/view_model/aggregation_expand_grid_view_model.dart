import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

import '../../../component/view_model/expand_switch_icon_text_view_model.dart';

class AggregationExpandGridViewModel extends ComponentBaseViewModel {
  List<ComponentBaseViewModel> roomList = <ExpandSwitchIconTextViewModel>[];

  AggregationExpandGridViewModel({required this.roomList, super.expandFlex});

  @override
  ComponentType componentType() {
    return ComponentType.expandGridView;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationExpandGridViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(roomList, other.roomList);

  @override
  int get hashCode => super.hashCode ^ listHashCode(roomList);
}
