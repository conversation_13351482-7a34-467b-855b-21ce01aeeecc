import 'package:plugin_device/model/common_models.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';

import '../../../agg_store/aggregation_device_reducer_support.dart';
import '../../util/aggregation_device_util.dart';

class CurtainDevicesByRoomModel extends AggregationDevicesByRoomViewModel {
  CurtainDevicesByRoomModel({super.roomInfo});

  @override
  String get roomDeviceAllOpenTip => '所有窗帘已开启';

  @override
  String get roomDeviceAllCloseTip => '所有窗帘已闭合';

  @override
  void emitPowerCommand(bool powerOn) {
    // 窗帘结合开合度处理
    final Map<String, List<Command>> batchCommands = <String, List<Command>>{};
    updateCurtainPowerCommand(batchCommands, deviceList, powerOn);
    aggregationBatchPowerCommandEngine(batchCommands);
  }
}
