import 'package:flutter/cupertino.dart';
import 'package:library_widgets/common/util.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';

import '../../../../component_view_model/component_view_model.dart';
import '../../../../device_info_model/smart_home_device_basic_info.dart';
import '../../../../device_view_model/device_card_view_model.dart';
import '../../../../resize_device_card/resize_base_model.dart';
import '../../../agg_store/aggregation_device_reducer_support.dart';
import '../../util/aggregation_device_util.dart';
import '../../view_model/aggregation_btns_view_model.dart';
import '../../view_model/aggregation_devices_by_room_model.dart';
import '../../view_model/aggregation_empty_view_model.dart';

const String tag = 'AggregationCurtainViewModel';

class AggregationCurtainViewModel extends AggregationBaseViewModel {
  AggregationCurtainViewModel({required super.device});

  @override
  String get managePageTitle => '窗帘设备管理';

  @override
  String get managePageSubTitle => '添加或删除在窗帘聚合页面展示的设备';

  @override
  String get manageDialogTitle => '添加窗帘设备';

  @override
  String get manageListEmpty => '暂无窗帘设备';

  @override
  String get wholeHouseDeviceAllOpenTip => '所有窗帘已开启';

  @override
  String get wholeHouseDeviceAllCloseTip => '所有窗帘已闭合';

  // 标题复写
  @override
  String get deviceName => AggregationDeviceConstant.aggregationCurtainTitle;

  @override
  String get deviceIcon =>
      'assets/images/icon_aggregation_curtain_card.webp'; // 设备卡片默认使用的是网络图片，不支持本地图片

  /// 大卡片副标题复写
  @override
  String get unionLargeCardStatus {
    return getStatus();
  }

  @override
  String getStatus() {
    if (aggregationOperating) {
      return '正在运行中';
    }
    if (devicesByRoom.isEmpty) {
      return AggregationDeviceConstant.aggregationCurtainSubTitleNoLightDevice;
    } else if (deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_OFFLINE) {
      return AggregationDeviceConstant
          .aggregationCurtainSubTitleAllClose; //离线算关闭
    } else if (deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_OPEN) {
      return AggregationDeviceConstant.aggregationCurtainSubTitleAllOpen;
    } else if (deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_CLOSE) {
      return AggregationDeviceConstant.aggregationCurtainSubTitleAllClose;
    }
    return '$openRoomCount${AggregationDeviceConstant.aggregationCurtainSubTitlePartOpen}';
  }

  /// 中卡片副标题复写
  @override
  String get unionMiddleCardStatus {
    return getStatus();
  }

  /// 小卡片副标题复写
  @override
  String get unionSmallCardStatus {
    return getStatus();
  }

  /// 全开全关按钮区域复写
  @override
  ComponentBaseViewModel? get unionTopRightComponentViewModel {
    if (devicesByRoom.isEmpty || deviceCardType != DeviceCardType.largeCard) {
      return null;
    }
    return AggregationBtnViewModel(
        allClose: !aggregationOperating &&
            (deviceStateForAggregation == DeviceStateForAggregation.ALL_CLOSE ||
                deviceStateForAggregation ==
                    DeviceStateForAggregation
                        .ALL_OFFLINE), // 离线认为是关闭，因此全部离线认为是全部关闭
        allOpen: !aggregationOperating &&
            deviceStateForAggregation == DeviceStateForAggregation.ALL_OPEN,
        closeClickCallback: (BuildContext context) {
          gioTrack(AggregationDeviceConstant.cardClickGio,
              <String, dynamic>{'sourceName': '窗帘', 'value': '全关'});
          turnOffAllDevices(context);
        },
        openClickCallback: (BuildContext context) {
          gioTrack(AggregationDeviceConstant.cardClickGio,
              <String, dynamic>{'sourceName': '窗帘', 'value': '全开'});
          turnOnAllDevices(context);
        });
  }

  /// 房间列表区域复写
  @override
  Map<String, LargeDeviceCardFunctionSet> get unionLargeCardFuncMap {
    const String curtainKey = '窗帘聚合';
    if (devicesByRoom.isEmpty) {
      return <String, LargeDeviceCardFunctionSet>{
        curtainKey: LargeDeviceCardFunctionSet(
            name: curtainKey, componentViewModelList: getEmptyViewModel())
      };
    }
    if (devicesByRoom.length > 3) {
      return <String, LargeDeviceCardFunctionSet>{
        curtainKey: LargeDeviceCardFunctionSet(
            name: curtainKey,
            componentAlign: ComponentAlign.noInterval,
            componentViewModelList: getRoomListViewModel())
      };
    } else if (devicesByRoom.length > 1) {
      return <String, LargeDeviceCardFunctionSet>{
        curtainKey: LargeDeviceCardFunctionSet(
          name: curtainKey,
          componentAlign: ComponentAlign.interval,
          componentViewModelList: getRoomComponentViewModelList(),
        )
      };
    } else {
      return <String, LargeDeviceCardFunctionSet>{
        curtainKey: LargeDeviceCardFunctionSet(
          name: curtainKey,
          componentAlign: ComponentAlign.interval,
          componentViewModelList: getRoomComponentViewModelListOne(),
        )
      };
    }
  }

  /// 功能区空数据VM复写
  @override
  List<ComponentBaseViewModel> getEmptyViewModel() {
    return <ComponentBaseViewModel>[
      AggregationEmptyViewModel(
          emptyTitle: AggregationDeviceConstant.aggNoCurtainDevice)
    ];
  }

  @override
  String getRoomIcon(bool isRoomOn) {
    return isRoomOn
        ? 'assets/images/icon_aggregation_curtain_open.webp'
        : 'assets/images/icon_aggregation_curtain_close.webp';
  }

  @override
  void emitPowerCommand(bool powerOn) {
    final Map<String, List<Command>> batchCommands = <String, List<Command>>{};
    final List<String> devices = <String>[];

    devicesByRoom.forEach((SmartHomeRoomInfo key,
        AggregationDevicesByRoomViewModel roomViewModel) {
      devices.addAll(roomViewModel.deviceList);
    });
    updateCurtainPowerCommand(batchCommands, devices, powerOn);
    aggregationBatchPowerCommandEngine(batchCommands);
  }
}
