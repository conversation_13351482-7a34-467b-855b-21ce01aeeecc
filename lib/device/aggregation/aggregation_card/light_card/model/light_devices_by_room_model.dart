import 'package:plugin_device/model/common_models.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';

import '../../../agg_store/aggregation_device_reducer_support.dart';
import '../../util/aggregation_device_util.dart';

class LightDevicesByRoomModel extends AggregationDevicesByRoomViewModel {
  LightDevicesByRoomModel({super.roomInfo});

  @override
  String get roomDeviceAllOpenTip => '所有灯光已开启';

  @override
  String get roomDeviceAllCloseTip => '所有灯光已关闭';

  @override
  void emitPowerCommand(bool powerOn) {
    final Map<String, List<Command>> batchCommands = <String, List<Command>>{};
    updateLightPowerCommand(batchCommands, deviceList, powerOn);
    aggregationBatchPowerCommandEngine(batchCommands);
  }
}
