import 'package:flutter/cupertino.dart';
import 'package:library_widgets/common/util.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_util.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_btns_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';

import '../../../../component_view_model/component_view_model.dart';
import '../../../../device_info_model/smart_home_device_basic_info.dart';
import '../../../../device_view_model/device_card_view_model.dart';
import '../../../../resize_device_card/resize_base_model.dart';
import '../../../agg_store/aggregation_device_reducer_support.dart';
import '../../view_model/aggregation_empty_view_model.dart';

const String tag = 'AggregationLightViewModel';

class AggregationLightViewModel extends AggregationBaseViewModel {
  AggregationLightViewModel({required super.device});

  @override
  String get managePageTitle => '灯光设备管理';

  @override
  String get managePageSubTitle => '添加或删除在灯光聚合页面展示的设备';

  @override
  String get manageDialogTitle => '添加灯光设备';

  @override
  String get manageListEmpty => '暂无灯光设备';

  @override
  String get wholeHouseDeviceAllOpenTip => '所有灯光已开启';

  @override
  String get wholeHouseDeviceAllCloseTip => '所有灯光已关闭';

  // 标题复写
  @override
  String get deviceName => AggregationDeviceConstant.aggregationLightTitle;

  @override
  String get deviceIcon =>
      'assets/images/icon_aggregation_light_card.webp'; // 设备卡片默认使用的是网络图片，不支持本地图片

  /// 大卡片副标题复写
  @override
  String get unionLargeCardStatus {
    return getStatus();
  }

  /// 卡片副标题获取
  @override
  String getStatus() {
    if (aggregationOperating) {
      return '正在运行中';
    }
    if (devicesByRoom.isEmpty) {
      return AggregationDeviceConstant.aggregationLightSubTitleNoLightDevice;
    } else if (deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_OFFLINE) {
      return AggregationDeviceConstant
          .aggregationLightSubTitleAllClose; // 离线算关闭
    } else if (deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_OPEN) {
      return AggregationDeviceConstant.aggregationLightSubTitleAllOpen;
    } else if (deviceStateForAggregation ==
        DeviceStateForAggregation.ALL_CLOSE) {
      return AggregationDeviceConstant.aggregationLightSubTitleAllClose;
    }
    return '$openRoomCount${AggregationDeviceConstant.aggregationLightSubTitlePartOpen}';
  }

  /// 中卡片副标题复写
  @override
  String get unionMiddleCardStatus {
    return getStatus();
  }

  /// 小卡片副标题复写
  @override
  String get unionSmallCardStatus {
    return getStatus();
  }

  /// 全开全关按钮区域复写
  @override
  ComponentBaseViewModel? get unionTopRightComponentViewModel {
    if (devicesByRoom.isEmpty || deviceCardType != DeviceCardType.largeCard) {
      return null;
    }
    return AggregationBtnViewModel(
        allClose: !aggregationOperating &&
            (deviceStateForAggregation == DeviceStateForAggregation.ALL_CLOSE ||
                deviceStateForAggregation ==
                    DeviceStateForAggregation.ALL_OFFLINE), // 全部离线认为是全部关闭
        allOpen: !aggregationOperating &&
            deviceStateForAggregation == DeviceStateForAggregation.ALL_OPEN,
        closeClickCallback: (BuildContext context) {
          gioTrack(AggregationDeviceConstant.cardClickGio,
              <String, dynamic>{'sourceName': '灯光', 'value': '全关'});
          turnOffAllDevices(context);
        },
        openClickCallback: (BuildContext context) {
          gioTrack(AggregationDeviceConstant.cardClickGio,
              <String, dynamic>{'sourceName': '灯光', 'value': '全开'});
          turnOnAllDevices(context);
        });
  }

  /// 房间列表区域复写
  @override
  Map<String, LargeDeviceCardFunctionSet> get unionLargeCardFuncMap {
    const String lightKey = '灯光聚合';
    if (devicesByRoom.isEmpty) {
      return <String, LargeDeviceCardFunctionSet>{
        lightKey: LargeDeviceCardFunctionSet(
            name: lightKey, componentViewModelList: getEmptyViewModel())
      };
    }
    if (devicesByRoom.keys.length > 3) {
      return <String, LargeDeviceCardFunctionSet>{
        lightKey: LargeDeviceCardFunctionSet(
            name: lightKey,
            componentAlign: ComponentAlign.noInterval,
            componentViewModelList: getRoomListViewModel())
      };
    } else if (devicesByRoom.keys.length > 1) {
      return <String, LargeDeviceCardFunctionSet>{
        lightKey: LargeDeviceCardFunctionSet(
          name: lightKey,
          componentViewModelList: getRoomComponentViewModelList(),
        )
      };
    } else {
      return <String, LargeDeviceCardFunctionSet>{
        lightKey: LargeDeviceCardFunctionSet(
          name: lightKey,
          componentViewModelList: getRoomComponentViewModelListOne(),
        )
      };
    }
  }

  /// 功能区空数据VM复写
  @override
  List<ComponentBaseViewModel> getEmptyViewModel() {
    return <ComponentBaseViewModel>[
      AggregationEmptyViewModel(
          emptyTitle: AggregationDeviceConstant.aggNoLightDevice)
    ];
  }

  @override
  String getRoomIcon(bool isRoomOn) {
    return isRoomOn
        ? 'assets/images/icon_aggregation_light_open.webp'
        : 'assets/images/icon_aggregation_light_close.webp';
  }

  @override
  void emitPowerCommand(bool powerOn) {
    final Map<String, List<Command>> batchCommands = <String, List<Command>>{};
    final List<String> devices = <String>[];

    devicesByRoom.forEach((SmartHomeRoomInfo key,
        AggregationDevicesByRoomViewModel roomViewModel) {
      devices.addAll(roomViewModel.deviceList);
    });
    updateLightPowerCommand(batchCommands, devices, powerOn);
    aggregationBatchPowerCommandEngine(batchCommands);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationLightViewModel &&
          runtimeType == other.runtimeType;

  @override
  int get hashCode => super.hashCode;
}
