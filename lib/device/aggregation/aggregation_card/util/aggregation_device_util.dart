import 'package:plugin_device/model/common_models.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';

import '../../../../common/constant.dart';
import '../../../../store/smart_home_store.dart';
import '../../../device_info_model/smart_home_device.dart';
import '../../../device_info_model/smart_home_device_attribute.dart';
import '../../../device_info_model/smart_home_device_state.dart';
import '../../../device_view_model/device_card_view_model.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';

///在线1,不在线0
const int onLineCode = 1;

///Power on 2
const int powerOnCode = 2;

bool isDeviceOffLine(SmartHomeDevice? device) {
  if (device != null) {
    return device.onlineState == SmartHomeDeviceOnlineState.offline;
  }
  return true;
}

bool isClose(SmartHomeDevice device) {
  final int value = _getValue(device);
  if (value & onLineCode > 0 && value & powerOnCode == 0) {
    return true;
  }
  return false;
}

int _getValue(SmartHomeDevice device) {
  ///在线1,不在线0
  final int onlineValue = !isDeviceOffLine(device) ? onLineCode : 0;

  ///Power on 2
  final int powerStatus = isPowerOn(device) ? powerOnCode : 0;
  final int value = onlineValue | powerStatus;
  return value;
}

bool isOpen(SmartHomeDevice device) {
  // 是窗帘
  final bool isDeviceCurtainAgg =
      isDeviceCurtainAggregation(device.basicInfo.aggregationParentId);
  if (isDeviceCurtainAgg) {
    return _isCurtainOpen(device);
  }
  return isPowerOn(device);
}

// 判断智能开关/大脑屏是否开启
bool isSwitchOpen(DeviceCardViewModel model) {
  // 查询智能开关/大脑屏的附件设备列表
  // 判断附件设备中是否有开启的设备
  // 有一个是开的，说明开关为开
  bool _isSwitchOpen = false;
  final Map<String, DeviceCardViewModel>? _childrenMap =
      model.childViewModelMap;
  if (_childrenMap is Map<String, DeviceCardViewModel>) {
    final int _childrenCount = _childrenMap.length;
    for (int i = 0; i < _childrenCount; i++) {
      final DeviceCardViewModel? model =
          _childrenMap[_childrenMap.keys.toList()[i]];
      if (model is DeviceCardViewModel) {
        final bool _isDeviceOpen = isOpen(model.device);
        if (_isDeviceOpen) {
          _isSwitchOpen = true;
          break;
        }
      }
    }
  }
  return _isSwitchOpen;
}

// 判断智能开关/大脑屏是否开启
bool isSwitchClose(DeviceCardViewModel model) {
  // 查询智能开关/大脑屏的附件设备列表
  // 判断附件设备是否全部为关
  // 全部是关的，说明开关为关
  // 有一个不是关的，说明开关非关
  bool _isSwitchClose = true;
  final Map<String, DeviceCardViewModel>? _childrenMap =
      model.childViewModelMap;
  if (_childrenMap is Map<String, DeviceCardViewModel>) {
    final int _childrenCount = _childrenMap.length;
    for (int i = 0; i < _childrenCount; i++) {
      final DeviceCardViewModel? model =
          _childrenMap[_childrenMap.keys.toList()[i]];
      if (model is DeviceCardViewModel) {
        final bool _isDeviceClose = isClose(model.device);
        if (!_isDeviceClose) {
          _isSwitchClose = false;
          break;
        }
      }
    }
  }
  return _isSwitchClose;
}

bool isCurtainPowOff(String deviceId) {
  final DeviceCardViewModel? model = smartHomeStore
      .state.deviceState.allCardViewModelMap[deviceId] as DeviceCardViewModel?;
  if (model is DeviceCardViewModel) {
    final SmartHomeDevice device = model.device;
    final int value = _getValue(device);
    if (CurtainLimit.spec_curtain_power_state == device.basicInfo.typeId) {
      return value & powerOnCode < 0;
    }
    final int openGegree = _getOpenGegree(device);
    return openGegree < 100 || (value & powerOnCode < 0);
  }
  return true;
}

bool isDevicePowerOn(String deviceId) {
  final DeviceCardViewModel? model = smartHomeStore
      .state.deviceState.allCardViewModelMap[deviceId] as DeviceCardViewModel?;
  if (model is DeviceCardViewModel) {
    final SmartHomeDevice device = model.device;
    final int value = _getValue(device);
    return value & powerOnCode > 0;
  }
  return false;
}

/// 灯光批量下发命令组装
void updateLightPowerCommand(Map<String, List<Command>> batchCommands,
    List<String> deviceList, bool powerOn) {
  deviceList.forEach((String devId) {
    final DeviceCardViewModel? model = smartHomeStore
        .state.deviceState.allCardViewModelMap[devId] as DeviceCardViewModel?;
    final bool isDeviceOffline = model?.deviceOffline ?? false;
    if (model != null && !isDeviceOffline) {
      final List<Command> _cmdList = _getLightCommandList(model, powerOn);
      if (_cmdList.isNotEmpty) {
        batchCommands[devId] = _cmdList;
      }
      // 将附件设备的命令添加到批量下发命令的map里面
      final Map<String, List<Command>> _childrenCommands =
          _getLightSwitchBatchCommand(model, powerOn);
      if (_childrenCommands.isNotEmpty) {
        batchCommands.addAll(_childrenCommands);
      }
    }
  });
}

// 获取灯光设备命令下发列表
List<Command> _getLightCommandList(DeviceCardViewModel model, bool powerOn) {
  if (model.powerOn == !powerOn) {
    final Map<String, String> cmdMap = <String, String>{
      SmartHomeConstant.deviceAttrName: SmartHomeConstant.deviceOnOffStatus
    };
    cmdMap[SmartHomeConstant.deviceAttrValue] = powerOn
        ? SmartHomeConstant.deviceAttrTrue
        : SmartHomeConstant.deviceAttrFalse;
    return <Command>[Command.fromMap(cmdMap)];
  }
  return <Command>[];
}

// 获取聚合灯中的智能开关/大脑屏的附件设备的批量下发命令map
Map<String, List<Command>> _getLightSwitchBatchCommand(
    DeviceCardViewModel? model, bool powerOn) {
  final Map<String, List<Command>> batchCommands = <String, List<Command>>{};
  if (model != null) {
    if (isDeviceSmartSwitchOrScreen(model)) {
      // 需要将附件设备一起添加到下发命令中
      final Map<String, DeviceCardViewModel>? _childrenMap =
          model.childViewModelMap;
      if (_childrenMap is Map<String, DeviceCardViewModel>) {
        _childrenMap
            .forEach((String childDevId, DeviceCardViewModel childModel) {
          // 判断附件设备是否应该加到批量下发命令的map里面
          final bool isChildOffline = childModel.deviceOffline;
          if (!isChildOffline) {
            /// 处理附件设备下发命令
            final List<Command> _cmdList =
                _getLightCommandList(childModel, powerOn);
            if (_cmdList.isNotEmpty) {
              batchCommands[childDevId] = _cmdList;
            }
          }
        });
      }
    }
  }
  return batchCommands;
}

// 获取聚合窗帘中的智能开关/大脑屏的附件设备的批量下发命令map
Map<String, List<Command>> _getCurtainSwitchBatchCommand(
    DeviceCardViewModel? model, bool powerOn) {
  final Map<String, List<Command>> batchCommands = <String, List<Command>>{};
  if (model != null) {
    if (isDeviceSmartSwitchOrScreen(model)) {
      // 需要将附件设备一起添加到下发命令中
      final Map<String, DeviceCardViewModel>? _childrenMap =
          model.childViewModelMap;
      if (_childrenMap is Map<String, DeviceCardViewModel>) {
        _childrenMap
            .forEach((String childDevId, DeviceCardViewModel childModel) {
          // 判断附件设备是否应该加到批量下发命令的map里面
          final bool isChildOffline = childModel.deviceOffline;
          if (!isChildOffline) {
            final List<Command> _cmdList =
                _getCurtainCommandList(childDevId, childModel, powerOn);
            if (_cmdList.isNotEmpty) {
              ///单控删除,用批控替换
              batchCommands[childDevId] = _cmdList;
            }
          }
        });
      }
    }
  }
  return batchCommands;
}

// 获取窗帘设备命令下发列表
List<Command> _getCurtainCommandList(
    String devId, DeviceCardViewModel model, bool powerOn) {
  ///判断状态是否相同
  /// 当前设备是非打开状态   开合度部位100 , 和powerOff
  final bool deviceIsPowerOff = isCurtainPowOff(devId);
  bool deviceIsPowerOn = false;
  if (!deviceIsPowerOff) {
    deviceIsPowerOn = isDevicePowerOn(devId);
  }
  bool powerStateEq = false;
  if (powerOn) {
    if (deviceIsPowerOn) {
      powerStateEq = true;
    }
  } else {
    if (deviceIsPowerOff) {
      powerStateEq = true;
    }
  }

  if (!powerStateEq) {
    final Map<String, String> cmdMap = <String, String>{};
    final String curtainTypeId = model.device.basicInfo.typeId;
    if (curtainTypeId == CurtainLimit.spec_curtain_power_state) {
      cmdMap[SmartHomeConstant.deviceAttrName] = CurtainLimit.keyCurtainStatus;
      cmdMap[SmartHomeConstant.deviceAttrValue] = powerOn
          ? CurtainLimit.valueCurtainOnOffStatus1
          : CurtainLimit.valueCurtainOnOffStatus2;
    } else {
      cmdMap[SmartHomeConstant.deviceAttrName] =
          SmartHomeConstant.deviceOnOffStatus;
      cmdMap[SmartHomeConstant.deviceAttrValue] = powerOn
          ? SmartHomeConstant.deviceAttrTrue
          : SmartHomeConstant.deviceAttrFalse;
    }

    ///单控删除,用批控替换
    return <Command>[Command.fromMap(cmdMap)];
  }
  return <Command>[];
}

/// 窗帘批量下发命令组装
void updateCurtainPowerCommand(Map<String, List<Command>> batchCommands,
    List<String> deviceList, bool powerOn) {
  deviceList.forEach((String devId) {
    final DeviceCardViewModel? model = smartHomeStore
        .state.deviceState.allCardViewModelMap[devId] as DeviceCardViewModel?;
    final bool isDeviceOffline = model?.deviceOffline ?? false;
    if (model != null && !isDeviceOffline) {
      final List<Command> _cmdList =
          _getCurtainCommandList(devId, model, powerOn);
      if (_cmdList.isNotEmpty) {
        ///单控删除,用批控替换
        batchCommands[devId] = _cmdList;
      }
      // 将附件设备的命令添加到批量下发命令的map里面
      final Map<String, List<Command>> _childrenCommands =
          _getCurtainSwitchBatchCommand(model, powerOn);
      if (_childrenCommands.isNotEmpty) {
        batchCommands.addAll(_childrenCommands);
      }
    }
  });
}

bool _isCurtainOpen(SmartHomeDevice device) {
  if (!isDeviceOffLine(device)) {
    final int value = _getValue(device);
    if (CurtainLimit.spec_curtain_power_state == device.basicInfo.typeId) {
      return value & powerOnCode > 0;
    }
    final int openGegree = _getOpenGegree(device);
    if (openGegree >= 100 && value & powerOnCode > 0) {
      return true;
    }
  }
  return false;
}

int _getOpenGegree(SmartHomeDevice device) {
  final String typeId = device.basicInfo.typeId;

  if (CurtainLimit.spec_curtain == typeId ||
      CurtainLimit.spec_curtain_2 == typeId) {
    if (device.attributeMap[SmartHomeConstant.deviceOnOffStatus]?.value ==
        SmartHomeConstant.deviceAttrTrue) {
      return 100;
    } else {
      return 0;
    }
  } else {
    const String kOpenDegree = CurtainLimit.openGegree;
    final String? data = device.attributeMap[kOpenDegree]?.value;
    if (data != null) {
      return int.tryParse(data) ?? 0;
    }
  }
  return 0;
}

/// 获取窗帘的开关机状态
/// 0 离线, 1 在线 2 PowerOn
int _getCurtainPowerStatus(SmartHomeDevice device) {
  if (device == null) {
    return 0;
  }
  if (CurtainLimit.spec_curtain_power_state == device.basicInfo.typeId) {
    ///curtainStatus 1-开；2-关；3-暂停
    const String keyCurtainOnOffStatus = CurtainLimit.keyCurtainStatus;
    const String valueCurtainOnOffStatus1 =
        CurtainLimit.valueCurtainOnOffStatus1;
    const String valueCurtainOnOffStatus3 =
        CurtainLimit.valueCurtainOnOffStatus3;
    final String? data = device.attributeMap[keyCurtainOnOffStatus]?.value;
    if (data != null) {
      if (valueCurtainOnOffStatus1 == data ||
          valueCurtainOnOffStatus3 == data) {
        return 2;
      }
    }
  } else {
    return _getPowerSwitcherStatus(device);
  }
  return 0;
}

bool isPowerOn(SmartHomeDevice device) {
  // 是窗帘
  final bool isDeviceCurtainAgg =
      isDeviceCurtainAggregation(device.basicInfo.aggregationParentId);
  if (isDeviceCurtainAgg) {
    return _getCurtainPowerStatus(device) == 2;
  }
  if (device != null) {
    return _getPowerSwitcherStatus(device) == 2;
  }
  return false;
}

int _getPowerSwitcherStatus(SmartHomeDevice _device) {
  if (_device.onlineState == SmartHomeDeviceOnlineState.offline) {
    return 0;
  }
  const String KEY_ON_OFF_STATUS = SmartHomeConstant.deviceOnOffStatus;
  final SmartHomeDeviceAttribute? writableAttribute =
      getCardAttributeByKey(_device.attributeMap, KEY_ON_OFF_STATUS);
  if (writableAttribute == null) {
    return 0;
  }
  if (writableAttribute.value == SmartHomeConstant.deviceAttrTrue &&
      writableAttribute.writable) {
    return 2;
  }
  return 0;
}

/// 获取卡片属性
SmartHomeDeviceAttribute? getCardAttributeByKey(
    Map<String, SmartHomeDeviceAttribute> attrs, String attrKey) {
  if (attrs.isEmpty) {
    return null;
  }
  return attrs[attrKey];
}

class CurtainLimit {
  ///不要乱改顺序
  /// 这个typeId会特殊取开合度
  static const String spec_curtain =
      '201c120024000810140200000000000000000000000000000000000000000040';

  static const String spec_curtain_2 =
      '201c80c70c50031c14132acaedd2bf00000083ff386b5bd068bb062a8b69d440';

  ///这个typeId会特殊取PowerState
  static const String spec_curtain_power_state =
      '201c80c70c50031c1413c001c47138000000ec24bf805b4531c836bfe2d0b740';
  static final List<String> curtainLimitList = <String>[spec_curtain, '14#13'];

  static const String keyCurtainStatus = 'curtainStatus';
  static const String valueCurtainOnOffStatus1 = '1';
  static const String valueCurtainOnOffStatus2 = '2';
  static const String valueCurtainOnOffStatus3 = '3';
  static const String openGegree = 'openDegree';
}

bool isEnvAgg(String deviceId) {
  return deviceId.contains(AggregationSettingConstant.env_id);
}

bool isNonNetAgg(String deviceId) {
  return deviceId.contains(AggregationSettingConstant.non_net_id);
}

bool isCameraAgg(String deviceId) {
  return deviceId.contains(AggregationSettingConstant.camera_id);
}

// 判断是否是长期离线聚合卡片
bool isOfflineAgg(String deviceId) {
  return deviceId.contains(AggregationSettingConstant.offline_id);
}

bool isNonNetDevice(SmartHomeDevice device) {
  return device.basicInfo.netType == 'nonNetDevice';
}

bool isBleDevice(SmartHomeDevice device) {
  final String modeType = device.basicInfo.communicationMode.toLowerCase();
  return modeType == 'ble' ||
      modeType == 'blemesh' ||
      modeType == 'blebroadcast';
}

bool isLongOfflineDevice(SmartHomeDevice device) {
  if (isNonNetDevice(device) ||
      isBleDevice(device) ||
      device.basicInfo.noKeepAlive == 1 ||
      device.onlineState != SmartHomeDeviceOnlineState.offline) {
    return false;
  }
  return device.offlineDays > 30 || device.offlineDays == -1;
}

// 判断是否是长期离线聚合设备
bool isOfflineAggDevice(String deviceId) {
  final String aggOfflineDeviceId =
      '${AggregationSettingConstant.offline_id}${smartHomeStore.state.familyState.familyId}';
  final Set<String> offlineDeviceSet = smartHomeStore
          .state.aggregationState.aggDeviceIdSetMap[aggOfflineDeviceId] ??
      <String>{};
  return offlineDeviceSet.contains(deviceId);
}

/// 是否是已聚合卡片【全量】
/// id 传 deviceId时判断是否是聚合卡片
bool isDeviceAggregation(String id) {
  return isDeviceLightAggregation(id) ||
      isDeviceCurtainAggregation(id) ||
      isEnvAgg(id) ||
      isNonNetAgg(id) ||
      isCameraAgg(id) ||
      isOfflineAgg(id);
}
