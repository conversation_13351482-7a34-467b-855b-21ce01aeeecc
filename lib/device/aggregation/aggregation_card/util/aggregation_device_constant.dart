class AggregationDeviceConstant {
  static const String aggregationBtnAllOpen = '全开';
  static const String aggregationBtnAllClose = '全关';

  // 灯光聚合
  static const String aggregationLightTitle = '灯光';
  static const String aggregationLightSubTitleAllOpen = '灯全开着';
  static const String aggregationLightSubTitleAllClose = '灯全关着';
  static const String aggregationLightSubTitlePartOpen = '个房间灯亮';
  static const String aggregationLightSubTitleAllOffline = '灯全关着'; //离线认为是关闭
  static const String aggregationLightSubTitleNoLightDevice = '暂无灯光设备';
  // 窗帘聚合
  static const String aggregationCurtainTitle = '窗帘';
  static const String aggregationCurtainSubTitleAllOpen = '窗帘全开着';
  static const String aggregationCurtainSubTitleAllClose = '窗帘全闭合';
  static const String aggregationCurtainSubTitlePartOpen = '个房间窗帘开着';
  static const String aggregationCurtainSubTitleAllOffline = '窗帘全闭合'; //离线认为是关闭
  static const String aggregationCurtainSubTitleNoLightDevice = '暂无窗帘设备';

  // 环境聚合卡片常量
  static const String aggEnvDeviceName = '环境';
  static const String aggEnvFuncKey = '环境聚合';
  static const String aggEnvDeviceIcon =
      'assets/images/icon_aggregation_env.webp';
  static const String aggNoEnvDevice = '暂无环境设备';
  static const String aggNoEnvDeviceRunning = '暂无设备运行';
  static const String aggNoEnvInfo = '暂无环境信息';
  static const String aggEnvDevicePartOpen = '个设备运行';
  static const String aggEnvLabelDescTemperature = '温度';
  static const String aggEnvLabelDescHumidity = '湿度';
  static const String aggEnvLabelDescPm25 = 'PM2.5';
  static const String aggEnvUnitPm25 = 'μg/m³';
  static const String aggEnvUnitTemperature = '°C';
  static const String aggEnvUnitHumidity = '%';
  static const String aggEnvDefaultValue = '--';

  // 聚合灯光和聚合窗帘卡片无设备文案优化
  static const String aggNoLightDevice = '暂无灯光设备';
  static const String aggNoCurtainDevice = '暂无窗帘设备';

  // 不支持联网设备聚合
  static const String aggNonNetTitle = '不可连网设备';
  // 不支持联网的家电设备
  static const String aggNonNetTitleSub = '不支持连接网络的家电设备';
  // 长时间离线设备聚合
  static const String aggOfflineTitle = '长期离线设备';

  // 聚合卡片点位
  static const String cardClickGio = 'MB38635';

  // 聚合埋点value属性值
  static const String gioWholeHouseOpen = '全屋全开';
  static const String gioWholeHouseClose = '全屋全关';
  static const String gioRoomOpen = '房间全开';
  static const String gioRoomClose = '房间全关';
  static const String gioCameraCard = '摄像头卡片';
  static const String gioCameraMiddleSize = '摄像头切换中视图';
  static const String gioCameraBigSize = '摄像头切换大视图';
  static const String gioCameraSort = '摄像头排序';
  static const String gioWholeHouseEnvInfo = '全屋环境信息';
  static const String gioRoomEnvInfo = '空间环境信息';
  static const String gioCameraTitle = '摄像头';
}
