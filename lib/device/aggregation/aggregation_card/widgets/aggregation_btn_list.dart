import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_btns_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_flex_switch_text_view_model.dart';

import 'aggregation_flex_switch_text.dart';

class AggregationBtnList extends StatelessWidget {
  const AggregationBtnList({super.key, required this.vm});

  final AggregationBtnViewModel vm;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, right: 8),
      child: Sized<PERSON><PERSON>(
        width: 122,
        height: 32,
        child: Row(
          children: <Widget>[
            AggregationFlexSwitchTextWidget(
                viewModel: AggregationFlexSwitchTextViewModel(
                    text: '全开',
                    enable: !vm.allOffline,
                    isOn: vm.allOpen,
                    clickCallback: vm.openClickCallback)),
            const SizedBox(
              width: 10,
            ),
            AggregationFlexSwitchTextWidget(
                viewModel: AggregationFlexSwitchTextViewModel(
                    text: '全关',
                    enable: !vm.allOffline,
                    isOn: vm.allClose,
                    clickCallback: vm.closeClickCallback)),
          ],
        ),
      ),
    );
  }
}
