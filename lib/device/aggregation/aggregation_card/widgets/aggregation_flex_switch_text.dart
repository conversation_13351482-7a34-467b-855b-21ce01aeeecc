import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../../common/smart_home_text_widget.dart';
import '../../../component/widget/click_effect_circular_widget.dart';
import '../../../component/widget/debounce_throttler/throttler_widget.dart';
import '../view_model/aggregation_flex_switch_text_view_model.dart';

class AggregationFlexSwitchTextWidget extends StatelessWidget {
  const AggregationFlexSwitchTextWidget({super.key, required this.viewModel});

  final AggregationFlexSwitchTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SizedBox(
        width: 56,
        height: 32,
        child: ThrottlerWidget(
          throttlerCallback: (BuildContext context) {
            viewModel.clickCallback?.call(context);
          },
          child: Opacity(
            opacity: viewModel.enable ? 1.0 : 0.39,
            child: ClickEffectCircularWidget(
              height: 32,
              enable: viewModel.enable,
              isOn: viewModel.isOn,
              borderRadius: 8,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (viewModel.text.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: SmartHomeText(
                        text: viewModel.text,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: viewModel.isOn
                            ? AppSemanticColors.item.information.primary
                            : AppSemanticColors.item.primary,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
