import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../../common/smart_home_text_widget.dart';
import '../view_model/aggregation_empty_view_model.dart';

class AggregationEmptyView extends StatelessWidget {
  const AggregationEmptyView({super.key, required this.viewModel});
  final AggregationEmptyViewModel viewModel;
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SizedBox(
        width: double.infinity,
        height: 52,
        child: Stack(
          children: <Widget>[
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(left: 8),
                child: SmartHomeText(
                  text: viewModel.emptyTitle,
                  fontSize: 16,
                  color: AppSemanticColors.item.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Image.asset(
                  'assets/images/icon_aggregation_no_device.webp',
                  package: 'smart_home',
                  width: 32,
                  height: 32,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
