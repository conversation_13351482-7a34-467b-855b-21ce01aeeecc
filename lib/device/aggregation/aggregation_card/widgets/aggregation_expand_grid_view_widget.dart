import 'package:flutter/material.dart';

import '../../../component/view_model/expand_switch_icon_text_view_model.dart';
import '../../../component/widget/expand_switch_icon_text_widget.dart';
import '../view_model/aggregation_expand_grid_view_model.dart';

class AggregationExpandGridViewWidget extends StatelessWidget {
  const AggregationExpandGridViewWidget({super.key, required this.viewModel});

  final AggregationExpandGridViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: SizedBox(
        width: double.infinity,
        height: 52,
        child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: viewModel.roomList.length,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 1,
              mainAxisSpacing: 16,
              mainAxisExtent: 80,
            ),
            itemBuilder: (BuildContext context, int index) {
              return _item(
                  viewModel.roomList[index] as ExpandSwitchIconTextViewModel);
            }),
      ),
    );
  }

  Widget _item(ExpandSwitchIconTextViewModel viewModel) {
    return Row(
      children: <Widget>[
        ExpandSwitchIconTextWidget(
            viewModel: ExpandSwitchIconTextViewModel(
                clickCallback: viewModel.clickCallback,
                text: viewModel.text,
                isOn: viewModel.isOn,
                enable: viewModel.enable,
                icon: viewModel.icon))
      ],
    );
  }
}
