import 'package:smart_home/store/smart_home_store.dart';

import '../../../../../whole_house/device_env/env_device_state.dart';
import '../../../../../whole_house/device_env/services/env_devices_response_model.dart';
import '../../../../component/view_model/expand_label_view_model.dart';
import '../../../../component_view_model/component_view_model.dart';
import '../../../../device_view_model/device_card_view_model.dart';
import '../../util/aggregation_device_constant.dart';
import '../../view_model/aggregation_base_view_model.dart';
import '../../view_model/aggregation_devices_by_room_model.dart';
import '../../view_model/aggregation_empty_view_model.dart';

class AggEnvViewModel extends AggregationBaseViewModel {
  AggEnvViewModel({required super.device});

  /// 环境设备打开数量
  int? _envDeviceOpenCount;

  /// 温度
  String? _envTemperatureValue;

  /// 湿度
  String? _envHumidityValue;

  /// 空气质量PM2.5
  String? _envPm25Value;

  @override
  bool get isOperate => false;

  @override
  bool get isShowRoomList => false;

  /// 聚合环境卡片名称复写
  @override
  String get deviceName => AggregationDeviceConstant.aggEnvDeviceName;

  /// 聚合环境卡片icon复写
  @override
  String get deviceIcon => AggregationDeviceConstant.aggEnvDeviceIcon;

  @override
  String get managePageTitle => '环境设备管理';

  @override
  String get managePageSubTitle => '添加或删除在环境聚合页面展示的设备';

  @override
  String get manageDialogTitle => '添加环境设备';

  @override
  String get manageListEmpty => '暂无环境设备';

  /// 大卡片副标题复写
  @override
  String get unionLargeCardStatus {
    return getCardStatus();
  }

  /// 获取大卡副标题
  String getCardStatus() {
    // 没有设备显示“暂无环境设备”
    if (devicesByRoom.isEmpty) {
      return AggregationDeviceConstant.aggNoEnvDevice;
    }
    _envDeviceOpenCount = _getEnvDeviceOpenCount();
    // 运行设备数为0的时候显示“暂无设备运行”
    // 空气检测仪因为没有开关机属性，因此不会被计入运行设备数
    if (_envDeviceOpenCount == 0) {
      return AggregationDeviceConstant.aggNoEnvDeviceRunning;
    }
    return '$_envDeviceOpenCount${AggregationDeviceConstant.aggEnvDevicePartOpen}';
  }

  /// 获取环境设备打开数量
  int _getEnvDeviceOpenCount() {
    return devicesByRoom.values.fold(
        0,
        (int sum, AggregationDevicesByRoomViewModel roomModel) =>
            sum + roomModel.openCount);
  }

  /// 中卡片副标题复写
  @override
  String get unionMiddleCardStatus {
    return getMiddleCardStatus();
  }

  /// 小卡片副标题复写
  @override
  String get unionSmallCardStatus {
    return getMiddleCardStatus();
  }

  /// 获取中卡小卡副标题
  String getMiddleCardStatus() {
    updateWHEnvInfo();
    if (_shouldShowEmptyInfo()) {
      return AggregationDeviceConstant.aggNoEnvInfo;
    }
    final List<String> middleCardStatus = <String>[];
    if (_showTemperature) {
      middleCardStatus.add(
          '$_envTemperatureValue${AggregationDeviceConstant.aggEnvUnitTemperature}');
    }
    if (_showHumidity) {
      middleCardStatus.add(
          '$_envHumidityValue${AggregationDeviceConstant.aggEnvUnitHumidity}');
    }
    if (_showPm25) {
      middleCardStatus.add('$_envPm25Value');
    }
    if (middleCardStatus.isNotEmpty) {
      return middleCardStatus.join(' ');
    }
    return AggregationDeviceConstant.aggNoEnvInfo;
  }

  /// 功能区域复写
  @override
  Map<String, LargeDeviceCardFunctionSet> get unionLargeCardFuncMap {
    const String envKey = AggregationDeviceConstant.aggEnvFuncKey;
    updateWHEnvInfo();
    // 无设备或者环境信息均为空，返回"暂无环境信息"
    if (_shouldShowEmptyInfo()) {
      return <String, LargeDeviceCardFunctionSet>{
        envKey: LargeDeviceCardFunctionSet(
            name: envKey, componentViewModelList: getEmptyViewModel())
      };
    }
    return <String, LargeDeviceCardFunctionSet>{
      envKey: LargeDeviceCardFunctionSet(
        name: envKey,
        componentViewModelList: <ComponentBaseViewModel>[
          if (_showTemperature) _labelTemperature,
          if (_showHumidity) _labelHumidity,
          if (_showPm25) _labelPm25,
        ],
      )
    };
  }

  /// 判断环境信息是否展示
  bool _shouldShowEmptyInfo() {
    return devicesByRoom.isEmpty ||
        !(_showTemperature || _showHumidity || _showPm25);
  }

  /// 功能区空数据VM
  @override
  List<ComponentBaseViewModel> getEmptyViewModel() {
    return <ComponentBaseViewModel>[
      AggregationEmptyViewModel(
          emptyTitle: AggregationDeviceConstant.aggNoEnvInfo)
    ];
  }

  /// 更新全屋环境信息
  void updateWHEnvInfo() {
    final EnvDeviceState envDeviceState =
        smartHomeStore.state.wholeHouseState.envDeviceState;
    final SpaceEnvironmentModel? spaceModel =
        envDeviceState.getSpace(smartHomeStore.state.familyState.familyId);
    if (spaceModel is SpaceEnvironmentModel) {
      _envTemperatureValue = _getWholeHouseEnvInfoValue(
          devices: spaceModel.temperature, type: 'temperature');
      _envHumidityValue = _getWholeHouseEnvInfoValue(
          devices: spaceModel.humidity, type: 'humidity');
      _envPm25Value =
          _getWholeHouseEnvInfoValue(devices: spaceModel.pm25, type: 'pm25');
    }
  }

  String? _getWholeHouseEnvInfoValue(
      {required List<EnvDeviceItemModel> devices, required String type}) {
    // 过滤出已选择且在线的设备
    final List<EnvDeviceItemModel> filteredDevices = devices
        .where(
            (EnvDeviceItemModel device) => device.selected && device.isOnline)
        .toList();

    if (filteredDevices.isEmpty) {
      return null;
    }
    const Set<String> validTypes = <String>{'temperature', 'humidity', 'pm25'};
    final String normalizedType = type.toLowerCase();

    final String? tmpValue = validTypes.contains(normalizedType)
        ? filteredDevices.first.reportedValue
        : null;
    return tmpValue;
  }

  bool get _showTemperature => _canValueShow(_envTemperatureValue);

  bool get _showHumidity => _canValueShow(_envHumidityValue);

  bool get _showPm25 => _canValueShow(_envPm25Value);

  bool _canValueShow(String? value) {
    return value != null && value.isNotEmpty;
  }

  ComponentBaseViewModel get _labelTemperature {
    const String desc = AggregationDeviceConstant.aggEnvLabelDescTemperature;
    final String value = '$_envTemperatureValue';
    return ExpandLabelViewModel(
        value: value.isNotEmpty
            ? value
            : AggregationDeviceConstant.aggEnvDefaultValue,
        unit: value.isNotEmpty
            ? AggregationDeviceConstant.aggEnvUnitTemperature
            : '',
        desc: desc,
        enable: value.isNotEmpty);
  }

  ComponentBaseViewModel get _labelHumidity {
    const String desc = AggregationDeviceConstant.aggEnvLabelDescHumidity;
    final String value = '$_envHumidityValue';
    return ExpandLabelViewModel(
        value: value.isNotEmpty
            ? value
            : AggregationDeviceConstant.aggEnvDefaultValue,
        unit: value.isNotEmpty
            ? AggregationDeviceConstant.aggEnvUnitHumidity
            : '',
        desc: desc,
        enable: value.isNotEmpty);
  }

  ComponentBaseViewModel get _labelPm25 {
    const String desc = AggregationDeviceConstant.aggEnvLabelDescPm25;
    final String value = '$_envPm25Value';
    return ExpandLabelViewModel(
        value: value.isNotEmpty
            ? value
            : AggregationDeviceConstant.aggEnvDefaultValue,
        desc: desc,
        enable: value.isNotEmpty);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggEnvViewModel &&
          runtimeType == other.runtimeType &&
          _envDeviceOpenCount == other._envDeviceOpenCount &&
          _envTemperatureValue == other._envTemperatureValue &&
          _envHumidityValue == other._envHumidityValue &&
          _envPm25Value == other._envPm25Value;

  @override
  int get hashCode =>
      super.hashCode ^
      _envDeviceOpenCount.hashCode ^
      _envTemperatureValue.hashCode ^
      _envHumidityValue.hashCode ^
      _envPm25Value.hashCode;
}
