// 聚合开关接口请求model
// 接口文档详见：https://stp.haier.net/project/79/interface/api/228295
class AggregationSwitchRequestModel {
  String source = '';
  List<FamilyAgg> familyAgg;

  AggregationSwitchRequestModel({
    required this.source,
    required this.familyAgg,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['source'] = source;
    data['familyAgg'] = familyAgg.map((FamilyAgg v) => v.toJson()).toList();
    return data;
  }

  @override
  String toString() {
    return 'AggregationSwitchRequestModel{source: $source, familyAgg: $familyAgg}';
  }
}

class FamilyAgg {
  String familyId = '';
  String? lightAgg;
  String? curtainAgg;
  String? envAgg;
  String? cameraAgg;
  String? nonnetAgg;
  String? offlineAgg;
//使用新增属性修改当前class
  FamilyAgg({
    required this.familyId,
    this.lightAgg,
    this.curtainAgg,
    this.offlineAgg,
    this.envAgg,
    this.cameraAgg,
    this.nonnetAgg,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    if (lightAgg != null) {
      data['lightAgg'] = lightAgg;
    }
    if (curtainAgg != null) {
      data['curtainAgg'] = curtainAgg;
    }
    if (envAgg != null) {
      data['envAgg'] = envAgg;
    }
    if (offlineAgg != null) {
      data['offlineAgg'] = offlineAgg;
    }
    if (cameraAgg != null) {
      data['cameraAgg'] = cameraAgg;
    }
    if (nonnetAgg != null) {
      data['nonnetAgg'] = nonnetAgg;
    }
    return data;
  }

  @override
  String toString() {
    return 'FamilyAgg{familyId: $familyId, lightAgg: $lightAgg, curtainAgg: $curtainAgg, envAgg: $envAgg, cameraAgg: $cameraAgg, nonnetAgg: $nonnetAgg, offlineAgg: $offlineAgg}';
  }
}
