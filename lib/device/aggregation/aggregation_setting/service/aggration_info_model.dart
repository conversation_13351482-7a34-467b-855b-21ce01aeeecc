import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

/// 开关查询接口数据模型
class AggregationSwitchInfoServerModel {
  bool lightAggregationStatus = false;
  bool curtainAggregationStatus = false;

  AggregationSwitchInfoServerModel.fromJson(Map<dynamic, dynamic> json) {
    lightAggregationStatus =
        json.nullableStringValueForKey('lightAggregationStatus') == '1';
    curtainAggregationStatus =
        json.nullableStringValueForKey('curtainAggregationStatus') == '1';
  }
}

class AggregationSwitchResponseModel extends UhomeResponseModel {
  AggregationSwitchResponseModel.fromJson(super.data) : super.fromJson() {
    aggregationSwitchInfoServerModel =
        AggregationSwitchInfoServerModel.fromJson(super.retData);
  }

  AggregationSwitchInfoServerModel? aggregationSwitchInfoServerModel;
}
