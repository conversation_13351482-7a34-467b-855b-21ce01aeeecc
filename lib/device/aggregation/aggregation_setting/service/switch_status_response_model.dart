import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class SwitchStatusResponseModel extends UhomeResponseModel {
  SwitchStatusResponseModel.fromJson(super.data) : super.fromJson() {
    data = SwitchStatusData.fromJson(super.retData);
  }

  SwitchStatusData data = SwitchStatusData.fromJson(<dynamic, dynamic>{});

  @override
  String toString() {
    return 'SwitchStatusResponseModel{data: $data}';
  }
}

class SwitchStatusData {
  String lightAggStatus = ''; // 灯光聚合开关状态 0:关 1:开
  String curtainAggStatus = ''; // 窗帘聚合开关状态 0:关 1:开
  String envAggStatus = ''; // 环境聚合开关状态 0:关 1:开
  String offlineAggStatus = ''; // 长期离线聚合开关状态 0:关 1:开
  String nonnetAggStatus = ''; // 不可联网聚合开关状态 0:关 1:开
  String cameraAggStatus = ''; // 摄像头聚合开关状态 0:关 1:开

  SwitchStatusData.fromJson(Map<dynamic, dynamic> json) {
    lightAggStatus = json.stringValueForKey('lightAggStatus', '');
    curtainAggStatus = json.stringValueForKey('curtainAggStatus', '');
    envAggStatus = json.stringValueForKey('envAggStatus', '');
    offlineAggStatus = json.stringValueForKey('offlineAggStatus', '');
    nonnetAggStatus = json.stringValueForKey('nonnetAggStatus', '');
    cameraAggStatus = json.stringValueForKey('cameraAggStatus', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['lightAggStatus'] = lightAggStatus;
    data['curtainAggStatus'] = curtainAggStatus;
    data['envAggStatus'] = envAggStatus;
    data['offlineAggStatus'] = offlineAggStatus;
    data['nonnetAggStatus'] = nonnetAggStatus;
    data['cameraAggStatus'] = cameraAggStatus;
    return data;
  }

  @override
  String toString() {
    return 'SwitchStatusData{lightAggStatus: $lightAggStatus,'
        'curtainAggStatus: $curtainAggStatus}'
        'envAggStatus: $envAggStatus}'
        'offlineAggStatus: $offlineAggStatus}'
        'nonnetAggStatus: $nonnetAggStatus}'
        'cameraAggStatus: $cameraAggStatus}';
  }
}
