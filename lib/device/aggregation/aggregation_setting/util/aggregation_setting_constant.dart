class AggregationSettingConstant {
  static const String closeIcon = 'assets/images/edit/dialog_close.png';

  // 聚合设置页面
  static const String aggSettingSheetTitle = '房间编辑';
  static const String aggSettingTitle = '设备聚合';
  static const String aggSettingSubTitle = '开启要在首页展示的设备聚合';
  static const String aggLightingSettingName = '灯光聚合';
  static const String aggCurtainSettingName = '窗帘聚合';
  static const String aggEnvSettingName = '环境聚合';
  static const String aggCameraSettingName = '摄像头聚合';
  static const String aggOfflineSettingName = '长期离线设备聚合';
  static const String aggOfflineSettingSub = '离线超过30天为长期离线';
  static const String aggNonnetSettingName = '不可连网设备聚合';
  static const String aggNonnetSettingSub = '不支持连接网络的家电设备';

  static const String aggLightOpenDialogTitle = '打开灯光聚合';
  static const String aggCurtainOpenDialogTitle = '打开窗帘聚合';
  static const String aggEnvOpenDialogTitle = '打开环境聚合';
  static const String aggCameraOpenDialogTitle = '打开摄像头聚合';
  static const String aggOfflineOpenDialogTitle = '打开长期离线设备聚合';
  static const String aggNonNetOpenDialogTitle = '打开不可连网设备聚合';

  static const String aggLightCloseDialogTitle = '关闭灯光聚合';
  static const String aggCurtainCloseDialogTitle = '关闭窗帘聚合';
  static const String aggEnvCloseDialogTitle = '关闭环境聚合';
  static const String aggCameraCloseDialogTitle = '关闭摄像头聚合';
  static const String aggOfflineCloseDialogTitle = '关闭长期离线设备聚合';
  static const String aggNonNetCloseDialogTitle = '关闭不可连网设备聚合';

  static const String aggLightOpenDialogContent = '打开后，灯光设备将在首页合成一张卡片。';
  static const String aggCurtainOpenDialogContent = '打开后，窗帘设备将在首页合成一张卡片。';
  static const String aggEnvOpenDialogContent =
      '打开后，环境设备(空调、新风机、加湿器、除湿机、空气净化器等)将在首页合成一张卡片。';
  static const String aggCameraOpenDialogContent = '打开后，摄像头设备将在首页合成一张卡片。';
  static const String aggNonNetOpenDialogContent = '打开后，不支持连网设备将在首页合成一张卡片。';
  static const String aggOfflineOpenDialogContent = '打开后，长期离线设备将在首页合成一张卡片。';
  static const String aggLightCloseDialogContent =
      '关闭后，灯光聚合将不在首页展示，灯光设备可在全屋或房间查看使用。';
  static const String aggCurtainCloseDialogContent =
      '关闭后，窗帘聚合将不在首页展示，窗帘设备可在全屋或房间查看使用。';
// 在现有 aggLightCloseDialogContent 和 aggCurtainCloseDialogContent 下方添加：
  static const String aggEnvCloseDialogContent =
      '关闭后，环境聚合将不在首页展示，环境设备可在全屋或房间查看使用。';
  static const String aggCameraCloseDialogContent =
      '关闭后，摄像头聚合卡片将不在首页展示，摄像头设备可在全屋或房间查看使用。';
  static const String aggNonNetCloseDialogContent = '关闭后，不可连网设备聚合卡片将不在首页展示。';
  static const String aggOfflineCloseDialogContent = '关闭后，长期离线设备聚合卡片将不在首页展示。';

  static const String aggGuideSheetTitle = '同类聚合，批量操控';
  static const String aggGuideSheetSubTitle = '聚合同类设备，批量操控更方便';
  static const String aggGuideSheetTip = '请开启需要聚合的设备';
  static const String aggGuideSheetSubTip = '打开后，灯光设备将在首页合成一张卡片。';
  static const String aggCancel = '取消';
  static const String aggSure = '确定';
  static const String networkUnavailable = '网络不可用';

  // 聚合卡片id前缀
  static const String agg_light_id = 'lightAggId';
  static const String agg_curtain_id = 'curtainAggId';
  static const String env_id = 'envAggId';
  static const String non_net_id = 'nonnetAggId';
  static const String camera_id = 'cameraAggId';
  static const String offline_id = 'offlineAggId';

  // 聚合设置点位
  static const String settingClickGio = 'MB38626';

  static const String deviceSelectableSuffix = '个设备可选';
}
