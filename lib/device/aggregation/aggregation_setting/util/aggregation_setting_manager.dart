import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_util.dart';

import '../../agg_store/aggregation_device_reducer_support.dart';
import 'aggregation_setting_constant.dart';

/// 聚合编辑-工具类
class AggregationSettingManager {
  // 关闭聚合设置弹窗--标题
  static String getDialogTitleBySwitchAndId(String id, bool isOpen) {
    switch (id) {
      case AggregationSettingConstant.agg_light_id:
        return isOpen
            ? AggregationSettingConstant.aggLightOpenDialogTitle
            : AggregationSettingConstant.aggLightCloseDialogTitle;
      case AggregationSettingConstant.agg_curtain_id:
        return isOpen
            ? AggregationSettingConstant.aggCurtainOpenDialogTitle
            : AggregationSettingConstant.aggCurtainCloseDialogTitle;
      case AggregationSettingConstant.camera_id:
        return isOpen
            ? AggregationSettingConstant.aggCameraOpenDialogTitle
            : AggregationSettingConstant.aggCameraCloseDialogTitle;
      case AggregationSettingConstant.env_id:
        return isOpen
            ? AggregationSettingConstant.aggEnvOpenDialogTitle
            : AggregationSettingConstant.aggEnvCloseDialogTitle;
      case AggregationSettingConstant.offline_id:
        return isOpen
            ? AggregationSettingConstant.aggOfflineOpenDialogTitle
            : AggregationSettingConstant.aggOfflineCloseDialogTitle;
      case AggregationSettingConstant.non_net_id:
        return isOpen
            ? AggregationSettingConstant.aggNonNetOpenDialogTitle
            : AggregationSettingConstant.aggNonNetCloseDialogTitle;
      default:
        return '';
    }
  }

  // 关闭聚合设置弹窗--内容
  static String getDialogContentBySwitchAndId(String id, bool isOpen) {
    switch (id) {
      case AggregationSettingConstant.agg_light_id:
        return isOpen
            ? AggregationSettingConstant.aggLightOpenDialogContent
            : AggregationSettingConstant.aggLightCloseDialogContent;
      case AggregationSettingConstant.agg_curtain_id:
        return isOpen
            ? AggregationSettingConstant.aggCurtainOpenDialogContent
            : AggregationSettingConstant.aggCurtainCloseDialogContent;
      case AggregationSettingConstant.camera_id:
        return isOpen
            ? AggregationSettingConstant.aggCameraOpenDialogContent
            : AggregationSettingConstant.aggCameraCloseDialogContent;
      case AggregationSettingConstant.env_id:
        return isOpen
            ? AggregationSettingConstant.aggEnvOpenDialogContent
            : AggregationSettingConstant.aggEnvCloseDialogContent;
      case AggregationSettingConstant.offline_id:
        return isOpen
            ? AggregationSettingConstant.aggOfflineOpenDialogContent
            : AggregationSettingConstant.aggOfflineCloseDialogContent;
      case AggregationSettingConstant.non_net_id:
        return isOpen
            ? AggregationSettingConstant.aggNonNetOpenDialogContent
            : AggregationSettingConstant.aggNonNetCloseDialogContent;
      default:
        return '';
    }
  }

  static String getSourceNameById(String id) {
    if (isDeviceLightAggregation(id)) {
      return '灯光';
    } else if (isDeviceCurtainAggregation(id)) {
      return '窗帘';
    } else if (isOfflineAgg(id)) {
      return '长期离线';
    } else if (isNonNetAgg(id)) {
      return '不可连网';
    } else if (isCameraAgg(id)) {
      return '摄像头';
    } else if (isEnvAgg(id)) {
      return '环境';
    } else {
      return '';
    }
  }
}
