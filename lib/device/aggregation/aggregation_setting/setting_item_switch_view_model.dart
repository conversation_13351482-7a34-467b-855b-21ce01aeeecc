/// 房间编辑的switch设置项vm
class SettingItemSwitchViewModel {
  /// 名称
  final String name;

  /// 描述
  final String desc;

  /// 开关状态值，true 表示开启，false 表示关闭
  final bool value;

  /// 构造函数
  SettingItemSwitchViewModel({
    required this.name,
    required this.value,
    this.desc = '',
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SettingItemSwitchViewModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          desc == other.desc &&
          value == other.value;

  @override
  int get hashCode => name.hashCode ^ desc.hashCode ^ value.hashCode;

  @override
  String toString() {
    return 'SettingItemSwitchViewModel{name: $name, desc: $desc, value: $value}';
  }
}
