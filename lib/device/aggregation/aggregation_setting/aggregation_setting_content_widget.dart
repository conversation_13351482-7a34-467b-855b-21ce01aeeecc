import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant_gio_scene.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_manager.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/widgets/whole_house_preference_setting_widget.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../../common/constant.dart';
import '../../../common/constant_gio.dart';
import '../../../widget_common/card_text_style.dart';
import '../agg_store/aggregation_action.dart';
import 'aggregation_setting_sheet.dart';
import 'util/aggregation_setting_constant.dart';

class AggregationSettingContentWidget extends StatelessWidget {
  final AggregationSettingListViewModel vm;

  AggregationSettingContentWidget({super.key, required this.vm});

  final Dialogs _dialogs = Dialogs();

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
        child: CustomScrollView(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            slivers: <Widget>[
              _buildDivider(20),
              const SliverToBoxAdapter(
                  child: WholeHousePreferenceSettingWidget()),
              _buildManualControlEntrance(context),
              _buildDivider(20),
              ..._buildWhContent(vm.aggregationCardList)
            ]));
  }

  Widget _buildDivider(double distance) {
    return SliverToBoxAdapter(child: Container(height: distance));
  }

  Widget _buildManualControlEntrance(BuildContext context) {
    return SliverToBoxAdapter(
      child: GestureDetector(
        onTap: () {
          gioTrack(GioScene.gioSceneControlClick);
          final String sceneManagePage =
              '${SmartHomeConstant.sceneManagePage}?familyId=${vm.familyId}&roomId=${vm.roomId}';
          goToPageWithDebounce(sceneManagePage);
        },
        child: Container(
            height: 54,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: Row(
                children: <Widget>[
                  Text(
                    '手动控制',
                    style: TextStyle(
                        fontSize: 16,
                        fontFamilyFallback: fontFamilyFallback(),
                        color: AppSemanticColors.item.primary,
                        fontWeight: FontWeight.w500),
                  ),
                  Expanded(child: Container()),
                  Image.asset(
                    'assets/icons/arrow_right.webp',
                    width: 16,
                    height: 16,
                    package: SmartHomeConstant.package,
                  ),
                ],
              ),
            )),
      ),
    );
  }

  // 构建聚合设置列表
  List<Widget> _buildWhContent(
      List<AggregationSettingBaseViewModel> whListViewModel) {
    return whListViewModel.isNotEmpty
        ? <Widget>[
            SliverToBoxAdapter(
              child: Container(
                height: 40,
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(AggregationSettingConstant.aggSettingTitle,
                        style: TextStyle(
                            fontSize: 12,
                            fontFamilyFallback: fontFamilyFallback(),
                            color: AppSemanticColors.item.secondary,
                            fontWeight: FontWeight.w400)),
                    const Spacer(), // 占据中间空间
                    Text(
                      AggregationSettingConstant.aggSettingSubTitle,
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 12,
                        fontFamilyFallback: fontFamilyFallback(),
                        color: AppSemanticColors.item.secWeaken,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverList.builder(
              itemCount: whListViewModel.length,
              itemBuilder: (BuildContext context, int index) {
                return _buildAggregationCard(whListViewModel[index], context,
                    index, whListViewModel.length);
              },
            )
          ]
        : <Widget>[];
  }

  // 构建聚合设置Widget
  Widget _buildAggregationCard(AggregationSettingBaseViewModel vm,
      BuildContext context, int index, int listLen) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: _getBorderRadiusByIndex(index, listLen),
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: Row(
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    vm.name,
                    style: TextStyle(
                        fontSize: 16,
                        fontFamilyFallback: fontFamilyFallback(),
                        color: AppSemanticColors.item.primary,
                        fontWeight: FontWeight.w500),
                  ),
                  if (vm.subTitle.isNotEmpty) ...<Widget>[
                    const SizedBox(height: 4),
                    Text(
                      vm.subTitle,
                      style: TextStyle(
                          fontSize: 12,
                          fontFamilyFallback: fontFamilyFallback(),
                          color: AppSemanticColors.item.secondary,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ],
              ),
            ),
            CustomSwitch(
              value: vm.isSelected,
              activeColor: AppSemanticColors.item.information.primary,
              inactiveColor: Colors.grey.shade300,
              onChanged: (bool value) async {
                _handleSwitchChange(value, vm, context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleSwitchChange(
      bool value, AggregationSettingBaseViewModel vm, BuildContext context) {
    Network.isOnline().then((NetworkStatus networkStatus) {
      if (!networkStatus.isOnline) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return;
      }
      _dialogs.showDoubleBtnDialog(
        context: context,
        title:
            AggregationSettingManager.getDialogTitleBySwitchAndId(vm.id, value),
        content: AggregationSettingManager.getDialogContentBySwitchAndId(
            vm.id, value),
        confirmCallback: () {
          _handleConfirm(value, vm);
        },
      );
    });
  }

  void _handleConfirm(bool value, AggregationSettingBaseViewModel vm) {
    Future<void>.delayed(const Duration(milliseconds: 500), () {
      gioTrack(GioConst.aggSetting, <String, dynamic>{
        'sourceName': AggregationSettingManager.getSourceNameById(vm.id),
        'value': value ? '开' : '关'
      });
      Network.isOnline().then((NetworkStatus networkStatus) {
        if (!networkStatus.isOnline) {
          ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
          return;
        }
        smartHomeStore.dispatch(
            UpdateAggregationStateAction(id: vm.id, isSelected: value));
      });
    });
  }

  BorderRadius _getBorderRadiusByIndex(int index, int listLen) {
    if (index == 0) {
      return const BorderRadius.only(
          topLeft: Radius.circular(16), topRight: Radius.circular(16));
    } else if (index == listLen - 1) {
      return const BorderRadius.only(
          bottomLeft: Radius.circular(16), bottomRight: Radius.circular(16));
    }
    return BorderRadius.circular(0);
  }
}
