import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../../widget_common/card_text_style.dart';

/// 通用switch设置开关项组件
class SettingItemSwitchWidget extends StatelessWidget {
  /// 标题
  final String name;

  /// 描述
  final String desc;

  /// 开关状态
  final bool value;

  /// 开关状态变更回调
  final ValueChanged<bool> onChanged;

  const SettingItemSwitchWidget({
    super.key,
    required this.name,
    this.desc = '',
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 92,
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(name,
                      style: TextStyle(
                          fontSize: 16,
                          fontFamilyFallback: fontFamilyFallback(),
                          color: AppSemanticColors.item.primary,
                          fontWeight: FontWeight.w500)),
                  const SizedBox(height: 4),
                  Text(desc,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12,
                          fontFamilyFallback: fontFamilyFallback(),
                          color: const Color(0xFF666666),
                          fontWeight: FontWeight.w400)),
                ],
              ),
            ),
            const SizedBox(width: 12),
            CustomSwitch(
                value: value,
                activeColor: AppSemanticColors.item.information.primary,
                inactiveColor: Colors.grey.shade300,
                onChanged: onChanged)
          ],
        ));
  }
}
