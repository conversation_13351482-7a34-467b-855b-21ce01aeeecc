import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:device_utils/typeId_parse/type_id_parse.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_filter/device_filter_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/pack_gift/model/user_pack_model.dart';
import 'package:smart_home/smart_home/widget/unlogin/new_user_pack.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:function_toggle/function_toggle.dart';

// class FilterListDataModel {
//   List<String> floorList = <String>[];
//   List<String> roomList = <String>[];
//   List<String> deviceCategoryList = <String>[];
//
//   FilterListDataModel({
//     required this.floorList,
//     required this.roomList,
//     required this.deviceCategoryList,
//   });
//   @override
//   bool operator ==(Object other) =>
//       identical(this, other) ||
//       other is FilterListDataModel &&
//           runtimeType == other.runtimeType &&
//           listEquals(floorList, other.floorList) &&
//           listEquals(roomList, other.roomList) &&
//           listEquals(deviceCategoryList, other.deviceCategoryList);
//
//   @override
//   int get hashCode =>
//       listHashCode(floorList) ^
//       listHashCode(roomList) ^
//       listHashCode(deviceCategoryList);
// }
//
// FilterListDataModel handleDeviceFilterData(
//     FilterDeviceCategoryMapModel filterDeviceCategoryMapModel,
//     String tempSelectedDeviceCategory,
//     String tempSelectedFloor) {
//   final Set<String> floorList = <String>{};
//   final Set<String> roomList = <String>{};
//   final Set<String> deviceCategoryList = <String>{};
//
//   // 所有设备均要显示
//   deviceCategoryList.add(SmartHomeConstant.deviceFilterSelectAll);
//   deviceCategoryList.addAll(
//       filterDeviceCategoryMapModel.filterDeviceCategoryMapModel.keys.toList());
//
//   // 楼层根据选择的设备有变化，分为两种情况
//   // 1. 设备选择全部时  2.设备选择具体某一个时
//   floorList.add(SmartHomeConstant.deviceFilterSelectAll);
//   if (tempSelectedDeviceCategory == SmartHomeConstant.deviceFilterSelectAll) {
//     filterDeviceCategoryMapModel.filterDeviceCategoryMapModel.values
//         .forEach((FilterFloorMap element) {
//       floorList.addAll(element.filterFloorMap.keys.toList());
//     });
//   } else {
//     filterDeviceCategoryMapModel
//         .filterDeviceCategoryMapModel[tempSelectedDeviceCategory]
//         ?.filterFloorMap
//         .keys
//         .forEach((String element) {
//       floorList.add(element);
//     });
//   }
//
//   // 房间会根据选择的设备和楼层有变化
//   // 1. 设备选择全部，楼层选择全部
//   // 2. 设备选择全部，楼层选择具体的某一个
//   // 3. 设备选择具体的某一个，楼层选择全部
//   // 4. 设备选择具体的某一个，楼层选择具体的某一个
//   roomList.add(SmartHomeConstant.deviceFilterSelectAll);
//   if (tempSelectedDeviceCategory == SmartHomeConstant.deviceFilterSelectAll) {
//     if (tempSelectedFloor == SmartHomeConstant.deviceFilterSelectAll) {
//       filterDeviceCategoryMapModel.filterDeviceCategoryMapModel.values
//           .forEach((FilterFloorMap ele) {
//         ele.filterFloorMap.values.forEach((FilterRoomList element) {
//           roomList.addAll(element.filterRoomList.toList());
//         });
//       });
//     } else {
//       filterDeviceCategoryMapModel.filterDeviceCategoryMapModel.values
//           .forEach((FilterFloorMap ele) {
//         ele.filterFloorMap[tempSelectedFloor]?.filterRoomList
//             .forEach((String element) {
//           roomList.add(element);
//         });
//       });
//     }
//   } else {
//     if (tempSelectedFloor == SmartHomeConstant.deviceFilterSelectAll) {
//       filterDeviceCategoryMapModel
//           .filterDeviceCategoryMapModel[tempSelectedDeviceCategory]
//           ?.filterFloorMap
//           .values
//           .forEach((FilterRoomList element) {
//         roomList.addAll(element.filterRoomList.toList());
//       });
//     } else {
//       filterDeviceCategoryMapModel
//           .filterDeviceCategoryMapModel[tempSelectedDeviceCategory]
//           ?.filterFloorMap[tempSelectedFloor]
//           ?.filterRoomList
//           .forEach((String element) {
//         roomList.add(element);
//       });
//     }
//   }
//   return FilterListDataModel(
//     floorList: floorList.toList(),
//     roomList: roomList.toList(),
//     deviceCategoryList: deviceCategoryList.toList(),
//   );
// }

void addNewUserCardViewModelToAllCardViewModelMap(
    List<NewUserPackItem> itemList) {
  if (itemList.isNotEmpty) {
    smartHomeStore
            .state.deviceState.allCardViewModelMap[new_user_guide_card_id] =
        NewUserPackViewModel(
            bgImg: itemList[0].pictureUrl,
            title: itemList[0].title,
            subtitle: itemList[0].subtitle,
            detailsUrl: itemList[0].detailsUrl,
            cardSortId: new_user_guide_card_id);
    if (itemList.length > 1) {
      smartHomeStore.state.deviceState
              .allCardViewModelMap[new_user_gift_pack_card_id] =
          NewUserPackViewModel(
              bgImg: itemList[1].pictureUrl,
              title: itemList[1].title,
              subtitle: itemList[1].subtitle,
              detailsUrl: itemList[1].detailsUrl,
              cardSortId: new_user_gift_pack_card_id);
    }
  }
}

Future<QuickControlExcludeDevice> decodeDeviceExcludeJson() async {
  try {
    return await FunctionToggle.instance.getFunctiontoggleModel(
        'SmartDevice_QuickControlExclude', (Map<dynamic, dynamic> json) {
      DevLogger.info(
          tag: 'SmartDevice_QuickControlExclude',
          msg: 'decodeDeviceExcludeJson, json: $json');
      final QuickControlExcludeDevice exclude =
          QuickControlExcludeDevice.fromJson(json);
      return exclude;
    });
  } catch (e) {
    DevLogger.error(
        tag: 'SmartDevice_QuickControlExclude',
        msg: 'decodeDeviceExcludeJson exception: $e');
    return QuickControlExcludeDevice.fromJson(<String, dynamic>{});
  }
}

class QuickControlExcludeDevice extends FunctionToggleModel {
  String version = '';
  List<String> excludeDevice = <String>[];

  QuickControlExcludeDevice.fromJson(Map<dynamic, dynamic> json)
      : super.fromJson(json) {
    if (json['version'] is String) {
      version = json['version'] as String;
    }
    if (json['exclude'] is List<dynamic>) {
      final List<dynamic> list = json['exclude'] as List<dynamic>;
      for (final dynamic item in list) {
        if (item is String) {
          excludeDevice.add(item);
        }
      }
    }
  }

  @override
  String toString() {
    return 'QuickControlExcludeDevice{version: $version, excludeDevice: $excludeDevice }';
  }
}
