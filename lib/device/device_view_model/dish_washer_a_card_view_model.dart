import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class DishWasherACardViewModel extends DeviceCardViewModel {
  DishWasherACardViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  bool get runningMode {
    final String onOffStatus =
        getDeviceCardAttrValueByName(SmartHomeConstant.deviceOnOffStatus);
    final String cyclePhase = getDeviceCardAttrValueByName('cyclePhase');
    if (onOffStatus == SmartHomeConstant.deviceAttrTrue) {
      final int remainTime = getRemainTime();
      if (cyclePhase.isNotEmpty) {
        if (cyclePhase != '0' && cyclePhase != '11' && remainTime > 0) {
          return true;
        }
        if (cyclePhase == '12' && remainTime > 0) {
          return true;
        }
      }
    }

    return false;
  }

  int getRemainTime() {
    final String remainHH = getDeviceCardAttrValueByName('remainingTimeHH');
    final String remainMM = getDeviceCardAttrValueByName('remainingTimeMM');
    final int hour = int.tryParse(remainHH) ?? 0;
    final int minute = int.tryParse(remainMM) ?? 0;
    final int remainTime = hour * 60 + minute;
    return remainTime;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (device.basicInfo.typeId ==
        '201c10c7141202100a0111508061d5ff857dbe03a6db79cbf4246387b5108840') {
      const String key = 'washProgRead';
      final String value = device.attributeMap[key]?.value ?? '';

      final String attr = value.isEmpty ? '' : _assignWashModeRedValue(value);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    } else {
      const String key = 'washProg';
      final String value = device.attributeMap[key]?.value ?? '';
      final String attr = value.isEmpty ? '' : _assignWashModeValue(value);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    }
  }

  String _assignWashModeRedValue(String value) {
    switch (value) {
      case '0':
        return '待机';
      case '8':
        return '智能洗';
      case '3':
        return '强力洗';
      case '13':
        return '标准洗';
      case '18':
        return '速洗';
      case '9':
        return '经济洗';
      case '4':
        return '骨瓷洗';
      default:
        return '';
    }
  }

  String _assignWashModeValue(String value) {
    switch (value) {
      case '0':
        return '待机';
      case '18':
        return '速洗';
      case '15':
        return '童婴洗';
      case '14':
        return '果蔬洗';
      case '2':
        return '多餐';
      case '3':
        return '强力洗';
      case '4':
        return '骨瓷洗';
      case '5':
        return '冲洗';
      case '7':
        return '消毒洗';
      case '8':
        return '自动洗';
      case '9':
        return '节约洗';
      case '10':
        return '玻璃洗';
      case '12':
        return '混合洗';
      case '13':
        return '标准洗';

      case '16':
        return '海鲜洗';
      case '1':
        return '即时洗';
      case '21':
        return '节能洗';
      case '22':
        return '智能洗';
      case '23':
        return '密胺';
      case '24':
        return '蒸汽洗';
      case '25':
        return '除味洗';
      case '27':
        return '水果洗';
      case '28':
        return '浆果洗';
      case '29':
        return '核果洗';
      case '30':
        return '仁果洗';
      case '31':
        return '瓜果洗';
      case '32':
        return '蔬菜洗';
      case '33':
        return '根菜洗';
      case '34':
        return '叶菜洗';
      case '35':
        return '茎菜洗';
      case '36':
        return '果实洗';
      case '37':
        return '食用菌';
      case '38':
        return '甲壳类';
      case '39':
        return '贝类';
      case '40':
        return '螺类';
      default:
        return '';
    }
  }
}
