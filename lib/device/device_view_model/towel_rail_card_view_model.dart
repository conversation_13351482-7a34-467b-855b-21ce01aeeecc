import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class TowelRailCardViewModel extends DeviceCardViewModel {
  TowelRailCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String operationValue =
          device.attributeMap['operationMode']?.value ?? '';
      if (operationValue == '2') {
        return DeviceCardAttribute(label: '', value: '保温', unit: '');
      }

      if (operationValue == '3') {
        return DeviceCardAttribute(label: '', value: '加热', unit: '');
      }
    }
    return null;
  }
}
