import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CBandMotionSensorCardViewModel extends DeviceCardViewModel {
  CBandMotionSensorCardViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['securityControl']?.value ?? '';

    if (value == trueValue) {
      return DeviceCardAttribute(label: '', value: '布防', unit: '');
    }

    if (value == falseValue) {
      return DeviceCardAttribute(label: '', value: '撤防', unit: '');
    }

    return null;
  }
}
