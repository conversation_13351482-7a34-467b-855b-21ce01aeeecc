import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class PanelSensorCardViewModel extends DeviceCardViewModel {
  PanelSensorCardViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }
  
  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['airQuality']?.value ?? '';
    if (value == '') {
      return null;
    }
    final String attr = _getModel(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  String _getModel(String value) {
    final Map<String, String> map = <String, String>{};
    map['0'] = '优';
    map['1'] = '良';
    map['2'] = '中';
    map['3'] = '差';

    final String returnValue = map.stringValueForKey(value, '');

    return returnValue;
  }
}
