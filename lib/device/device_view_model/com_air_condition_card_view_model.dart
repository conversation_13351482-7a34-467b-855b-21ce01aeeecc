import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/air_condition_base_view_model.dart';

class ComAirConditionCardViewModel extends AirConditionBaseViewModel {
  ComAirConditionCardViewModel({required super.device});

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  String get smallCardStatus {
    if (powerOff) {
      return '关';
    }
    if (powerOn) {
      return '开';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      const String key = 'indoorTemperature';
      final String value = device.attributeMap[key]?.value ?? '';
      return value.isNotEmpty
          ? DeviceCardAttribute(label: '室温', value: value, unit: '°C')
          : null;
    }
    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (powerOn) {
      final String value = device.attributeMap['operationMode']?.value ?? '';
      final String attr = _getModel(value);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '模式', value: attr, unit: '')
          : null;
    }
    return null;
  }

  String _getModel(String value) {
    final Map<String, String> map = <String, String>{};
    map['0'] = '智能';
    map['1'] = '制冷';
    map['2'] = '除湿';
    map['3'] = '健康除湿';
    map['4'] = '制热';
    map['5'] = '节能';
    map['6'] = '送风';
    map['7'] = '地暖';
    map['8'] = '制热+地暖';

    final String returnValue = map.stringValueForKey(value, '');

    return returnValue;
  }
}
