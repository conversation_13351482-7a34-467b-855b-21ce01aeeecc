import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//窗帘
class WineCurtainCardViewModel extends DeviceCardViewModel {
  WineCurtainCardViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }
}
