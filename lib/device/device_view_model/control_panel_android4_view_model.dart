import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/control_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class ControlPanelAndroid4ViewModel extends ControlPanelViewModel {
  ControlPanelAndroid4ViewModel({required super.device});

  final String switchType1AttributeKey = 'switchType1';
  final String switchType2AttributeKey = 'switchType2';
  final String switchType3AttributeKey = 'switchType3';
  final String enabledStatusKey = 'enabledStatus';

  @override
  AccessoryDeviceType getAccessoryDeviceType(
      int index, int sortCode, DeviceCardViewModel viewmodel) {
    final List<String> typeKeys = <String>[
      switchType1AttributeKey,
      switchType2AttributeKey,
      switchType3AttributeKey
    ];
    if (index < 0 || index >= typeKeys.length) {
      return AccessoryDeviceType.unknown;
    }
    final SmartHomeDeviceAttribute? attr = getDeviceAttribute(typeKeys[index]);
    return mapValueToDeviceType(attr?.value);
  }

  @override
  bool isDeviceEnabled(int index, int sortCode, DeviceCardViewModel child,
      AccessoryDeviceType type) {
    if (type == AccessoryDeviceType.normalLight ||
        type == AccessoryDeviceType.smartLight) {
      final SmartHomeDeviceAttribute? enabled =
          child.getDeviceAttribute(enabledStatusKey);
      return enabled?.value == SmartHomeConstant.deviceAttrTrue;
    } else if (type == AccessoryDeviceType.unknown) {
      return false;
    }
    return true;
  }
}
