import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SmartDoorCardViewModel extends DeviceCardViewModel {
  SmartDoorCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = getDeviceCardAttrValueByName('battery').trim();

    if (value.isEmpty) {
      return null;
    }

    return DeviceCardAttribute(label: '电量', value: value, unit: '%');
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }
}
