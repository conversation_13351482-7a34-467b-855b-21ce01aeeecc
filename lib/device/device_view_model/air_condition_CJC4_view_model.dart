/*
 * 描述：空调型号：CJC-4
 * 作者：songFJ
 * 创建时间：2024/9/6
 */

import 'package:smart_home/device/device_view_model/air_condition_card_view_model.dart';

class AirConditionCJC4ViewModel extends AirConditionCardViewModel {
  AirConditionCJC4ViewModel({required super.device});

  @override
  Map<String, AirConditionModeModel> get allModeMap =>
      <String, AirConditionModeModel>{
        '0': AirConditionModeModel(
            value: '0',
            desc: '智能',
            icon: 'assets/components/mode/mode_intellect.webp'),
        '1': AirConditionModeModel(
            value: '1',
            desc: '制冷',
            icon: 'assets/components/mode/mode_cold.webp'),
        '2': AirConditionModeModel(
            value: '2',
            desc: '除湿',
            icon: 'assets/components/mode/mode_dry.webp'),
        '4': AirConditionModeModel(
            value: '4',
            desc: '制热',
            icon: 'assets/components/mode/mode_warm.webp'),
        '6': AirConditionModeModel(
            value: '6',
            desc: '送风',
            icon: 'assets/components/mode/mode_wind.webp'),
      };
}
