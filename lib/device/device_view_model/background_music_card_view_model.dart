import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

/// 背景音乐卡片的ViewModel
class BackgroundMusicCardViewModel extends DeviceCardViewModel {
  BackgroundMusicCardViewModel({required super.device});

  /// 开机且播放中
  @override
  bool get runningMode {
    if (powerOn) {
      const String key = 'playStatus';
      final String value = device.attributeMap[key]?.value ?? '';
      if (value == trueValue) {
        return true;
      }
    }

    return false;
  }
}
