import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 烤箱/蒸烤一体机
class BakeOvenNewViewModel extends DeviceCardViewModel {
  BakeOvenNewViewModel({required super.device});

  String get _onOffStatusPublishKey => 'onOffStatus@publish';

  String get _bakingStaKey => 'BakingSta';

  SmartHomeDeviceAttribute? get _onOffStatusPublishAttribute {
    return device.attributeMap[_onOffStatusPublishKey];
  }

  SmartHomeDeviceAttribute? get _bakingStaAttribute =>
      getDeviceAttribute(_bakingStaKey);

  @override
  bool get powerOff {
    return _onOffStatusPublishAttribute?.value == '1';
  }

  @override
  bool get powerOn {
    return _onOffStatusPublishAttribute?.value == '2' ||
        _onOffStatusPublishAttribute?.value == '3';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }

    if (powerOn) {
      final String backingSta = _bakingStaAttribute?.value ?? '';
      if (backingSta.isEmpty) {
        return null;
      }
      if (backingSta == '1' || backingSta == '6') {
        return DeviceCardAttribute(label: '', value: '待机', unit: '');
      } else {
        return DeviceCardAttribute(label: '', value: '工作中', unit: '');
      }
    }
    return null;
  }
}
