/*
 * 描述：新风机 2008610800820324240297adc1f58000000059ffd9f30d8999ff6cac48748640
 * 作者：songFJ
 * 创建时间：2025/3/12
 */

import 'package:smart_home/device/device_view_model/new_fan_base_view_model.dart';

import '../component_view_model/component_view_model.dart';

class NewFan48640ViewModel extends NewFanBaseViewModel {
  NewFan48640ViewModel({required super.device});

  @override
  bool get supportHumidity {
    if ((humidificationExistAttribute?.value ?? falseValue) == trueValue) {
      return true;
    }
    return false;
  }

  @override
  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      defaultModeComponent(),
      defaultWindSpeedComponent,
      if (supportHumidity) defaultHumidityComponent,
    ];
  }

  @override
  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      modeComponent(),
      windSpeedComponent,
      if (supportHumidity) humidityComponent,
    ];
  }
}
