import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//安防 门磁
class GateMagnetismViewModel extends DeviceCardViewModel {
  GateMagnetismViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String controlValue = getDeviceCardAttrValueByName('securityControl');
    if (controlValue == falseValue) {
      return DeviceCardAttribute(label: '', value: '撤防', unit: '');
    } else if (controlValue == trueValue) {
      return DeviceCardAttribute(label: '', value: '布防', unit: '');
    }

    return null;
  }
}
