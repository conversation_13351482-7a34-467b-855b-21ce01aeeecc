import 'lingyun/lingyun_constant.dart';
import 'lingyun/lingyun_model.dart';
import 'lingyun_switch_base_view_model.dart';

//凌云玻璃开关-一键开关
class LingYunSwitchOneKeyViewModel extends LingYunSwitchBaseViewModel {
  LingYunSwitchOneKeyViewModel({required super.device});

  @override
  List<LingYunSwitchModel> get switchModelList => <LingYunSwitchModel>[
        LingYunSwitchModel(
          switchName: '按键',
          switchTypeKey: LingYunSwitchTypeKey.switchType1Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus1Key,
        )
      ];
}
