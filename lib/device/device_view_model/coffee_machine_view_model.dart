import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 特殊咖啡机
class CoffeeMachineViewModel extends DeviceCardViewModel {
  CoffeeMachineViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String workStatusValue = getDeviceCardAttrValueByName('workStatus');

    String value = '';
    if (workStatusValue == '0') {
      value = '待机';
    } else if (workStatusValue == '1') {
      value = '工作中';
    } else if (workStatusValue == '2') {
      value = '开';
    }
    return DeviceCardAttribute(label: '', value: value, unit: '');
  }
}
