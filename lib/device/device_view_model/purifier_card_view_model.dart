import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component/view_model/flush_small_component_view_model.dart';
import 'package:smart_home/device/component/view_model/purified_grid_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/purified_consumables_model.dart';
import 'package:smart_home/device/resize_device_card/resize_base_model.dart';

import '../component/view_model/circular_percentage_view_model.dart';
import '../component/view_model/flush_large_component_view_model.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../purified_consumable_presenter.dart';

// 提取图标常量

/// 净水器卡片viewModel
class PurifierCardViewModel extends DeviceCardViewModel {
  PurifierCardViewModel({required super.device});

  String get _flushIcon => 'assets/components/flush.webp';

  /// 滤芯耗材列表viewModel
  PurifiedGridViewModel _purifiedGirdViewModel = PurifiedGridViewModel(
    purifiedList: <CircularPercentageModel>[],
  );

  String get _flushStatusKey => 'flushStatus';

  String get _purifiedWaterVolumeKey => 'purifiedWaterVolume';

  SmartHomeDeviceAttribute? get _flushStatusAttribute {
    return device.attributeMap[_flushStatusKey];
  }

  SmartHomeDeviceAttribute? get _purifiedWaterVolumeAttribute {
    return device.attributeMap[_purifiedWaterVolumeKey];
  }

  /// 运行状态：冲洗中：true；未冲洗：false
  bool get _flushStatus =>
      (_flushStatusAttribute?.value ?? '') == SmartHomeConstant.deviceAttrTrue;

  /// 用水量
  String get _waterVolume => _purifiedWaterVolumeAttribute?.value ?? '--';

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    /// 水质
    final String value = device.attributeMap['purifiedTDS']?.value ?? '';
    if (value.isNotEmpty) {
      return DeviceCardAttribute(label: '出水TDS ', value: value, unit: '');
    }

    /// 用水量
    return DeviceCardAttribute(label: '已用水量 ', value: _waterVolume, unit: 'L');
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '净水器';

    final List<ComponentBaseViewModel> componentViewModelList =
        <ComponentBaseViewModel>[];

    /// 滤芯耗材
    final ComponentBaseViewModel purifiedModel = _purifiedComponentViewModel();
    if (purifiedModel is PurifiedGridViewModel &&
        purifiedModel.purifiedList.isNotEmpty) {
      componentViewModelList.add(purifiedModel);
    }

    // 空数据展示3个固定的置灰滤芯
    if (purifiedModel is PurifiedGridViewModel &&
        componentViewModelList.isEmpty) {
      _addOfflineFilters(componentViewModelList);
    }
    if (componentViewModelList.isEmpty) {
      return null;
    }

    final LargeDeviceCardFunctionSet largeDeviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
      name: name,
      componentViewModelList: componentViewModelList,
    );
    return <String, LargeDeviceCardFunctionSet>{
      largeDeviceCardFunctionSet.name: largeDeviceCardFunctionSet
    };
  }

  void _addOfflineFilters(List<ComponentBaseViewModel> componentViewModelList) {
    for (int i = 0; i < 3; i++) {
      componentViewModelList
          .add(CircularPercentageModel(title: '--', enable: false));
    }
  }

  /// 滤芯耗材组件
  ComponentBaseViewModel _purifiedComponentViewModel() {
    /// 等级是否为空
    bool isLevelEmpty = false;

    /// 重置一下滤芯为空
    _purifiedGirdViewModel = PurifiedGridViewModel(
      purifiedList: <CircularPercentageModel>[],
    );

    final List<PurifiedConsumableModel> list =
        PurifiedConsumablePresenter.getInstance().purifiedConsumeMap[prodNo] ??
            <PurifiedConsumableModel>[];

    for (final PurifiedConsumableModel model in list) {
      if (model.level.isEmpty) {
        isLevelEmpty = true;
        break;
      }
    }

    list.forEach((PurifiedConsumableModel element) {
      final String remainAttributeKey = '${element.attributeKeyPrefix}Remained';
      final String selectAttributeKey = '${element.attributeKeyPrefix}Select';
      final String selectValue =
          getDeviceCardAttrValueByName(selectAttributeKey);
      String remainValue = getDeviceCardAttrValueByName(remainAttributeKey);
      if (remainValue.isEmpty || selectValue == falseValue || deviceOffline) {
        remainValue = '--';
      }

      _purifiedGirdViewModel.purifiedList.add(CircularPercentageModel(
          title: element.name,
          subTitle: isLevelEmpty ? '' : element.level,
          percentage: remainValue,
          sortId: int.tryParse(remainValue) ?? 0,
          enable: !(deviceOffline || onlineNotReady),
          clickCallback: (BuildContext? context) {
            gio(cloudProgramName: '查看滤芯详情');
            final String jumpUrl = element.detailsUrl + deviceId;
            cardClick(context, jumpUrl: jumpUrl);
          }));
    });

    /// 取前三个, 不够3个，取全部
    _purifiedGirdViewModel.purifiedList.length =
        _purifiedGirdViewModel.purifiedList.length > 3
            ? 3
            : _purifiedGirdViewModel.purifiedList.length;

    return _purifiedGirdViewModel;
  }

  /// 一键冲洗按钮
  @override
  ComponentBaseViewModel? get topRightComponentViewModel {
    /// 没有冲洗状态,不显示一键冲洗按钮
    if (_flushStatusAttribute == null) {
      return null;
    }
    final String desc = _flushStatus ? '正在冲洗' : '一键冲洗';
    final bool _writable = _flushStatusAttribute?.writable ?? false;
    switch (deviceCardType) {
      case DeviceCardType.largeCard:
        return _createFlushComponent(
            desc: desc,
            writable: _writable,
            isLarge: true);
      case DeviceCardType.middleCard:
        return _createFlushComponent(
            desc: desc,
            writable: _writable,
            isLarge: false);
      case DeviceCardType.smallCard:
        return null;
      default:
        return _createFlushComponent(
            desc: desc,
            writable: _writable,
            isLarge: false);
    }
  }

  /// 根据是否大卡片创建对应的冲洗组件 ViewModel
  ComponentBaseViewModel _createFlushComponent({
    required bool isLarge,
    required String desc,
    required bool writable,
  }) {
    return isLarge
        ? FlushLargeComponentViewModel(
            text: desc,
            icon: _flushIcon,
            isOn: _flushStatus,

            /// 冲洗中，有透明度遮罩 enable = false
            enable: !_flushStatus,
            clickCallback: (BuildContext? context) {
              _handleFlushClick(desc, writable);
            },
          )
        : FlushSmallComponentViewModel(
            icon: _flushIcon,
            isOn: _flushStatus,
            enable: !_flushStatus,
            clickCallback: (BuildContext? context) {
              _handleFlushClick(desc, writable);
            },
          );
  }

  /// 一键冲洗按钮点击事件
  void _handleFlushClick(String desc, bool _writable) {
    gio(cloudProgramName: desc);
    // 冲洗中，writeable 传 true, 否则会提示‘操作失败’
    checkDeviceState(writable: _flushStatus || _writable, checkChildLock: false)
        .then((bool pass) {
      if (pass) {
        /// 冲洗中，提示设备正在冲洗中
        if (_flushStatus) {
          return ToastHelper.showToast(SmartHomeConstant.toastFlushing);
        }

        /// 下发命令
        final Map<String, String> commands = <String, String>{
          _flushStatusKey: '${!_flushStatus}'
        };

        quickCtrlLECommand(device.basicInfo.deviceId, commands,
            onErrorCallback: (String errMsg) {
          ToastHelper.showToast(errMsg);
        });
      }
    });
  }
}

