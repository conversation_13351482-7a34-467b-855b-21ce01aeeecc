import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/control_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class ControlPanelLinux4ViewModel extends ControlPanelViewModel {
  ControlPanelLinux4ViewModel({required super.device});

  final String switchType1AttributeKey = 'switchType1';
  final String switchType2AttributeKey = 'switchType2';
  final String enabledStatusKey = 'enabledStatus';
  final String floorHeatingTypeId =
      '201c80c70c50031c25015ef02423b8000000c41fe72da8d01e7cd77c9582df40';

  @override
  AccessoryDeviceType getAccessoryDeviceType(
      int index, int sortCode, DeviceCardViewModel viewmodel) {
    final List<String> typeKeys = <String>[
      switchType1AttributeKey,
      switchType2AttributeKey,
    ];
    if (index < 0 || index >= typeKeys.length) {
      return AccessoryDeviceType.unknown;
    }
    if (viewmodel.device.basicInfo.typeId == floorHeatingTypeId) {
      return AccessoryDeviceType.floorHeating;
    }
    final SmartHomeDeviceAttribute? attr = getDeviceAttribute(typeKeys[index]);
    return mapValueToDeviceType(attr?.value);
  }

  @override
  bool isDeviceEnabled(int index, int sortCode, DeviceCardViewModel child,
      AccessoryDeviceType type) {
    final SmartHomeDeviceAttribute? enabled =
        child.getDeviceAttribute(enabledStatusKey);

    if (type == AccessoryDeviceType.normalLight) {
      if (index == 0) {
        return enabled?.value != SmartHomeConstant.deviceAttrFalse;
      }
      return enabled?.value == SmartHomeConstant.deviceAttrTrue;
    }
    if (type == AccessoryDeviceType.smartLight) {
      return enabled?.value == SmartHomeConstant.deviceAttrTrue;
    }
    if (type == AccessoryDeviceType.unknown) {
      return false;
    }
    return true;
  }
}
