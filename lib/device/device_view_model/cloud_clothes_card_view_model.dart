import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CloudClothesCardViewModel extends DeviceCardViewModel {
  CloudClothesCardViewModel({required super.device});

  static const String _moveStatusKey = 'moveStatus';
  static const String _positionKey = 'position';

  static const String _dryingMachineStatusPause = '0';
  static const String _dryingMachineStatusUp = '1';
  static const String _dryingMachineStatusDown = '2';

  static const String _dryingMachinePositionTop = '1';
  static const String _dryingMachinePositionMiddle = '2';
  static const String _dryingMachinePositionBottom = '3';

  static const String _dryingMachineToastPause = '晾衣机已停止';
  static const String _dryingMachineToastTop = '已上升至最高位';
  static const String _dryingMachineToastBottom = '已下降至最低位';

  String? get _moveStatusValue {
    return device.attributeMap[_moveStatusKey]?.value ?? '';
  }

  String? get _positionValue {
    return device.attributeMap[_positionKey]?.value ??
        _dryingMachinePositionMiddle;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '晾衣机';
    final LargeDeviceCardFunctionSet largeDeviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
            name: name,
            componentViewModelList: <ComponentBaseViewModel>[
          _buttonWith(ButtonType.up),
          _buttonWith(ButtonType.pause),
          _buttonWith(ButtonType.down),
        ]);
    return <String, LargeDeviceCardFunctionSet>{
      largeDeviceCardFunctionSet.name: largeDeviceCardFunctionSet
    };
  }

  ComponentBaseViewModel _buttonWith(ButtonType type) {
    final _ButtonIconModel btnViewModel = _btnViewModel(type);
    final bool compEnable = !(deviceOffline || alarm);

    final ComponentBaseViewModel buttonComponentViewModel =
        ExpandSwitchIconTextViewModel(
            text: btnViewModel.name,
            icon: btnViewModel.icon,
            isOn: false,
            enable: compEnable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '晾衣机-${btnViewModel.name}');
              checkDeviceState(writable: !alarm, checkPowerOff: false)
                  .then((bool pass) {
                if (pass) {
                  _sendCommand(type);
                }
              }).catchError((dynamic err) {
                DevLogger.error(
                    tag: SmartHomeConstant.package,
                    msg: 'CloudClothesCardViewModel checkDeviceState err:$err');
              });
            });

    return buttonComponentViewModel;
  }

  void _sendCommand(ButtonType type) {
    String cmdValue = '';
    if (type == ButtonType.up) {
      cmdValue = _dryingMachineStatusUp;
    } else if (type == ButtonType.down) {
      cmdValue = _dryingMachineStatusDown;
    } else if (type == ButtonType.pause) {
      cmdValue = _dryingMachineStatusPause;
    }

    if (cmdValue == _dryingMachineStatusUp &&
        _positionValue == _dryingMachinePositionTop) {
      ToastHelper.showToast(_dryingMachineToastTop);
      return;
    } else if (cmdValue == _dryingMachineStatusDown &&
        _positionValue == _dryingMachinePositionBottom) {
      ToastHelper.showToast(_dryingMachineToastBottom);
      return;
    } else if (cmdValue == _dryingMachineStatusPause &&
        _moveStatusValue == _dryingMachineStatusPause) {
      ToastHelper.showToast(_dryingMachineToastPause);
      return;
    }

    final Map<String, String> commands = <String, String>{
      _moveStatusKey: cmdValue,
    };
    quickCtrlLECommand(device.basicInfo.deviceId, commands);
  }

  @override
  String get smallCardStatus => deviceCardAttributeOne()?.desc ?? '';

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['moveStatus']?.value ?? '';
    if (value == '') {
      return null;
    }
    String attr = '';
    if (value == '1') {
      attr = '正在上升';
    } else if (value == '2') {
      attr = '正在下降';
    } else if (value == '0') {
      attr = '停止';
    } else if (value == '3') {
      attr = '一键收纳';
    }

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }
}

_ButtonIconModel _btnViewModel(ButtonType type) {
  if (type == ButtonType.up) {
    return _ButtonIconModel(
        name: '上升', icon: 'assets/components/clothes_up.webp');
  }
  if (type == ButtonType.down) {
    return _ButtonIconModel(
        name: '下降', icon: 'assets/components/clothes_down.webp');
  }
  if (type == ButtonType.pause) {
    return _ButtonIconModel(
        name: '暂停', icon: 'assets/components/clothes_pause.webp');
  }
  return _ButtonIconModel();
}

enum ButtonType { up, down, pause }

class _ButtonIconModel {
  String name = '';
  String icon = '';

  _ButtonIconModel({this.name = '', this.icon = ''});
}
