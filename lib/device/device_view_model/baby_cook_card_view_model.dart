import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class BabyCookCardViewModel extends DeviceCardViewModel {
  BabyCookCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['runningMode']?.value ?? '';
    if (value == '') {
      return null;
    }
    if (value == '2') {
      return DeviceCardAttribute(label: '', value: '暂停', unit: '');
    }
    if (value == '4') {
      return DeviceCardAttribute(label: '', value: '停止', unit: '');
    }
    if (value == '1') {
      final String operationValue =
          device.attributeMap['operationMode']?.value ?? '';
      final String attr = _getModel(operationValue);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    }

    return null;
  }

  String _getModel(String value) {
    final Map<String, String> map = <String, String>{};
    map['0'] = '待机';
    map['1'] = '热食';
    map['2'] = '蒸煮';
    map['3'] = '解冻';
    map['4'] = '消毒';

    final String returnvalue = map.stringValueForKey(value, '');

    return returnvalue;
  }
}
