import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/component/view_model/expand_value_list_long_touch_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_switch_icon_text_view_model.dart';
import 'package:smart_home/device/resize_device_card/resize_base_model.dart';

import '../component_view_model/component_view_model.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../device_info_model/smart_home_device_state.dart';
import 'device_card_view_model.dart';

// 电视
class TvCardViewModel extends DeviceCardViewModel {
  TvCardViewModel({required super.device});

  final String _standbyStatus = '息屏';
  final String _toastWithStandby = '电视息屏，无法操作';
  final String _toastWithDeviceOffline = '仅开机或息屏时可用手机远程操控';

  @override
  String? get otherDeviceOfflineInfo {
    if (deviceCardType == DeviceCardType.smallCard) {
      return '';
    }
    return '已关机或未联网';
  }

  @override
  String get additionalOfflineToast => _toastWithDeviceOffline;

  /// 开机
  @override
  bool get runningMode => powerOn;

  @override
  bool supportPowerOnOff() => true;

  ///是否息屏
  bool get tvIsStandby =>
      powerOff && device.onlineState == SmartHomeDeviceOnlineState.onlineReady;

  ///是否开机
  bool get tvIsPowerOn =>
      powerOn && device.onlineState == SmartHomeDeviceOnlineState.onlineReady;


  @override
  Future<bool> powerButtonCheckContinue() {
    return checkDeviceState(
        writable: onOffAttribute?.writable ?? false,
        checkPowerOff: false);
  }

  @override
  ComponentBaseViewModel? get topRightComponentViewModel {
    if (supportPowerOnOff()) {
      final SmartHomeDeviceAttribute? attribute = onOffAttribute;
      if (attribute == null) {
        return null;
      }

      //关机(离线)时隐藏开关按钮
      if (deviceOffline) {
        return null;
      }

      return super.topRightComponentViewModel;
    }
    return null;
  }

  @override
  String get largeCardStatus {
    if (tvIsStandby) {
      return _standbyStatus;
    } else {
      return '';
    }
  }

  @override
  String get middleCardStatus {
    if (tvIsStandby) {
      return _standbyStatus;
    } else {
      return '';
    }
  }


  @override
  String get smallCardStatus {
    final SmartHomeDeviceOnlineState onlineState = device.onlineState;
    if (tvIsStandby) {
      return _standbyStatus;
    } else if (tvIsPowerOn) {
      return '开';
    } else {
      return '';
    }
  }

  ///统一下发”keyboardOperation“的指令
  ///
  /// 指令包括：11-静音，8-关机
  void _sendCtrlLeCommand(String commandValue) {
    final Map<String, String> cmdMap = <String, String>{
      'keyboardOperation': commandValue
    };
    quickCtrlLECommand(deviceId, cmdMap, onErrorCallback: (String errMsg) {
      DevLogger.warning(
          tag: DevLogger.LOG_W, msg: '执行指令[$commandValue]失败：$errMsg');
    });
  }

  /// 获取大卡片功能集合
  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    return <String, LargeDeviceCardFunctionSet>{
      'power': LargeDeviceCardFunctionSet(
          name: '电视',
          componentViewModelList: <ComponentBaseViewModel>[
            FixedSwitchIconTextViewModel(
                icon: 'assets/icons/icon_tv_sound_off.webp',
                text: '静音',
                enable: tvIsPowerOn,
                isOn: false,
                throttlerMillionSeconds: 150,
                clickCallback: (BuildContext context) {
                  _validateDeviceState().then((bool isAllow) {
                    if (isAllow) {
                      gio(cloudProgramName: '静音');
                      //下发静音指令。需求：下发两次指令{'keyboardOperation':'11'}，间隔时间为150ms
                      _sendCtrlLeCommand('11');
                    }
                  });
                }),
            ExpandValueListLongTouchViewModel(
              valueList: <String>['音量'],
              currentIndex: 0,
              valueTextFontSize: 16,
              enable: tvIsPowerOn,
              needUpdateBtnState: false,
              checkContinue:
                  (bool isLongTouchEvent, bool isFirstTouchRun) async {
                bool isPass = true;
                // 长按时只弹一次吐司
                if ((isLongTouchEvent && isFirstTouchRun) ||
                    !isLongTouchEvent) {
                  isPass = await _validateDeviceState();
                }else if(isLongTouchEvent && !isFirstTouchRun){
                  isPass = await _validateDeviceStateSilently();
                }
                return isPass;
              },
              btnClickPreCallback:
                  (_, bool isLongTouchEvent, bool isFirstTouchRun) {
                //长按只算一次埋点事件
                if (isLongTouchEvent && isFirstTouchRun) {
                  gio(cloudProgramName: '音量减');
                } else if (!isLongTouchEvent) {
                  gio(cloudProgramName: '音量减');
                }
                //下发音量减小指令
                _sendCtrlLeCommand('13');
              },
              btnClickNextCallback:
                  (_, bool isLongTouchEvent, bool isFirstTouchRun) {
                //长按只算一次埋点事件
                if (isLongTouchEvent && isFirstTouchRun) {
                  gio(cloudProgramName: '音量加');
                } else if (!isLongTouchEvent) {
                  gio(cloudProgramName: '音量加');
                }
                //下发音量增加指令
                _sendCtrlLeCommand('12');
              },
            ),
            FixedSwitchIconTextViewModel(
                icon: 'assets/icons/icon_tv_remote_control.webp',
                text: '遥控器',
                isOn: false,
                enable: tvIsPowerOn || tvIsStandby,
                clickCallback: (_) {
                  _validateDeviceStateIgnorePowerOff().then((bool isAllow) {
                    if (isAllow) {
                      gio(cloudProgramName: '遥控器');
                      String deviceBrand = '';

                      //品牌列表按下面兼容性处理，如果有其他新品牌，走默认路径：/remoteControlH
                      final String brand = device.basicInfo.brand.toLowerCase();
                      if (brand.contains('统帅') || brand.contains('leader')) {
                        deviceBrand = '/remoteControlL';
                      } else if (brand.contains('海尔') ||
                          brand.contains('haier')) {
                        deviceBrand = '/remoteControlH';
                      } else if (brand.contains('卡萨帝') ||
                          brand.contains('casarte')) {
                        deviceBrand = '/remoteControlC';
                      } else {
                        deviceBrand = '/remoteControlH';
                      }

                      DevLogger.debug(
                          tag: 'TvCardViewModel',
                          msg:
                              'brand:${device.basicInfo.brand} \n url :${SmartHomeConstant.vdnDeviceDetail}$deviceId#$deviceBrand');
                      goToPageWithDebounce(
                          '${SmartHomeConstant.vdnDeviceDetail}$deviceId#$deviceBrand');
                    }
                  });
                })
          ])
    };
  }


  Future<bool> _validateDeviceState() async {
    return checkDeviceState(powerOffMsg: _toastWithStandby);
  }

  Future<bool> _validateDeviceStateSilently() async {
    return checkDeviceState(needShowToast: false);
  }

  Future<bool> _validateDeviceStateIgnorePowerOff() async {
    return checkDeviceState(checkPowerOff: false);
  }


  /// 通用状态检查
  @override
  Future<bool> checkDeviceState(
      {bool checkOffline = true,
      bool checkLoading = true,
      String loadingMsg = '',
      bool checkOnlineNotReady = false,
      bool checkPowerOff = true,
      String powerOffMsg = '',
      bool checkChildLock = true,
      bool checkWritable = true,
      bool writable = true,
      String writableMsg = '',
      bool checkAlarm = true,
      bool needShowToast = true}) async {
    void showToast(String msg) {
      if (needShowToast) {
        ToastHelper.showToast(msg);
      }
    }

    bool otherCheck({
      bool checkOffline = true,
      bool checkLoading = true,
      String loadingMsg = '',
      bool checkOnlineNotReady = false,
      bool checkPowerOff = true,
      String powerOffMsg = '',
      bool checkChildLock = true,
      bool checkWritable = true,
      bool writable = true,
      String writableMsg = '',
      bool checkAlarm = true,
    }) {
      /// 离线
      if (checkOffline && deviceOffline) {
        if (additionalOfflineToast.isNotEmpty) {
          showToast(additionalOfflineToast);
        } else {
          showToast(SmartHomeConstant.toastDeviceOfflineInfo);
        }
        return false;
      }

      /// 低功耗&wifi关闭
      if (lowerPowerOrWifiClose) {
        return false;
      }

      /// 加载中
      if (checkLoading && loading) {
        if (loadingMsg.isNotEmpty) {
          showToast(loadingMsg);
        } else {
          showToast(SmartHomeConstant.toastDeviceLoadingInfo);
        }

        return false;
      }

      /// 在线未就绪
      if (checkOnlineNotReady && onlineNotReady) {
        showToast(SmartHomeConstant.toastDeviceOnlineNotReadyInfo);
        return false;
      }

      /// 息屏
      ///
      /// 这里TV设备比较特殊，（powerOff 并且 onlineReady） 是息屏状态
      if (checkPowerOff && powerOff) {
        if (powerOffMsg.isNotEmpty) {
          showToast(powerOffMsg);
        } else {
          showToast(SmartHomeConstant.toastDevicePowerOff);
        }

        return false;
      }

      /// 童锁
      if (checkChildLock && childLockOn) {
        showToast(SmartHomeConstant.toastChildLockOnMsg);
        return false;
      }

      /// 不可写特殊提示
      if (writableMsg.isNotEmpty && checkWritable && !writable) {
        showToast(writableMsg);
        return false;
      }

      /// 告警&&不可写
      if (checkWritable && !writable && checkAlarm && alarm) {
        showToast(SmartHomeConstant.toastDeviceAlarmInfo);
        return false;
      }

      if (checkWritable && !writable) {
        showToast(SmartHomeConstant.toastNotWritable);
        return false;
      }

      return true;
    }

    return Network.isOnline().then((NetworkStatus networkStatus) {
      if (!networkStatus.isOnline) {
        showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return false;
      }
      return otherCheck(
        checkOffline: checkOffline,
        checkLoading: checkLoading,
        loadingMsg: loadingMsg,
        checkOnlineNotReady: checkOnlineNotReady,
        checkPowerOff: checkPowerOff,
        powerOffMsg: powerOffMsg,
        checkChildLock: checkChildLock,
        checkWritable: checkWritable,
        writable: writable,
        writableMsg: writableMsg,
      );
    }, onError: (_) {
      return otherCheck(
        checkOffline: checkOffline,
        checkLoading: checkLoading,
        loadingMsg: loadingMsg,
        checkOnlineNotReady: checkOnlineNotReady,
        checkPowerOff: checkPowerOff,
        powerOffMsg: powerOffMsg,
        checkChildLock: checkChildLock,
        checkWritable: checkWritable,
        writable: writable,
        writableMsg: writableMsg,
      );
    });
  }
}
