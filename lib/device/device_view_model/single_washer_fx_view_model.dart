import 'package:smart_home/device/device_view_model/single_washer_view_model.dart';

import '../../common/constant.dart';
import '../device_info_model/device_card_attribute.dart';

// 斐雪派克洗碗机
class SingleWasherFxViewModel extends SingleWasherViewModel {
  SingleWasherFxViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String onOffStatus =
        getDeviceCardAttrValueByName(SmartHomeConstant.deviceOnOffStatus);
    if (onOffStatus.isEmpty ||
        onOffStatus == SmartHomeConstant.deviceAttrFalse) {
      return super.deviceCardAttributeOne();
    }

    final String cyclePhase = getDeviceCardAttrValueByName('cyclePhase');
    if (cyclePhase == '0' || cyclePhase == '11') {
      return DeviceCardAttribute(label: '', value: '待机', unit: '');
    }

    final int remainTime = getRemainTime();

    if (remainTime == 0) {
      return defaultDeviceCardAttribute;
    }

    if (cyclePhase == '12') {
      return DeviceCardAttribute(
          label: '预约中', value: '剩$remainTime', unit: 'min');
    }

    return cyclePhase.isEmpty
        ? null
        : DeviceCardAttribute(label: '', value: '剩$remainTime', unit: 'min');
  }
}
