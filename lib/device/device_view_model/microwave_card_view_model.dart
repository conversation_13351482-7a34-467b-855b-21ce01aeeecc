import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class MicrowaveCardViewModel extends DeviceCardViewModel {
  MicrowaveCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'runningMode';
    final String value = device.attributeMap[key]?.value ?? '';
    String attr = '';
    switch (value) {
      case '1':
        attr = '启动';
      case '2':
        attr = '暂停';
      case '3':
        attr = '取消';
      case '4':
        attr = '继续';
      default:
        break;
    }
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }
}
