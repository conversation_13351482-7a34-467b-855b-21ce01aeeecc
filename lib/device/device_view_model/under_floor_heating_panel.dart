import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//地暖温控面板
class UnderFloorHeatingPanel extends DeviceCardViewModel {
  UnderFloorHeatingPanel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String indoorTemperatureValue =
        getDeviceCardAttrValueByName('indoorTemperature');

    if (indoorTemperatureValue.isNotEmpty) {
      return DeviceCardAttribute(
          label: '室内', value: indoorTemperatureValue, unit: '℃');
    }
    return super.deviceCardAttributeOne();
  }
}
