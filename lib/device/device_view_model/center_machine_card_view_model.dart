import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CenterMachineCardViewModel extends DeviceCardViewModel {
  CenterMachineCardViewModel({required super.device});

  String get purifiedWaterQualityKey => 'purifiedWaterQuality';

  String get softWaterQualityKey => 'softWaterQuality';

  SmartHomeDeviceAttribute? get purifiedWaterQualityAttribute =>
      getDeviceAttribute(purifiedWaterQualityKey);

  SmartHomeDeviceAttribute? get softWaterQualityAttribute =>
      getDeviceAttribute(softWaterQualityKey);

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String purifiedValue = purifiedWaterQualityAttribute?.value ?? '';
    String value = _getSoftWaterQuality(purifiedValue);

    if (value.isNotEmpty) {
      return DeviceCardAttribute(label: '', value: value, unit: '');
    }

    final String softValue = softWaterQualityAttribute?.value ?? '';
    value = _getSoftWaterQuality(softValue);
    if (value.isNotEmpty) {
      return DeviceCardAttribute(label: '', value: value, unit: '');
    }
    return null;
  }

  String _getSoftWaterQuality(String value) {
    String returnValue = '';
    if (value == '1') {
      returnValue = '优';
    } else if (value == '2') {
      returnValue = '良';
    } else if (value == '3') {
      returnValue = '差';
    }

    return returnValue;
  }
}
