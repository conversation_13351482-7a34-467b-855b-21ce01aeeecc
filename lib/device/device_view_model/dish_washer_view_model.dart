import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//洗碗机
class DishWasherViewModel extends DeviceCardViewModel {
  DishWasherViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = getDeviceCardAttrValueByName('washProg');
    final String value16 =
        value.isNotEmpty ? (int.tryParse(value) ?? 0).toRadixString(16) : '';
    return value.isNotEmpty
        ? DeviceCardAttribute(
            label: '', value: assignWashModeValue(value16), unit: '')
        : null;
  }

  String assignWashModeValue(String value) {
    switch (value) {
      case '0':
        return '待机';
      case '16':
        return '智能洗';
      case '3':
        return '强力洗';
      case '30':
        return '红酒洗';
      case '7':
        return '消毒洗';
      case '1':
        return '即时洗';
      case 'e':
        return '果蔬洗';
      case '35':
        return '分区洗';
      default:
        return '';
    }
  }
}
