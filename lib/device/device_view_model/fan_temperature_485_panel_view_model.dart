import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//485新风设备
class FanTemperature485PanelViewModel extends DeviceCardViewModel {
  FanTemperature485PanelViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  String get smallCardStatus {
    if (powerOn) {
      return '开';
    }
    if (powerOff) {
      return '关';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String windSpeedValue = getDeviceCardAttrValueByName('windSpeed');
      String attrValue = '';
      switch (windSpeedValue) {
        case '1':
          attrValue = '高风';
        case '2':
          attrValue = '中风';
        case '4':
          attrValue = '低风';
      }

      return attrValue.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attrValue, unit: '')
          : null;
    }

    return null;
  }
}
