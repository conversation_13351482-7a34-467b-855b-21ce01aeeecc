import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/resize_device_card/resize_base_model.dart';

class AirCleanerCardViewModel extends DeviceCardViewModel {
  AirCleanerCardViewModel({required super.device});

  Map<String, String> get allAirQualityMap => <String, String>{
        '1': '优',
        '2': '良',
        '3': '中',
        '4': '差',
      };

  String get pm2p5ValueKey => 'pm2p5Value';

  String get airQualityKey => 'airQuality';

  SmartHomeDeviceAttribute? get pm2p5ValueAttribute {
    return getDeviceAttribute(pm2p5ValueKey);
  }

  SmartHomeDeviceAttribute? get airQualityAttribute {
    return getDeviceAttribute(airQualityKey);
  }

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  String get smallCardStatus {
    if (powerOn) {
      return '开';
    }
    if (powerOff) {
      return '关';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String value = pm2p5ValueAttribute?.value ?? '';
      return value.isNotEmpty
          ? DeviceCardAttribute(label: 'PM2.5 ', value: value, unit: '')
          : null;
    }
    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (powerOn) {
      if (deviceCardType != DeviceCardType.largeCard) {
        return null;
      }
      final String airQuality =
          allAirQualityMap[airQualityAttribute?.value ?? ''] ?? '';
      if (airQuality.isNotEmpty) {
        return DeviceCardAttribute(label: '空气质量', value: airQuality, unit: '');
      }
    }
    return null;
  }
}
