import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class RangeHoodCardViewModel extends DeviceCardViewModel {
  RangeHoodCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  /// 已开机且风量有值上报或照明开
  @override
  bool get runningMode {
    if (powerOn) {
      const String key = 'lightingStatus';
      final String value = device.attributeMap[key]?.value ?? '';
      if (value == trueValue) {
        return true;
      }
      final String windSpeedLevel =
          device.attributeMap['windSpeedLevel']?.value ?? '';
      if (windSpeedLevel.isNotEmpty && windSpeedLevel != '0') {
        return true;
      }

      final String smartWindLevel =
          device.attributeMap['smartWindLevel']?.value ?? '';
      if (smartWindLevel == trueValue) {
        return true;
      }
    }

    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'lightingStatus';
    final String value = device.attributeMap[key]?.value ?? '';
    final String attr = value.isEmpty ? '' : (value == trueValue ? '开' : '关');
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '照明', value: attr, unit: '')
        : null;
  }
}
