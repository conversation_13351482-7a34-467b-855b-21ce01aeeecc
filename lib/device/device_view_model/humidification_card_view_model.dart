import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class HumidificationCardViewModel extends DeviceCardViewModel {
  HumidificationCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'indoorHumidity';
    final String value = device.attributeMap[key]?.value ?? '';
    return value.isNotEmpty
        ? DeviceCardAttribute(label: '湿度', value: value, unit: '%')
        : null;
  }
}
