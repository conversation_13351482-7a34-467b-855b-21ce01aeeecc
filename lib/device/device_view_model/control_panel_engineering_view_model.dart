import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_view_model/control_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 工程10寸 工程6寸 魔方4寸
class ControlPanelEngineeringViewModel extends ControlPanelViewModel {
  ControlPanelEngineeringViewModel({required super.device});

  final String enabledStatusKey = 'enabledStatus';
  final String switchType1Key = 'switchType1';
  final String switchType2Key = 'switchType2';
  final String switchType3Key = 'switchType3';
  final String normalLight = '1';
  final String smartLight = '2';

  @override
  AccessoryDeviceType getAccessoryDeviceType(
      int index, int sortCode, DeviceCardViewModel viewmodel) {
    final List<String> typeKeys = <String>[
      switchType1Key,
      switchType2Key,
      switchType3Key
    ];
    if (index < 0 || index >= typeKeys.length) {
      return AccessoryDeviceType.unknown;
    }
    final String value = getDeviceCardAttrValueByName(typeKeys[index]);
    if (value == normalLight) {
      return AccessoryDeviceType.normalLight;
    } else if (value == smartLight) {
      return AccessoryDeviceType.smartLight;
    }
    return AccessoryDeviceType.unknown;
  }

  @override
  bool isDeviceEnabled(int index, int sortCode, DeviceCardViewModel child,
      AccessoryDeviceType type) {
    final String enabledStatusValue =
        child.getDeviceCardAttrValueByName(enabledStatusKey);
    return enabledStatusValue == SmartHomeConstant.deviceAttrTrue;
  }
}
