/*
 * 描述：新风机 20086108008203242402100297e9dd0000006e1aee96cf8785e425b3ebc19a40
 * 作者：songFJ
 * 创建时间：2025/3/11
 */

import 'package:smart_home/device/device_view_model/new_fan_base_view_model.dart';

class NewFan19a40ViewModel extends NewFanBaseViewModel {
  NewFan19a40ViewModel({required super.device});

  @override
  bool get runningMode => powerOn;

  @override
  Map<String, ModeModel> get allModeMap => <String, ModeModel>{
        '0': ModeModel(value: '0', desc: '自动', icon: 'assets/new_fan/zi.webp'),
        '7': ModeModel(value: '7', desc: '全热', icon: 'assets/new_fan/re.webp'),
        '9': ModeModel(value: '9', desc: '新风', icon: 'assets/new_fan/xin.webp'),
        '10':
            ModeModel(value: '10', desc: '排风', icon: 'assets/new_fan/pai.webp'),
        '8':
            ModeModel(value: '8', desc: '旁通', icon: 'assets/new_fan/pang.webp'),
        '11': ModeModel(
            value: '11', desc: '内循环', icon: 'assets/new_fan/nei.webp'),
        '13': ModeModel(
            value: '13', desc: '睡眠', icon: 'assets/new_fan/shui.webp'),
      };
}
