import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 用于特殊 cancelKey的ViewModel
class DeviceAlarmCancelKeyViewModel extends DeviceCardViewModel {
  String cancelKey;

  DeviceAlarmCancelKeyViewModel(this.cancelKey, {required super.device});

  @override
  String provideAlarmCancelKey() {
    if (cancelKey.isNotEmpty) {
      return cancelKey;
    } else {
      return super.provideAlarmCancelKey();
    }
  }
}
