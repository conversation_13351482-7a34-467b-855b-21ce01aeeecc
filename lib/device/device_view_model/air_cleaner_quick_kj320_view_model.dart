import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_quick_card_view_model.dart';

import '../component/view_model/expand_value_list_view_model.dart';
import '../component/view_model/fixed_switch_icon_text_view_model.dart';

//空气净化器
class AirCleanerQuickKj320ViewModel extends AirCleanerQuickCardViewModel {
  AirCleanerQuickKj320ViewModel({required super.device});

  @override
  String get windSpeedAttrKey => 'windSpeed';

  @override
  String get autoModeAttrKey => 'operationMode';

  @override
  String get autoModeAttrValue => '2';

  @override
  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      widSpeedComponentViewModel,
      autoModeViewModel,
    ];
  }

  @override
  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      ExpandValueListViewModel(
        valueList: <String>[],
        currentIndex: -1,
        unit: '',
        valueListEmptyCallback: (_) {
          checkDeviceState();
        },
        enable: false,
      ),
      FixedSwitchIconTextViewModel(
        icon: 'assets/icons/auto_mode.webp',
        text: '自动模式',
        enable: false,
        isOn: false,
        clickCallback: (_) {
          checkDeviceState();
        },
      ),
    ];
  }
}
