import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//商空线控器
class AirPipeLineViewModel extends DeviceCardViewModel {
  AirPipeLineViewModel({required super.device});

  bool isTemperatureRange(double temperature) {
    return temperature <= 90 && temperature >= -50;
  }

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  String get smallCardStatus {
    if (powerOff) {
      return '关';
    }
    if (powerOn) {
      return '开';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String indoorValue =
        getDeviceCardAttrValueByName('indoorTemperature'); //室内温度
    if (indoorValue.isNotEmpty) {
      final double indoorTemperature = double.tryParse(indoorValue) ?? 0;
      if (isTemperatureRange(indoorTemperature)) {
        return DeviceCardAttribute(
            label: '出水', value: indoorTemperature.toString(), unit: '℃');
      }
    }

    final String currentOutletValue =
        getDeviceCardAttrValueByName('currentOutletTemperature'); //当前出水温度
    if (currentOutletValue.isNotEmpty) {
      final double currentOutletTemperature =
          double.tryParse(currentOutletValue) ?? 0;
      if (isTemperatureRange(currentOutletTemperature)) {
        return DeviceCardAttribute(
            label: '出水', value: currentOutletTemperature.toString(), unit: '℃');
      }
    }
    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (powerOn) {
      final String value = getDeviceCardAttrValueByName('operationMode'); //模式
      final String attr = getModel(value);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    }
    return null;
  }

  String getModel(String value) {
    final Map<String, String> map = <String, String>{};
    map['1'] = '制冷';
    map['4'] = '制热';
    map['7'] = '地暖';
    map['8'] = '制热&地暖';

    final String returnValue = map.stringValueForKey(value, '');

    return returnValue;
  }
}
