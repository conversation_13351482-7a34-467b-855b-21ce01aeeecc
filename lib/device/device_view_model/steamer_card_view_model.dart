import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../resize_device_card/resize_base_model.dart';

class SteamerCardViewModel extends DeviceCardViewModel {
  SteamerCardViewModel({required super.device});

  // 离线提示"已关机或未联网"的型号集合
  List<String> get _hintShutdownOrNotConnectedNetModelsList => <String>[
        'C7S46BGU1',
        'C7S46CGU1',
        'C6S46BGU1',
        'GSMC0501BSGU1',
        'GSMC0501BBGU1',
        'C7S46AGU1',
        'C6S46AGU1',
        'C6S46CGU1',
      ];

  @override
  String? get otherDeviceOfflineInfo {
    if (_hintShutdownOrNotConnectedNetModelsList
        .contains(device.basicInfo.model)) {
      if (deviceCardType == DeviceCardType.smallCard) {
        return '';
      }
      return '已关机或未联网';
    }
    return super.otherDeviceOfflineInfo;
  }

  @override
  bool get runningMode {
    if (powerOn) {
      final String workStatus = getDeviceCardAttrValueByName('workStatus');
      if (workStatus.isNotEmpty) {
        return workStatus != '0';
      }

      final String backingSta = getDeviceCardAttrValueByName('BakingSta');
      if (backingSta.isNotEmpty) {
        return backingSta != '1';
      }
    }

    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }

    if (powerOn) {
      final String workStatus = getDeviceCardAttrValueByName('workStatus');
      if (workStatus.isNotEmpty) {
        return DeviceCardAttribute(
            label: '', value: workStatus == '0' ? '待机' : '工作中', unit: '');
      }

      final String backingSta = getDeviceCardAttrValueByName('BakingSta');
      if (backingSta.isNotEmpty) {
        return DeviceCardAttribute(
            label: '', value: backingSta == '1' ? '待机' : '工作中', unit: '');
      }
    }

    return null;
  }
}
