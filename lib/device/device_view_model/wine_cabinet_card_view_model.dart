import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class WineCabinetCardViewModel extends DeviceCardViewModel {
  WineCabinetCardViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'lightStatus';
    final String value = device.attributeMap[key]?.value ?? '';
    String attr = '';

    if (powerOn && value == trueValue) {
      attr = '开';
    } else if (powerOff && value == falseValue) {
      attr = '关';
    }

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '照明', value: attr, unit: '')
        : null;
  }
}
