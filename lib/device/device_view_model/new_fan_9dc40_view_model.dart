/*
 * 描述：新风机 20086108008203242411f663554cc74a117ad583419138a79684f1f5ba89dc40
 * 作者：songFJ
 * 创建时间：2025/3/11
 */

import 'package:smart_home/device/component/view_model/expand_empty_view_model.dart';
import 'package:smart_home/device/device_view_model/new_fan_base_view_model.dart';

import '../component_view_model/component_view_model.dart';

class NewFan9dc40ViewModel extends NewFanBaseViewModel {
  NewFan9dc40ViewModel({required super.device});

  @override
  Map<String, ModeModel> get allModeMap => <String, ModeModel>{
        '0': ModeModel(value: '0', desc: '智能', icon: 'assets/new_fan/zhi.webp'),
        '1': ModeModel(
            value: '1', desc: '强劲', icon: 'assets/new_fan/qiang.webp'),
        '2': ModeModel(value: '2', desc: '柔风', icon: 'assets/new_fan/rou.webp'),
        '3':
            ModeModel(value: '3', desc: '睡眠', icon: 'assets/new_fan/shui.webp'),
      };

  @override
  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      ExpandEmptyViewModel(),
      defaultModeComponent(expandFlex: 2),
      ExpandEmptyViewModel(),
    ];
  }

  @override
  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      ExpandEmptyViewModel(),
      modeComponent(expandFlex: 2),
      ExpandEmptyViewModel(),
    ];
  }
}
