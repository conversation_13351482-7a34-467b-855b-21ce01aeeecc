import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SterilizerCardViewModel extends DeviceCardViewModel {
  SterilizerCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    final String lockStatus = getDeviceCardAttrValueByName('lockStatus');
    if (lockStatus == trueValue) {
      return false;
    }
    return true;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'intelligenceStatus';
    final String value = device.attributeMap[key]?.value ?? '';
    final String attr = value.isEmpty ? '' : (value == trueValue ? '开' : '关');
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '智能', value: attr, unit: '')
        : null;
  }
}
