import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//洗碗机
class DishWasherMoreViewModel extends DeviceCardViewModel {
  DishWasherMoreViewModel({required super.device});

  final String cyclePhaseKey = 'cyclePhase';

  SmartHomeDeviceAttribute? get cyclePhaseAttribute =>
      getDeviceAttribute(cyclePhaseKey);

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }

    final String cyclePhaseValue = cyclePhaseAttribute?.value ?? '';
    if (cyclePhaseValue == '0' || cyclePhaseValue == '11') {
      return DeviceCardAttribute(label: '', value: '待机', unit: '');
    }

    if (cyclePhaseValue.isNotEmpty && (_getRemainTime != 0)) {
      return DeviceCardAttribute(label: '', value: '运行中', unit: '');
    }

    return null;
  }

  int get _getRemainTime {
    final String remainHH = device.attributeMap['remainingTimeHH']?.value ?? '';
    final String remainMM = device.attributeMap['remainingTimeMM']?.value ?? '';
    final int hour = int.tryParse(remainHH) ?? 0;
    final int minute = int.tryParse(remainMM) ?? 0;
    final int remainTime = hour * 60 + minute;
    return remainTime;
  }
}
