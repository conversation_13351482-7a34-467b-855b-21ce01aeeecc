import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//集成灶
class IntegratedStoveCardViewModel extends DeviceCardViewModel {
  IntegratedStoveCardViewModel({required super.device});

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = getDeviceCardAttrValueByName('windLightingStatus');

    String attr = '';
    if (value == trueValue) {
      attr = '开';
    } else if (value == falseValue) {
      attr = '关';
    }

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '照明', value: attr, unit: '')
        : null;
  }
}
