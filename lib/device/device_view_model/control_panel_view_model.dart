import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_empty_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_empty_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_label_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

enum AccessoryDeviceType {
  normalLight, // 普通灯
  smartLight, // 智能灯
  floorHeating, // 地暖
  exhaustFan, // 排风扇
  generalSwitch, // 通用开关
  unknown, // 未知类型
}

abstract class IAccessoryDeviceTypeProvider {
  AccessoryDeviceType getAccessoryDeviceType(
      int index, int sortCode, DeviceCardViewModel viewmodel);

  bool isDeviceEnabled(int index, int sortCode, DeviceCardViewModel child,
      AccessoryDeviceType type);
}

abstract class ControlPanelViewModel extends DeviceCardViewModel
    implements IAccessoryDeviceTypeProvider {
  ControlPanelViewModel({required super.device});

  final String onOffStatusKey = 'onOffStatus';

  @override
  String get smallCardStatus => '';

  @override
  String get largeCardStatus => '';

  @override
  String get middleCardStatus {
    if (_isAttributeNotReported) {
      return '';
    }
    final List<DeviceCardViewModel> sortedChildViewModels =
        _getSortedChildViewModels();
    String status = '';
    for (int i = 0; i < sortedChildViewModels.length; i++) {
      final DeviceCardViewModel child = sortedChildViewModels[i];
      final int sortCode = child.device.basicInfo.attachmentSortCode;
      final AccessoryDeviceType accessoryDeviceType =
          getAccessoryDeviceType(i, sortCode, child);
      final bool deviceEnabled =
          isDeviceEnabled(i, sortCode, child, accessoryDeviceType);
      if (!deviceEnabled ||
          accessoryDeviceType == AccessoryDeviceType.smartLight) {
        continue;
      }
      final bool isOn = _isDeviceOn(child);
      status += isOn ? '开 ' : '关 ';
    }
    return status;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '控制面板';
    return <String, LargeDeviceCardFunctionSet>{
      name: LargeDeviceCardFunctionSet(
          name: name, componentViewModelList: getComponentViewModelList())
    };
  }

  List<ComponentBaseViewModel> getComponentViewModelList() {
    if (loading || deviceOffline) {
      return _nonAttributeComponents();
    }
    return _contentComponents();
  }

  List<ComponentBaseViewModel> _contentComponents() {
    final List<ComponentBaseViewModel> componentList =
        <ComponentBaseViewModel>[];
    final List<DeviceCardViewModel> sortedChildViewModels =
        _getSortedChildViewModels();

    for (int i = 0; i < sortedChildViewModels.length; i++) {
      final DeviceCardViewModel child = sortedChildViewModels[i];
      final int sortCode = child.device.basicInfo.attachmentSortCode;
      final AccessoryDeviceType accessoryDeviceType =
          getAccessoryDeviceType(i, sortCode, child);
      final bool deviceEnabled =
          isDeviceEnabled(i, sortCode, child, accessoryDeviceType);
      if (!deviceEnabled ||
          accessoryDeviceType == AccessoryDeviceType.smartLight) {
        continue;
      }
      final bool isOn = _isDeviceOn(child);
      final String deviceIcon = _getIconByDeviceType(accessoryDeviceType, isOn);
      componentList.add(ExpandSwitchIconTextViewModel(
          icon: deviceIcon,
          text: child.deviceName,
          enable: !deviceOffline,
          isOn: isOn,
          clickCallback: (BuildContext context) {
            final bool isOn = _isDeviceOn(child);
            _executePowerOnOff(isOn, child);
          }));
    }
    if (componentList.isEmpty) {
      return _emptyComponents();
    } else if (componentList.length == 1) {
      componentList.first.expandFlex = 2;
      componentList.insert(0, ExpandEmptyViewModel());
      componentList.add(ExpandEmptyViewModel());
    }
    return componentList;
  }

  List<ComponentBaseViewModel> _nonAttributeComponents() {
    return <ComponentBaseViewModel>[ExpandLabelViewModel(enable: false)];
  }

  AccessoryDeviceType mapValueToDeviceType(String? value) {
    switch (value) {
      case '1':
        return AccessoryDeviceType.normalLight;
      case '2':
        return AccessoryDeviceType.smartLight;
      case '3':
        return AccessoryDeviceType.floorHeating;
      case '4':
        return AccessoryDeviceType.exhaustFan;
      case '5':
        return AccessoryDeviceType.generalSwitch;
      default:
        return AccessoryDeviceType.unknown;
    }
  }

  List<ComponentBaseViewModel> _emptyComponents() {
    return <ComponentBaseViewModel>[
      AggregationEmptyViewModel(
        emptyTitle: '暂无可控设备',
      )
    ];
  }

  // 设备属性是否未上报完成
  bool get _isAttributeNotReported {
    return device.attributeMap.isEmpty ||
        childViewModelMap == null ||
        childViewModelMap!.isEmpty ||
        childViewModelMap!.values.any((DeviceCardViewModel viewModel) =>
            viewModel.device.attributeMap.isEmpty);
  }

  List<DeviceCardViewModel> _getSortedChildViewModels() {
    if (childViewModelMap == null || childViewModelMap!.isEmpty) {
      return <DeviceCardViewModel>[];
    } else {
      return childViewModelMap!.values.toList()
        ..sort((DeviceCardViewModel a, DeviceCardViewModel b) => a
            .device.basicInfo.attachmentSortCode
            .compareTo(b.device.basicInfo.attachmentSortCode));
    }
  }

  Future<void> _executePowerOnOff(
      bool powerOn, DeviceCardViewModel viewmodel) async {
    if (deviceOffline) {
      ToastHelper.showToast(SmartHomeConstant.toastDeviceOfflineInfo);
      return;
    }
    final Map<String, String> commands = <String, String>{};
    commands[onOffStatusKey] = powerOn ? falseValue : trueValue;
    quickCtrlLECommand(viewmodel.device.basicInfo.deviceId, commands,
        onErrorCallback: (String errMsg) {
      ToastHelper.showToast(errMsg);
    });
  }

  bool _isDeviceOn(DeviceCardViewModel child) {
    final SmartHomeDeviceAttribute? onOffStatus =
        child.getDeviceAttribute(onOffStatusKey);
    if (onOffStatus?.value == SmartHomeConstant.deviceAttrTrue) {
      return true;
    } else if (onOffStatus?.value == SmartHomeConstant.deviceAttrFalse) {
      return false;
    } else {
      return false;
    }
  }

  String _getIconByDeviceType(AccessoryDeviceType type, bool isOn) {
    switch (type) {
      case AccessoryDeviceType.normalLight:
        return isOn
            ? 'assets/images/icon_aggregation_light_open.webp'
            : 'assets/images/icon_aggregation_light_close.webp';
      case AccessoryDeviceType.floorHeating:
        return isOn
            ? 'assets/components/floor_heating_icon_on.webp'
            : 'assets/components/floor_heating_icon_off.webp';
      case AccessoryDeviceType.exhaustFan:
        return isOn
            ? 'assets/components/exhaust_fan_icon_on.webp'
            : 'assets/components/exhaust_fan_icon_off.webp';
      case AccessoryDeviceType.generalSwitch:
        return isOn
            ? 'assets/components/general_switch_on.webp'
            : 'assets/components/general_switch_off.webp';
      default:
        return 'assets/components/switch_icon.webp';
    }
  }
}
