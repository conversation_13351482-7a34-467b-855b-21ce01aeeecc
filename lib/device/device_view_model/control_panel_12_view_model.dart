import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/control_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class ControlPanel12ViewModel extends ControlPanelViewModel {
  ControlPanel12ViewModel({required super.device});

  final String switchType1ExAttributeKey = 'switchType1Ex';
  final String switchType2ExAttributeKey = 'switchType2Ex';
  final String switchType3ExAttributeKey = 'switchType3Ex';
  final String switchType1AttributeKey = 'switchType1';
  final String switchType2AttributeKey = 'switchType2';
  final String switchType3AttributeKey = 'switchType3';
  final String enabledStatusKey = 'enabledStatus';

  @override
  AccessoryDeviceType getAccessoryDeviceType(
      int index, int sortCode, DeviceCardViewModel viewmodel) {
    final List<String> exTypeKeys = <String>[
      switchType1ExAttributeKey,
      switchType2ExAttributeKey,
      switchType3ExAttributeKey
    ];
    final List<String> typeKeys = <String>[
      switchType1AttributeKey,
      switchType2AttributeKey,
      switchType3AttributeKey
    ];
    if (index < 0 || index >= exTypeKeys.length || index >= typeKeys.length) {
      return AccessoryDeviceType.unknown;
    }
    final SmartHomeDeviceAttribute? attr =
        getDeviceAttribute(exTypeKeys[index]) ??
            getDeviceAttribute(typeKeys[index]);
    return mapValueToDeviceType(attr?.value);
  }

  @override
  bool isDeviceEnabled(int index, int sortCode, DeviceCardViewModel child,
      AccessoryDeviceType type) {
    if (type == AccessoryDeviceType.normalLight ||
        type == AccessoryDeviceType.smartLight) {
      final SmartHomeDeviceAttribute? enabled =
          child.getDeviceAttribute(enabledStatusKey);
      return enabled?.value == SmartHomeConstant.deviceAttrTrue;
    } else if (type == AccessoryDeviceType.unknown) {
      return false;
    }
    return true;
  }
}
