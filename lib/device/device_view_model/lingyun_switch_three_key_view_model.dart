import 'lingyun/lingyun_constant.dart';
import 'lingyun/lingyun_model.dart';
import 'lingyun_switch_base_view_model.dart';

//凌云玻璃开关-三键开关
class LingYunSwitchThreeKeyViewModel extends LingYunSwitchBaseViewModel {
  LingYunSwitchThreeKeyViewModel({required super.device});

  @override
  List<LingYunSwitchModel> get switchModelList => <LingYunSwitchModel>[
        LingYunSwitchModel(
          switchName: '左键',
          switchTypeKey: LingYunSwitchTypeKey.switchType1Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus1Key,
        ),
        LingYunSwitchModel(
          switchName: '中键',
          switchTypeKey: LingYunSwitchTypeKey.switchType2Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus2Key,
        ),
        LingYunSwitchModel(
          switchName: '右键',
          switchTypeKey: LingYunSwitchTypeKey.switchType3Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus3Key,
        )
      ];
}
