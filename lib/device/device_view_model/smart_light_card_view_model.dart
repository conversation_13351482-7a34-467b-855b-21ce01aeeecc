import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../common/constant.dart';
import '../../store/smart_home_store.dart';
import '../component_view_model/component_view_model.dart';
import '../component_view_model/slider_component_view_model.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../store/device_action.dart';

class SmartLightCardViewModel extends DeviceCardViewModel {
  SmartLightCardViewModel({required super.device});

  static const String lightGroupUrl =
      'https://pic-resource.haigeek.com/ossdownload/resource/selfService/hardware/modelimg/c72a575e671a43c58742259e17bc17e7.png';

  @override
  String get deviceIcon {
    if (device.basicInfo.deviceGroupType == 'group') {
      return lightGroupUrl;
    }
    return device.basicInfo.cardPageImg;
  }

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  String get smallCardStatus {
    final String onOffValue =
        getDeviceCardAttrValueByName(SmartHomeConstant.deviceOnOffStatus);
    return onOffValue == SmartHomeConstant.deviceAttrTrue ? '开' : '关';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String value = getDeviceCardAttrValueByName('brightness');
      return value.isNotEmpty
          ? DeviceCardAttribute(label: '亮度', value: value, unit: '%')
          : null;
    }
    return null;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    final SmartHomeDeviceAttribute? cardAttribute =
    getDeviceAttribute('brightness');
    final double min =
        double.tryParse(cardAttribute?.valueRange.dataStep.minValue ?? '0') ??
            0;
    final double max =
        double.tryParse(cardAttribute?.valueRange.dataStep.maxValue ?? '100') ??
            100;
    final double step =
        double.tryParse(cardAttribute?.valueRange.dataStep.step ?? '1') ?? 1;
    double brightnessValue =
        double.tryParse(getDeviceCardAttrValueByName('brightness')) ?? 0;
    if (brightnessValue <= min) {
      brightnessValue = min;
    } else if (brightnessValue >= max) {
      brightnessValue = max;
    }

    final List<ComponentBaseViewModel> list = <ComponentBaseViewModel>[
      SliderComponentViewModel(
          maxValue: max,
          minValue: min,
          currentValue: brightnessValue,
          isDisabled: deviceOfflineOrPowerOff || loading,
          slideValueChange: (double currentValue) {
            checkDeviceState().then((bool pass) {
              if (pass) {
                final Map<String, String> commands = <String, String>{};
                commands['brightness'] =
                    (currentValue - currentValue % step).toString();
                quickCtrlLECommand(device.basicInfo.deviceId, commands);
              }
            });
          })
    ];

    const String name = '智能灯';
    final LargeDeviceCardFunctionSet functionSet =
    LargeDeviceCardFunctionSet(name: name, componentViewModelList: list);
    return <String, LargeDeviceCardFunctionSet>{name: functionSet};
  }

  @override
  Future<void> executePowerOnOff(bool powerOn, bool writable) {
    final SmartHomeDeviceAttribute attribute = SmartHomeDeviceAttribute(
        name: SmartHomeConstant.deviceOnOffStatus,
        value: powerOn ? 'false' : 'true');
    smartHomeStore.dispatch(UpdateCustomAttributeAction(deviceId, attribute));
    return super.executePowerOnOff(powerOn, writable);
  }
}
