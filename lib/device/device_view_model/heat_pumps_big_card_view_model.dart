import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/component/view_model/expand_empty_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/device_view_model/heat_pumps_card_view_model.dart';

import '../device_info_model/device_card_attribute.dart';

// 热泵：支持预约用水的大卡片
class HeatPumpsBigCardViewModel extends HeatPumpsCardViewModel {
  HeatPumpsBigCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (powerOff) {
      return null;
    }
    final String workStatus = getDeviceCardAttrValueByName('workStatus');
    String value = '';
    if (workStatus == '1') {
      value = '保温';
    } else if (workStatus == '2') {
      value = '加热';
    }
    return workStatus.isNotEmpty
        ? DeviceCardAttribute(label: '', value: value, unit: '')
        : null;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    final bool hasViewModel =
        (_isNewModelHeatPumpDevice() && _isNewAttributeSupport()) ||
            _isOldAttributeSupport();

    if (!hasViewModel) {
      return null;
    }
    final ComponentBaseViewModel viewModel = heatPumpControlViewModel();

    final List<ComponentBaseViewModel> list = <ComponentBaseViewModel>[
      ExpandEmptyViewModel(),
      viewModel,
      ExpandEmptyViewModel(),
    ];

    if (list.isEmpty) {
      return null;
    }

    const String name = '热泵';
    final LargeDeviceCardFunctionSet deviceCardFunctionSet =
        LargeDeviceCardFunctionSet(name: name, componentViewModelList: list);
    return <String, LargeDeviceCardFunctionSet>{name: deviceCardFunctionSet};
  }

  bool getButtonStatus() {
    if (deviceOfflineOrPowerOff || loading) {
      return false;
    }
    final String resn1RunningStatus =
        getDeviceCardAttrValueByName('resn1RunningStatus');
    final String resn2RunningStatus =
        getDeviceCardAttrValueByName('resn2RunningStatus');
    if (resn1RunningStatus == trueValue || resn2RunningStatus == trueValue) {
      return true;
    }

    final String resn1TimeHH = getDeviceCardAttrValueByName('resn1TimeHH');
    if (resn1TimeHH == trueValue) {
      return true;
    }

    if (device.attributeMap.containsKey('tp1StartTimeHH')) {
      String tipStatus = '';
      for (int i = 1; i <= 9; i++) {
        final String status =
            device.attributeMap['tp$i' + 'Status']?.value ?? '';
        if (status == trueValue) {
          tipStatus = trueValue;
          break;
        }
      }
      if (tipStatus == trueValue) {
        return true;
      }
    }

    return false;
  }

  ComponentBaseViewModel heatPumpControlViewModel() {
    final bool compEnable = !(deviceOfflineOrPowerOff || loading);
    final ComponentBaseViewModel buttonComponentViewModel =
    ExpandSwitchIconTextViewModel(
            icon: 'assets/components/card_water_on_off.webp',
            isOn: getButtonStatus(),
            text: '预约用水',
            expandFlex: 2,
            enable: compEnable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '预约用水');
              checkDeviceState(powerOffMsg: '热泵关机，无法操作').then((bool pass) {
                if (pass) {
                  goToPageWithDebounce(
                      '${SmartHomeConstant.vdnDeviceDetail}${device.basicInfo.deviceId}${SmartHomeConstant.quickControlStoveReservationSuffix}');
                }
              });
            });
    return buttonComponentViewModel;
  }

  bool _isNewModelHeatPumpDevice() {
    return _newModelHeatPumpDeviceTypeIds.contains(device.basicInfo.typeId) ||
        device.basicInfo.model == newModelHeatPumpDeviceModel;
  }

  String newModelHeatPumpDeviceModel = 'LHPA200-1.0A(U1)';

  final List<String> _newModelHeatPumpDeviceTypeIds = <String>[
    '201c120000118674200100418007574800000000000000000000000000000040',
    '201c120000118674200100418007574800000000000000000000000000000040',
    '201c120000118674200100418007574100000000000000000000000000000040',
    '201c120000118674200100418010284300000000000000000000000000000040',
    '201c120000118674200100418007524800000000000000000000000000000040',
    '201c120000118674200100418014874600000000000000000000000000000040'
  ];

  bool _isOldAttributeSupport() {
    return device.attributeMap.containsKey('tp1StartTimeHH') ||
        device.attributeMap.containsKey('resn1TimeHH');
  }

  bool _isNewAttributeSupport() {
    return device.attributeMap.containsKey('resn1RunningStatus') ||
        device.attributeMap.containsKey('resn2RunningStatus');
  }
}
