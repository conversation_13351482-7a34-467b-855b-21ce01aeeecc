import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

/// 扫地机器人卡片的ViewModel
class SweepingRobotCardViewModel extends DeviceCardViewModel {
  SweepingRobotCardViewModel({required super.device});

  /// 运行中: workState   3 工作中，12扫地中，13拖地中；
  @override
  bool get runningMode {
    final String workState = getDeviceCardAttrValueByName('workState');
    if (workState == '3' || workState == '12' || workState == '13') {
      return true;
    }
    return false;
  }
}
