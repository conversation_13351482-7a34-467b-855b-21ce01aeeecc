/*
 * 描述：卡片基类
 * 作者：songFJ
 * 创建时间：2024/7/19
 */

import 'package:smart_home/device/resize_device_card/resize_base_model.dart';

enum CardType {
  deviceCard,
  curtainCard,
  lightCard,
  securityCard,
  musicCard,
  cameraCard,
  addCard,
  repairCard,
  newUserPackCard,
}

///
/// 外层列表全屋卡片依赖这个字符串.其他大卡片一般使用deviceID作为sortedID
/// whLightSortedId 全屋照明外层列表使用
/// whCurtainSortedId 全屋照明外层列表使用
///
const String whCurtainSortedId = 'cardSortedIDForCurtain';
const String whLightSortedId = 'cardSortedIDForLight';

const String add_device_card_id = 'card_id_add_device';
const String report_for_repair_card_id = 'card_id_report_for_repair';

const String new_user_guide_card_id = 'new_user_guide_card_id';
const String new_user_gift_pack_card_id = 'new_user_gift_pack_card_id';

abstract class CardBaseViewModel extends ResizeBaseModel {
  CardType cardType = CardType.deviceCard;
  bool isSelected = false;
  int traceId = -1;

  CardBaseViewModel(super.key, super.size);

  // 设备排序用的id
  String sortId();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CardBaseViewModel &&
          runtimeType == other.runtimeType &&
          cardType == other.cardType &&
          isSelected == other.isSelected;

  @override
  int get hashCode => cardType.hashCode ^ isSelected.hashCode;
}
