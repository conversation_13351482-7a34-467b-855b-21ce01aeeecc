import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//声光同步盒
class SoundLightSyncBoxViewModel extends DeviceCardViewModel {
  SoundLightSyncBoxViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String syncStatusValue = getDeviceCardAttrValueByName('syncStatus');
    if (syncStatusValue == trueValue) {
      return DeviceCardAttribute(label: '光影律动', value: '开', unit: '');
    } else if (syncStatusValue == falseValue) {
      return DeviceCardAttribute(label: '光影律动', value: '关', unit: '');
    }
    return super.deviceCardAttributeOne();
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    final String syncStatusValue = getDeviceCardAttrValueByName('syncStatus');
    if (syncStatusValue == trueValue) {
      final String syncContentValue =
          getDeviceCardAttrValueByName('syncContent');
      String model = '';
      switch (syncContentValue) {
        case '1':
          model = '视频模式';
        case '2':
          model = '音乐模式';
        case '3':
          model = '游戏模式';
      }
      if (model.isNotEmpty) {
        return DeviceCardAttribute(label: '', value: model, unit: '');
      }
    }

    return null;
  }
}
