class CameraHeaderButtonViewModel {
  final bool isEdit;
  final bool isSelect;

  CameraHeaderButtonViewModel({required this.isEdit, required this.isSelect});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CameraHeaderButtonViewModel &&
          runtimeType == other.runtimeType &&
          isEdit == other.isEdit &&
          isSelect == other.isSelect;

  @override
  int get hashCode => isEdit.hashCode ^ isSelect.hashCode;

  @override
  String toString() {
    return 'CameraHeaderButtonViewModel{isEdit: $isEdit, isSelect: $isSelect}';
  }
}