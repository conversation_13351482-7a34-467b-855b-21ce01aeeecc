import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SolarEnergyCardViewModel extends DeviceCardViewModel {
  SolarEnergyCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String workStatus = device.attributeMap['workStatus']?.value ?? '';
      if (workStatus == '1') {
        return DeviceCardAttribute(label: '', value: '保温', unit: '');
      }
      if (workStatus == '2') {
        return DeviceCardAttribute(label: '', value: '加热', unit: '');
      }
    }
    return null;
  }
}
