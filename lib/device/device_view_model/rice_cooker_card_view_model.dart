import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class RiceCookerCardViewModel extends DeviceCardViewModel {
  RiceCookerCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String startStatus = device.attributeMap['startStatus']?.value ?? '';

    if (startStatus == falseValue) {
      return DeviceCardAttribute(label: '', value: '未启动', unit: '');
    }
    if (startStatus == trueValue) {
      final String heatingStatus =
          device.attributeMap['heatingStatus']?.value ?? '';

      if (heatingStatus == '1') {
        return DeviceCardAttribute(label: '', value: '保温', unit: '');
      }

      final String cookingProgram =
          device.attributeMap['cookingProgram']?.value ?? '';

      if (cookingProgram.isNotEmpty) {
        final String attr = _getMode(cookingProgram);
        return attr.isNotEmpty
            ? DeviceCardAttribute(label: '', value: attr, unit: '')
            : null;
      }
    }

    return null;
  }

  String _getMode(String value) {
    final Map<String, String> map = <String, String>{};
    map['4'] = '杂粮饭';
    map['26'] = '粥';
    map['55'] = '柴火饭';
    map['56'] = '快速饭';
    map['58'] = '煲仔饭';
    map['59'] = '蛋糕';
    map['60'] = '煲汤';
    map['61'] = '热饭';
    map['62'] = '稀饭';
    map['77'] = '蒸煮';
    map['81'] = '云食谱';

    final String returnValue = map[value] ?? '';
    return returnValue;
  }
}
