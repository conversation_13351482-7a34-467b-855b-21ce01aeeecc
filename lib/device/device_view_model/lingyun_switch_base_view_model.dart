import 'package:device_utils/log/log.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import 'lingyun/lingyun_constant.dart';
import 'lingyun/lingyun_model.dart';

/// 凌云开关基础视图模型
class LingYunSwitchBaseViewModel extends DeviceCardViewModel {
  LingYunSwitchBaseViewModel({required super.device});

  final String onOffStatusKey = LingYunSwitchConstants.onOffStatusKey;

  @override
  String get smallCardStatus => '';

  @override
  String get largeCardStatus => '';

  List<LingYunSwitchModel> get switchModelList => <LingYunSwitchModel>[];

  int _index = 0;

  @override
  String get middleCardStatus {
    if (switchModelList.isEmpty) {
      return '';
    }
    final List<String>? deviceIdList = _getSortedDeviceIdList;
    _resetIndex();
    return switchModelList
        .map((LingYunSwitchModel model) =>
            _getSwitchDisplayStatus(model, deviceIdList))
        .join(' ');
  }

  List<ComponentBaseViewModel> _getComponentViewModelList() {
    final List<String>? deviceIdList = _getSortedDeviceIdList;
    _resetIndex();
    return switchModelList
        .map((LingYunSwitchModel model) =>
            createComponentViewModel(model, deviceIdList))
        .toList();
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    if (switchModelList.isEmpty) {
      return null;
    }
    return <String, LargeDeviceCardFunctionSet>{
      LingYunSwitchConstants.switchName: LargeDeviceCardFunctionSet(
        name: LingYunSwitchConstants.switchName,
        componentViewModelList: _getComponentViewModelList(),
      ),
    };
  }

  List<String>? get _getSortedDeviceIdList {
    final List<DeviceCardViewModel> devices =
        childViewModelMap?.values.toList() ?? <DeviceCardViewModel>[];

    if (devices.isEmpty) {
      return null;
    }

    devices.sort((DeviceCardViewModel a, DeviceCardViewModel b) => a
        .device.basicInfo.attachmentSortCode
        .compareTo(b.device.basicInfo.attachmentSortCode));
    return devices.map((DeviceCardViewModel v) => v.deviceId).toList();
  }

  bool _isDeviceControllable() {
    return !(deviceOffline || loading);
  }

  bool _isDeviceOnlineAndSwitchOn(String status) {
    return _isDeviceControllable() && _isSwitchOn(status);
  }

  bool _isSwitchOn(String status) {
    return status == SmartHomeConstant.deviceAttrTrue;
  }

  String _getSwitchStatusText(bool isOn) {
    return isOn
        ? SwitchStatusConstant.switchOn
        : SwitchStatusConstant.switchOff;
  }

  String _getSwitchDisplayStatus(
      LingYunSwitchModel model, List<String>? deviceIdList) {
    final String typeValue = getDeviceCardAttrValueByName(model.switchTypeKey);
    if (typeValue == LingYunSwitchType.ordinary) {
      return _getOrdinarySwitchStatus(deviceIdList);
    } else if (typeValue == LingYunSwitchType.smartLight) {
      return _getSmartLightSwitchStatus(model.alwaysOnStatusKey);
    }
    return SwitchStatusConstant.switchUnknown;
  }

  String _getOrdinarySwitchStatus(List<String>? deviceIdList) {
    final DeviceCardViewModel? cardViewModel =
        _getSwitchDeviceViewModel(deviceIdList);
    if (cardViewModel == null) {
      return SwitchStatusConstant.switchUnknown;
    }
    final String status =
        cardViewModel.getDeviceCardAttrValueByName(onOffStatusKey);
    return _getSwitchStatusText(_isSwitchOn(status));
  }

  String _getSmartLightSwitchStatus(String alwaysOnStatusKey) {
    final String status = getDeviceCardAttrValueByName(alwaysOnStatusKey);
    return _getSwitchStatusText(_isSwitchOn(status));
  }

  SwitchViewModelData _createDefaultSwitchViewModelData(
      LingYunSwitchModel model) {
    return SwitchViewModelData(
      isOn: false,
      enable: false,
      onOffStatusValue: '',
      cardViewModel: null,
      model: model,
    );
  }

  SwitchViewModelData _createOrdinarySwitchViewModelData(
      LingYunSwitchModel model, List<String>? deviceIdList) {
    final DeviceCardViewModel? cardViewModel =
        _getSwitchDeviceViewModel(deviceIdList);
    if (cardViewModel == null) {
      return _createDefaultSwitchViewModelData(model);
    }

    final String onOffStatusValue =
        cardViewModel.getDeviceCardAttrValueByName(onOffStatusKey);
    final bool isOn = _isDeviceOnlineAndSwitchOn(onOffStatusValue);
    final LingYunSwitchModel updatedModel = LingYunSwitchModel(
      switchName: cardViewModel.device.basicInfo.deviceName,
      switchTypeKey: model.switchTypeKey,
      alwaysOnStatusKey: model.alwaysOnStatusKey,
    );

    return SwitchViewModelData(
      isOn: isOn,
      enable: _isDeviceControllable(),
      onOffStatusValue: onOffStatusValue,
      cardViewModel: cardViewModel,
      model: updatedModel,
    );
  }

  SwitchViewModelData _createSmartLightSwitchViewModelData(
      LingYunSwitchModel model) {
    final String status = getDeviceCardAttrValueByName(model.alwaysOnStatusKey);
    final bool isOn = _isDeviceOnlineAndSwitchOn(status);

    return SwitchViewModelData(
      isOn: isOn,
      enable: false,
      onOffStatusValue: status,
      cardViewModel: null,
      model: model,
    );
  }

  SwitchViewModelData _createSwitchViewModelData(
      LingYunSwitchModel model, List<String>? deviceIdList) {
    final String typeValue = getDeviceCardAttrValueByName(model.switchTypeKey);
    if (typeValue == LingYunSwitchType.ordinary) {
      return _createOrdinarySwitchViewModelData(model, deviceIdList);
    } else if (typeValue == LingYunSwitchType.smartLight) {
      return _createSmartLightSwitchViewModelData(model);
    }

    return _createDefaultSwitchViewModelData(model);
  }

  ComponentBaseViewModel _buildSwitchViewModel(SwitchViewModelData data) {
    return ExpandSwitchIconTextViewModel(
      icon: LingYunSwitchConstants.switchIconPath,
      text: data.model.switchName,
      enable: data.enable,
      isOn: data.isOn,
      clickCallback: (BuildContext context) => _handleSwitchClick(
          data.enable, data.cardViewModel, data.onOffStatusValue),
    );
  }

  ComponentBaseViewModel createComponentViewModel(
      LingYunSwitchModel model, List<String>? deviceIdList) {
    final SwitchViewModelData data =
        _createSwitchViewModelData(model, deviceIdList);
    return _buildSwitchViewModel(data);
  }

  String _getNextSwitchState(String currentState) {
    return currentState == SmartHomeConstant.deviceAttrTrue
        ? SmartHomeConstant.deviceAttrFalse
        : SmartHomeConstant.deviceAttrTrue;
  }

  void _handleSwitchClick(bool enable, DeviceCardViewModel? cardViewModel,
      String onOffStatusValue) {
    checkDeviceState(
            checkPowerOff: false,
            writable: enable,
            writableMsg: LingYunSwitchConstants.writableMsg)
        .then((bool pass) {
      _switchClickGio();
      if (!_validateSwitchState(pass, cardViewModel)) {
        return;
      }
      _sendSwitchCommand(cardViewModel!, onOffStatusValue);
    });
  }

  void _sendSwitchCommand(
      DeviceCardViewModel cardViewModel, String onOffStatusValue) {
    try {
      final Map<String, String> commands = <String, String>{};
      commands[onOffStatusKey] = _getNextSwitchState(onOffStatusValue);

      quickCtrlLECommand(
        cardViewModel.deviceId,
        commands,
        onErrorCallback: (String errMsg) {
          ToastHelper.showToast(errMsg);
        },
      );
    } catch (e) {
      DevLogger.error(
        tag: 'LingYunSwitchBaseViewModel',
        msg: 'Failed to send switch command: $e',
      );
    }
  }

  void _switchClickGio() {
    gio(cloudProgramName: LingYunSwitchConstants.switchName);
  }

  bool _validateSwitchState(bool pass, DeviceCardViewModel? cardViewModel) {
    if (!pass || cardViewModel == null) {
      DevLogger.debug(
        tag: 'LingYunSwitchBaseViewModel',
        msg: 'pass:$pass cardViewModel==null:${cardViewModel == null}',
      );
      return false;
    }
    return true;
  }

  DeviceCardViewModel? _getSwitchDeviceViewModel(List<String>? deviceIdList) {
    if (deviceIdList == null || _index >= deviceIdList.length) {
      return null;
    }
    return childViewModelMap?[deviceIdList[_index++]];
  }

  void _resetIndex() {
    _index = 0;
  }
}
