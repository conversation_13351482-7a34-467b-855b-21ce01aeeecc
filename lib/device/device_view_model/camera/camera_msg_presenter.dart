import 'dart:convert';

import 'package:device_utils/log/log.dart';
import 'package:device_utils/task_queue/task_queue.dart';
import 'package:smart_home/device/device_view_model/camera/service/camera_msg_response_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:storage/storage.dart';

import '../../../common/constant.dart';
import '../camera_view_model.dart';

const String _keyAttNameCardSizedChanged = 'cameraCardSizeChanged';

const String _keyAttNameShowCameraMsg = 'showCameraLatestMsgChanged';

const String _enableShowCameraFromServer = '1';
const int _delayMilliseconds = 150;
const int _maxMsgSize = 3;

class CameraMsgPresenter {
  static String tag = 'CameraMsgPresenter';

  CameraMsgPresenter._internal();

  // 静态实例
  static final CameraMsgPresenter _instance = CameraMsgPresenter._internal();

  // 提供一个全局访问点
  static CameraMsgPresenter get instance => _instance;

  void addListenerForCameraMsg() {
    Storage.addNodeListner(_keyAttNameCardSizedChanged, _cardSizeListener);
    Storage.addNodeListner(_keyAttNameShowCameraMsg, _showCameraMsgListener);
  }

  void removeListenerForCameraMsg() {
    Storage.removeNodeListener(
        _keyAttNameShowCameraMsg, _showCameraMsgListener);
    Storage.removeNodeListener(_keyAttNameCardSizedChanged, _cardSizeListener);
  }

  void _showCameraMsgListener(String attrName, String actonType) {
    DevLogger.info(
        tag: tag,
        msg: '_showCameraMsgListener key is $attrName,action is $actonType ');
    smartHomeStore.dispatch(FetchCameraMsgAction());
  }

  void _cardSizeListener(String attrName, String actonType) {
    DevLogger.info(
        tag: tag,
        msg: '_cardSizeListener key is $attrName,action is $actonType');
  }

  Future<void> getCameraMsgList(
      Set<String> params1,
      void Function(Map<String, CameraMsgVM>) callback,
      String currentFamily) async {
    if (params1.isEmpty) {
      return Future<void>.value();
    }

    final List<Set<String>> splitParams = _splitSet(params1, 10);
    TaskQueueUtil.get(tag).cancelTask();
    TaskQueueUtil.get(tag).addTask(() async {
      for (final Set<String> subset in splitParams) {
        final Map<String, CameraMsgVM> value =
            await _getCameraMsgList2(subset, currentFamily);
        if (value.isNotEmpty) {
          callback(value);
        }
        await Future<void>.delayed(
            const Duration(milliseconds: _delayMilliseconds));
      }
    });

    return Future<void>.value();
  }

  static List<Set<String>> _splitSet(Set<String> originalSet, int maxSize) {
    final List<Set<String>> subsets = <Set<String>>[];
    final List<String> paramsList = originalSet.toList();

    for (int i = 0; i < paramsList.length; i += maxSize) {
      int end = i + maxSize;
      if (end > paramsList.length) {
        end = paramsList.length;
      }
      subsets.add(Set<String>.from(paramsList.sublist(i, end)));
    }

    return subsets;
  }

  static Future<Map<String, CameraMsgVM>> _getCameraMsgList2(
      Set<String> params, String currentFamily) async {
    final Map<String, CameraMsgVM> result = <String, CameraMsgVM>{};
    try {
      final CameraMsgResponseModel? responseModel =
          await HttpService.getCamerasMsg(params, currentFamily);
      if (responseModel != null) {
        if (responseModel.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
          if (responseModel.data.data.isNotEmpty) {
            responseModel.data.data.forEach((CameraMsgList element) {
              if (element.deviceId.isNotEmpty) {
                final CameraMsgVM tmp = CameraMsgVM();
                tmp.isShowTips =
                    element.switchStatus == _enableShowCameraFromServer;
                if (element.msgs.isNotEmpty) {
                  int size = 0;
                  element.msgs.forEach((CameraMsgListItem element) {
                    final CameraMsgItem item = CameraMsgItem(
                        formatTime(element.msgTime),
                        element.msgTime,
                        element.picUrl);
                    if (size < _maxMsgSize) {
                      tmp.msgs.add(item);
                    }
                    size++;
                  });
                }
                result[element.deviceId] = tmp;
              }
            });
          }
        }
      }
    } catch (e) {
      DevLogger.info(
          tag: SmartHomeConstant.package,
          msg: 'getCameraMsgList2 occur error $e ');
    }
    return result;
  }

  static String formatTime(String timestampString) {
    final int timestamp = int.tryParse(timestampString) ?? 0;
    final DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final String hours = dateTime.hour.toString().padLeft(2, '0');
    final String minutes = dateTime.minute.toString().padLeft(2, '0');
    final String formattedTime = '$hours:$minutes';
    return formattedTime;
  }
}
