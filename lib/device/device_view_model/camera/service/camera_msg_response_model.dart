import 'dart:convert';

import 'package:upservice/map_analysis_extension/map_analysis_extension.dart';

class CameraMsgResponseModel {
  String _retCode = '';
  String _retInfo = '';
  String get retCode => _retCode;
  String get retInfo => _retInfo;

  CameraMsgResponseModel.fromJson(dynamic data) {
    Map<dynamic, dynamic> json = <dynamic, dynamic>{};
    if (data is String) {
      json = convertType<Map<dynamic, dynamic>>(
          jsonDecode(data), <dynamic, dynamic>{});
    } else if (data is Map) {
      json = data;
    }
    _retCode = json.stringValueForKey('retCode', '');
    _retInfo = json.stringValueForKey('retInfo', '');
    this.data = CameraMsgResponseModel.fromJsonx(data) ??
        CameraMsgResponseData.fromJson(<dynamic, dynamic>{});
  }

  static CameraMsgResponseData? fromJsonx(dynamic data) {
    return data is Map<dynamic, dynamic>
        ? CameraMsgResponseData.fromJson(data)
        : null;
  }

  CameraMsgResponseData data =
      CameraMsgResponseData.fromJson(<dynamic, dynamic>{});

  @override
  String toString() {
    return 'CameraMsgResponseModel{data: ${super.toString()} retCode = $retCode , retInfo = $retInfo , ${data.toString()}';
  }
}

class CameraMsgListItem {
  String picUrl = '';

  String msgTime = '';

  CameraMsgListItem.fromJson(Map<dynamic, dynamic> json) {
    picUrl = json.stringValueForKey('picUrl', '');
    msgTime = json['msgTime']?.toString() ?? '';
  }

  @override
  String toString() {
    return 'CameraMsgListItem{picUrl: $picUrl, msgTime: $msgTime}';
  }
}

class CameraMsgList {
  String deviceId = '';
  String switchStatus = '';
  List<CameraMsgListItem> msgs = <CameraMsgListItem>[];

  CameraMsgList.fromJson(Map<dynamic, dynamic> json) {
    deviceId = json.stringValueForKey('deviceId', '');
    switchStatus = json.stringValueForKey('switchStatus', '');

    final List<dynamic> list = json.listValueForKey('msgs', <dynamic>[]);
    list.forEach((dynamic element) {
      if (element is Map) {
        msgs.add(CameraMsgListItem.fromJson(element));
      }
    });
  }

  @override
  String toString() {
    return 'CameraMsgList{deviceId: $deviceId, msgs: $msgs}';
  }
}

class CameraMsgResponseData {
  List<CameraMsgList> data = <CameraMsgList>[];

  CameraMsgResponseData.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> list = json.listValueForKey('data', <dynamic>[]);
    list.forEach((dynamic element) {
      if (element is Map) {
        data.add(CameraMsgList.fromJson(element));
      }
    });
  }

  @override
  String toString() {
    return 'CameraMsgResponseData{CameraMsgResponseData: $data}';
  }
}
