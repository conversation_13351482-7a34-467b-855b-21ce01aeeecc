import 'package:flutter/material.dart';
const int _playDelayTimes = 2;

enum CameraStreamType {
  main,
  secondary,
}

enum CameraStreamCount{
  one,
  two
}

abstract class SmartHomeCameraPlayerInterface {
  void init();

  void addListener(SmartHomeCameraPlayerInterfaceListener listener);

  void removeListener(SmartHomeCameraPlayerInterfaceListener listener);

  Widget createPlayer(double w, double h,
      {Orientation orientation = Orientation.portrait});

  void dispose();

  void play();

  void mute(bool mute);

  void destroyPlayer();

  bool isMute();

  void stop();

  Future<bool> goToFullScreenCameraPage(BuildContext context) async {
    return Future<bool>.value(false);
  }

  bool switchCameraStream() {
    return false;
  }

  void setCameraStream(CameraStreamType streamType) {}

  CameraStreamType getCameraStream() {
    return CameraStreamType.main;
  }

  int getPlayDelayTimes() {
    return _playDelayTimes;
  }
}

abstract class SmartHomeCameraPlayerInterfaceListener {
  void onCameraPlayerError(String error);

  void onCameraPlayerPlaying();

  void onCameraPlayerStopped();
}
