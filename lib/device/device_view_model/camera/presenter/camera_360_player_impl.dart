import 'dart:async';

import 'package:cx_player/cx_player.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_widget/camera_land_play.dart';

class Camera360Player extends SmartHomeCameraPlayerInterface {
  final String _deviceId;

  final CameraStreamCount _cameraStreamCount;

  final String _tag = 'Camera360Player';

  Camera360Player(this._deviceId, this._cameraStreamCount);

  late ILivePlayer _player;
  @override
  int getPlayDelayTimes() {
    return 0;
  }

  StreamSubscription<PlayState>? _subscription;

  PlayState currentPlayerState = PlayState.idle;

  SmartHomeCameraPlayerInterfaceListener? _callback;

  @override
  void addListener(SmartHomeCameraPlayerInterfaceListener listener) {
    _callback = listener;
  }

  void _fitVideoView(Widget? view) {
    if (view != null && view is IVideoView) {
      (view as IVideoView).setFit(BoxFit.cover);
    }
  }

  Widget? _fitVideoViewByOrientation(
      CameraStreamType streamType, Orientation orientation) {
    final Widget? playerView = streamType == CameraStreamType.main
        ? _player.getVideoView()
        : _player.getExtraVideoView();
    if (orientation == Orientation.portrait) {
      _fitVideoView(playerView);
    }
    return playerView;
  }

  @override
  Widget createPlayer(double w, double h,
      {Orientation orientation = Orientation.portrait}) {
    final Widget? retPlayer =
        _fitVideoViewByOrientation(_streamType, orientation);
    DevLogger.debug(
        tag: _tag,
        msg:
            ' create player [${retPlayer?.runtimeType}] , _streamType = $_streamType , orientation = $orientation $_deviceId');
    return retPlayer ?? const SizedBox.shrink();
  }

  @override
  void dispose() {
    DevLogger.debug(tag: _tag, msg: ' dispose $_deviceId');
    _player.destroy();
    _subscription?.cancel();
  }

  bool get isSupportMultiCameraStream => _cameraStreamCount == CameraStreamCount.two;

  @override
  void init() {
    _subscription?.cancel();
    _player = ILivePlayer.create(_deviceId,
        isDual: isSupportMultiCameraStream);
    _subscription = _player.observePlayState().listen((PlayState event) {
      DevLogger.debug(
          tag: _tag, msg: ' camera player status $event $_deviceId');
      currentPlayerState = event;
      if (event == PlayState.playing) {
        _callback?.onCameraPlayerPlaying();
      } else if (event == PlayState.error) {
        _callback?.onCameraPlayerError('');
      } else if (event == PlayState.loading) {
        mute(true);
      } else if (event == PlayState.idle) {
        _callback?.onCameraPlayerStopped();
      }
    });
  }

  @override
  void mute(bool mute) {
    DevLogger.debug(tag: _tag, msg: ' mute $_deviceId');
    if (mute) {
      _player.mute();
    } else {
      _player.unMute();
    }
  }

  @override
  void play() {
    DevLogger.debug(tag: _tag, msg: ' play $_deviceId');
    _player.startPlay();
  }

  @override
  void removeListener(SmartHomeCameraPlayerInterfaceListener listener) {
    DevLogger.debug(tag: _tag, msg: ' removeListener $_deviceId');
    _subscription?.cancel();
  }

  @override
  void stop() {
    _player.stopPlay();
  }

  @override
  void destroyPlayer() {
    DevLogger.debug(
        tag: _tag,
        msg: ' destroyPlayer ,but this player can not do it , $_deviceId');
  }

  CameraStreamType _streamType = CameraStreamType.main;

  @override
  void setCameraStream(CameraStreamType streamType) {
    DevLogger.debug(
        tag: _tag,
        msg:
            ' setCameraStream _deviceId = $_deviceId , currentStream is $_streamType , set stream is $streamType');
    _streamType = streamType;
  }

  @override
  CameraStreamType getCameraStream() {
    return _streamType;
  }

  @override
  bool switchCameraStream() {
    if (isSupportMultiCameraStream) {
      if (_streamType == CameraStreamType.main) {
        _streamType = CameraStreamType.secondary;
      } else {
        _streamType = CameraStreamType.main;
      }
      return true;
    }
    return false;
  }

  @override
  Future<bool> goToFullScreenCameraPage(BuildContext context) async {
    DevLogger.debug(
        tag: _tag,
        msg:
            ' goToFullScreenCameraPage _deviceId = $_deviceId, isSupportMultiCameraStream = $isSupportMultiCameraStream , _streamType = $_streamType');
    final CameraStreamType? popCameraStream = await showCameraLandPlay(
        context, _deviceId, _cameraStreamCount, _player, _streamType);
    bool needRefresh = false;
    if (isSupportMultiCameraStream) {
      if (popCameraStream != null) {
        if (popCameraStream == _streamType) {
          needRefresh = false;
        } else {
          setCameraStream(popCameraStream);
          needRefresh = true;
        }
      }
    }
    DevLogger.debug(
        tag: _tag,
        msg:
            ' goToFullScreenCameraPage back2card _deviceId = $_deviceId, isSupportMultiCameraStream = $isSupportMultiCameraStream , _streamType = $_streamType , pop stream is [$popCameraStream], refresh is $needRefresh');
    return needRefresh;
  }

  @override
  bool isMute() {
    return _player.isMute();
  }
}
