import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:videoview/iot_liveplayers/iot_players_helper.dart';
import 'package:videoview/iot_view_factory.dart';

class TencentCameraPlayer extends SmartHomeCameraPlayerInterface
    implements IotLivePlayerCreatedListener, IotLivePlayerListener {
  final String _deviceId;

  final String _tag = 'TencentCameraPlayer';

  TencentCameraPlayer(this._deviceId);

  final IotNativeWidgetFactory _factoryManager = IotNativeWidgetFactory();

  SmartHomeCameraPlayerInterfaceListener? callback;

  @override
  void addListener(SmartHomeCameraPlayerInterfaceListener listener) {
    callback = listener;
  }

  Widget? player;
  String presenterKey = '';
  IotFactoryMessageListener? _mListener;

  @override
  void init() {
    IotNativeWidgetFactory();
    final DateTime now = DateTime.now();
    final int timestamp = now.millisecondsSinceEpoch;
    presenterKey = '${CameraConstant.playerKeyPrefix}${timestamp}_$_deviceId';
    _mListener = IotFactoryLivePlayerListener(this, this);
    _factoryManager.addListener(presenterKey, _mListener,
        isOverrideListener: true);
  }

  Widget _createPlayer(double playerWidth, double playerHeight) {
    player ??= IotNativeWidgetFactory().createWidget(
      IotNativeWidgetFactory.VIEW_PLAYER_NAME,
      _deviceId,
      presenterKey,
      playerWidth,
      playerHeight,
    );
    DevLogger.debug(
        tag: _tag, msg: ' create player [${player?.hashCode}] $_deviceId');
    return player!;
  }

  @override
  Widget createPlayer(double w, double h,
      {Orientation orientation = Orientation.portrait}) {
    player ??= _createPlayer(w, h);
    DevLogger.debug(
        tag: _tag,
        msg:
            ' currentPlayer [${player?.hashCode}] $_deviceId $hashCode , h = $h w = $w');
    return player ?? const SizedBox.shrink();
    ;
  }

  @override
  void dispose() {
    callback = null;
    DevLogger.debug(tag: _tag, msg: ' dispose $_deviceId');
    _factoryManager.removeListener(presenterKey);
  }

  @override
  void removeListener(SmartHomeCameraPlayerInterfaceListener listener) {
    callback = null;
  }

  bool _isMute = false;

  @override
  void mute(bool mute) {
    try {
      DevLogger.debug(tag: _tag, msg: ' mute $_deviceId');
      _factoryManager.livePlayerHelper?.mute(_deviceId, presenterKey, mute);
      _isMute = mute;
    } catch (e) {
      DevLogger.debug(
          tag: _tag, msg: '$_tag completed  mute device error $_deviceId ');
    }
  }

  @override
  void play() {
    try {
      DevLogger.debug(tag: _tag, msg: ' play $_deviceId');
      _factoryManager.livePlayerHelper?.play(_deviceId, presenterKey);
    } catch (e) {
      DevLogger.info(
          tag: _tag, msg: 'camera_live_widget livePlayerHelper play error $e ');
    }
  }

  @override
  void destroyPlayer() {
    try {
      DevLogger.debug(tag: _tag, msg: ' destroyPlayer $_deviceId');
      _factoryManager.livePlayerHelper?.destroyPlayer(_deviceId, presenterKey);
      player = null;
    } catch (e) {
      DevLogger.debug(
          tag: _tag, msg: '$_tag destroyPlayer $_deviceId error $e');
    }
  }

  @override
  void stop() {
    DevLogger.debug(tag: _tag, msg: ' stop $_deviceId');
    _factoryManager.livePlayerHelper?.stop(_deviceId, presenterKey);
  }

  bool _isCreatePlayerSuccess = false;

  @override
  void completed(
      IotPlayerCompleteStatus status, String deviceId, dynamic resultData) {
    if (deviceId != _deviceId) {
      return;
    }
    if (player == null) {
      return;
    }
    DevLogger.debug(tag: _tag, msg: ' camera completed $status $deviceId');
    if (status != IotPlayerCompleteStatus.CREATE_SUCCESS) {
      _isCreatePlayerSuccess = false;
      callback?.onCameraPlayerError('$resultData');
    } else {
      _isCreatePlayerSuccess = true;
      mute(true);
    }
  }

  @override
  void playerStatusChanged(
      String deviceId, IotPlayerListenerStatus status, dynamic resultData) {
    if (deviceId != _deviceId) {
      return;
    }
    if (player == null) {
      return;
    }
    DevLogger.debug(
        tag: _tag, msg: ' camera playerStatusChanged $status $deviceId');
    if (status == IotPlayerListenerStatus.PLAY) {
      ////开流上报埋点
      callback?.onCameraPlayerPlaying();
      return;
    }

    if (status == IotPlayerListenerStatus.STOP && _isCreatePlayerSuccess) {
      callback?.onCameraPlayerStopped();
    }
  }

  @override
  bool isMute() {
    return _isMute;
  }
}
