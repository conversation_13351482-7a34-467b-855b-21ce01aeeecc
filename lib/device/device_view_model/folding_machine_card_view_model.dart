import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class FoldingMachineCardViewModel extends DeviceCardViewModel {
  FoldingMachineCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'workStatus';
    final String value = device.attributeMap[key]?.value ?? '';
    String attr = '';
    if (value == '0') {
      attr = '空闲';
    } else if (value == '1') {
      attr = '初始化';
    } else if (value == '2') {
      attr = '待机';
    } else if (value == '3') {
      attr = '检测中';
    } else if (value == '4') {
      attr = '检测结束';
    } else if (value == '5') {
      attr = '送衣中';
    } else if (value == '6') {
      attr = '叠衣中';
    } else if (value == '7') {
      attr = '叠衣结束';
    }
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }
}
