import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SwitchBaseViewModel extends DeviceCardViewModel {
  SwitchBaseViewModel({required super.device});

  String switchTypeOrdinary = '1';
  String switchTypeScene = '2';

  @override
  String get smallCardStatus => '';

  @override
  String get largeCardStatus => '';

  List<ComponentBaseViewModel> getComponentViewModelList() {
    return <ComponentBaseViewModel>[];
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '开关';
    return <String, LargeDeviceCardFunctionSet>{
      name: LargeDeviceCardFunctionSet(
          name: name, componentViewModelList: getComponentViewModelList())
    };
  }

  List<String>? get getSwitchName {
    final List<DeviceCardViewModel> device = <DeviceCardViewModel>[];
    childViewModelMap?.forEach((String key,DeviceCardViewModel viewModel) {
      device.add(viewModel);
    });

    if(device.isEmpty) {
      return null;
    }

    //对设备进行增序排序
    device.sort((DeviceCardViewModel a, DeviceCardViewModel b) {
      return a.device.basicInfo.attachmentSortCode.compareTo(b.device.basicInfo.attachmentSortCode);
    });

    return  device.map((DeviceCardViewModel viewModel) => viewModel.deviceName).toList();
  }


  String switchStatus(
      String switchTypeKey, String alwaysOnStatusKey, String onOffStatusKey) {
    String switchStatus = '-';
    final String onOffStatusValue =
        getDeviceCardAttrValueByName(onOffStatusKey);
    final String switchTypeValue = getDeviceCardAttrValueByName(switchTypeKey);
    final String alwaysOnStatusValue =
        getDeviceCardAttrValueByName(alwaysOnStatusKey);

    if (switchTypeValue == switchTypeOrdinary) {
      if (onOffStatusValue == SmartHomeConstant.deviceAttrTrue) {
        switchStatus = '开';
      } else if (onOffStatusValue == SmartHomeConstant.deviceAttrFalse) {
        switchStatus = '关';
      }
    } else if (switchTypeValue == switchTypeScene) {
      if (alwaysOnStatusValue == SmartHomeConstant.deviceAttrTrue) {
        switchStatus = '开';
      } else if (alwaysOnStatusValue == SmartHomeConstant.deviceAttrFalse) {
        switchStatus = '关';
      }
    }
    return switchStatus;
  }

  ComponentBaseViewModel switchComponentViewModel(
      String switchTypeKey, String alwaysOnStatusKey, String onOffStatusKey,String switchName,
      {int expandFlex = 1}) {
    bool _isOn = false;
    bool _enable = false;
    final String onOffStatusValue =
        getDeviceCardAttrValueByName(onOffStatusKey);
    String writableMsg = '按键不可操控';

    if (!deviceOffline && !loading) {
      final String switchTypeValue =
          getDeviceCardAttrValueByName(switchTypeKey);
      final String alwaysOnStatusValue =
          getDeviceCardAttrValueByName(alwaysOnStatusKey);

      if (switchTypeValue == switchTypeOrdinary) {
        if (onOffStatusValue == SmartHomeConstant.deviceAttrTrue) {
          _enable = true;
          _isOn = true;
        } else if (onOffStatusValue == SmartHomeConstant.deviceAttrFalse) {
          _enable = true;
          _isOn = false;
        }
      } else if (switchTypeValue == switchTypeScene) {
        if (alwaysOnStatusValue == SmartHomeConstant.deviceAttrTrue) {
          _isOn = true;
        }
        if (alwaysOnStatusValue.isNotEmpty) {
          writableMsg = '已转场景按键，不可操控';
        }
      }
    }

    return ExpandSwitchIconTextViewModel(
        icon: 'assets/components/switch_icon.webp',
        text: switchName,
        enable: _enable,
        isOn: _isOn,
        expandFlex: expandFlex,
        clickCallback: (BuildContext context) {
          checkDeviceState(
                  checkPowerOff: false,
                  writable: _enable,
                  writableMsg: writableMsg)
              .then((bool pass) {
            gio(cloudProgramName: '开关按键');
            if (pass) {
              final Map<String, String> commands = <String, String>{};
              if (onOffStatusValue == SmartHomeConstant.deviceAttrTrue) {
                commands[onOffStatusKey] = SmartHomeConstant.deviceAttrFalse;
              } else if (onOffStatusValue ==
                  SmartHomeConstant.deviceAttrFalse) {
                commands[onOffStatusKey] = SmartHomeConstant.deviceAttrTrue;
              }

              quickCtrlLECommand(device.basicInfo.deviceId, commands,
                  onErrorCallback: (String errMsg) {
                ToastHelper.showToast(errMsg);
              });
            }
          });
        });
  }
}
