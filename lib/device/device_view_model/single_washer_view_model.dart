import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';

import 'device_card_view_model.dart';

// 单抽洗碗机
class SingleWasherViewModel extends DeviceCardViewModel {
  SingleWasherViewModel({required super.device});

  int getRemainTime() {
    final String remainHH = getDeviceCardAttrValueByName('remainingTimeHH');
    final String remainMM = getDeviceCardAttrValueByName('remainingTimeMM');
    final int hour = int.tryParse(remainHH) ?? 0;
    final int minute = int.tryParse(remainMM) ?? 0;
    final int remainTime = hour * 60 + minute;
    return remainTime;
  }

  /// 已开机且有剩余时间时
  /// 开关机上报"开机" :
  ///  1.1条件：当前"运行阶段(cyclePhase )"有值且不为"0"和“11”时
  ///  1.2条件：当前"运行阶段(cyclePhase )"为"12"时
  @override
  bool get runningMode {
    final String onOffStatus =
        getDeviceCardAttrValueByName(SmartHomeConstant.deviceOnOffStatus);
    final String cyclePhase = getDeviceCardAttrValueByName('cyclePhase');
    if (onOffStatus == SmartHomeConstant.deviceAttrTrue) {
      final int remainTime = getRemainTime();
      if (cyclePhase.isNotEmpty) {
        if (cyclePhase != '0' && cyclePhase != '11' && remainTime > 0) {
          return true;
        }
        if (cyclePhase == '12' && remainTime > 0) {
          return true;
        }
      }
    }

    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String onOffStatus =
        getDeviceCardAttrValueByName(SmartHomeConstant.deviceOnOffStatus);
    if (onOffStatus.isEmpty ||
        onOffStatus == SmartHomeConstant.deviceAttrFalse) {
      return super.deviceCardAttributeOne();
    }

    final String cyclePhase = getDeviceCardAttrValueByName('cyclePhase');
    if (cyclePhase == '0' || cyclePhase == '11') {
      return DeviceCardAttribute(label: '', value: '待机', unit: '');
    }

    if (cyclePhase == '16') {
      final String intelligentStoreTime =
          getDeviceCardAttrValueByName('intelligentStoreTime');
      if (intelligentStoreTime.isEmpty || intelligentStoreTime == '0') {
        return super.deviceCardAttributeOne();
      }
      return DeviceCardAttribute(
          label: '智存中|剩余', value: intelligentStoreTime, unit: 'day');
    }

    final int remainTime = getRemainTime();

    if (remainTime == 0) {
      return null;
    }

    if (cyclePhase == '12') {
      return DeviceCardAttribute(
          label: '预约中', value: '剩$remainTime', unit: 'min');
    }

    return cyclePhase.isEmpty
        ? null
        : DeviceCardAttribute(label: '剩余', value: '$remainTime', unit: 'min');
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }
}
