import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CenterSoftWaterCardViewModel extends DeviceCardViewModel {
  CenterSoftWaterCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['operationMode']?.value ?? '';
    final String attr = value.isEmpty ? '' : _getRunningMode(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    final String value = device.attributeMap['softWaterQuality']?.value ?? '';
    if (value.isEmpty) {
      return null;
    }
    final String attr = _getSoftWaterQuality(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  String _getRunningMode(String value) {
    String mode = '';
    switch (value) {
      case '1':
        mode = '供水中';
      case '2':
        mode = '补水中';
      case '3':
        mode = '反洗中';
      case '4':
        mode = '再生中';
      case '5':
        mode = '正洗中';
      default:
        break;
    }
    return mode;
  }

  String _getSoftWaterQuality(String value) {
    String returnValue = '';
    if (value == '1') {
      returnValue = '柔软';
    } else if (value == '2') {
      returnValue = '舒适';
    } else if (value == '3') {
      returnValue = '一般';
    }

    return returnValue;
  }
}
