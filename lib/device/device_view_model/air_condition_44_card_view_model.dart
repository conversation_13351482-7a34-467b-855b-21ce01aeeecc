import 'package:smart_home/device/device_info_model/device_card_attribute.dart';

import 'air_condition_base_view_model.dart';

class AirCondition44CardViewModel extends AirConditionBaseViewModel {
  AirCondition44CardViewModel({required super.device});

  @override
  String get smallCardStatus {
    if (powerOff) {
      return '关';
    }
    if (powerOn) {
      return '开';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      const String key = 'indoorTemperature';
      final String value = device.attributeMap[key]?.value ?? '';
      return value.isNotEmpty
          ? DeviceCardAttribute(label: '室温', value: value, unit: '℃')
          : null;
    }
    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (powerOn) {
      final String value = device.attributeMap['operationMode']?.value ?? '';
      final String attr = _getModeText(value);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    }
    return null;
  }

  String _getModeText(String valueKey) {
    if (valueKey.isEmpty) {
      return valueKey;
    }
    switch (valueKey) {
      case '0':
        return '智能';
      case '1':
        return '制冷';
      case '2':
        return '除湿';
      case '3':
        return '健康除湿';
      case '5':
        return '节能';
      case '6':
        return '送风';
      default:
        return '';
    }
  }
}
