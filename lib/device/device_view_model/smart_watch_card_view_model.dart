import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SmartWatchCardViewModel extends DeviceCardViewModel {
  SmartWatchCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['steps']?.value ?? '';
    return value.isNotEmpty
        ? DeviceCardAttribute(label: '步数', value: value, unit: '')
        : null;
  }
}
