import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/air_condition_card_view_model.dart';
import 'package:smart_home/device/device_view_model/home_central_air_condition_view_model.dart';

class HomeCentralAirCondition040ViewModel extends HomeCentralAirConditionViewModel {
  HomeCentralAirCondition040ViewModel({required super.device});

  bool get isIntelligence {
    return operationModeAttribute != null &&
        operationModeAttribute!.valueRange.dataList.any(
                (SmartHomeDataItem dataItem) =>
            dataItem.data == '0' && dataItem.desc == intelligence);
  }

  @override
  AirConditionModeModel get operationMode0Model {
    if (isIntelligence) {
      return AirConditionModeModel(
          value: '0',
          desc: intelligence,
          icon: 'assets/components/mode/mode_intellect.webp');
    } else {
      return AirConditionModeModel(
          value: '0',
          desc: pmv,
          icon: 'assets/components/mode/mode_pmv.webp');
    }
  }
}