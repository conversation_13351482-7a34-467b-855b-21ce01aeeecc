import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';

import '../resize_device_card/resize_base_model.dart';

class AddDeviceCardViewModel extends CardBaseViewModel {
  AddDeviceCardViewModel()
      : super(const ValueKey<String>(add_device_card_id),
            DeviceCardType.middleCard) {
    title = '添加设备';
    buttonIconUrl = 'assets/icons/device_add_icon.webp';
    questionIconUrl = 'assets/icons/question.webp';
    buttonText = '去添加';
    activityJsonPath = 'assets/theme/summer_activity_small.json';
  }

  @override
  CardType get cardType => CardType.addCard;

  String title = '';
  String buttonIconUrl = '';
  String questionIconUrl = '';
  String buttonText = '';
  String activityJsonPath = '';

  void cardClick() {
    gioTrack(SmartHomeConstant.addDeviceCardClickGio);
    goToPageWithDebounce(SmartHomeConstant.vdnBindPage);
  }

  void questionClick() {
    gioTrack(SmartHomeConstant.addDeviceCardBindGuideClickGio);
    goToPageWithDebounce(SmartHomeConstant.vdnBindGuidePage);
  }

  @override
  String sortId() {
    return add_device_card_id;
  }
}
