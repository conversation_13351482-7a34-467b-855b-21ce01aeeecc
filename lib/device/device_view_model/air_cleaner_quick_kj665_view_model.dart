import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_quick_card_view_model.dart';

//空气净化器
class AirCleanerQuickKj665ViewModel extends AirCleanerQuickCardViewModel {
  AirCleanerQuickKj665ViewModel({required super.device});

  @override
  Map<String, String> get allAirQualityMap => <String, String>{
        '0': '优',
        '1': '良',
        '2': '中',
        '3': '差',
      };

  @override
  String get windSpeedAttrKey => 'windSpeedLevel';

  @override
  String get autoModeAttrKey => 'operationMode';

  @override
  String get autoModeAttrValue => '2';

  @override
  String get sleepModeAttrKey => 'sleepMode';

  @override
  String get sleepModeAttrValue => 'true';
}
