import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CloseesToolCardViewModel extends DeviceCardViewModel {
  CloseesToolCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['seatStatus']?.value ?? '';
    String attr = '';
    if (value == trueValue) {
      attr = '有人';
    } else if (value == falseValue) {
      attr = '无人';
    }

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }
}
