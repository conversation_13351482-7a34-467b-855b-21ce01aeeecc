import 'package:smart_home/device/component/view_model/expand_empty_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/switch_base_view_model.dart';

//一键智能开关
class SwitchOneKeyViewModel extends SwitchBaseViewModel {
  SwitchOneKeyViewModel({required super.device});

  final String switchTypeKey = 'switchType';
  final String alwaysOnStatusKey = 'alwaysOnStatus';
  final String onOffStatusKey = 'onOffStatus';

  @override
  String get middleCardStatus =>
      switchStatus(switchTypeKey, alwaysOnStatusKey, onOffStatusKey);

  @override
  List<ComponentBaseViewModel> getComponentViewModelList() {
    final List<String>? switchNames = getSwitchName;
    final String switchName1 =
    switchNames != null && switchNames.isNotEmpty ? switchNames[0] : '';
    return <ComponentBaseViewModel>[
      ExpandEmptyViewModel(),
      switchComponentViewModel(
          switchTypeKey, alwaysOnStatusKey, onOffStatusKey, switchName1,
          expandFlex: 2),
      ExpandEmptyViewModel()
    ];
  }
}
