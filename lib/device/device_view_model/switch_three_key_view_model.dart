import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/switch_base_view_model.dart';

//三键智能开关
class SwitchThreeKeyViewModel extends SwitchBaseViewModel {
  SwitchThreeKeyViewModel({required super.device});

  final String switchType1Key = 'switchType1';
  final String alwaysOnStatus1Key = 'alwaysOnStatus1';
  final String onOffStatus1Key = 'onOffStatus1';

  final String switchType2Key = 'switchType2';
  final String alwaysOnStatus2Key = 'alwaysOnStatus2';
  final String onOffStatus2Key = 'onOffStatus2';

  final String switchType3Key = 'switchType3';
  final String alwaysOnStatus3Key = 'alwaysOnStatus3';
  final String onOffStatus3Key = 'onOffStatus3';

  @override
  String get middleCardStatus =>
      '${switchStatus(switchType1Key, alwaysOnStatus1Key, onOffStatus1Key)} '
      '${switchStatus(switchType2Key, alwaysOnStatus2Key, onOffStatus2Key)} '
      '${switchStatus(switchType3Key, alwaysOnStatus3Key, onOffStatus3Key)}';

  @override
  List<ComponentBaseViewModel> getComponentViewModelList() {
    final List<String>? switchNames = getSwitchName;
    final String switchName1 =
    switchNames != null && switchNames.isNotEmpty ? switchNames[0] : '';
    final String switchName2 =
    switchNames != null && switchNames.length > 1 ? switchNames[1] : '';
    final String switchName3 =
    switchNames != null && switchNames.length > 2 ? switchNames[2] : '';
    return <ComponentBaseViewModel>[
      switchComponentViewModel(
          switchType1Key, alwaysOnStatus1Key, onOffStatus1Key, switchName1),
      switchComponentViewModel(
          switchType2Key, alwaysOnStatus2Key, onOffStatus2Key, switchName2),
      switchComponentViewModel(
          switchType3Key, alwaysOnStatus3Key, onOffStatus3Key,switchName3),
    ];
  }
}
