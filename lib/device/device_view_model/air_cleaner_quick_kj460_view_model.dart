import 'package:smart_home/device/device_view_model/air_cleaner_quick_card_view_model.dart';

//空气净化器
class AirCleanerQuickKj460ViewModel extends AirCleanerQuickCardViewModel {
  AirCleanerQuickKj460ViewModel({required super.device});

  @override
  Map<String, String> get allAirQualityMap => <String, String>{
        '0': '优',
        '1': '良',
        '2': '中',
        '3': '差',
      };

  @override
  String get windSpeedAttrKey => 'StatusFog';

  @override
  String get autoModeAttrKey => 'operationMode';

  @override
  String get autoModeAttrValue => '2';

  @override
  String get sleepModeAttrKey => 'operationMode';

  @override
  String get sleepModeAttrValue => '4';
}
