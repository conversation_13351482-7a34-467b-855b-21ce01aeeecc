import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//垃圾处理器
class GarbageDisposalViewModel extends DeviceCardViewModel {
  GarbageDisposalViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {

    if (powerOn) {
      final String operationValue =
          getDeviceCardAttrValueByName('operationMode');
      String attrValue = '';
      switch (operationValue) {
        case '1':
          attrValue = '安静';
        case '2':
          attrValue = '智能';
        case '3':
          attrValue = '强劲';
      }

      if (attrValue.isNotEmpty) {
        return DeviceCardAttribute(label: '模式', value: attrValue, unit: '');
      }
    }

    return super.deviceCardAttributeOne();
  }
}
