import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CoffeeMachineModelCardViewModel extends DeviceCardViewModel {
  CoffeeMachineModelCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String workStatus = getDeviceCardAttrValueByName('workStatus');

    if (powerOn && workStatus == '1') {
      final String operationMode =
          getDeviceCardAttrValueByName('operationMode');

      final String attr =
          operationMode.isEmpty ? '' : _assignCoffeeValue(operationMode);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    }
    return null;
  }

  String _assignCoffeeValue(String value) {
    switch (value) {
      case '1':
        return '芮斯崔朵';
      case '4':
        return '热水';
      case '6':
        return '玛奇朵';
      case '7':
        return '澳白咖啡';
      case '8':
        return '除垢';
      case '9':
        return '热牛奶';
      case '10':
        return '意式';
      case '11':
        return '美式';
      case '12':
        return '拿铁';
      case '13':
        return '卡布奇诺';
      case '14':
        return '奶沫';
      case '15':
        return '牛奶';
      case '16':
        return '冲泡器清洗';
      case '17':
        return '奶泡器清洗';
      case '18':
        return '系统清洗';
      case '19':
        return '开机自检';
      case '20':
        return '关机自检';
      case '21':
        return '清洗中';
      case '22':
        return '雷斯特雷托';
      case '23':
        return '大杯浓缩';
      case '24':
        return '馥芮白';
      case '25':
        return '可塔朵';
      case '26':
        return '咖啡粉制作';
      case '27':
        return '意式奶咖';
      case '28':
        return '现磨咖啡';
      case '29':
        return '拿铁玛奇朵';
      case '30':
        return '热奶沫';
      case '31':
        return '大壶咖啡';
      case '32':
        return '美式奶咖';
      case '33':
        return '自定义模式';
      case '34':
        return '长萃咖啡';
      case '35':
        return '蒸汽模式';
      case '36':
        return '喜爱咖啡';
      default:
        return '';
    }
  }
}
