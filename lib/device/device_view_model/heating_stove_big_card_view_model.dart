/*
 * 描述：采暖炉大卡片
 * 作者：fancunshuo
 * 建立时间: 2025/6/16
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../component/view_model/expand_switch_icon_text_view_model.dart';
import '../component_view_model/component_view_model.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import 'heating_stove_card_view_model.dart';

class HeatingStoveBigCardViewModel extends HeatingStoveCardViewModel {
  HeatingStoveBigCardViewModel({required super.device});

  final Dialogs _dialogs = Dialogs();

  final String _chStatusKey = 'chStatus';
  final String _dhwStatusKey = 'dhwStatus';
  final String _chTemperatureKey = 'chTemperature';
  final String _dhwTemperatureKey = 'dhwTemperature';
  final String _onOffStatusKey = 'onOffStatus';

  final String _powerOffMsg = '采暖炉关机，无法操作';
  final String _notWritableMsg = '暂时无法操作';

  final String _heatingLabel = '采暖';
  final String _hotWaterLabel = '热水';

  @override
  String get smallCardStatus {
    if (powerOn) {
      return '开';
    }
    if (powerOff) {
      return '关';
    }
    return '';
  }

  SmartHomeDeviceAttribute? get _chStatusAttribute {
    return device.attributeMap[_chStatusKey];
  }

  bool get _chStatus {
    return _chStatusAttribute?.value == trueValue;
  }

  bool _getChStatusWritable() {
    return _chStatusAttribute?.writable ?? false;
  }

  bool _getHeatingStatus() {
    if (deviceOfflineOrPowerOff || loading) {
      return false;
    }
    return _chStatus;
  }

  SmartHomeDeviceAttribute? get _dhwStatusAttribute {
    return device.attributeMap[_dhwStatusKey];
  }

  bool get _dhwStatus {
    return _dhwStatusAttribute?.value == trueValue;
  }

  bool _getDhwStatusWritable() {
    return _dhwStatusAttribute?.writable ?? false;
  }

  bool _getHotWaterStatus() {
    if (deviceOfflineOrPowerOff || loading) {
      return false;
    }
    return _dhwStatus;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    return _createTemperatureAttribute(_heatingLabel, _getHeatingTemperature());
  }

  String _getHeatingTemperature() {
    final String chStatus = getDeviceCardAttrValueByName(_chStatusKey);
    return chStatus == trueValue
        ? getDeviceCardAttrValueByName(_chTemperatureKey)
        : '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (_shouldHideHotWaterAttribute()) {
      return null;
    }

    return _createTemperatureAttribute(
        _hotWaterLabel, _getHotWaterTemperature());
  }

  DeviceCardAttribute? _createTemperatureAttribute(
      String label, String temperature) {
    return _isValidTemperature(temperature)
        ? DeviceCardAttribute(label: label, value: temperature, unit: '°C')
        : null;
  }

  bool _shouldHideHotWaterAttribute() {
    return powerOff;
  }

  String _getHotWaterTemperature() {
    final String dhwStatus = getDeviceCardAttrValueByName(_dhwStatusKey);
    return dhwStatus == trueValue
        ? getDeviceCardAttrValueByName(_dhwTemperatureKey)
        : '';
  }

  bool _isValidTemperature(String value) {
    return value.isNotEmpty && value != '0';
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '采暖炉';
    final LargeDeviceCardFunctionSet deviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
            name: name,
            componentViewModelList: <ComponentBaseViewModel>[
          _heatingControlViewModel(),
          _hotWaterControlViewModel()
        ]);
    return <String, LargeDeviceCardFunctionSet>{name: deviceCardFunctionSet};
  }

  ComponentBaseViewModel _createControlViewModel({
    required String icon,
    required String text,
    required bool Function() getWritable,
    required bool Function() getStatus,
    required void Function(BuildContext? context) handleControl,
  }) {
    final bool compEnable = !(deviceOfflineOrPowerOff || loading);
    return ExpandSwitchIconTextViewModel(
        icon: icon,
        isOn: getStatus(),
        text: text,
        expandFlex: 2,
        enable: compEnable,
        clickCallback: (BuildContext? context) {
          gio(cloudProgramName: text);
          checkDeviceState(
                  powerOffMsg: _powerOffMsg,
                  writable: getWritable(),
                  writableMsg: _notWritableMsg)
              .then((bool pass) {
            if (pass) {
              handleControl(context);
            }
          });
        });
  }

  ComponentBaseViewModel _heatingControlViewModel() {
    return _createControlViewModel(
      icon: 'assets/components/heating_stove_heating.webp',
      text: _heatingLabel,
      getWritable: _getChStatusWritable,
      getStatus: _getHeatingStatus,
      handleControl: _handleHeatingControl,
    );
  }

  void _handleHeatingControl(BuildContext? context) {
    _handleDeviceControl(
      context,
      mode: _heatingLabel,
      commandKey: _chStatusKey,
      currentStatus: _chStatus,
      otherModeStatus: _dhwStatus,
    );
  }

  ComponentBaseViewModel _hotWaterControlViewModel() {
    return _createControlViewModel(
      icon: 'assets/components/heating_stove_hot_water.webp',
      text: _hotWaterLabel,
      getWritable: _getDhwStatusWritable,
      getStatus: _getHotWaterStatus,
      handleControl: _handleHotWaterControl,
    );
  }

  void _handleHotWaterControl(BuildContext? context) {
    _handleDeviceControl(
      context,
      mode: _hotWaterLabel,
      commandKey: _dhwStatusKey,
      currentStatus: _dhwStatus,
      otherModeStatus: _chStatus,
    );
  }

  void _handleDeviceControl(
    BuildContext? context, {
    required String mode,
    required String commandKey,
    required bool currentStatus,
    required bool otherModeStatus,
  }) {
    if (!otherModeStatus && currentStatus) {
      _powerOffWithConfirm(context);
    } else {
      final Map<String, String> commands = <String, String>{};
      commands[commandKey] = currentStatus ? falseValue : trueValue;
      quickCtrlLECommand(device.basicInfo.deviceId, commands);
    }
  }

  void _powerOffWithConfirm(BuildContext? context) {
    final Map<String, String> commands = <String, String>{};
    if (context != null) {
      _dialogs.showDoubleBtnDialog(
          context: context,
          title: '是否继续操作？',
          content: '采暖和热水同时关闭，设备将关机',
          barrierDismissible: false,
          cancelCallback: () {
            gioTrack(SmartHomeConstant.heatingStovePowerOffDialog,
                <String, String>{'button_name': '取消'});
          },
          confirmCallback: () {
            gioTrack(SmartHomeConstant.heatingStovePowerOffDialog,
                <String, String>{'button_name': '确定'});
            commands[_onOffStatusKey] = falseValue;
            quickCtrlLECommand(device.basicInfo.deviceId, commands);
          });
    }
  }
}
