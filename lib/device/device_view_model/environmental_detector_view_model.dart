import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//环境检测仪
class EnvironmentalDetectorViewModel extends DeviceCardViewModel {
  EnvironmentalDetectorViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    //环境温度
    final String value = getDeviceCardAttrValueByName('envTemperature');
    if (value.isNotEmpty) {
      return DeviceCardAttribute(label: '', value: value, unit: '°C');
    }
    return super.deviceCardAttributeOne();
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    //环境湿度
    final String value = getDeviceCardAttrValueByName('envHumidity');
    if (value.isNotEmpty) {
      return DeviceCardAttribute(label: '', value: value, unit: '%');
    }
    return super.deviceCardAttributeTwo();
  }
}
