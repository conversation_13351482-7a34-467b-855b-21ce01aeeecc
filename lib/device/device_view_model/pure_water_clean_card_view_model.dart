import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class PureWaterCleanCardViewModel extends DeviceCardViewModel {
  PureWaterCleanCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['purifiedTDS']?.value ?? '';
    final String attr = _getSoftWaterQuality(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  String _getSoftWaterQuality(String value) {
    final int? parseValue = int.tryParse(value);

    if (parseValue is int) {
      if (parseValue >= 1 && parseValue <= 50) {
        return '优';
      }

      if (parseValue >= 51 && parseValue <= 200) {
        return '良';
      }

      if (parseValue >= 201 && parseValue <= 400) {
        return '一般';
      }
    }

    return '';
  }
}
