import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/component/view_model/fixed_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_card_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../component/view_model/expand_value_list_view_model.dart';

//空气净化器
class AirCleanerQuickCardViewModel extends AirCleanerCardViewModel {
  AirCleanerQuickCardViewModel({required super.device});

  //风速档位的Key
  String get windSpeedAttrKey => 'windSpeed';

  //自动模式字段
  String get autoModeAttrKey => 'operationMode';

  String get autoModeAttrValue => '2';

  //睡眠模式字段
  String get sleepModeAttrKey => 'operationMode';

  String get sleepModeAttrValue => '3';

  SmartHomeDeviceAttribute? get autoModeAttribute {
    return device.attributeMap[autoModeAttrKey];
  }

  SmartHomeDeviceAttribute? get sleepModeAttribute {
    return device.attributeMap[sleepModeAttrKey];
  }

  SmartHomeDeviceAttribute? get windSpeedAttrAttribute {
    return device.attributeMap[windSpeedAttrKey];
  }

  /// 风速原始数据，不要直接使用，请使用get属性
  List<SmartHomeDataItem> _windSpeedDataList = <SmartHomeDataItem>[];

  /// 风速调节展示数据，不要直接使用，请使用get属性
  List<String> _windSpeedValueList = <String>[];

  /// 风速调节展示数据
  List<String> get windSpeedValueList {
    if (_windSpeedValueList.isNotEmpty) {
      return _windSpeedValueList;
    }

    final List<String> valueList = <String>[];
    int index = 0;
    windSpeedDataList.forEach((SmartHomeDataItem element) {
      valueList.add('${index + 1}');
      if (windSpeedAttrAttribute?.value == element.data) {}
      index++;
    });
    _windSpeedValueList = valueList;
    return _windSpeedValueList;
  }

  /// 风速调节原始数据
  List<SmartHomeDataItem> get windSpeedDataList {
    if (_windSpeedDataList.isNotEmpty) {
      return _windSpeedDataList;
    }

    final List<SmartHomeDataItem> items =
        getDeviceAttribute(windSpeedAttrKey)?.valueRange.dataList ??
            <SmartHomeDataItem>[];

    // 该设备睡眠模式时，风速为1：静音档，为与详情页保持一致，故将风速为1时删除。
    if (device.basicInfo.typeId ==
        '2054a00039044b14210100000000000000000000000000000000000000000040') {
      items.removeWhere((SmartHomeDataItem element) {
        return element.data == '1'; //静音档
      });
    }
    // 该设备，风速为0：空档，为与详情页保持一致，故将风速为0时删除。
    if (device.basicInfo.typeId ==
        '200c5208003101102101c64aa8bb0b000000fc75b1eb79ef465c23e6a705a640') {
      items.removeWhere((SmartHomeDataItem element) {
        return element.data == '0'; //空档
      });
    }
    _windSpeedDataList = items;

    return _windSpeedDataList;
  }

  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      widSpeedComponentViewModel,
      autoModeViewModel,
      sleepModeViewModel,
    ];
  }

  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      ExpandValueListViewModel(
        valueList: <String>[],
        currentIndex: -1,
        unit: '',
        valueListEmptyCallback: (_) {
          checkDeviceState();
        },
        enable: false,
      ),
      FixedSwitchIconTextViewModel(
        icon: 'assets/icons/auto_mode.webp',
        text: '自动模式',
        enable: false,
        isOn: false,
        clickCallback: (_) {
          checkDeviceState();
        },
      ),
      FixedSwitchIconTextViewModel(
        icon: 'assets/icons/sleep_mode.webp',
        text: '睡眠模式',
        enable: false,
        isOn: false,
        clickCallback: (_) {
          checkDeviceState();
        },
      ),
    ];
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '空气净化器';
    final LargeDeviceCardFunctionSet deviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
      name: name,
      componentViewModelList: (deviceOffline || loading)
          ? componentOfflineOrPowerOffViewModelList
          : componentViewModelList,
    );
    return <String, LargeDeviceCardFunctionSet>{name: deviceCardFunctionSet};
  }

  ComponentBaseViewModel get autoModeViewModel {
    final bool enable = powerOn &&
        (autoModeAttribute?.writable ?? false) &&
        !alarm &&
        !childLockOn;
    final bool isOn = (autoModeAttribute?.value ?? '') == autoModeAttrValue;
    return FixedSwitchIconTextViewModel(
      icon: 'assets/icons/auto_mode.webp',
      text: '自动模式',
      enable: enable,
      isOn: isOn,
      clickCallback: (_) {
        gio(cloudProgramName: '自动模式');
        checkDeviceState().then((bool pass) {
          if (pass) {
            final Map<String, String> commands = <String, String>{};
            commands[autoModeAttrKey] = autoModeAttrValue;
            quickCtrlLECommand(device.basicInfo.deviceId, commands,
                onErrorCallback: (String errMsg) {
              ToastHelper.showToast(errMsg);
            });
          }
        });
      },
    );
  }

  ComponentBaseViewModel get sleepModeViewModel {
    final bool enable = powerOn &&
        (sleepModeAttribute?.writable ?? false) &&
        !alarm &&
        !childLockOn;
    final bool isOn = sleepModeAttribute?.value == sleepModeAttrValue;
    return FixedSwitchIconTextViewModel(
      icon: 'assets/icons/sleep_mode.webp',
      text: '睡眠模式',
      enable: enable,
      isOn: isOn,
      clickCallback: (_) {
        gio(cloudProgramName: '睡眠模式');
        checkDeviceState().then((bool pass) {
          if (pass) {
            final Map<String, String> commands = <String, String>{};
            commands[sleepModeAttrKey] = sleepModeAttrValue;
            quickCtrlLECommand(deviceId, commands,
                onErrorCallback: (String errMsg) {
              ToastHelper.showToast(errMsg);
            });
          }
        });
      },
    );
  }

  Future<bool> _checkWindSpeedContinue() {
    return checkDeviceState(
        writable: windSpeedAttrAttribute?.writable ?? false,
        writableMsg: '当前模式无法调节档位');
  }

  ComponentBaseViewModel get widSpeedComponentViewModel {
    int currentIndex = 0;

    int index = 0;
    for (final SmartHomeDataItem element in windSpeedDataList) {
      if (windSpeedAttrAttribute?.value == element.data) {
        currentIndex = index;
        break;
      }
      index++;
    }

    final bool enable = powerOn &&
        (windSpeedAttrAttribute?.writable ?? false) &&
        windSpeedValueList.isNotEmpty;

    final ExpandValueListViewModel valueListViewModel =
        ExpandValueListViewModel(
      valueList: windSpeedValueList,
      currentIndex: currentIndex,
      unit: '档',
      enable: enable,
      checkContinue: _checkWindSpeedContinue,
      valueListEmptyCallback: (_) {
        if (windSpeedValueList.isEmpty) {
          ToastHelper.showToast('无法调节档位');
        } else {
          checkDeviceState();
        }
      },
      noMorePreCallback: (_) {
        ToastHelper.showToast('已调至最低档位');
      },
      noMoreNextCallback: (_) {
        ToastHelper.showToast('已调至最高档位');
      },
      valueChangeCallback: (BuildContext context, String value, int index) {
        gio(cloudProgramName: '档位设置');
        final Map<String, String> commands = <String, String>{};
        commands[windSpeedAttrKey] = windSpeedDataList[index].data;
        quickCtrlLECommand(device.basicInfo.deviceId, commands,
            onErrorCallback: (String errMsg) {
          ToastHelper.showToast(errMsg);
        });
      },
    );

    return valueListViewModel;
  }
}
