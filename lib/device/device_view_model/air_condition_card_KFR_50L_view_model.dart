/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/7/12
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component/view_model/care_mode_view_model.dart';
import 'package:smart_home/device/component_view_model/circular_switch_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/air_condition_card_view_model.dart';

import '../../store/smart_home_store.dart';
import '../component_view_model/component_view_model.dart';
import '../component_view_model/grid_view_model.dart';
import '../component_view_model/popup_component_view_model.dart';
import '../store/device_action.dart';

class AirConditionKFR50LCardViewModel extends AirConditionCardViewModel {
  AirConditionKFR50LCardViewModel({required super.device});

  String get specialModeKey => 'specialMode';

  @override
  Map<String, AirConditionModeModel> get allModeMap =>
      <String, AirConditionModeModel>{
        '0': AirConditionModeModel(
            value: '0',
            desc: 'Care智能',
            icon: 'assets/components/mode/mode_care.webp'),
        '1': AirConditionModeModel(
            value: '1',
            desc: '制冷',
            icon: 'assets/components/mode/mode_cold.webp'),
        '2': AirConditionModeModel(
            value: '2',
            desc: '除湿',
            icon: 'assets/components/mode/mode_dry.webp'),
        '4': AirConditionModeModel(
            value: '4',
            desc: '制热',
            icon: 'assets/components/mode/mode_warm.webp'),
        '6': AirConditionModeModel(
            value: '6',
            desc: '送风',
            icon: 'assets/components/mode/mode_wind.webp'),
      };

  Map<String, AirConditionModeModel> get allCareModeMap =>
      <String, AirConditionModeModel>{
        '0': AirConditionModeModel(value: '0', desc: '成年男性'),
        '1': AirConditionModeModel(value: '1', desc: '成年女性'),
        '2': AirConditionModeModel(value: '2', desc: '孕/童'),
        '3': AirConditionModeModel(value: '3', desc: '老人'),
      };

  SmartHomeDeviceAttribute? get specialModeAttribute {
    return device.attributeMap[specialModeKey];
  }

  @override
  PopupComponentViewModel modePopupComponent(String attributeKey,
      SmartHomeDeviceAttribute? attribute, String currentValue) {
    final bool enable = !powerOff && (attribute?.writable ?? false);

    final List<ComponentBaseViewModel> list = <ComponentBaseViewModel>[];

    final List<SmartHomeDataItem> dataList =
        attribute?.valueRange.dataList ?? <SmartHomeDataItem>[];

    final Map<String, AirConditionModeModel> modeMap =
        <String, AirConditionModeModel>{};
    modeMap.addAll(allModeMap);
    modeMap.forEach((String key, AirConditionModeModel modeModel) {
      if (dataList.any((SmartHomeDataItem dataItem) => dataItem.data == key)) {
        final CircularSwitchComponentModel viewModel =
            CircularSwitchComponentModel(
                icon: modeModel.icon,
                text: modeModel.desc,
                selected: key == currentValue,
                enable: enable,
                clickCallback: (BuildContext? context) {
                  modelSelectGio(workMode: modeModel.desc);
                  checkDeviceState(
                          writable: enable,
                          writableMsg: SmartHomeConstant.toastAirModeDisable,
                          powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo)
                      .then((bool pass) {
                    if (pass) {
                      if (currentValue != modeModel.value) {
                        final Map<String, String> commands = <String, String>{};
                        commands[operationModeKey] = modeModel.value;
                        quickCtrlLECommand(device.basicInfo.deviceId, commands,
                            onErrorCallback: (String errMsg) {
                          ToastHelper.showToast(errMsg);
                        });
                      }
                    }
                  });
                  if (!enable || modeModel.value != '0') {
                    hidePopup(context);
                  }
                });
        list.add(viewModel);
      }
    });
    final List<ComponentBaseViewModel> popupComponentList =
        <ComponentBaseViewModel>[
      GridViewModel(
          componentList: list,
          crossAxisCount: 4,
          mainAxisExtent: 73,
          crossAxisSpacing: 0,
          mainAxisSpacing: 24)
    ];

    if (operationMode0) {
      popupComponentList.add(_careModeComponent());
    }
    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '模式选择',
            deviceId: device.basicInfo.deviceId,
            attributeKey: attributeKey,
            componentList: popupComponentList);
    return popupComponentViewModel;
  }

  ComponentBaseViewModel _careModeComponent() {
    final SmartHomeDeviceAttribute? attribute = specialModeAttribute;
    final String currentValue = attribute?.value ?? '';
    final bool enable = attribute?.writable ?? false;
    final int selectedIndex = int.tryParse(currentValue) ?? -1;
    final List<SmartHomeDataItem> dataList =
        attribute?.valueRange.dataList ?? <SmartHomeDataItem>[];

    final Map<String, AirConditionModeModel> modeMap =
        <String, AirConditionModeModel>{};
    modeMap.addAll(allCareModeMap);
    final List<String> modeList = <String>[];
    modeMap.forEach((String key, AirConditionModeModel modeModel) {
      if (dataList.any((SmartHomeDataItem dataItem) => dataItem.data == key)) {
        modeList.add(modeModel.desc);
      }
    });

    return CareModeSelectViewModel(
        modeList: modeList,
        selectedIndex: selectedIndex,
        enable: enable,
        modeSelectCallback: (BuildContext context, int index) {
          modelSelectGio(modelValue: modeList[index]);
          checkDeviceState(writable: enable, writableMsg: '暂时无法选择人群')
              .then((bool pass) {
            if (pass) {
              if (selectedIndex != index) {
                final Map<String, String> commands = <String, String>{};
                commands[specialModeKey] =
                    modeMap[index.toString()]?.value ?? '';
                quickCtrlLECommand(device.basicInfo.deviceId, commands,
                    onErrorCallback: (String errMsg) {
                  ToastHelper.showToast(errMsg);
                });
              }
            }
          });

          /// 隐藏弹框
          if (smartHomeStore.state.deviceState.popupComponentViewModel !=
              null) {
            if (context.mounted) {
              Navigator.pop(context);
              Future<void>.delayed(const Duration(milliseconds: 200), () {
                smartHomeStore
                    .dispatch(UpdatePopupComponentViewModelAction(null));
              });
            }
          }
        });
  }
}
