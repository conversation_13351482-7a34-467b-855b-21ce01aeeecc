import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class HeatingStoveCardViewModel extends DeviceCardViewModel {
  HeatingStoveCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'chTemperature';
    final String value = device.attributeMap[key]?.value ?? '';
    return value.isNotEmpty
        ? DeviceCardAttribute(label: '温度', value: value, unit: '°C')
        : null;
  }
}
