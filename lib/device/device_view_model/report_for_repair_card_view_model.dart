import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';

import '../resize_device_card/resize_base_model.dart';

class ReportForRepairCardViewModel extends CardBaseViewModel {
  ReportForRepairCardViewModel()
      : super(const ValueKey<String>(report_for_repair_card_id),
            DeviceCardType.middleCard) {
    title = '报装报修';
    subTitle = '全流程可视化服务';
    imgUrl = 'assets/images/report_repair.webp';
  }

  @override
  CardType get cardType => CardType.repairCard;

  String title = '';
  String subTitle = '';
  String imgUrl = '';

  void cardClick() {
    gioTrack(SmartHomeConstant.reportForRepairGio);
    goToPageWithDebounce(SmartHomeConstant.urlInstallRepair);
  }

  @override
  String sortId() {
    return report_for_repair_card_id;
  }
}
