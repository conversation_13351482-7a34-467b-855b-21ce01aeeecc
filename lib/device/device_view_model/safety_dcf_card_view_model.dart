import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SafetyDcfCardViewModel extends DeviceCardViewModel {
  SafetyDcfCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      return DeviceCardAttribute(label: '', value: '开', unit: '');
    }
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }
    return null;
  }
}
