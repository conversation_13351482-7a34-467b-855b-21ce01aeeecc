import '../device_card_view_model.dart';

class LingYunSwitchModel {
  LingYunSwitchModel({
    required this.switchName,
    required this.switchTypeKey,
    required this.alwaysOnStatusKey,
  });

  final String switchName;
  final String switchTypeKey;
  final String alwaysOnStatusKey;
}

class SwitchViewModelData {
  final bool isOn;
  final bool enable;
  final String onOffStatusValue;
  final DeviceCardViewModel? cardViewModel;
  final LingYunSwitchModel model;

  SwitchViewModelData({
    required this.isOn,
    required this.enable,
    required this.onOffStatusValue,
    required this.cardViewModel,
    required this.model,
  });
}
