import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/air_condition_card_view_model.dart';

class HomeCentralAirConditionViewModel extends AirConditionCardViewModel {
  HomeCentralAirConditionViewModel({required super.device});

  String get currentOutletTemperatureKey => 'currentOutletTemperature';

  String get temperatureControlModeKey => 'temperatureControlMode';

  String get intelligence => '智能';

  @override
  String get pmvPopupTitle => 'PMV模式';

  @override
  String get targetTemperatureKey {
    if (operationModeAttribute?.value == '7' &&
        _temperatureControlModeAttribute?.value == trueValue) {
      return 'outletTemperatureSetting';
    } else {
      return 'targetTemperature';
    }
  }

  SmartHomeDeviceAttribute? get _currentOutletTemperatureAttribute {
    return device.attributeMap[currentOutletTemperatureKey];
  }

  SmartHomeDeviceAttribute? get _temperatureControlModeAttribute {
    return device.attributeMap[temperatureControlModeKey];
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final DeviceCardAttribute? deviceCardAttribute =
        super.deviceCardAttributeOne();
    if (deviceCardAttribute != null) {
      if (deviceCardAttribute.value != '0') {
        return deviceCardAttribute;
      }
      return null;
    }
    final String value = _currentOutletTemperatureAttribute?.value ?? '';

    if (value.isEmpty || value == '0') {
      return null;
    }
    return DeviceCardAttribute(label: '出水', value: value, unit: '℃');
  }

  @override
  AirConditionModeModel get operationMode0Model => AirConditionModeModel(
      value: '0',
      desc: intelligence,
      icon: 'assets/components/mode/mode_intellect.webp');

  @override
  Map<String, AirConditionModeModel> get allModeMap =>
      <String, AirConditionModeModel>{
        '0': operationMode0Model,
        '1': AirConditionModeModel(
            value: '1',
            desc: '制冷',
            icon: 'assets/components/mode/mode_cold.webp'),
        '2': AirConditionModeModel(
            value: '2',
            desc: '除湿',
            icon: 'assets/components/mode/mode_dry.webp'),
        '3': AirConditionModeModel(
            value: '3',
            desc: '健康除湿',
            icon: 'assets/components/mode/mode_dry_healthy.webp'),
        '4': AirConditionModeModel(
            value: '4',
            desc: '制热',
            icon: 'assets/components/mode/mode_warm.webp'),
        '5': AirConditionModeModel(
            value: '5',
            desc: '节能',
            icon: 'assets/components/mode/mode_energy_conservation.webp'),
        '6': AirConditionModeModel(
            value: '6',
            desc: '送风',
            icon: 'assets/components/mode/mode_wind.webp'),
        '7': AirConditionModeModel(
            value: '7',
            desc: '地暖',
            icon: 'assets/components/mode/mode_floor_heating.webp'),
        '8': AirConditionModeModel(
            value: '8',
            desc: '制热+地暖',
            icon: 'assets/components/mode/mode_hot_floor_heating.webp'),
        '9': AirConditionModeModel(
            value: '9',
            desc: '制冷+地暖',
            icon: 'assets/components/mode/mode_cold_floor_heating.webp'),
        '10': AirConditionModeModel(
            value: '10',
            desc: '生活热水',
            icon: 'assets/components/mode/mode_hot_water.webp'),
        '11': AirConditionModeModel(
            value: '11',
            desc: '泳池',
            icon: 'assets/components/mode/mode_swimming_pool.webp'),
        '12': AirConditionModeModel(
            value: '12',
            desc: '制热+泳池',
            icon: 'assets/components/mode/mode_hot_swimming_pool.webp'),
        '13': AirConditionModeModel(
            value: '13',
            desc: '自动+生活热水',
            icon: 'assets/components/mode/mode_auto_hot_water.webp'),
        '14': AirConditionModeModel(
            value: '14',
            desc: '制冷+生活热水',
            icon: 'assets/components/mode/mode_cold_hot_water.webp'),
        '15': AirConditionModeModel(
            value: '15',
            desc: '制热+生活热水',
            icon: 'assets/components/mode/mode_hot_hot_water.webp'),
      };
}
