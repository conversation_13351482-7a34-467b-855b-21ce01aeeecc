import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component/view_model/expand_gas_value_list_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/g_water_heater_fixed_switch_text_view_model.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../common/smart_home_util.dart';

///燃热水器卡片ViewModel
///
/// @update: WuXu 2025/03/26
///         去掉e感温按钮替换为单次零冷水，保留调温组件与e感温相关的处理
///
class GWaterHeaterCardViewModel extends DeviceCardViewModel {
  GWaterHeaterCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  String get _notSupportEgwTypeId =>
      '201c120000118674181700418007134200000002000000000000000000000040';

  List<String> get _notSupportEgwModelList => <String>[
        'CX5',
        'CT7',
        '16ES',
        'T31',
        'CE3',
        'CQ3',
        'CD3',
        'CZ3',
        'CA3',
        'CWA',
        'CV1G',
        'CJS3',
        'CH3UAU1',
        'CV5FRXGU1',
      ];

  List<String> get _zgwModelList => <String>['CW3', 'CWR3', 'CXR3'];

  List<String> get _sjswModelList => <String>[
        'FC3',
        'HN7',
        'PZ5PRO',
        'HG5',
        'HG3',
        'CS5',
        'HU5',
        'CUS',
        'CT3',
        'CR5S',
        'CRV',
        'CZ7F',
        'CU5',
        'CRT15',
        'GD0RGF002',
        'GD0RGGU02',
        'GD0RGHT02'
      ];

  List<String> get _znyModelList => <String>['JZ3', 'LWJ', 'TM3', 'MY5'];

  List<String> get _egwProdNolList => <String>[
        'GDOR7GTOA',
        'GDOR7JT09',
        'GD0R7KT08',
        'GDOR7L009',
        'GDOR7LU08',
        'GDOR7M008',
        'GDOR7NU08',
        'GDOR7S008',
        'GDOR7TT08',
        'GDOR7UT08',
        'GD0R7VU08',
        'GDOR7Y008',
        'GDOR7ZU09',
        'GDOR75T09',
        'GDOR76TOA',
        'GDOR77UOA',
        'GDOR77U09',
        'GDOR7800A',
        'GDOR76009',
        'GDORD7T09',
        'GDORD9009',
        'GDORDAT09',
        'GDORDB009',
        'GDORDCT09',
        'GDORDE009',
        'GDORDF009',
        'GDORGP004',
        'GDORK5U02',
        'GDORK7T02',
        'GDORK6002'
      ];

  /// 洗浴零冷水
  ///
  /// 判断条件（下面判断前后顺序是根据产业代码进行整理的）：
  ///
  /// 1. typeId 是 201c120000118674181700418007880000000000000000000000000000000040 并且 型号不包含 L5N
  /// 2. 型号包含'WN7S','YTS','YT7','WN9S','IDOL5'
  /// 3. 型号包含WR3且不包含CWR3和WR3S
  /// 4. 型号包含'ER5','IDOL3','WR5U1'
  bool get isWashZeroColdWater {
    //1. typeId 是 201c120000118674181700418007880000000000000000000000000000000040 并且 型号不包含 L5N
    if (device.basicInfo.typeId ==
            '201c120000118674181700418007880000000000000000000000000000000040' &&
        !device.basicInfo.model.contains('L5N')) {
      return true;
    }

    //2. 型号包含'WN7S','YTS','YT7','WN9S','IDOL5'
    for (final String model in <String>[
      'WN7S',
      'YTS',
      'YT7',
      'WN9S',
      'IDOL5',
    ]) {
      if (device.basicInfo.model.contains(model)) {
        return true;
      }
    }

    //3. 型号包含WR3且不包含CWR3和WR3S
    if (device.basicInfo.model.contains('WR3') &&
        !device.basicInfo.model.contains('CWR3') &&
        !device.basicInfo.model.contains('WR3S')) {
      return true;
    }

    //4. 型号包含'ER5','IDOL3','WR5U1'
    for (final String model in <String>[
      'IDOL3',
      'ER5',
      'WR5U1',
    ]) {
      if (device.basicInfo.model.contains(model)) {
        return true;
      }
    }

    return false;
  }

  String get _targetTempKey => 'targetTemp';

  String get _scourModeKey => 'scourMode';

  String get _scourStateKey => 'scourState';

  String get _autoTimeOnOffKey => 'autoTimeOnOff';

  String get _prtlVersionKey => 'prtlVersion';

  String get _flameStatusKey => 'flameStatus';

  String get _egwValue => '5'; // E感温开启

  String get _normalValue => '1'; // E感温关闭

  String get _safetyLockStatusKey => 'safetyLockStatus'; // 50度安全锁

  String get _zeroColdWaterStatusKey => 'zeroColdWaterStatus'; // 零冷水状态

  String get _zeroColdWaterCloseValue => '0'; // 单次零冷水关闭

  String get _zeroColdWaterOpenValue => '1'; // 单次零冷水开启

  bool get _supportEgwDevice {
    if (device.basicInfo.typeId == _notSupportEgwTypeId) {
      return false;
    }
    for (final String typeId in _notSupportEgwModelList) {
      if (device.basicInfo.model.contains(typeId)) {
        return false;
      }
    }
    return true;
  }

  bool get _supportEgw {
    return _prtlVersionValueNot0 &&
        _valueRangeIncludeEgwValue &&
        _supportEgwDevice;
  }

  bool get _prtlVersionValueNot0 {
    final SmartHomeDeviceAttribute? attribute = _prtlVersionAttribute;
    return attribute is SmartHomeDeviceAttribute;
  }

  bool get _valueRangeIncludeEgwValue {
    final SmartHomeDeviceAttributeValueRange? valueRange =
        _scourStateAttribute?.valueRange;
    if (valueRange is! SmartHomeDeviceAttributeValueRange) {
      return false;
    }
    final List<SmartHomeDataItem> items = valueRange.dataList;
    for (SmartHomeDataItem item in items) {
      if (item.data == _egwValue) {
        return true;
      }
    }
    return false;
  }

  bool get _FA5OrFR5 =>
      device.basicInfo.model.contains('FA5') ||
      device.basicInfo.model.contains('FR5');

  /// 非浴缸注水检查
  bool get _showBurningAlarm {
    return _burning && !_scourState2;
  }

  bool get _burning => _flameStatusAttribute?.value == 'true';

  SmartHomeDeviceAttribute? get _targetTempAttribute {
    return device.attributeMap[_targetTempKey];
  }

  SmartHomeDeviceAttribute? get _scourModeAttribute {
    return device.attributeMap[_scourModeKey];
  }

  SmartHomeDeviceAttribute? get _flameStatusAttribute {
    return device.attributeMap[_flameStatusKey];
  }

  SmartHomeDeviceAttribute? get _safetyLockStatusKeyAttribute {
    return device.attributeMap[_safetyLockStatusKey];
  }

  bool get _safetyLockOpen => _safetyLockStatusKeyAttribute?.value == 'true';

  bool get _scourMode3 {
    return _scourModeAttribute?.value == '3';
  }

  bool get _scourState7 {
    return _scourStateAttribute?.value == '7';
  }

  bool get _scourState2 {
    return _scourStateAttribute?.value == '2';
  }

  bool get _egwOn {
    return _scourStateAttribute?.value == _egwValue;
  }

  /// 是否支持显示零冷水按钮
  ///
  /// zeroColdWaterStatus属性不为空，且范围包含 1
  bool get _supportZeroColdWater {
    final SmartHomeDeviceAttribute? zeroColdWaterStatusAttribute =
        _zeroColdWaterStatusAttribute;

    if (zeroColdWaterStatusAttribute == null) {
      return false;
    }

    for (final SmartHomeDataItem item
        in zeroColdWaterStatusAttribute.valueRange.dataList) {
      if (item.data == '1') {
        return true;
      }
    }

    return false;
  }

  /// 零冷水状态为开启，即按钮高亮
  bool get _zeroColdWaterOn {
    return _zeroColdWaterStatusAttribute?.value == '1';
  }

  SmartHomeDeviceAttribute? get _zeroColdWaterStatusAttribute {
    return device.attributeMap[_zeroColdWaterStatusKey];
  }

  SmartHomeDeviceAttribute? get _scourStateAttribute {
    return device.attributeMap[_scourStateKey];
  }

  SmartHomeDeviceAttribute? get _autoTimeOnOffKeyAttribute {
    return device.attributeMap[_autoTimeOnOffKey];
  }

  SmartHomeDeviceAttribute? get _prtlVersionAttribute {
    return device.attributeMap[_prtlVersionKey];
  }

  @override
  String get largeCardStatus => _commonCardState;

  @override
  String get middleCardStatus => _commonCardState;

  @override
  String get smallCardStatus {
    final String onOffStatus = onOffAttribute?.value ?? '';
    if (onOffStatus == trueValue) {
      return '开';
    } else if (onOffStatus == falseValue) {
      return '关';
    }
    return '';
  }

  String get _commonCardState {
    String status = '';
    final String temperature = _targetTempAttribute?.value ?? '';
    status += "水温${temperature.isNotEmpty ? '$temperature℃' : '--'}";
    //关机不显示加热状态

    if ((onOffAttribute?.value ?? falseValue) != trueValue) {
      return status;
    }

    if ((_flameStatusAttribute?.value ?? '') == trueValue && !powerOff) {
      status += ' 燃烧';
    } else if ((_flameStatusAttribute?.value ?? '') == falseValue) {
      status += ' 待机';
    }
    return status;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'targetTemp';
    final String value = device.attributeMap[key]?.value ?? '';
    return value.isNotEmpty
        ? DeviceCardAttribute(label: '水温', value: value, unit: '°C')
        : null;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '燃气热水器';
    final LargeDeviceCardFunctionSet largeDeviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
            name: name,
            componentViewModelList: <ComponentBaseViewModel>[
          _targetTempComponentViewModel(),
          if (_supportZeroColdWater) _zeroColdWaterViewModel(),
          _timeOnOffComponentViewModel(),
        ]);
    return <String, LargeDeviceCardFunctionSet>{
      largeDeviceCardFunctionSet.name: largeDeviceCardFunctionSet
    };
  }

  Future<bool> _checkTargetTemperatureContinue(
      SmartHomeDeviceAttribute? attribute) {
    return checkDeviceState(
        writable: attribute?.writable ?? false, writableMsg: '当前模式无法调温');
  }

  ComponentBaseViewModel _targetTempComponentViewModel() {
    final SmartHomeDeviceAttribute? attribute = _targetTempAttribute;
    final double currentValue = double.tryParse(attribute?.value ?? '0') ?? 0;
    final bool enable =
        !deviceOfflineOrPowerOff && (attribute?.writable ?? false);
    double maxValue = 0;
    double minValue = 0;
    double stepper = 0;

    List<double> valueList = <double>[];
    final SmartHomeDeviceAttributeValueRange? valueRange =
        attribute?.valueRange;
    if (valueRange?.type == SmartHomeDeviceAttributeValueRangeType.step) {
      final SmartHomeDataStep? dataStep = valueRange?.dataStep;
      maxValue = double.tryParse(dataStep?.maxValue ?? '0') ?? 0;
      minValue = double.tryParse(dataStep?.minValue ?? '0') ?? 0;
      stepper = double.tryParse(dataStep?.step ?? '0') ?? 0;
      valueList = _targetTempValueList(minValue, maxValue, stepper);
    } else if (valueRange?.type ==
        SmartHomeDeviceAttributeValueRangeType.list) {
      for (final SmartHomeDataItem item
          in valueRange?.dataList ?? <SmartHomeDataItem>[]) {
        valueList.add(double.tryParse(item.data) ?? 0);
      }
    }

    if (deviceOffline || loading) {
      valueList = <double>[];
    }
    final ExpandGasValueListViewModel valueListComponentViewModel =
        ExpandGasValueListViewModel(
      valueList: valueList,
      currentValue: currentValue,
      enable: enable,
      egwOn: _egwOn,
      egwTitle: _egwTitle().name,
      checkContinue: _checkTargetTemperatureContinue,
      safetyLockStatus: _safetyLockOpen,
      showBurningAlarm: _showBurningAlarm,
      clickCallback: (
        BuildContext? context,
      ) {
        checkDeviceState(
                writable: enable,
                writableMsg: '当前模式无法调温',
                powerOffMsg: '热水器关机，无法操作')
            .then((bool pass) {
          gio(cloudProgramName: SmartHomeConstant.temperatureControlDesc);
        });
      },
      valueChangeCallback: (BuildContext? context, String value, int index) {
        final Map<String, String> commands = <String, String>{};
        commands[attribute?.name ?? ''] = value;
        quickCtrlLECommand(device.basicInfo.deviceId, commands);
      },
    );
    return valueListComponentViewModel;
  }

  ComponentBaseViewModel _timeOnOffComponentViewModel() {
    final SmartHomeDeviceAttribute? attribute = _autoTimeOnOffKeyAttribute;
    final String autoOnOff = attribute?.value ?? '';
    final bool resnOn = autoOnOff == trueValue;
    final bool compEnable =
        !(deviceOffline || loading || alarm || autoOnOff.isEmpty);

    final ComponentBaseViewModel buttonComponentViewModel =
    FixedSwitchIconTextViewModel(
            icon: 'assets/components/card_auto_on_off.webp',
            isOn: compEnable && resnOn,
            text: '定时',
            enable: compEnable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '定时开关');
              checkDeviceState(
                      checkChildLock: false,
                      checkPowerOff: false,
                      writable: !alarm)
                  .then((bool pass) {
                if (pass) {
                  goToPageWithDebounce(
                      'http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=${device.basicInfo.deviceId}&businessType=waterHouseTimerSwitch');
                }
              });
            });

    return buttonComponentViewModel;
  }

  _IconTextBean _egwTitle() {
    for (final String model in _zgwModelList) {
      if (device.basicInfo.model.contains(model)) {
        return _IconTextBean(
            name: '智感温', icon: 'assets/components/card_egw.webp');
      }
    }
    for (final String model in _sjswModelList) {
      if (device.basicInfo.model.contains(model)) {
        return _IconTextBean(
            name: '四季随温', icon: 'assets/components/card_egw.webp');
      }
    }
    for (final String model in _znyModelList) {
      if (device.basicInfo.model.contains(model)) {
        return _IconTextBean(
            name: '智能浴', icon: 'assets/components/card_egw.webp');
      }
    }
    for (final String model in _egwProdNolList) {
      if (device.basicInfo.prodNo.contains(model)) {
        return _IconTextBean(
            name: 'E感温', icon: 'assets/components/card_egw.webp');
      }
    }
    return _IconTextBean(name: 'E感温', icon: 'assets/components/card_egw.webp');
  }

  /// 零冷水按钮ViewModel
  ComponentBaseViewModel _zeroColdWaterViewModel() {
    final _TextBean zeroColdWaterIconTextBean = _zeroColdWaterTitle();

    final SmartHomeDeviceAttribute? attribute = _zeroColdWaterStatusAttribute;
    final String value = attribute?.value ?? '';

    final bool compEnable =
        !(deviceOfflineOrPowerOff || onlineNotReady || alarm || value.isEmpty);

    final ComponentBaseViewModel buttonComponentViewModel =
        GWaterHeaterFixedTextViewModel(
            title: zeroColdWaterIconTextBean.title,
            subTitle: zeroColdWaterIconTextBean.subTitle,
            isOn: _zeroColdWaterOn,
            enable: compEnable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '单次零冷水');
              checkDeviceState(writable: !alarm, checkPowerOff: false)
                  .then((bool pass) {
                if (powerOff) {
                  ToastHelper.showToast('热水器关机，无法操作');
                  return false;
                }
                if (false == _zeroColdWaterStatusAttribute?.writable) {
                  ToastHelper.showToast('暂时无法操作');
                  return false;
                }

                if (pass) {
                  final Map<String, String> commands = <String, String>{
                    _zeroColdWaterStatusKey: _zeroColdWaterOn
                        ? _zeroColdWaterCloseValue
                        : _zeroColdWaterOpenValue
                  };
                  quickCtrlLECommand(device.basicInfo.deviceId, commands);
                }
              });
            });

    return buttonComponentViewModel;
  }

  /// 零冷水按钮标题
  _TextBean _zeroColdWaterTitle() {
    return _TextBean(title: isWashZeroColdWater ? '洗浴' : '单次', subTitle: '零冷水');
  }

  List<double> _targetTempValueList(
      double minValue, double maxValue, double stepper) {
    double value = minValue;

    final List<double> valueList = <double>[];

    if (device.basicInfo.typeId ==
        '201c120000118674181400418005560000000000000000000000000000000040') {
      if (_scourMode3) {
        valueList.add(32);
      }
      value = 35;
      while (stepper != 0 && value <= maxValue) {
        valueList.add(value);
        value += stepper;
      }
    } else if (_FA5OrFR5) {
      if (_scourState7) {
        valueList.add(30);
        valueList.add(32);
      }
      value = 35;
      while (stepper != 0 && value <= maxValue) {
        valueList.add(value);
        value += stepper;
      }
    } else {
      while (stepper != 0 && value <= maxValue) {
        valueList.add(value);
        value += stepper;
      }
    }
    return valueList;
  }
}

/// 图标文本数据
///
/// e感温使用
class _IconTextBean {
  String name = '';
  String icon = '';

  _IconTextBean({this.name = '', this.icon = ''});
}

/// 文本数据
///
/// 零冷水使用
class _TextBean {
  String title = '';
  String subTitle = '';
  _TextBean({this.title = '', this.subTitle = ''});
}
