/*
 * 描述：新风机 200861080082032424111cc6dcc2e20000005877d1e4bece3eb38894d1f48040
 * 作者：songFJ
 * 创建时间：2025/3/11
 */

import 'package:smart_home/device/device_view_model/new_fan_base_view_model.dart';

import '../component/view_model/expand_empty_view_model.dart';
import '../component_view_model/component_view_model.dart';

class NewFan48040ViewModel extends NewFanBaseViewModel {
  NewFan48040ViewModel({required super.device});

  @override
  String get operationModeKey => 'windMode';

  @override
  Map<String, ModeModel> get allModeMap => <String, ModeModel>{
        '0': ModeModel(value: '0', desc: '智能', icon: 'assets/new_fan/zhi.webp'),
        '1': ModeModel(value: '1', desc: '新风', icon: 'assets/new_fan/xin.webp'),
        '2':
            ModeModel(value: '2', desc: '净化', icon: 'assets/new_fan/jing.webp'),
        '3': ModeModel(value: '3', desc: '节能', icon: 'assets/new_fan/jie.webp'),
      };

  @override
  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      ExpandEmptyViewModel(),
      defaultModeComponent(expandFlex: 2),
      ExpandEmptyViewModel(),
    ];
  }

  @override
  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      ExpandEmptyViewModel(),
      modeComponent(expandFlex: 2),
      ExpandEmptyViewModel(),
    ];
  }
}
