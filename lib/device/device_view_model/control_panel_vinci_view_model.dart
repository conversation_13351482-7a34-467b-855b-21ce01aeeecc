import 'package:smart_home/device/device_view_model/control_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 达芬奇7寸 卡萨帝空气7寸集控
class ControlPanelVinciViewModel extends ControlPanelViewModel {
  ControlPanelVinciViewModel({required super.device});

  final String normalLightTypeId =
      '201c80c70c50031c101124baab48330000001bc44a0777e459aa8670af6c0740';
  final String smartLightTypeId =
      '201c80c70c50031c101124baab483300000061ac87249775b91bcb70f81da040';
  final String floorHeatingTypeId =
      '201c80c70c50031c250189e808d930000000a88543b054c96db5cb016ada0740';
  final String exhaustFanTypeId =
      '201c80c70c50031c340524baab48330000006880e54be492446138993b45db40';
  final String generalSwitchTypeId =
      '201c80c70c50031c141124baab483300000081469ad934a737bbee16f527f540';

  @override
  AccessoryDeviceType getAccessoryDeviceType(
      int index, int sortCode, DeviceCardViewModel viewmodel) {
    final String typeId = viewmodel.device.basicInfo.typeId;
    if (typeId == normalLightTypeId) {
      return AccessoryDeviceType.normalLight;
    } else if (typeId == smartLightTypeId) {
      return AccessoryDeviceType.smartLight;
    } else if (typeId == floorHeatingTypeId) {
      return AccessoryDeviceType.floorHeating;
    } else if (typeId == exhaustFanTypeId) {
      return AccessoryDeviceType.exhaustFan;
    } else if (typeId == generalSwitchTypeId) {
      return AccessoryDeviceType.generalSwitch;
    }
    return AccessoryDeviceType.unknown;
  }

  @override
  bool isDeviceEnabled(int index, int sortCode, DeviceCardViewModel child,
      AccessoryDeviceType type) {
    return type != AccessoryDeviceType.unknown;
  }
}
