/*
 * 描述：02空调
 * 作者：songFJ
 * 创建时间：2024/7/1
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/device/component/view_model/expand_value_list_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_popup_icon_text_view_model.dart';
import 'package:smart_home/device/component/view_model/text_view_model.dart';
import 'package:smart_home/device/component_view_model/circular_switch_view_model.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';
import 'package:smart_home/device/component_view_model/wind_speed_component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../common/constant.dart';
import '../component/view_model/expand_air_condition_value_list_view_model.dart';
import '../component_view_model/component_view_model.dart';
import '../component_view_model/grid_view_model.dart';
import '../device_info_model/device_card_attribute.dart';
import 'air_condition_base_view_model.dart';

class AirConditionCardViewModel extends AirConditionBaseViewModel {
  AirConditionCardViewModel({required super.device});

  /// -------------------  getter -----------------------//

  /// PMV对应关系
  Map<String, PMVModel> get pmvModelMap => <String, PMVModel>{
        '-3': PMVModel(value: '-3', desc: '冷'),
        '-2': PMVModel(value: '-2', desc: '凉'),
        '-1': PMVModel(value: '-1', desc: '稍凉'),
        '0': PMVModel(value: '0', desc: '舒适'),
        '1': PMVModel(value: '1', desc: '稍暖'),
        '2': PMVModel(value: '2', desc: '暖'),
        '3': PMVModel(value: '3', desc: '热'),
      };

  /// 子类若对应关系发生改变，则重写
  Map<String, AirConditionModeModel> get allModeMap =>
      <String, AirConditionModeModel>{
        '0': operationMode0Model,
        '1': AirConditionModeModel(
            value: '1',
            desc: '制冷',
            icon: 'assets/components/mode/mode_cold.webp'),
        '2': AirConditionModeModel(
            value: '2',
            desc: '除湿',
            icon: 'assets/components/mode/mode_dry.webp'),
        '4': AirConditionModeModel(
            value: '4',
            desc: '制热',
            icon: 'assets/components/mode/mode_warm.webp'),
        '6': AirConditionModeModel(
            value: '6',
            desc: '送风',
            icon: 'assets/components/mode/mode_wind.webp'),
      };

  /// 子类若对应关系发生改变，则重写
  Map<String, AirConditionWindSpeedModel> get allWindSpeedMap =>
      <String, AirConditionWindSpeedModel>{
        '5': AirConditionWindSpeedModel(value: '5', desc: '自动'),
        '4': AirConditionWindSpeedModel(value: '4', desc: '微风'),
        '3': AirConditionWindSpeedModel(value: '3', desc: '低风'),
        '8': AirConditionWindSpeedModel(value: '8', desc: '中低'),
        '2': AirConditionWindSpeedModel(value: '2', desc: '中风'),
        '7': AirConditionWindSpeedModel(value: '7', desc: '中高'),
        '1': AirConditionWindSpeedModel(value: '1', desc: '高风'),
        '6': AirConditionWindSpeedModel(value: '6', desc: '快速'),
      };

  String get pmv => 'PMV';

  String get shuShi => '舒适';

  String get zhiLengRe => '智冷热';

  String get targetTemperatureKey => 'targetTemperature';

  String get operationModeKey => 'operationMode';

  String get windSpeedKey => 'windSpeed';

  String get windSpeedLKey => 'windSpeedL';

  String get windSpeedRKey => 'windSpeedR';

  String get windSpeedAutoValue => '5';

  String get tempCompensationStatusKey => 'tempCompensationStatus';

  String get halfDegreeSettingStatusKey => 'halfDegreeSettingStatus';

  String get indoorTemperatureKey => 'indoorTemperature';

  String get pmvPopupTitle {
    String subPopupTitle = 'PMV(自控温/舒适/智冷热)';
    if (casarte) {
      subPopupTitle = '舒适(PMV)';
    }
    return subPopupTitle;
  }

  SmartHomeDeviceAttribute? get _indoorTemperatureAttribute {
    return device.attributeMap[indoorTemperatureKey];
  }

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  String get smallCardStatus => powerOff ? '关' : '开';

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = _indoorTemperatureAttribute?.value ?? '';

    if (value.isEmpty) {
      return null;
    }
    return DeviceCardAttribute(label: '室温', value: value, unit: '℃');
  }

  /// -------------------  public -----------------------//
  SmartHomeDeviceAttribute? get tempCompensationStatusAttribute {
    return device.attributeMap[tempCompensationStatusKey];
  }

  bool get tempCompensation {
    /// tempCompensationStatus为true，当前温度为：targetTemperature +0.5
    /// tempCompensationStatus为false，当前温度为： targetTemperature
    final String tempCompensationStatusValue =
        tempCompensationStatusAttribute?.value ?? falseValue;
    return tempCompensationStatusValue == trueValue;
  }

  bool get operationMode0 {
    return operationModeAttribute?.value == '0';
  }

  bool get pmvMode {
    return operationMode0 &&
        (allModeMap['0']?.desc == pmv ||
            allModeMap['0']?.desc == shuShi ||
            allModeMap['0']?.desc == zhiLengRe);
  }

  bool get isIntelligentHeatingCooling {
    return operationModeAttribute != null &&
        operationModeAttribute!.valueRange.dataList.any(
            (SmartHomeDataItem dataItem) =>
                dataItem.data == '0' && dataItem.desc == zhiLengRe);
  }

  bool get casarte => device.basicInfo.brand == '卡萨帝';

  AirConditionModeModel get operationMode0Model {
    if (casarte) {
      return AirConditionModeModel(
          value: '0',
          desc: shuShi,
          icon: 'assets/components/mode/mode_comfort.webp');
    } else if (isIntelligentHeatingCooling) {
      return AirConditionModeModel(
          value: '0',
          desc: zhiLengRe,
          icon: 'assets/components/mode/mode_intelligent_heating.webp');
    } else {
      return AirConditionModeModel(
          value: '0', desc: pmv, icon: 'assets/components/mode/mode_pmv.webp');
    }
  }

  bool get careMode {
    return operationMode0 && (allModeMap['0']?.desc == 'Care智能');
  }

  bool get operationMode6 {
    return operationModeAttribute?.value == '6';
  }

  SmartHomeDeviceAttribute? get halfDegreeSettingStatusAttribute {
    return device.attributeMap[halfDegreeSettingStatusKey];
  }

  SmartHomeDeviceAttribute? get targetTemperatureAttribute {
    return device.attributeMap[targetTemperatureKey];
  }

  SmartHomeDeviceAttribute? get operationModeAttribute {
    return device.attributeMap[operationModeKey];
  }

  SmartHomeDeviceAttribute? get windSpeedAttribute {
    return device.attributeMap[windSpeedKey];
  }

  SmartHomeDeviceAttribute? get windSpeedLAttribute {
    return device.attributeMap[windSpeedLKey];
  }

  SmartHomeDeviceAttribute? get windSpeedRAttribute {
    return device.attributeMap[windSpeedRKey];
  }

  bool get doubleWindSpeed {
    return (windSpeedRAttribute?.value ?? '') != '';
  }

  AirConditionModeModel? airConditionModeModelWithModeValue(String? modeValue) {
    final AirConditionModeModel? modeModel = allModeMap[modeValue];
    return modeModel;
  }

  String airConditionWindSpeedModelWithValue(String? windValue) {
    final AirConditionWindSpeedModel? windSpeedModel =
        allWindSpeedMap[windValue];
    return windSpeedModel?.desc ?? '风速';
  }

  /// -------------------  private -----------------------//
  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '空调';
    if (deviceOffline || loading) {
      final LargeDeviceCardFunctionSet deviceCardFunctionSet =
          LargeDeviceCardFunctionSet(
              name: name,
              componentViewModelList: <ComponentBaseViewModel>[
            ExpandValueListViewModel(
              valueList: <String>[],
              currentIndex: -1,
              unit: '',
              valueListEmptyCallback: (_) {
                checkDeviceState();
              },
              enable: false,
            ),
            FixedPopupIconTextViewModel(
                text: '模式',
                icon: 'assets/components/mode/mode_cold.webp',
                enable: false,
                clickCallback: (_) {
                  checkDeviceState();
                }),
            FixedPopupIconTextViewModel(
                text: '风速',
                icon: 'assets/components/wind_speed.webp',
                enable: false,
                clickCallback: (_) {
                  checkDeviceState();
                }),
          ]);
      return <String, LargeDeviceCardFunctionSet>{name: deviceCardFunctionSet};
    }

    final LargeDeviceCardFunctionSet deviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
            name: name,
            componentViewModelList: <ComponentBaseViewModel>[
          temperatureComponentViewModel(),
          modeComponentViewModel(),
          windSpeedComponentViewModel(),
        ]);
    return <String, LargeDeviceCardFunctionSet>{name: deviceCardFunctionSet};
  }

  /// ------------------- 温控 -------------------------//
  /// 温度显示
  ComponentBaseViewModel temperatureComponentViewModel() {
    final SmartHomeDeviceAttribute? attribute = targetTemperatureAttribute;
    //温控异常提示
    String writableMsg = '当前状态无法调温';
    if (powerOff) {
      writableMsg = SmartHomeConstant.toastAirPowerOffInfo;
    } else if (careMode) {
      writableMsg = 'care模式无法调温';
    } else if (operationMode6 &&
        !(targetTemperatureAttribute?.writable ?? false)) {
      writableMsg = '送风模式无法调温';
    }

    //温控按钮是否可点
    final bool enable =
        !powerOff && !careMode && (attribute?.writable ?? false);

    //温控组件所需参数默认值
    final List<double> valueList = <double>[];
    final List<ValueModel> valueModelList = <ValueModel>[];
    int _currentIndex = -1;

    final SmartHomeDeviceAttributeValueRange? valueRange =
        attribute?.valueRange;
    if (valueRange?.type == SmartHomeDeviceAttributeValueRangeType.step) {
      double _maxValue = 0;
      double _minValue = 0;
      double _stepper = 0;
      final SmartHomeDataStep? dataStep = valueRange?.dataStep;
      if (dataStep == null ||
          double.tryParse(dataStep.maxValue) == null ||
          double.tryParse(dataStep.minValue) == null ||
          double.tryParse(dataStep.step) == null) {
      } else {
        _maxValue = double.tryParse(dataStep.maxValue) ?? 0;
        _minValue = double.tryParse(dataStep.minValue) ?? 0;
        _stepper = double.tryParse(dataStep.step) ?? 0;

        //步长为0.5特殊逻辑
        if (tempCompensationStatusAttribute != null && !pmvMode) {
          _stepper = 0.5;
        }

        //通过步长、最大值、最小值组装数组
        if (_minValue == _maxValue) {
          valueList.add(_minValue);
        } else if (_minValue < _maxValue && _stepper == 0) {
          valueList.add(_minValue);
          valueList.add(_maxValue);
        } else if (_minValue < _maxValue && _stepper != 0) {
          for (double i = _minValue; i <= _maxValue; i += _stepper) {
            valueList.add(i);
          }
        }
        //将数组转为string类型供组件使用：步长为1保留整数，补充为0.5保留一位小数
        final int _fractionDigits = _stepper == 1 ? 0 : 1;
        if (double.tryParse(attribute?.value ?? '') != null) {
          double currentTempValue =
              double.tryParse(attribute?.value ?? '') ?? 0;
          if (tempCompensationStatusAttribute != null && tempCompensation) {
            currentTempValue = currentTempValue + 0.5;
          }

          for (int i = 0; i < valueList.length; i++) {
            if (pmvMode) {
              final String value =
                  valueList[i].toStringAsFixed(_fractionDigits);
              final PMVModel? model = pmvModelMap[value];
              String unit = '($value)';
              if (model == null) {
                unit = '℃';
              }

              valueModelList
                  .add(ValueModel(value: model?.desc ?? value, unit: unit));
            } else {
              valueModelList.add(ValueModel(
                  value: valueList[i].toStringAsFixed(_fractionDigits),
                  unit: '℃'));
            }

            if (valueList[i] == currentTempValue) {
              _currentIndex = i;
            }
          }
        }
      }
    }

    final ExpandAirConditionValueListViewModel valueListViewModel =
        ExpandAirConditionValueListViewModel(
            valueList: valueModelList,
            currentIndex: _currentIndex,
            enable: enable,
            noMorePreCallback: (BuildContext context) {
              ToastHelper.showToast('已是最低温度');
            },
            noMoreNextCallback: (BuildContext context) {
              ToastHelper.showToast('已是最高温度');
            },
            valueListEmptyCallback: (_) {
              checkDeviceState(
                  writable: enable,
                  writableMsg: writableMsg,
                  powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo);
            },
            checkContinue: () {
              return _checkTemperatureContinue(writableMsg, enable);
            },
            valueChangeCallback:
                (BuildContext context, String value, int currentIndex) {
              gio(cloudProgramName: SmartHomeConstant.temperatureControlDesc);

              final Map<String, String> commands = <String, String>{};
              final double currentValue = valueList[currentIndex];

              /// 有该属性，就下发false
              if (halfDegreeSettingStatusAttribute != null) {
                commands[halfDegreeSettingStatusKey] = falseValue;
              }

              /// 非pmv模式
              if (!pmvMode) {
                if (tempCompensationStatusAttribute != null) {
                  if (currentValue % 1 == 0) {
                    commands[tempCompensationStatusKey] = falseValue;
                  } else {
                    commands[tempCompensationStatusKey] = trueValue;
                  }
                  commands[targetTemperatureKey] = currentValue.toString();
                } else {
                  commands[targetTemperatureKey] = currentValue.toString();
                }
              } else {
                commands[targetTemperatureKey] = currentValue.toString();
              }
              quickCtrlLECommand(device.basicInfo.deviceId, commands,
                  onErrorCallback: (String errMsg) {
                ToastHelper.showToast(errMsg);
              });
            });
    return valueListViewModel;
  }

  Future<bool> _checkTemperatureContinue(String writableMsg, bool writable) {
    return checkDeviceState(
        writable: writable,
        writableMsg: writableMsg,
        powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo);
  }

  Future<bool> _checkWindSpeedContinue(SmartHomeDeviceAttribute? attribute) {
    return checkDeviceState(
        writable: attribute?.writable ?? false,
        writableMsg: SmartHomeConstant.toastAirWindDisable);
  }

  /// ------------------- 模式选择 -------------------------//
  /// 模式选择
  ComponentBaseViewModel modeComponentViewModel() {
    final SmartHomeDeviceAttribute? attribute = operationModeAttribute;
    final String? value = attribute?.value;
    final bool enable = !powerOff && (attribute?.writable ?? false);

    /// 存储模式选择弹出框
    final PopupComponentViewModel popupComponentViewModel =
        modePopupComponent(operationModeKey, attribute, value ?? '');
    popupFuncSet[popupComponentViewModel.identification] =
        popupComponentViewModel;
    final ComponentBaseViewModel buttonComponentViewModel =
        FixedPopupIconTextViewModel(
            popupComponentViewModel: popupComponentViewModel,
            text: airConditionModeModelWithModeValue(value)?.desc ?? '模式',
            icon: airConditionModeModelWithModeValue(value)?.icon ??
                'assets/components/mode/mode_cold.webp',
            enable: enable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: popupComponentViewModel.title);
              checkDeviceState(
                      writable: enable,
                      writableMsg: SmartHomeConstant.toastAirModeDisable,
                      powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo)
                  .then((bool pass) {
                if (pass) {
                  showPopup(context, popupComponentViewModel);
                }
              });
            });

    return buttonComponentViewModel;
  }

  /// 模式选择弹出框
  PopupComponentViewModel modePopupComponent(String attributeKey,
      SmartHomeDeviceAttribute? attribute, String currentValue) {
    final bool enable = !powerOff && (attribute?.writable ?? false);
    final List<ComponentBaseViewModel> list = <ComponentBaseViewModel>[];

    final List<SmartHomeDataItem> dataList =
        attribute?.valueRange.dataList ?? <SmartHomeDataItem>[];

    bool pmvDevice = false;

    final Map<String, AirConditionModeModel> modeMap =
        <String, AirConditionModeModel>{};
    modeMap.addAll(allModeMap);
    modeMap.forEach((String key, AirConditionModeModel modeModel) {
      if (dataList.any((SmartHomeDataItem dataItem) => dataItem.data == key)) {
        final CircularSwitchComponentModel viewModel =
            CircularSwitchComponentModel(
                icon: modeModel.icon,
                text: modeModel.desc,
                selected: key == currentValue,
                enable: enable,
                clickCallback: (BuildContext? context) {
                  modelSelectGio(workMode: modeModel.desc);
                  checkDeviceState(
                          writable: enable,
                          writableMsg: SmartHomeConstant.toastAirModeDisable,
                          powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo)
                      .then((bool pass) {
                    if (pass) {
                      if (currentValue != modeModel.value) {
                        final Map<String, String> commands = <String, String>{};
                        commands[operationModeKey] = modeModel.value;
                        quickCtrlLECommand(device.basicInfo.deviceId, commands,
                            onErrorCallback: (String errMsg) {
                          ToastHelper.showToast(errMsg);
                        });
                      }
                    }
                  });
                  hidePopup(context);
                });
        list.add(viewModel);
        if (viewModel.text == pmv ||
            viewModel.text == shuShi ||
            viewModel.text == zhiLengRe) {
          pmvDevice = true;
        }
      }
    });
    final String subPopupTitle = pmvPopupTitle;
    PopupComponentViewModel? subPopupComponentViewModel;
    if (pmvDevice) {
      subPopupComponentViewModel = PopupComponentViewModel(
          title: subPopupTitle,
          cancelTitle: '我知道了',
          deviceId: deviceId,
          attributeKey: '${attributeKey}_sub',
          componentList: <ComponentBaseViewModel>[
            TextViewModel(
              text:
                  '在$subPopupTitle模式下，一键智能自动运行，无须多次调整。空调将自动采集到温度、湿度、风速等参数，通过PMV智能控制系统处理后，按照使人体最舒适的方式运行，省心省力。',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textColor: AppSemanticColors.item.primary,
            ),
            TextViewModel(
              text:
                  '考虑到不同人群对温度的需求不同，除了初始默认温度以外，还可以设置为冷(-3℃)、凉(-2℃)、稍凉(-1℃)、稍暖(+1℃)、暖(+2℃)、热(+3℃)。完成个性化设置后，系统将记忆温度偏好，下次开机直接按照定制温度运行。\n\n注：部分产品采集不到的参数按默认值处理',
              fontSize: 12,
              textColor: AppSemanticColors.item.primary,
            ),
          ]);
    }

    final List<ComponentBaseViewModel> popupComponentList =
        <ComponentBaseViewModel>[
      GridViewModel(
          componentList: list,
          crossAxisCount: 4,
          mainAxisExtent: 73,
          crossAxisSpacing: 0,
          mainAxisSpacing: 24)
    ];

    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '模式选择',
            deviceId: device.basicInfo.deviceId,
            attributeKey: attributeKey,
            componentList: popupComponentList,
            subPopupComponentViewModel: subPopupComponentViewModel);
    return popupComponentViewModel;
  }

  void modelSelectGio({String? workMode, String? modelValue}) {
    gioTrack(GioConst.gioModeSelect, <String, String>{
      'product_type': appTypeName,
      'workMode': workMode ?? '',
      'model_value': modelValue ?? ''
    });
  }

  /// ------------------- 风速调节 -------------------------//
  /// 风速
  ComponentBaseViewModel windSpeedComponentViewModel() {
    if (doubleWindSpeed) {
      /// 双风速显示
      final PopupComponentViewModel popupComponentViewModel =
          _doubleWindSpeedPopupComponent();
      popupFuncSet[popupComponentViewModel.identification] =
          popupComponentViewModel;

      final bool enable = !powerOff &&
          ((windSpeedLAttribute?.writable ?? false) ||
              (windSpeedRAttribute?.writable ?? false));

      /// 双风速显示
      final String doubleWindSpeedText =
          '${airConditionWindSpeedModelWithValue(windSpeedLAttribute?.value)}'
          '|${airConditionWindSpeedModelWithValue(windSpeedRAttribute?.value)}';
      final ComponentBaseViewModel buttonComponentViewModel =
          FixedPopupIconTextViewModel(
              popupComponentViewModel: popupComponentViewModel,
              text: doubleWindSpeedText,
              icon: 'assets/components/wind_speed.webp',
              enable: enable,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: popupComponentViewModel.title);
                checkDeviceState(
                        writable: enable,
                        writableMsg: SmartHomeConstant.toastAirWindDisable,
                        powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo)
                    .then((bool pass) {
                  if (pass) {
                    showPopup(context, popupComponentViewModel);
                  }
                });
              });

      return buttonComponentViewModel;
    } else {
      /// 单
      final bool windSpeedL =
          windSpeedLAttribute != null && windSpeedAttribute == null;
      final SmartHomeDeviceAttribute? attribute =
          windSpeedL ? windSpeedLAttribute : windSpeedAttribute;
      final String? value = attribute?.value;
      final bool enable = !powerOff && (attribute?.writable ?? false);
      PopupComponentViewModel popupComponentViewModel = PopupComponentViewModel(
          deviceId: device.basicInfo.deviceId, attributeKey: windSpeedKey);
      if (windSpeedL) {
        popupComponentViewModel = _windSpeedPopupComponent(
            windSpeedLKey, windSpeedLAttribute, value ?? '');
        popupFuncSet[popupComponentViewModel.identification] =
            popupComponentViewModel;
      } else {
        popupComponentViewModel = _windSpeedPopupComponent(
            windSpeedKey, windSpeedAttribute, value ?? '');
        popupFuncSet[popupComponentViewModel.identification] =
            popupComponentViewModel;
      }

      final ComponentBaseViewModel viewModel = FixedPopupIconTextViewModel(
        text: airConditionWindSpeedModelWithValue(value),
        icon: 'assets/components/wind_speed.webp',
        enable: enable,
        popupComponentViewModel: popupComponentViewModel,
        clickCallback: (BuildContext? context) {
          gio(cloudProgramName: popupComponentViewModel.title);
          checkDeviceState(
                  writable: enable,
                  writableMsg: SmartHomeConstant.toastAirWindDisable,
                  powerOffMsg: SmartHomeConstant.toastAirPowerOffInfo)
              .then((bool pass) {
            if (pass) {
              showPopup(context, popupComponentViewModel);
            }
          });
        },
      );

      return viewModel;
    }
  }

  /// 双风速调节弹框
  PopupComponentViewModel _doubleWindSpeedPopupComponent() {
    final WindSpeedComponentViewModel speedComponentViewModelL =
        _windSpeedViewModel(windSpeedLKey, windSpeedLAttribute,
            windSpeedLAttribute?.value ?? '',
            name: '左塔');
    final WindSpeedComponentViewModel speedComponentViewModelR =
        _windSpeedViewModel(windSpeedRKey, windSpeedRAttribute,
            windSpeedRAttribute?.value ?? '',
            name: '右塔');
    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '风速调节',
            deviceId: device.basicInfo.deviceId,
            attributeKey: windSpeedLKey + windSpeedRKey,
            componentList: <ComponentBaseViewModel>[
          speedComponentViewModelL,
          speedComponentViewModelR
        ]);
    return popupComponentViewModel;
  }

  /// 单风速调节弹框
  PopupComponentViewModel _windSpeedPopupComponent(String attributeKey,
      SmartHomeDeviceAttribute? attribute, String currentValue) {
    final WindSpeedComponentViewModel viewModel =
        _windSpeedViewModel(attributeKey, attribute, currentValue);

    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '风速调节',
            deviceId: device.basicInfo.deviceId,
            attributeKey: attributeKey,
            componentList: <ComponentBaseViewModel>[viewModel]);
    return popupComponentViewModel;
  }

  WindSpeedComponentViewModel _windSpeedViewModel(String attributeKey,
      SmartHomeDeviceAttribute? attribute, String currentValue,
      {String? name}) {
    final SmartHomeDeviceAttributeValueRange? valueRange =
        attribute?.valueRange;
    final List<String> valueList = <String>[];
    final List<SmartHomeDataItem> dataList =
        valueRange?.dataList ?? <SmartHomeDataItem>[];

    final Map<String, AirConditionWindSpeedModel> speedMap =
        <String, AirConditionWindSpeedModel>{};
    speedMap.addAll(allWindSpeedMap);

    /// 送风模式，去掉自动档位
    if (operationMode6) {
      speedMap.remove(windSpeedAutoValue);
    }

    speedMap.forEach((String key, AirConditionWindSpeedModel speedModel) {
      if (dataList.any((SmartHomeDataItem dataItem) => dataItem.data == key)) {
        valueList.add(speedModel.desc);
      }
    });

    double index =
        valueList.indexOf(speedMap[currentValue]?.desc ?? '').toDouble();
    if (index < 0) {
      index = 0;
    }

    final WindSpeedComponentViewModel viewModel = WindSpeedComponentViewModel(
        title: name,
        valueList: valueList,
        selectedValue: index,
        attribute: attribute,
        checkContinue: _checkWindSpeedContinue,
        onChangeEnd: (double selectedIndex) {
          final Map<String, String> commands = <String, String>{};
          AirConditionWindSpeedModel? speedModel;
          speedMap.forEach((String key, AirConditionWindSpeedModel model) {
            if (model.desc == valueList[selectedIndex.toInt()]) {
              speedModel = model;
            }
          });
          if (speedModel != null) {
            commands[attributeKey] = speedModel!.value;
          }
          quickCtrlLECommand(device.basicInfo.deviceId, commands,
              onErrorCallback: (String errMsg) {
            ToastHelper.showToast(errMsg);
          });
        });
    return viewModel;
  }
}

class AirConditionModeModel {
  String value;
  String desc;
  String icon;

  AirConditionModeModel({this.value = '', this.desc = '', this.icon = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AirConditionModeModel &&
          runtimeType == other.runtimeType &&
          desc == other.desc &&
          icon == other.icon;

  @override
  int get hashCode => desc.hashCode ^ icon.hashCode;

  @override
  String toString() {
    return 'AirConditionModeModel{desc: $desc, icon: $icon}';
  }
}

class AirConditionWindSpeedModel {
  String value;
  String desc;

  AirConditionWindSpeedModel({this.value = '', this.desc = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AirConditionWindSpeedModel &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          desc == other.desc;

  @override
  int get hashCode => value.hashCode ^ desc.hashCode;

  @override
  String toString() {
    return 'AirConditionWindSpeedModel{value: $value, desc: $desc}';
  }
}

class PMVModel {
  String value;
  String desc;

  PMVModel({this.value = '', this.desc = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PMVModel &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          desc == other.desc;

  @override
  int get hashCode => value.hashCode ^ desc.hashCode;
}
