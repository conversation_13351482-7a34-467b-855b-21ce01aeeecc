import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class DoubleDrawerDishwasherCardViewModel extends DeviceCardViewModel {
  DoubleDrawerDishwasherCardViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  /// 已开机且有剩余时间时
  /// 开关机上报"开机" :
  ///  1.1条件：当前"运行阶段(cyclePhase )"有值且不为"0"和“11”时
  ///  1.2条件：当前"运行阶段(cyclePhase )"为"12"时
  @override
  bool get runningMode {
    if (powerOff) {
      return false;
    }
    final String isUpperDrawerOn =
        device.attributeMap['onOffStatusUP']?.value ?? '';
    final String isLowerDrawerOn =
        device.attributeMap['onOffStatusDN']?.value ?? '';
    final String cyclePhaseUpperDrawer =
        device.attributeMap['cyclePhaseUP']?.value ?? '';
    final String cyclePhaseLowerDrawer =
        device.attributeMap['cyclePhaseDN']?.value ?? '';

    if (_upRunningMode(isUpperDrawerOn, cyclePhaseUpperDrawer) ||
        _downRunningMode(isLowerDrawerOn, cyclePhaseLowerDrawer)) {
      return true;
    }

    return false;
  }

  /// 上桶运行状态
  bool _upRunningMode(String isUpperDrawerOn, String cyclePhaseUpperDrawer) {
    if (isUpperDrawerOn == SmartHomeConstant.deviceAttrTrue &&
        cyclePhaseUpperDrawer.isNotEmpty) {
      final String upRemainTime = _getRemainTime(1);
      if (cyclePhaseUpperDrawer != '0' &&
          cyclePhaseUpperDrawer != '11' &&
          upRemainTime.isNotEmpty) {
        return true;
      }
      if (cyclePhaseUpperDrawer == '12' && upRemainTime.isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  /// 下桶运行状态
  bool _downRunningMode(String isLowerDrawerOn, String cyclePhaseLowerDrawer) {
    if (isLowerDrawerOn == SmartHomeConstant.deviceAttrTrue &&
        cyclePhaseLowerDrawer.isNotEmpty) {
      final String downRemainTime = _getRemainTime(0);
      if (cyclePhaseLowerDrawer != '0' &&
          cyclePhaseLowerDrawer != '11' &&
          downRemainTime.isNotEmpty) {
        return true;
      }
      if (cyclePhaseLowerDrawer == '12' && downRemainTime.isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  bool _upAndDownOff(String isUpperDrawerOn, String isLowerDrawerOn) =>
      isUpperDrawerOn == SmartHomeConstant.deviceAttrFalse &&
      isLowerDrawerOn == SmartHomeConstant.deviceAttrFalse;

  bool upStandBy(String isUpperDrawerOn, String cyclePhaseUpperDrawer) =>
      isUpperDrawerOn == SmartHomeConstant.deviceAttrTrue &&
      (cyclePhaseUpperDrawer == '0' || cyclePhaseUpperDrawer == '11');

  bool downStandBy(String isLowerDrawerOn, String cyclePhaseLowerDrawer) =>
      isLowerDrawerOn == SmartHomeConstant.deviceAttrTrue &&
      (cyclePhaseLowerDrawer == '0' || cyclePhaseLowerDrawer == '11');

  bool _upAndDownStandby(String isUpperDrawerOn, String cyclePhaseUpperDrawer,
          String isLowerDrawerOn, String cyclePhaseLowerDrawer) =>
      upStandBy(isUpperDrawerOn, cyclePhaseUpperDrawer) &&
      downStandBy(isLowerDrawerOn, cyclePhaseLowerDrawer);

  @override
  bool get powerOff {
    final String isUpperDrawerOn =
        device.attributeMap['onOffStatusUP']?.value ?? '';
    final String isLowerDrawerOn =
        device.attributeMap['onOffStatusDN']?.value ?? '';
    if (_upAndDownOff(isUpperDrawerOn, isLowerDrawerOn)) {
      return true;
    }
    return false;
  }

  @override
  String get devicePowerOffInfo {
    if (powerOff) {
      return '已关机';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeUnit() {
    final String isUpperDrawerOn =
        device.attributeMap['onOffStatusUP']?.value ?? '';
    final String isLowerDrawerOn =
        device.attributeMap['onOffStatusDN']?.value ?? '';
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '', unit: '');
    }
    final String cyclePhaseUpperDrawer =
        device.attributeMap['cyclePhaseUP']?.value ?? '';
    final String cyclePhaseLowerDrawer =
        device.attributeMap['cyclePhaseDN']?.value ?? '';
    if (_upAndDownStandby(isUpperDrawerOn, cyclePhaseUpperDrawer,
        isLowerDrawerOn, cyclePhaseLowerDrawer)) {
      return DeviceCardAttribute(label: '', value: '待机', unit: '');
    }
    return null;
  }

  DeviceCardAttribute? _handleDeviceCardAttribute(bool isUp) {
    final String intelligentStoreTime = device
            .attributeMap[
                isUp ? 'intelligentStoreTimeUP' : 'intelligentStoreTimeDN']
            ?.value ??
        '';
    final String isDrawerOn =
        device.attributeMap[isUp ? 'onOffStatusUP' : 'onOffStatusDN']?.value ??
            '';
    final String cyclePhaseDrawer =
        device.attributeMap[isUp ? 'cyclePhaseUP' : 'cyclePhaseDN']?.value ??
            '';
    if (isDrawerOn == SmartHomeConstant.deviceAttrFalse) {
      return DeviceCardAttribute(
          label: isUp ? '上抽' : '下抽', value: '关机', unit: '');
    }
    if (isUp
        ? upStandBy(isDrawerOn, cyclePhaseDrawer)
        : downStandBy(isDrawerOn, cyclePhaseDrawer)) {
      return DeviceCardAttribute(
          label: isUp ? '上抽' : '下抽', value: '待机', unit: '');
    }

    if (isDrawerOn == SmartHomeConstant.deviceAttrTrue) {
      if (cyclePhaseDrawer == '16' || cyclePhaseDrawer == '17') {
        if (intelligentStoreTime != '0') {
          return DeviceCardAttribute(
              label: isUp ? '上抽剩' : '下抽剩',
              value: '$intelligentStoreTime天',
              unit: '');
        }
      }
      final String washProgDrawer =
          device.attributeMap[isUp ? 'washProgUP' : 'washProgDN']?.value ?? '';
      if (cyclePhaseDrawer.isNotEmpty &&
          cyclePhaseDrawer != '0' &&
          cyclePhaseDrawer != '11' &&
          cyclePhaseDrawer != '16' &&
          cyclePhaseDrawer != '17') {
        if (washProgDrawer == '65' && intelligentStoreTime != '0') {
          return DeviceCardAttribute(
              label: isUp ? '上抽剩' : '下抽剩',
              value: '$intelligentStoreTime天',
              unit: '');
        }

        if (washProgDrawer != '65') {
          //下抽剩x分钟
          final String remainTime = _getRemainTime(isUp ? 1 : 0);
          if (remainTime.isNotEmpty && remainTime != '0') {
            return DeviceCardAttribute(
                label: isUp ? '上抽剩' : '下抽剩', value: remainTime, unit: '');
          }
        }
      }
    }
    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    return _handleDeviceCardAttribute(true);
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    return _handleDeviceCardAttribute(false);
  }

  String _getRemainTime(int index) {
    final int remainTime = calculateRemainTime(index);

    if (remainTime == 0) {
      return '';
    }
    return '${remainTime}min';
  }

  int calculateRemainTime(int index) {
    String hourKey;
    String minuteKey;

    if (index == 1) {
      hourKey = 'remainingTimeHHUP';
      minuteKey = 'remainingTimeMMUP';
    } else {
      hourKey = 'remainingTimeHHDN';
      minuteKey = 'remainingTimeMMDN';
    }
    final String remainHH = device.attributeMap[hourKey]?.value ?? '';
    final String remainMM = device.attributeMap[minuteKey]?.value ?? '';
    final int hour = int.tryParse(remainHH) ?? 0;
    final int minute = int.tryParse(remainMM) ?? 0;
    final int remainTime = hour * 60 + minute;
    return remainTime;
  }
}
