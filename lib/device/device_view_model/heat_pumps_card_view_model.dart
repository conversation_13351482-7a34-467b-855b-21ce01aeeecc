import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class HeatPumpsCardViewModel extends DeviceCardViewModel {
  HeatPumpsCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  // 以下成品编码状态区显示"当前室温"
  List<String> get _roomTempeNameList => <String>[
    'GK0HUU000',
    'GK0HUT000',
    'GK0HUS000',
    'GK0HU2001',
    'GK0HU1001',
    'GK0HUZ000',
    'GK0HU3001',
    'GK0HUV000',
    'GK0HUY000',
    'GK0HUR000',
    'GK0HUX000',
    'GK0HU0001',
  ];

  String get tempName {
    return _roomTempeNameList.contains(device.basicInfo.prodNo) ? '室温' : '水温';
  }

  @override
  String get smallCardStatus {
    return powerOff ? '关' : '开';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = getDeviceCardAttrValueByName('currentTemperature');
    return value.isNotEmpty
        ? DeviceCardAttribute(label: tempName, value: value, unit: '°C')
        : null;
  }
}
