import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class PrefilterCardViewModel extends DeviceCardViewModel {
  PrefilterCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['runningMode']?.value ?? '';
    final String attr = _getSoftWaterQuality(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  String _getSoftWaterQuality(String value) {
    String returnValue = '';
    if (value == '1') {
      returnValue = '运行';
    } else if (value == '2') {
      returnValue = '冲洗';
    } else if (value == '3') {
      returnValue = '异常';
    }

    return returnValue;
  }
}
