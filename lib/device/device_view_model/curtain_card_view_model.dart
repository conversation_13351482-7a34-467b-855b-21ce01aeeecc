import 'dart:async';

import 'package:device_utils/log/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../common/constant.dart';
import '../../store/smart_home_store.dart';
import '../component/view_model/expand_switch_icon_text_view_model.dart';
import '../device_info_model/device_card_attribute.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../store/device_action.dart';

class CurtainCardViewModel extends DeviceCardViewModel {
  CurtainCardViewModel({required super.device});

  // 不支持暂停的typeId集合
  List<String> get _unSupportPauseTypeId => <String>[
    _0040TypeId,
    _d440TypeId,
  ];

  String get _0040TypeId =>
      '201c120024000810140200000000000000000000000000000000000000000040';
  String get _d440TypeId =>
      '201c80c70c50031c14132acaedd2bf00000083ff386b5bd068bb062a8b69d440';
  String get _b740TypeId =>
      '201c80c70c50031c1413c001c47138000000ec24bf805b4531c836bfe2d0b740';


  // 是否不支持暂停
  bool get _isUnSupportPause =>
      _unSupportPauseTypeId.contains(device.basicInfo.typeId);

  final String _allOpenText = '全开';
  final String _allCloseText = '全关';
  final String _runningText = '运行中';
  final String _allOpenToastText = '窗帘已打开，请勿重复操作';
  final String _allCloseToastText = '窗帘已关闭，请勿重复操作';

  String get _openDegreeKey => 'openDegree';

  String get _onOffStatusKey => 'onOffStatus';

  String get _pauseKey => 'pause';

  // 自定义属性判断运行中状态
  String get _curtainRunningKey => 'curtainRunning';

  // 是否全打开
  bool get _isAllOpen {
    final String onOffStatus = onOffAttribute?.value ?? '';
    return onOffStatus == trueValue;
  }

  bool get _isSupportBigCard {
    return
    '${device.basicInfo.bigClass}#${device.basicInfo.middleClass}' == '14#13' ||
        device.basicInfo.typeId == _0040TypeId ||
        device.basicInfo.typeId == _b740TypeId;
  }

  Timer? _runningTimer;

  SmartHomeDeviceAttribute? get _openDegreeAttribute {
    return device.attributeMap[_openDegreeKey];
  }

  SmartHomeDeviceAttribute? get _curtainRunningAttribute {
    return device.attributeMap[_curtainRunningKey];
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (_curtainStatusText().isNotEmpty) {
      return DeviceCardAttribute(label: '', value: _curtainStatusText(), unit: '');
    }
    return super.deviceCardAttributeOne();
  }

  String _curtainStatusText() {
    final String isRunning = _curtainRunningAttribute?.value ?? falseValue;
    if (isRunning == trueValue) {
      return _runningText;
    }
    switch (_openStatus) {
      case curtainOpenStatus.allOpen:
        return _allOpenText;
      case curtainOpenStatus.allClose:
        return _allCloseText;
      case curtainOpenStatus.halfOpen:
        final String openDegree = _openDegreeAttribute?.value ?? '';
        return openDegree.isNotEmpty ? '开着$openDegree%' : '';
      default:
        return '';
    }
  }

  // 窗帘开关状态
  curtainOpenStatus get _openStatus {

    if (_isUnSupportPause) {
      return _isAllOpen ? curtainOpenStatus.allOpen : curtainOpenStatus.allClose;
    }

    final String openDegree = _openDegreeAttribute?.value ?? '';
    if (openDegree.isNotEmpty) {
      final int degree = int.tryParse(openDegree) ?? 0;
      if (degree == 0) {
        return curtainOpenStatus.allClose;
      } else if (degree == 100) {
        return curtainOpenStatus.allOpen;
      } else {
        return curtainOpenStatus.halfOpen;
      }
    } else {
      return _isAllOpen ? curtainOpenStatus.allOpen : curtainOpenStatus.allClose;
    }
  }

  @override
  bool supportPowerOnOff() {
    return false;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    if (_isSupportBigCard) {
      const String name = '窗帘';
      final LargeDeviceCardFunctionSet largeDeviceCardFunctionSet =
      LargeDeviceCardFunctionSet(
        name: name,
        componentViewModelList: <ComponentBaseViewModel>[
          _buttonWith(ButtonType.open),
          if (!_isUnSupportPause) _buttonWith(ButtonType.pause),
          _buttonWith(ButtonType.close),
        ],
      );
      return <String, LargeDeviceCardFunctionSet>{
        largeDeviceCardFunctionSet.name: largeDeviceCardFunctionSet
      };
    }
    return null;
  }

  ComponentBaseViewModel _buttonWith(ButtonType type) {
    final _ButtonIconModel btnViewModel = _btnViewModel(type);
    final bool compEnable = !(deviceOffline || alarm || loading);

    final ComponentBaseViewModel buttonComponentViewModel =
        ExpandSwitchIconTextViewModel(
            text: btnViewModel.name,
            icon: btnViewModel.icon,
            isOn: false,
            enable: compEnable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '窗帘-${btnViewModel.name}');
              checkDeviceState(writable: !alarm, checkPowerOff: false)
                  .then((bool pass) {
                if (pass) {
                  _sendCommand(type);
                }
          }).catchError((dynamic err) {
            DevLogger.error(
                tag: SmartHomeConstant.package,
                msg: 'CurtainCardViewModel checkDeviceState err:$err');
          });
        });

    return buttonComponentViewModel;
  }

  void _sendCommand(ButtonType type) {
    String cmdKey = '';
    String cmdValue = '';
    if (type == ButtonType.open) {
      cmdKey = _onOffStatusKey;
      cmdValue = trueValue;
    } else if (type == ButtonType.pause) {
      cmdKey = _pauseKey;
      cmdValue = trueValue;
    } else if (type == ButtonType.close) {
      cmdKey = _onOffStatusKey;
      cmdValue = falseValue;
    }

    if (type == ButtonType.open && _openStatus == curtainOpenStatus.allOpen) {
      ToastHelper.showToast(_allOpenToastText);
      return;
    } else if (type == ButtonType.close && _openStatus == curtainOpenStatus.allClose) {
      ToastHelper.showToast(_allCloseToastText);
      return;
    }

    final Map<String, String> commands = <String, String>{
      cmdKey: cmdValue,
    };
    quickCtrlLECommand(device.basicInfo.deviceId, commands);

    // 更新运行中状态，点击后10S内显示运行中状态，10S后显示上报的真实状态
    if (type == ButtonType.open || type == ButtonType.close) {
      SmartHomeDeviceAttribute attribute =
      SmartHomeDeviceAttribute(name: _curtainRunningKey, value: trueValue);
      smartHomeStore
          .dispatch(UpdateCustomAttributeAction(deviceId, attribute));

      _runningTimer?.cancel();
      _runningTimer = Timer(const Duration(seconds: 10), () {
        attribute =
            SmartHomeDeviceAttribute(name: _curtainRunningKey, value: falseValue);
        smartHomeStore.dispatch(
            UpdateCustomAttributeAction(deviceId, attribute));
        _runningTimer?.cancel();
      });
    }
  }
}

_ButtonIconModel _btnViewModel(ButtonType type) {
  if (type == ButtonType.open) {
    return _ButtonIconModel(
        name: '全开', icon: 'assets/components/curtain_open_status.webp');
  }

  if (type == ButtonType.pause) {
    return _ButtonIconModel(
        name: '暂停', icon: 'assets/components/clothes_pause.webp');
  }

  if (type == ButtonType.close) {
    return _ButtonIconModel(
        name: '全关', icon: 'assets/components/curtain_close_status.webp');
  }
  return _ButtonIconModel();
}

class _ButtonIconModel {
  String name = '';
  String icon = '';

  _ButtonIconModel({this.name = '', this.icon = ''});
}

enum ButtonType { open, close, pause }

enum curtainOpenStatus { allOpen, allClose, halfOpen }

