import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class ShoeBoxCardViewModel extends DeviceCardViewModel {
  ShoeBoxCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'runningStatus';
    final String value = device.attributeMap[key]?.value ?? '';
    final String attr = _judgeRunningMode(value);

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  String _judgeRunningMode(String key) {
    String value = '';
    if (key == '0') {
      value = '待机';
    } else if (key == '1') {
      value = '除臭中';
    } else if (key == '2') {
      value = '杀菌中';
    } else if (key == '3') {
      value = '风干中';
    } else if (key == '4') {
      value = '烘干中';
    } else if (key == '5') {
      value = '香薰中';
    } else if (key == '6') {
      value = '工作暂停';
    } else if (key == '7') {
      value = '关机';
    } else if (key == '10') {
      value = '净化中';
    } else if (key == '11') {
      value = '保暖中';
    } else if (key == '12') {
      value = '工作完成';
    } else if (key == '13') {
      value = '空闲';
    }
    return value;
  }
}
