import 'package:smart_home/device/component/view_model/expand_label_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 安防门锁-双电量门锁
class SafetyCardViewModel extends DeviceCardViewModel {
  SafetyCardViewModel({required super.device});

  final String doorStatusKey = 'doorStatus';
  final String doubleLockStatusKey = 'doubleLockStatus';
  final String batteryKey = 'battery';
  final String lock2BatteryKey = 'lock2Battery';

  @override
  bool get childLockOn => false;

  @override
  bool supportPowerOnOff() {
    return false;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String doorStatusValue = _getDoorStatusAttrValue();
    final String doubleLockStatusValue = _getDoubleLockStatusAttrValue();
    String attr = '';
    if (doorStatusValue == trueValue) {
      attr = '未锁';
    } else if (doorStatusValue == falseValue) {
      if (doubleLockStatusValue == falseValue) {
        attr = '已锁';
      } else if (doubleLockStatusValue == trueValue) {
        attr = '反锁';
      }
    }

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    final String battery = _getBatteryAttrValue();
    final String lock2Battery = _getLock2BatteryAttrValue();
    String attr = '';
    if (battery.isNotEmpty) {
      attr = '$battery%';
      if (lock2Battery.isEmpty) {
        return DeviceCardAttribute(label: '电量', value: battery, unit: '%');
      }
    }

    if (lock2Battery.isNotEmpty) {
      attr = '$attr $lock2Battery%';
    }

    if (attr.isNotEmpty) {
      return DeviceCardAttribute(label: '电量', value: attr, unit: '');
    }

    return super.deviceCardAttributeTwo();
  }

  @override
  String get largeCardStatus => '';

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '门锁';
    if (deviceOffline || loading) {
      return <String, LargeDeviceCardFunctionSet>{
        name: LargeDeviceCardFunctionSet(
            name: name,
            componentViewModelList: _defaultComponentViewModelList())
      };
    }
    final List<ComponentBaseViewModel> componentModelList =
        <ComponentBaseViewModel>[];
    final String doorStatusValue = _getDoorStatusAttrValue();
    final String doubleLockStatusValue = _getDoubleLockStatusAttrValue();
    String lockStatus = '--';
    if (doorStatusValue == trueValue) {
      lockStatus = '未上锁';
    } else if (doorStatusValue == falseValue) {
      if (doubleLockStatusValue == falseValue) {
        lockStatus = '已上锁';
      } else if (doubleLockStatusValue == trueValue) {
        lockStatus = '已反锁';
      }
    }
    componentModelList.add(ExpandLabelViewModel(value: lockStatus, desc: '上锁状态'));

    final String battery = _getBatteryAttrValue();
    final String lock2Battery = _getLock2BatteryAttrValue();

    if (battery.isNotEmpty && lock2Battery.isNotEmpty) {
      componentModelList
        ..add(ExpandLabelViewModel(value: battery, unit: '%', desc: '电量1'))
        ..add(ExpandLabelViewModel(value: lock2Battery, unit: '%', desc: '电量2'));
    } else if (battery.isNotEmpty) {
      componentModelList.add(ExpandLabelViewModel(value: battery, unit: '%', desc: '电量'));
    } else if (lock2Battery.isNotEmpty) {
      componentModelList.add(ExpandLabelViewModel(value: lock2Battery, unit: '%', desc: '电量'));
    } else {
      componentModelList.add(ExpandLabelViewModel(desc: '电量'));
    }
    return <String, LargeDeviceCardFunctionSet>{
      name: LargeDeviceCardFunctionSet(
          name: name, componentViewModelList: componentModelList),
    };
  }

  String _getAttributeValue(String key) {
    return getDeviceCardAttrValueByName(key).trim();
  }
  String _getDoorStatusAttrValue() => _getAttributeValue(doorStatusKey);
  String _getDoubleLockStatusAttrValue() => _getAttributeValue(doubleLockStatusKey);
  String _getBatteryAttrValue() => _getAttributeValue(batteryKey);
  String _getLock2BatteryAttrValue() => _getAttributeValue(lock2BatteryKey);


  List<ComponentBaseViewModel> _defaultComponentViewModelList() {
    return <ComponentBaseViewModel>[
      ExpandLabelViewModel(desc: '上锁状态',enable: false),
      ExpandLabelViewModel(desc: '电量', enable: false),
    ];
  }
}
