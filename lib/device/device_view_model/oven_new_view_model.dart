import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 蒸烤一体机
class OvenNewViewModel extends DeviceCardViewModel {
  OvenNewViewModel({required super.device});

  String get workStatusKey => 'workStatus';

  SmartHomeDeviceAttribute? get workStatusAttribute =>
      getDeviceAttribute(workStatusKey);

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }

    if (powerOn) {
      final String workValue = workStatusAttribute?.value ?? '';

      if (workValue == '4' || workValue == '3') {
        return DeviceCardAttribute(label: '', value: '待机', unit: '');
      } else {
        return DeviceCardAttribute(label: '', value: '工作中', unit: '');
      }
    }
    return null;
  }
}
