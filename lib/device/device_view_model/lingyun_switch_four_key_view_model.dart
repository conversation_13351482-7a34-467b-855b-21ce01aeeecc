import 'lingyun/lingyun_constant.dart';
import 'lingyun/lingyun_model.dart';
import 'lingyun_switch_base_view_model.dart';

//凌云玻璃开关-四键开关
class LingYunSwitchFourKeyViewModel extends LingYunSwitchBaseViewModel {
  LingYunSwitchFourKeyViewModel({required super.device});

  @override
  List<LingYunSwitchModel> get switchModelList => <LingYunSwitchModel>[
        LingYunSwitchModel(
          switchName: '左上键',
          switchTypeKey: LingYunSwitchTypeKey.switchType1Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus1Key,
        ),
        LingYunSwitchModel(
          switchName: '右上键',
          switchTypeKey: LingYunSwitchTypeKey.switchType2Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus2Key,
        ),
        LingYunSwitchModel(
          switchName: '左下键',
          switchTypeKey: LingYunSwitchTypeKey.switchType3Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus3Key,
        ),
        LingYunSwitchModel(
          switchName: '右下键',
          switchTypeKey: LingYunSwitchTypeKey.switchType4Key,
          alwaysOnStatusKey: LingYunAlwaysOnStatusKey.alwaysOnStatus4Key,
        )
      ];
}
