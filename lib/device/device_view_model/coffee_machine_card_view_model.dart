import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class CoffeeMachineCardViewModel extends DeviceCardViewModel {
  CoffeeMachineCardViewModel({required super.device});

  String get runningPhaseKey => 'runningPhase';

  SmartHomeDeviceAttribute? get runningPhaseAttribute =>
      getDeviceAttribute(runningPhaseKey);

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }
    if (powerOn) {
      final String runningPhase = runningPhaseAttribute?.value ?? '';
      if (runningPhase == '0') {
        DeviceCardAttribute(label: '', value: '待机', unit: '');
      } else if (runningPhase.isNotEmpty) {
        DeviceCardAttribute(label: '', value: '工作中', unit: '');
      }
    }

    return null;
  }
}
