import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//新风温控面板
class FanTemperatureControlPanelViewModel extends DeviceCardViewModel {
  FanTemperatureControlPanelViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String windSpeedValue =
          getDeviceCardAttrValueByName('plateWindSpeed');
      String attrValue = '';
      switch (windSpeedValue) {
        case '3':
          attrValue = '风速 高';
        case '2':
          attrValue = '风速 中';
        case '1':
          attrValue = '风速 低';
      }

      return attrValue.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attrValue, unit: '')
          : null;
    }

    return null;
  }
}
