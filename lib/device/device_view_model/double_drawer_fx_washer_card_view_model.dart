import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';

import 'double_drawer_dish_washer_card_view_model.dart';

//斐雪派克双抽洗碗机
class DoubleDrawerFxWasherCardViewModel
    extends DoubleDrawerDishwasherCardViewModel {
  DoubleDrawerFxWasherCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String onOffStatusUP = getDeviceCardAttrValueByName('onOffStatusUP');
    if (onOffStatusUP.isEmpty ||
        onOffStatusUP == SmartHomeConstant.deviceAttrFalse) {
      return null;
    }

    final String cyclePhaseUP = getDeviceCardAttrValueByName('cyclePhaseUP');
    if (upStandBy(onOffStatusUP, cyclePhaseUP)) {
      return DeviceCardAttribute(label: '上抽', value: '待机', unit: '');
    }

    final int time = calculateRemainTime(1);
    if (time == 0) {
      return null;
    }
    if (cyclePhaseUP == '12') {
      return DeviceCardAttribute(label: '上抽预约', value: '剩$time', unit: 'min');
    }

    return cyclePhaseUP.isNotEmpty
        ? DeviceCardAttribute(label: '上抽', value: '剩$time', unit: 'min')
        : null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    final String onOffStatusDN = getDeviceCardAttrValueByName('onOffStatusDN');
    if (onOffStatusDN.isEmpty ||
        onOffStatusDN == SmartHomeConstant.deviceAttrFalse) {
      return null;
    }

    final String cyclePhaseDN = getDeviceCardAttrValueByName('cyclePhaseDN');
    if (downStandBy(onOffStatusDN, cyclePhaseDN)) {
      return DeviceCardAttribute(label: '下抽', value: '待机', unit: '');
    }

    final int time = calculateRemainTime(0);
    if (time == 0) {
      return null;
    }

    if (cyclePhaseDN == '12') {
      return DeviceCardAttribute(label: '下抽预约', value: '剩$time', unit: 'min');
    }

    return cyclePhaseDN.isNotEmpty
        ? DeviceCardAttribute(label: '下抽', value: '剩$time', unit: 'min')
        : null;
  }
}
