/*
 * 描述：洗干套机
 * 作者：songFJ
 * 创建时间：2024/7/17
 */

import 'package:device_utils/typeId_parse/type_id_parse.dart';
import 'package:smart_home/device/device_view_model/wash_device_view_model.dart';

class WasherDryerViewModel extends WashDeviceViewModel {
  WasherDryerViewModel({required super.device});

  final String _middleClass_5 = '5';
  final String _remoteCtrValid302On = '1';
  final String _powerStatus300On = '1';

  @override
  bool upUnitRemoteControlOn() {
    final String middleClass =
        TypeIdParse.secondTypeCode(device.basicInfo.typeId);
    if (middleClass == _middleClass_5) {
      return modelV3.remoteCtrValidUP302.value == _remoteCtrValid302On;
    }
    return true;
  }

  @override
  bool downUnitRemoteControlOn() {
    final String middleClass =
        TypeIdParse.secondTypeCode(device.basicInfo.typeId);
    if (middleClass == _middleClass_5) {
      return modelV3.remoteCtrValidDN302.value == _remoteCtrValid302On;
    }
    return true;
  }

  @override
  bool upUnitPowerOn() {
    final String middleClass =
        TypeIdParse.secondTypeCode(device.basicInfo.typeId);
    if (middleClass == _middleClass_5) {
      return modelV3.powerStatusUP300.value == _powerStatus300On;
    }
    return true;
  }

  @override
  bool downUnitPowerOn() {
    final String middleClass =
        TypeIdParse.secondTypeCode(device.basicInfo.typeId);
    if (middleClass == _middleClass_5) {
      return modelV3.powerStatusDN300.value == _powerStatus300On;
    }
    return true;
  }

  @override
  String get devicePowerOffInfo {
    if (powerOff) {
      if (!modelV3.supportRemoteOnOff) {
        return '已关机';
      }
    }
    return '';
  }
}
