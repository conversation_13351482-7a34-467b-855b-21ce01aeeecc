import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class NewWindAirConditionCardViewModel extends DeviceCardViewModel {
  NewWindAirConditionCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  String get smallCardStatus {
    if (powerOn) {
      return '开';
    }
    if (powerOff) {
      return '关';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final String value = device.attributeMap['operationMode']?.value ?? '';
      if (value == '') {
        return null;
      }
      final String attr = _getModel(value);
      return attr.isNotEmpty
          ? DeviceCardAttribute(label: '', value: attr, unit: '')
          : null;
    }
    return null;
  }

  String _getModel(String value) {
    final Map<String, String> map = <String, String>{};
    map['0'] = '智能';
    map['1'] = '强劲';
    map['2'] = '柔风';
    map['3'] = '睡眠';

    final String returnValue = map.stringValueForKey(value, '');

    return returnValue;
  }
}
