import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class DehumidifierCardViewModel extends DeviceCardViewModel {
  DehumidifierCardViewModel({required super.device});

  @override
  bool get runningMode {
    return powerOn;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    const String key = 'indoorHumidity';
    final String value = device.attributeMap[key]?.value ?? '';
    String attr = '';
    if (value != '' && value != '-1000') {
      attr = value;
    }
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '湿度', value: attr, unit: '%')
        : null;
  }
}
