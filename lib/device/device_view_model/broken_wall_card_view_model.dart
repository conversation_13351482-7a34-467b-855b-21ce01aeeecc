import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class BrokenWallCardViewModel extends DeviceCardViewModel {
  BrokenWallCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['operationMode']?.value ?? '';
    final String attr = value.isEmpty ? '' : _getModel(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }

  String _getModel(String value) {
    final Map<String, String> map = <String, String>{};
    map['0'] = '待机';
    map['1'] = '婴儿粥';
    map['5'] = '玉米汁';
    map['7'] = '浓汤';
    map['16'] = '奶昔';
    map['17'] = '沙冰';
    map['22'] = '保温';
    map['24'] = '云食谱';
    map['26'] = '杂粮糊';
    map['27'] = '养生粥';
    map['28'] = '五谷浆';
    map['29'] = '果汁';
    map['30'] = '烘干';

    final String returnValue = map.stringValueForKey(value, '');

    return returnValue;
  }
}
