import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class AirSmartControlCardViewModel extends DeviceCardViewModel {
  AirSmartControlCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['indoorTemperature']?.value ?? '';

    if (value == '') {
      return null;
    }
    String attr = '';
    final int iValue = value != '' ? int.parse(value) : 0;
    if (iValue > -38) {
      attr = value;
    }
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '温度', value: attr, unit: '°C')
        : null;
  }
}
