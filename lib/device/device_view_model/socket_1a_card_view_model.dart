import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class Socket1aCardViewModel extends DeviceCardViewModel {
  Socket1aCardViewModel({required super.device});

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['doorLockStatus']?.value ?? '';

    if (value == trueValue) {
      return DeviceCardAttribute(label: '', value: '开', unit: '');
    } else if (value == falseValue) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }
    return null;
  }
}
