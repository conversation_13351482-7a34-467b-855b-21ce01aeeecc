/*
 * 描述：新风机基础类
 * 作者：songFJ
 * 创建时间：2025/3/11
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/component/view_model/expand_popup_icon_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_text_unit_view_model.dart';
import 'package:smart_home/device/component/view_model/humidity_set_view_model.dart';
import 'package:smart_home/device/component_view_model/circular_switch_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';
import 'package:smart_home/device/component_view_model/wind_speed_component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../component_view_model/grid_view_model.dart';
import '../device_info_model/device_card_attribute.dart';

class NewFanBaseViewModel extends DeviceCardViewModel {
  NewFanBaseViewModel({required super.device});

  String get powerOffMsg => '新风机关机，无法操作';

  String get modeWritableMsg => '暂时无法选择模式';

  String get windSpeedWritableMsg => '当前状态无法调节风速';

  String get indoorTemperatureKey => 'indoorTemperature';

  String get indoorHumidityKey => 'indoorHumidity';

  String get operationModeKey => 'operationMode';

  String get windSpeedLevelKey => 'windSpeedLevel';

  String get setHumidityKey => 'setHumidity';

  String get humidityAdjustKey => 'humidityAdjust';

  String get humidificationStatusKey => 'humidificationStatus';

  String get dehumidificationStatusKey => 'dehumidificationStatus';

  String get humidificationExistKey => 'humidificationExist';

  SmartHomeDeviceAttribute? get indoorTemperatureAttribute =>
      getDeviceAttribute(indoorTemperatureKey);

  SmartHomeDeviceAttribute? get indoorHumidityAttribute =>
      getDeviceAttribute(indoorHumidityKey);

  SmartHomeDeviceAttribute? get operationModeAttribute =>
      getDeviceAttribute(operationModeKey);

  SmartHomeDeviceAttribute? get windSpeedLevelAttribute =>
      getDeviceAttribute(windSpeedLevelKey);

  SmartHomeDeviceAttribute? get setHumidityAttribute =>
      getDeviceAttribute(setHumidityKey);

  SmartHomeDeviceAttribute? get humidityAdjustAttribute =>
      getDeviceAttribute(humidityAdjustKey);

  SmartHomeDeviceAttribute? get humidificationStatusAttribute =>
      getDeviceAttribute(humidificationStatusKey);

  SmartHomeDeviceAttribute? get dehumidificationStatusAttribute =>
      getDeviceAttribute(dehumidificationStatusKey);

  SmartHomeDeviceAttribute? get humidificationExistAttribute =>
      getDeviceAttribute(humidificationExistKey);

  SmartHomeDeviceAttribute? get humidityOnOffAttribute {
    if (humidityAdjustAttribute != null) {
      return humidityAdjustAttribute;
    }
    if (humidificationStatusAttribute != null) {
      return humidificationStatusAttribute;
    }
    if (dehumidificationStatusAttribute != null) {
      return dehumidificationStatusAttribute;
    }
    return null;
  }

  String get humidityPopupTitle {
    if (humidityAdjustAttribute != null) {
      return '湿度调节';
    }
    if (humidificationStatusAttribute != null) {
      return '加湿调节';
    }
    if (dehumidificationStatusAttribute != null) {
      return '除湿调节';
    }
    return '湿度调节';
  }

  bool get supportHumidity {
    return false;
  }

  bool get humidityOn {
    return humidityAdjustAttribute?.value == trueValue ||
        humidificationStatusAttribute?.value == trueValue ||
        dehumidificationStatusAttribute?.value == trueValue;
  }

  bool get humidityOff {
    return humidityAdjustAttribute?.value == falseValue ||
        humidificationStatusAttribute?.value == falseValue ||
        dehumidificationStatusAttribute?.value == falseValue;
  }

  Map<String, ModeModel> get allModeMap => <String, ModeModel>{
        '0': ModeModel(value: '0', desc: '智能', icon: 'assets/new_fan/zhi.webp'),
        '7': ModeModel(value: '7', desc: '全热', icon: 'assets/new_fan/re.webp'),
        '9': ModeModel(value: '9', desc: '新风', icon: 'assets/new_fan/xin.webp'),
        '10':
            ModeModel(value: '10', desc: '排风', icon: 'assets/new_fan/pai.webp'),
        '8':
            ModeModel(value: '8', desc: '旁通', icon: 'assets/new_fan/pang.webp'),
        '11': ModeModel(
            value: '11', desc: '内循环', icon: 'assets/new_fan/nei.webp'),
      };

  Map<String, WindSpeedModel> get allWindSpeedMap => <String, WindSpeedModel>{
        '8': WindSpeedModel(value: '8', desc: '自动'),
        '1': WindSpeedModel(value: '1', desc: '1档'),
        '2': WindSpeedModel(value: '2', desc: '2档'),
        '3': WindSpeedModel(value: '3', desc: '3档'),
        '4': WindSpeedModel(value: '4', desc: '4档'),
        '5': WindSpeedModel(value: '5', desc: '5档'),
        '6': WindSpeedModel(value: '6', desc: '6档'),
        '7': WindSpeedModel(value: '7', desc: '7档'),
      };

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  String get smallCardStatus {
    if (powerOff) {
      return '关';
    }
    if (powerOn) {
      return '开';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String temp = indoorTemperatureAttribute?.value ?? '';
    if (temp.isNotEmpty && temp != '0') {
      return DeviceCardAttribute(label: '室温', value: temp, unit: '℃');
    }
    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    final String humidity = indoorHumidityAttribute?.value ?? '';
    if (humidity.isNotEmpty && humidity != '0') {
      return DeviceCardAttribute(label: '湿度', value: humidity, unit: '%');
    }

    return null;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '新风机';
    final LargeDeviceCardFunctionSet deviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
      name: name,
      componentViewModelList: (deviceOffline || loading)
          ? componentOfflineOrPowerOffViewModelList
          : componentViewModelList,
    );
    return <String, LargeDeviceCardFunctionSet>{name: deviceCardFunctionSet};
  }

  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      defaultModeComponent(),
      defaultWindSpeedComponent,
    ];
  }

  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      modeComponent(),
      windSpeedComponent,
    ];
  }

  ComponentBaseViewModel defaultModeComponent({int expandFlex = 1}) {
    return ExpandPopupIconTextViewModel(
      icon: 'assets/new_fan/zhi.webp',
      text: '模式',
      enable: false,
      expandFlex: expandFlex,
      clickCallback: (BuildContext context) {
        checkDeviceState(
            powerOffMsg: powerOffMsg,
            writable: false,
            writableMsg: modeWritableMsg);
      },
    );
  }
  ComponentBaseViewModel get defaultWindSpeedComponent {
    return ExpandPopupIconTextViewModel(
      icon: 'assets/new_fan/wind.webp',
      text: '风速',
      enable: false,
      clickCallback: (BuildContext context) {
        checkDeviceState(
            powerOffMsg: powerOffMsg,
            writable: false,
            writableMsg: windSpeedWritableMsg);
      },
    );
  }

  ComponentBaseViewModel get defaultHumidityComponent {
    return ExpandPopupTextTextUnitViewModel(
        text: '--',
        desc: '目标湿度',
        enable: false,
        clickCallback: (BuildContext context) {
          checkDeviceState(powerOffMsg: powerOffMsg, writable: false);
        });
  }

  /// 湿度调节
  ComponentBaseViewModel get humidityComponent {
    bool enable = false;

    final SmartHomeDeviceAttribute? humidityOnAttr = humidityOnOffAttribute;
    final SmartHomeDeviceAttribute? setHumidityAttr = setHumidityAttribute;
    if (humidityOnAttr == null || setHumidityAttr == null) {
      return defaultHumidityComponent;
    }
    if (humidityOnAttr.value.isEmpty) {
      return defaultHumidityComponent;
    }
    enable = powerOn;
    final bool isOn = humidityOn;

    final PopupComponentViewModel? popupComponentViewModel =
        humidityPopupViewModel;
    if (popupComponentViewModel != null) {
      popupFuncSet[popupComponentViewModel.identification] =
          popupComponentViewModel;
    }

    final ExpandPopupTextTextUnitViewModel viewModel =
        ExpandPopupTextTextUnitViewModel(
            text: setHumidityAttr.value,
            unit: '%',
            desc: '目标湿度',
            enable: enable,
            isOn: isOn,
            popupComponentViewModel: popupComponentViewModel,
            clickCallback: (BuildContext context) {
              gio(cloudProgramName: '湿度调节');
              checkDeviceState(
                      powerOffMsg: powerOffMsg,
                      writable: enable,
                      writableMsg: powerOffMsg)
                  .then((bool pass) {
                if (pass) {
                  if (popupComponentViewModel != null) {
                    showPopup(context, popupComponentViewModel);
                  }
                }
              });
            });
    return viewModel;
  }

  /// 目标湿度弹窗
  PopupComponentViewModel? get humidityPopupViewModel {
    /// 湿度设置开关状态
    final SmartHomeDeviceAttribute? humidityOnOffAttr = humidityOnOffAttribute;
    final SmartHomeDeviceAttribute? setHumidityAttr = setHumidityAttribute;
    if (humidityOnOffAttr == null || setHumidityAttr == null) {
      return null;
    }
    final bool humidityOn = humidityOnOffAttr.value == trueValue;
    final bool humidityOnEnable = powerOn && humidityOnOffAttr.writable;
    final bool humiditySetEnable = powerOn && setHumidityAttr.writable;
    final String currentValue = setHumidityAttr.value;
    final String minValue = setHumidityAttr.valueRange.dataStep.minValue;
    final String maxValue = setHumidityAttr.valueRange.dataStep.maxValue;
    final String step = setHumidityAttr.valueRange.dataStep.step;

    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: humidityPopupTitle,
            deviceId: deviceId,
            attributeKey: humidityOnOffAttr.name,
            componentList: <ComponentBaseViewModel>[
          HumiditySetViewModel(
              humidityTitle: humidityPopupTitle,
              humidityOn: humidityOn,
              checkHumidityOnOffContinue: _checkHumidityOnOffContinue,
              checkHumiditySetContinue: _checkHumiditySetContinue,
              humiditySetCurrentValue: int.tryParse(currentValue) ?? 0,
              humidityOnEnable: humidityOnEnable,
              humiditySetEnable: humiditySetEnable,
              humiditySetMinValue: int.tryParse(minValue) ?? 0,
              humiditySetMaxValue: int.tryParse(maxValue) ?? 100,
              humiditySetStep: int.tryParse(step) ?? 5,
              humidityOnOffCallback: (_, bool humidityOn) {
                final Map<String, String> commands = <String, String>{};
                commands[humidityOnOffAttr.name] =
                humidityOn ? trueValue : falseValue;
                quickCtrlLECommand(device.basicInfo.deviceId, commands,
                    onErrorCallback: (String errMsg) {
                      ToastHelper.showToast(errMsg);
                    });
              },
              humiditySetCallback: (_, int value) {
                final Map<String, String> commands = <String, String>{};
                commands[setHumidityKey] = '$value';
                quickCtrlLECommand(deviceId, commands,
                    onErrorCallback: (String errMsg) {
                      ToastHelper.showToast(errMsg);
                    });
              }),
        ]);
    return popupComponentViewModel;
  }

  /// 模式选择
  ComponentBaseViewModel modeComponent({int expandFlex = 1}) {
    bool enable = false;
    final SmartHomeDeviceAttribute? attribute = operationModeAttribute;
    if (attribute == null) {
      return defaultModeComponent();
    }

    final ModeModel? modeModel = allModeMap[attribute.value];
    if (modeModel == null) {
      return defaultModeComponent();
    }

    final PopupComponentViewModel popupComponentViewModel =
        modePopupViewModel(attribute);
    popupFuncSet[popupComponentViewModel.identification] =
        popupComponentViewModel;

    enable = powerOn && (attribute.writable);
    return ExpandPopupIconTextViewModel(
        icon: modeModel.icon,
        text: modeModel.desc,
        enable: enable,
        expandFlex: expandFlex,
        popupComponentViewModel: popupComponentViewModel,
        clickCallback: (BuildContext context) {
          gio(cloudProgramName: '模式选择');
          checkDeviceState(
                  powerOffMsg: powerOffMsg,
                  writable: enable,
                  writableMsg: modeWritableMsg)
              .then((bool pass) {
            if (pass) {
              showPopup(context, popupComponentViewModel);
            }
          });
        });
  }

  /// 模式选择弹窗
  PopupComponentViewModel modePopupViewModel(
      SmartHomeDeviceAttribute attribute) {
    final List<ComponentBaseViewModel> componentList =
        <ComponentBaseViewModel>[];
    final bool enable = powerOn && attribute.writable;

    final List<SmartHomeDataItem> dataList = attribute.valueRange.dataList;
    allModeMap.forEach((String key, ModeModel modeModel) {
      if (dataList.any((SmartHomeDataItem dataItem) => dataItem.data == key)) {
        final CircularSwitchComponentModel componentModel =
            CircularSwitchComponentModel(
                text: modeModel.desc,
                icon: modeModel.icon,
                selected: modeModel.value == attribute.value,
                enable: enable,
                clickCallback: (BuildContext? context) {
                  checkDeviceState(
                          powerOffMsg: powerOffMsg,
                          writable: enable,
                          writableMsg: modeWritableMsg)
                      .then((bool pass) {
                    if (pass) {
                      if (modeModel.value != attribute.value) {
                        final Map<String, String> commands = <String, String>{};
                        commands[operationModeKey] = modeModel.value;
                        quickCtrlLECommand(device.basicInfo.deviceId, commands,
                            onErrorCallback: (String errMsg) {
                          ToastHelper.showToast(errMsg);
                        });
                      }
                    }
                  });
                  hidePopup(context);
                });
        componentList.add(componentModel);
      }
    });

    final List<ComponentBaseViewModel> popupComponentList =
        <ComponentBaseViewModel>[
      GridViewModel(
          componentList: componentList,
          crossAxisCount: 4,
          mainAxisExtent: 73,
          crossAxisSpacing: 0,
          mainAxisSpacing: 24)
    ];

    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '模式选择',
            deviceId: deviceId,
            attributeKey: operationModeKey,
            componentList: popupComponentList);

    return popupComponentViewModel;
  }

  /// 风速调节
  ComponentBaseViewModel get windSpeedComponent {
    bool enable = false;
    final SmartHomeDeviceAttribute? attribute = windSpeedLevelAttribute;
    if (attribute == null) {
      return defaultWindSpeedComponent;
    }
    final WindSpeedModel? speedModel = allWindSpeedMap[attribute.value];
    if (speedModel == null) {
      return defaultWindSpeedComponent;
    }
    enable = powerOn && (attribute.writable);
    final PopupComponentViewModel popupComponentViewModel =
        windSpeedPopupViewModel(attribute);
    popupFuncSet[popupComponentViewModel.identification] =
        popupComponentViewModel;

    return ExpandPopupIconTextViewModel(
        icon: 'assets/components/wind_speed.webp',
        text: speedModel.desc,
        enable: enable,
        popupComponentViewModel: popupComponentViewModel,
        clickCallback: (BuildContext context) {
          checkDeviceState(
                  powerOffMsg: powerOffMsg,
                  writable: enable,
                  writableMsg: windSpeedWritableMsg)
              .then((bool pass) {
            gio(cloudProgramName: '风速调节');
            if (pass) {
              showPopup(context, popupComponentViewModel);
            }
          });
        });
  }

  /// 风速调节弹窗
  PopupComponentViewModel windSpeedPopupViewModel(
      SmartHomeDeviceAttribute attribute) {
    final List<String> valueList = <String>[];
    final List<SmartHomeDataItem> dataList = attribute.valueRange.dataList;
    allWindSpeedMap.forEach((String key, WindSpeedModel speedModel) {
      if (dataList.any((SmartHomeDataItem dataItem) => dataItem.data == key)) {
        valueList.add(speedModel.desc);
      }
    });
    double index = valueList
        .indexOf(allWindSpeedMap[attribute.value]?.desc ?? '')
        .toDouble();
    if (index < 0) {
      index = 0;
    }
    return PopupComponentViewModel(
        title: '风速调节',
        deviceId: deviceId,
        attributeKey: attribute.name,
        componentList: <ComponentBaseViewModel>[
          WindSpeedComponentViewModel(
              valueList: valueList,
              selectedValue: index,
              enable: powerOn && attribute.writable,
              attribute: attribute,
              checkContinue: _checkWindSpeedContinue,
              onChangeEnd: (double selectedValue) {
                final Map<String, String> commands = <String, String>{};
                WindSpeedModel? speedModel;
                allWindSpeedMap.forEach((String key, WindSpeedModel model) {
                  if (model.desc == valueList[selectedValue.toInt()]) {
                    speedModel = model;
                  }
                });
                if (speedModel != null) {
                  commands[windSpeedLevelKey] = speedModel!.value;
                }
                quickCtrlLECommand(device.basicInfo.deviceId, commands,
                    onErrorCallback: (String errMsg) {
                      ToastHelper.showToast(errMsg);
                    });
              }),
        ]);
  }

  Future<bool> _checkWindSpeedContinue(SmartHomeDeviceAttribute? attribute) {
    return checkDeviceState(
        powerOffMsg: powerOffMsg,
        writable: attribute?.writable ?? false,
        writableMsg: windSpeedWritableMsg);
  }

  Future<bool> _checkHumidityOnOffContinue() {
    return checkDeviceState(
        powerOffMsg: powerOffMsg,
        writable: humidityOnOffAttribute?.writable ?? false);
  }

  Future<bool> _checkHumiditySetContinue() {
    return checkDeviceState(
        powerOffMsg: powerOffMsg,
        writable: setHumidityAttribute?.writable ?? false);
  }
}

class ModeModel {
  final String value;
  final String desc;
  final String icon;

  ModeModel({required this.value, required this.desc, required this.icon});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModeModel &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          desc == other.desc &&
          icon == other.icon;

  @override
  int get hashCode => value.hashCode ^ desc.hashCode ^ icon.hashCode;

  @override
  String toString() {
    return 'ModeModel{value: $value, desc: $desc, icon: $icon}';
  }
}

class WindSpeedModel {
  final String value;
  final String desc;

  WindSpeedModel({required this.value, required this.desc});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WindSpeedModel &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          desc == other.desc;

  @override
  int get hashCode => value.hashCode ^ desc.hashCode;

  @override
  String toString() {
    return 'WindSpeedModel{value: $value, desc: $desc}';
  }
}
