import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

//安防-红外报警器
class SafetyAlarmFalseViewModel extends DeviceCardViewModel {
  SafetyAlarmFalseViewModel({required super.device});

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String controlValue = getDeviceCardAttrValueByName('securityControl');
    if (controlValue == falseValue) {
      return DeviceCardAttribute(label: '', value: '撤防', unit: '');
    } else if (controlValue == trueValue) {
      return DeviceCardAttribute(label: '', value: '布防', unit: '');
    }

    return super.deviceCardAttributeOne();
  }
}
