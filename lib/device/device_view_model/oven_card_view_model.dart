import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/resize_device_card/resize_base_model.dart';

class OvenCardViewModel extends DeviceCardViewModel {
  OvenCardViewModel({required super.device});

  // 离线提示"已关机或未联网"的型号集合
  List<String> get _hintShutdownOrNotConnectedNetModelsList => <String>[
        // 1e大类
        'C7O46CGU1',
        'C7O60CGU1',
        'C7O60BGU1',
        'C7O46BGU1',
        'C6O60BGU1',
        'GBMC0721ABGU1',
        'GBMC0721ASGU1',
        'C7O60AGU1',
        'C7O46AGU1',
        'C6O60AGU1',
        'C6O60CGU1',
        // 3e大类
        'C6SO46BGU1',
        'CO1SOU1',
        'C6SO46WGU1',
        'ZK1U1',
        'JZK1U1',
        'GSMC0501ASGU1',
        'GSMC0501ABGU1',
        'C7SO46AGU1',
        'CO3SOU1',
        'C6SO46IGU1',
        'C6SO60DGU1',
        'C5SO60FGU1',
        'C6SO60AGU1',
        'C3SO60BGU1',
        'JZK2U1',
        'C3SO46BGU1',
        'CDZK3U1',
        'CDZK1U1',
        'C6SO60WGU1',
        'C50-TCU1',
        'CQG-E72AU1',
        'C72-TAU1',
        'C6SO46FGU1',
      ];

  final String workStatusKey = 'workStatus';
  final String bakingStaKey = 'BakingSta';

  SmartHomeDeviceAttribute? get workStatusAttribute =>
      getDeviceAttribute(workStatusKey);

  SmartHomeDeviceAttribute? get bakingStaAttribute =>
      getDeviceAttribute(bakingStaKey);

  @override
  String? get otherDeviceOfflineInfo {
    if (_hintShutdownOrNotConnectedNetModelsList
        .contains(device.basicInfo.model)) {
      if (deviceCardType == DeviceCardType.smallCard) {
        return '';
      }
      return '已关机或未联网';
    }
    return super.otherDeviceOfflineInfo;
  }

  @override
  bool get runningMode {
    if (powerOn) {
      final String workStatus = workStatusAttribute?.value ?? '';
      if (workStatus.isEmpty) {
        final String backingSta = bakingStaAttribute?.value ?? '';
        // 烘焙状态
        if (backingSta.isNotEmpty && backingSta != '1') {
          return true;
        }
      } else {
        // 工作状态
        if (workStatus != '0') {
          return true;
        }
      }
    }

    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOff) {
      return DeviceCardAttribute(label: '', value: '关', unit: '');
    }
    if (powerOn) {
      final String workStatus = workStatusAttribute?.value ?? '';
      if (workStatus.isEmpty) {
        final String backingSta = bakingStaAttribute?.value ?? '';
        // 烘焙状态 1空闲
        if (backingSta == '1') {
          return DeviceCardAttribute(label: '', value: '待机', unit: '');
        } else if (backingSta.isNotEmpty) {
          return DeviceCardAttribute(label: '', value: '工作中', unit: '');
        }
        return null;
      } else if (workStatus == '0') {
        // 工作状态 0空闲
        return DeviceCardAttribute(label: '', value: '待机', unit: '');
      }
      return DeviceCardAttribute(label: '', value: '工作中', unit: '');
    }
    return null;
  }
}
