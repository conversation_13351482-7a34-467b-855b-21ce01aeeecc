import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

// 食材净化机
class FoodCleanKeyViewModel extends DeviceCardViewModel {
  FoodCleanKeyViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = getDeviceCardAttrValueByName('WorkingTimeDisplay');
    if (value.isNotEmpty) {
      return DeviceCardAttribute(label: '剩余时间', value: value, unit: 'mg/L');
    }
    return super.deviceCardAttributeOne();
  }
}
