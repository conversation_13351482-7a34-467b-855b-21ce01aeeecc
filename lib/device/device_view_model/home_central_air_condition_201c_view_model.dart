import 'package:smart_home/device/device_view_model/air_condition_card_view_model.dart';
import 'package:smart_home/device/device_view_model/home_central_air_condition_view_model.dart';

class HomeCentralAirCondition201cViewModel extends HomeCentralAirConditionViewModel {
  HomeCentralAirCondition201cViewModel({required super.device});

  @override
  Map<String, AirConditionWindSpeedModel> get allWindSpeedMap =>
      <String, AirConditionWindSpeedModel>{
        '5': AirConditionWindSpeedModel(value: '5', desc: '自动'),
        '6': AirConditionWindSpeedModel(value: '6', desc: '静音'),
        '4': AirConditionWindSpeedModel(value: '4', desc: '微风'),
        '3': AirConditionWindSpeedModel(value: '3', desc: '低风'),
        '9': AirConditionWindSpeedModel(value: '9', desc: '中低'),
        '2': AirConditionWindSpeedModel(value: '2', desc: '中风'),
        '8': AirConditionWindSpeedModel(value: '8', desc: '中高'),
        '1': AirConditionWindSpeedModel(value: '1', desc: '高风'),
        '7': AirConditionWindSpeedModel(value: '7', desc: '快速'),
        '0': AirConditionWindSpeedModel(value: '0', desc: '超强'),
      };
}