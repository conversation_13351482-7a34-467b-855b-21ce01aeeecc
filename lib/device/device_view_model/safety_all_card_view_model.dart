import 'package:device_utils/typeId_parse/type_id_parse.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class SafetyAllCardViewModel extends DeviceCardViewModel {
  SafetyAllCardViewModel({required super.device});

  @override
  bool supportPowerOnOff() {
    final String secondCode =
        TypeIdParse.secondTypeCode(device.basicInfo.typeId);
    if (secondCode == '1' || secondCode == 'e') {
      return true;
    }
    return false;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = device.attributeMap['securityControl']?.value ?? '';
    String attr = '';
    if (value == trueValue) {
      attr = '布防';
    } else if (value == falseValue) {
      attr = '撤防';
    }

    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '', value: attr, unit: '')
        : null;
  }
}
