import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/store/voice_box/server/voice_box_socket_util.dart';
import 'package:whole_house_music/music/api/music_api_enum.dart';
import 'package:whole_house_music/music/model/song_info_model.dart';

import '../component/view_model/expand_switch_icon_text_view_model.dart';

const String _leftIcon = 'assets/icons/left_arr_dark.webp';
const String _rightIcon = 'assets/icons/right_arr_dark.webp';
const String _playIcon = 'assets/icons/play_icon_dark.webp';
const String _pauseIcon = 'assets/icons/pause_icon_dark.webp';
class VoiceBoxCardViewModel extends DeviceCardViewModel {
  SongInfoModel? songInfoModel;

  VoiceBoxCardViewModel({required super.device});

  bool get _isPlaying => songInfoModel?.playStatus == MusicPlayStatus.playing;

  String get songName => songInfoModel?.resName ?? '';

  bool get _isEnable =>
      device.onlineState != SmartHomeDeviceOnlineState.offline &&
      (songInfoModel?.resName.isNotEmpty ?? false);

  @override
  bool supportPowerOnOff() {
    return false;
  }

  /// 开机且播放中
  @override
  bool get runningMode {
    return powerOn && _isPlaying;
  }

  @override
  String get largeCardStatus {
    if (_isPlaying) {
      return songName;
    }
    return '';
  }

  /// 小卡片状态区，子类按需重写
  @override
  String get smallCardStatus {
    if (_isPlaying) {
      return '播放中';
    }
    return '';
  }

  /// 中卡片状态区，子类按需重写
  @override
  String get middleCardStatus {
    if (_isPlaying) {
      return songName;
    }
    return '';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is VoiceBoxCardViewModel &&
          other.songInfoModel == songInfoModel;

  @override
  int get hashCode => super.hashCode ^ songInfoModel.hashCode;

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '智能音箱';

    List<ComponentBaseViewModel> componentModelList =
        <ComponentBaseViewModel>[];
    if (deviceOfflineOrPowerOff) {
      componentModelList = <ComponentBaseViewModel>[
        ExpandSwitchIconTextViewModel(
            text: SmartHomeConstant.songBoxPreSong,
            icon: _leftIcon,
            enable: false,
            isOn: false,
            clickCallback: (_) {
              checkDeviceState();
            }),
        ExpandSwitchIconTextViewModel(
            text: SmartHomeConstant.songBoxPlay,
            icon: _playIcon,
            enable: false,
            isOn: false,
            clickCallback: (_) {
              checkDeviceState();
            }),
        ExpandSwitchIconTextViewModel(
            text: SmartHomeConstant.songBoxNextSong,
            icon: _rightIcon,
            enable: false,
            isOn: false,
            clickCallback: (_) {
              checkDeviceState();
            }),
      ];
    } else {
      componentModelList = <ComponentBaseViewModel>[
        _preOrNextSongButton(false),
        _playOrPauseSongButton(),
        _preOrNextSongButton(true),
      ];
    }

    final LargeDeviceCardFunctionSet largeDeviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
            name: name, componentViewModelList: componentModelList);
    return <String, LargeDeviceCardFunctionSet>{
      largeDeviceCardFunctionSet.name: largeDeviceCardFunctionSet
    };
  }

  ComponentBaseViewModel _playOrPauseSongButton() {
    String desc = SmartHomeConstant.songBoxPlay;
    if (_isPlaying) {
      desc = SmartHomeConstant.songBoxPause;
    }

    final ExpandSwitchIconTextViewModel viewModel =
        ExpandSwitchIconTextViewModel(
            text: desc,
            icon: _isPlaying ? _pauseIcon : _playIcon,
            enable: _isEnable,
            isOn: false,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: desc);
              checkDeviceState(checkChildLock: false, checkAlarm: false)
                  .then((bool pass) {
                if (pass) {
              if (songInfoModel != null && songInfoModel!.resName.isNotEmpty) {
                VoiceDeviceSocket().musicCardClicked(
                    !_isPlaying ? VoiceActionType.play : VoiceActionType.pause,
                    songInfoModel,
                    '大卡片',
                    alarm);
                return;
              }

              _toastIfNoRes();
            }
          });
        });

    return viewModel;
  }

  void _toastIfNoRes() {
    ToastHelper.showToast('请前往资源界面, 点播内容');
  }

  ComponentBaseViewModel _preOrNextSongButton(bool isNext) {
    String desc = SmartHomeConstant.songBoxNextSong;
    if (!isNext) {
      desc = SmartHomeConstant.songBoxPreSong;
    }

    final ExpandSwitchIconTextViewModel viewModel =
        ExpandSwitchIconTextViewModel(
            text: desc,
            icon: isNext ? _rightIcon : _leftIcon,
            enable: _isEnable,
            isOn: false,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: desc);
              checkDeviceState(checkChildLock: false, checkAlarm: false)
                  .then((bool pass) {
                if (pass) {
              if (songInfoModel != null && songInfoModel!.resName.isNotEmpty) {
                VoiceDeviceSocket().musicCardClicked(
                    isNext ? VoiceActionType.next : VoiceActionType.last,
                    songInfoModel,
                    '大卡片',
                    alarm);
                return;
              }

              _toastIfNoRes();
            }
          });
        });

    return viewModel;
  }

  static VoiceBoxCardViewModel createFromWebSocket(SongInfoModel m) {
    final VoiceBoxCardViewModel _vm =
        VoiceBoxCardViewModel(device: SmartHomeDevice());
    _vm.songInfoModel = m;
    return _vm;
  }

  VoiceBoxCardViewModel updateFromWebSocket(SongInfoModel m) {
    final VoiceBoxCardViewModel _vm = VoiceBoxCardViewModel(device: device);
    _vm.isSelected = isSelected;
    _vm.songInfoModel = m;
    return _vm;
  }

  VoiceBoxCardViewModel updateDevice(SmartHomeDevice device) {
    final VoiceBoxCardViewModel _vm = VoiceBoxCardViewModel(device: device);
    _vm.isSelected = isSelected;
    _vm.songInfoModel = songInfoModel;
    return _vm;
  }
}
