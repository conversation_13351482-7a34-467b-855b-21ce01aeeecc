import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../component/view_model/expand_value_list_view_model.dart';
import '../component/view_model/fixed_switch_icon_text_view_model.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../resize_device_card/resize_base_model.dart';

class EWaterHeaterCardViewModel extends DeviceCardViewModel {
  EWaterHeaterCardViewModel({required super.device});

  List<String> get _maxValue75TypeId => <String>[
        '201c120000118674061200418007774100000000000000000000000000000040',
        '201c120000118674061200418007774200000000000000000000000000000040'
      ];

  // 不支持定时的typeId集合
  List<String> get _notSupportOrder => <String>[
        '201c120000118674061800418006590000000000000000000000000000000040',
        '201c120000118674061800418007514600000000000000000000000000000040',
        '201c120000118674061900418007515100000000000000000000000000000040',
        '201c120000118674061b00418013730000000000000000000000000000000040',
        '201c120000118674061b00418013750000000000000000000000000000000040',
        '201c120000118674061b00418008324300000000000000000000000000000040',
        '201c120000118674061b00418013724100000000000000000000000000000040',
        '201c120000118674061b00418015364100000000000041801580000000000040'
      ];

  // 判断typeId是否支持定时
  bool get _isTypeIdSupportOrder =>
      !_notSupportOrder.contains(device.basicInfo.typeId);

  bool get _constantTemperatureDevice =>
      _setConstantTemperatureAttribute != null &&
      device.basicInfo.typeId != _hengwen;

  String get _hengwen =>
      '201c120000118674061900418016890000000000000000000000000000000040';

  String get _MAX3 =>
      '201c120000118674061a00418005594200000000000000000000000000000040';

  String get _MAX5 =>
      '201c120000118674061a00408099200000000000000000000000000000000040';

  String get _SMART5 =>
      '111c120024000810061200418003720000000000000000000000000000000000';

  String get _BK3 =>
      '201c120000118674061600418011204700000000000000000000000000000040';

  String get _ZE5 =>
      '201c120000118674061b00418013750000000000000000000000000000000040';

  String get _AJ3 =>
      '201c120000118674061b00418008324300000000000000000000000000000040';

  String get _DSH =>
      '201c120000118674061b00418013730000000000000000000000000000000040';

  String get _oldElectricWaterHeaterTypeId =>
      '201c120000118674061800418006970000000000000000000000000000000040';

  final String _heatingStatus1 = '1';
  final String _heatingStatus2 = '2';

  final String _firewallMsg = '防火墙高温保护中';

  final String _offStatusMsg = '热水器关机，无法操作';

  final String _unControlMsg = '当前状态无法调温';

  final String _waterOutletMsg = '检测到正在出水，无法操控';

  final String _manual3dStatusKey = 'manual3dStatus'; // 3D速热功能
  final String _3dRunningStatusKey = '3dRunningStatus'; // Turbo速热运行中

  String get _currentTemperatureKey => 'currentTemperature';

  String get _oddHeatTimeKey => 'oddHeatTime';

  String get _heatingStatusKey => 'heatingStatus';

  String get _powerSettingSupportedKey => 'powerSettingSupported';

  String get _setConstantTemperatureKey => 'setConstantTemperature';

  String get _targetTemperatureKey => 'targetTemperature';

  String get _cloudSmartStatusKey => 'cloudSmartStatus';

  String get _resn1RunningStatusKey => 'resn1RunningStatus';

  String get _resn2RunningStatusKey => 'resn2RunningStatus';

  String get _waterOutletShutdownStatusKey => 'waterOutletShutdownStatus';

  String get _sceneKey => 'scene';

  String get _value2 => '2';

  String get _value3 => '3';

  String get _value4 => '4';

  String get _vastModeKey => 'vastMode';

  String get _waterModeKey => 'waterMode';

  String get _sterilizationStatusKey => 'sterilizationStatus';

  String get _privateBathStatusKey => 'privateBathStatus';

  String get _autoTimeOnOffKey => 'autoTimeOnOff';

  String get _firewallRunResultsKey => 'firewallRunResults';

  String get _openingStatusKey => 'openingStatus';

  SmartHomeDeviceAttribute? get _openingStatusAttribute {
    return device.attributeMap[_openingStatusKey];
  }

  SmartHomeDeviceAttribute? get _sceneAttribute {
    return device.attributeMap[_sceneKey];
  }

  SmartHomeDeviceAttribute? get _vastModeAttribute {
    return device.attributeMap[_vastModeKey];
  }

  SmartHomeDeviceAttribute? get _waterModeAttribute {
    return device.attributeMap[_waterModeKey];
  }

  SmartHomeDeviceAttribute? get _cloudSmartStatusAttribute {
    return device.attributeMap[_cloudSmartStatusKey];
  }

  SmartHomeDeviceAttribute? get _resn1RunningStatusAttribute {
    return device.attributeMap[_resn1RunningStatusKey];
  }

  SmartHomeDeviceAttribute? get _resn2RunningStatusAttribute {
    return device.attributeMap[_resn2RunningStatusKey];
  }

  SmartHomeDeviceAttribute? get _sterilizationStatusAttribute {
    return device.attributeMap[_sterilizationStatusKey];
  }

  SmartHomeDeviceAttribute? get _privateBathStatusAttribute {
    return device.attributeMap[_privateBathStatusKey];
  }

  SmartHomeDeviceAttribute? get _targetTemperatureAttribute {
    return device.attributeMap[_targetTemperatureKey];
  }

  SmartHomeDeviceAttribute? get _setConstantTemperatureAttribute {
    return device.attributeMap[_setConstantTemperatureKey];
  }

  SmartHomeDeviceAttribute? get _autoTimeOnOffKeyAttribute {
    return device.attributeMap[_autoTimeOnOffKey];
  }

  SmartHomeDeviceAttribute? get _currentTemperatureAttribute {
    return device.attributeMap[_currentTemperatureKey];
  }

  SmartHomeDeviceAttribute? get _oddHeatTimeAttribute {
    return device.attributeMap[_oddHeatTimeKey];
  }

  SmartHomeDeviceAttribute? get _heatingStatusAttribute {
    return device.attributeMap[_heatingStatusKey];
  }

  SmartHomeDeviceAttribute? get _powerSettingSupportedAttribute {
    return device.attributeMap[_powerSettingSupportedKey];
  }

  SmartHomeDeviceAttribute? get _manual3dStatusAttribute {
    return device.attributeMap[_manual3dStatusKey];
  }

  SmartHomeDeviceAttribute? get _3dRunningStatusAttribute {
    return device.attributeMap[_3dRunningStatusKey];
  }

  SmartHomeDeviceAttribute? get _firewallRunResultsAttribute {
    return device.attributeMap[_firewallRunResultsKey];
  }

  SmartHomeDeviceAttribute? get _waterOutletShutdownStatusAttribute {
    return device.attributeMap[_waterOutletShutdownStatusKey];
  }

  bool get _waterOutletShutdownStatusOn {
    return _waterOutletShutdownStatusAttribute?.value == trueValue;
  }

  bool get _firewallRunResultsStopHeating {
    return _firewallRunResultsAttribute?.value == '2';
  }

  //老式即热设备
  bool get _oldImmediateHeatDevice {
    final String powerSettingSupported =
        _powerSettingSupportedAttribute?.value ?? falseValue;
    if (_oldElectricWaterHeaterTypeId == device.basicInfo.typeId &&
        powerSettingSupported == trueValue) {
      return true;
    }
    return false;
  }

  @override
  String get smallCardStatus {
    String status = '';
    final String onOffStatus = onOffAttribute?.value ?? '';
    if (onOffAttribute != null) {
      status = onOffStatus == trueValue ? '开' : '关';
    }
    return status;
  }

  @override
  String get largeCardStatus {
    return _largeAndMiddleStatus();
  }

  @override
  String get middleCardStatus {
    return _largeAndMiddleStatus();
  }

  // 大、中卡片状态区显示
  String _largeAndMiddleStatus() {
    String status = '';
    final String onOffStatus = onOffAttribute?.value ?? '';

    final String temperature = _currentTemperatureAttribute?.value ?? '';
    if (temperature.isNotEmpty) {
      status += '水温$temperature ℃';
    }

    if (_waterOutletShutdownStatusAttribute?.value == trueValue) {
      return '$status 出水断电中';
    }

    if (_openingStatusAttribute?.value == trueValue) {
      return '$status 需拔电';
    }
    if (_firewallRunResultsStopHeating) {
      return '$status 高温保护';
    }

    String oddHeatTimeStatus = '';
    final String oddHeatTimeStr = _oddHeatTimeAttribute?.value ?? '';

    final int oddHeatTime = int.tryParse(oddHeatTimeStr) ?? 0;

    if (onOffStatus == trueValue) {
      String heatingStatus = '';
      final String heatingStatusValue = _heatingStatusAttribute?.value ?? '';
      if (heatingStatusValue == _heatingStatus1) {
        heatingStatus = '保温';
      } else if (heatingStatusValue == _heatingStatus2) {
        final String sterilizationStatus =
            _sterilizationStatusAttribute?.value ?? '';
        if (sterilizationStatus == trueValue) {
          heatingStatus = '抑菌加热';
          oddHeatTimeStatus = _oddHeatTimeStatus(oddHeatTime);
        } else if (sterilizationStatus == falseValue ||
            sterilizationStatus.isEmpty) {
          final String running3dStatus = _3dRunningStatusAttribute?.value ?? '';
          if (running3dStatus == trueValue) {
            heatingStatus = 'Turbo速热';
          } else if (running3dStatus == falseValue) {
            heatingStatus = '加热';
            oddHeatTimeStatus = _oddHeatTimeStatus(oddHeatTime);
          } else {
            final String manual3dStatus = _manual3dStatusAttribute?.value ?? '';
            if (manual3dStatus == trueValue) {
              heatingStatus = '速热';
            } else {
              heatingStatus = '加热';
              oddHeatTimeStatus = _oddHeatTimeStatus(oddHeatTime);
            }
          }
        }
        if (_oldImmediateHeatDevice) {
          oddHeatTimeStatus = '';
        }
        heatingStatus += oddHeatTimeStatus;
      }
      if (heatingStatus != '') {
        status += ' $heatingStatus';
      }
    }
    return status;
  }

  // 剩余加热时间状态，仅在大卡显示
  String _oddHeatTimeStatus(int oddHeatTime) {
    if (deviceCardType == DeviceCardType.largeCard &&
        oddHeatTime > 0 &&
        _openingStatusAttribute?.value != trueValue &&
        !_firewallRunResultsStopHeating) {
      return ' 剩余$oddHeatTime分钟';
    }
    return '';
  }

  @override
  bool supportPowerOnOff() {
    return true;
  }

  @override
  Future<bool> powerButtonCheckContinue() {
    final bool writable = onOffAttribute?.writable ?? false;
    final bool firewallOn = _firewallRunResultsStopHeating;
    final bool waterOutletOn = _waterOutletShutdownStatusOn;
    String writableMsg = '';
    if (firewallOn) {
      writableMsg = _firewallMsg;
    } else if (waterOutletOn) {
      writableMsg = _waterOutletMsg;
    }

    return checkDeviceState(
        checkPowerOff: false,
        checkChildLock: false,
        writable: !(!writable || firewallOn || waterOutletOn),
        writableMsg: writableMsg);
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '电热水器';
    final LargeDeviceCardFunctionSet largeDeviceCardFunctionSet =
        LargeDeviceCardFunctionSet(
      name: name,
      componentViewModelList: <ComponentBaseViewModel>[
        _temperatureComponentViewModel(),
        if (_resn1RunningStatusAttribute is SmartHomeDeviceAttribute ||
            _resn2RunningStatusAttribute is SmartHomeDeviceAttribute)
          _resnRunningStatusComponentViewModel(),
        if (_autoTimeOnOffKeyAttribute is SmartHomeDeviceAttribute &&
            _isTypeIdSupportOrder)
          _eHeatTimeOnOffComponentViewModel(),
      ],
    );
    return <String, LargeDeviceCardFunctionSet>{
      largeDeviceCardFunctionSet.name: largeDeviceCardFunctionSet
    };
  }

  // 调温组件VM
  ComponentBaseViewModel _temperatureComponentViewModel() {
    final SmartHomeDeviceAttribute? attribute = _constantTemperatureDevice
        ? _setConstantTemperatureAttribute
        : _targetTemperatureAttribute;
    double currentValue = double.tryParse(attribute?.value ?? '0') ?? 0;

    /// 特殊逻辑
    currentValue = _manageValueWith(currentValue, ValueType.target);

    // 组装温度list
    double maxValue = 0;
    double minValue = 0;
    double stepper = 0;
    List<String> _valueList = <String>[];
    final SmartHomeDeviceAttributeValueRange? valueRange =
        attribute?.valueRange;
    if (valueRange?.type == SmartHomeDeviceAttributeValueRangeType.step) {
      final SmartHomeDataStep? dataStep = valueRange?.dataStep;
      maxValue = double.tryParse(dataStep?.maxValue ?? '0') ?? 0;
      minValue = double.tryParse(dataStep?.minValue ?? '0') ?? 0;
      stepper = double.tryParse(dataStep?.step ?? '0') ?? 0;
    }
    minValue = _manageValueWith(minValue, ValueType.min);
    maxValue = _manageValueWith(maxValue, ValueType.max);

    // 可调控的温度list 和 当前温度下标
    if (deviceOffline) {
      _valueList = <String>[];
    } else {
      while (stepper != 0 && minValue <= maxValue) {
        _valueList.add(_tempDoubleToString(minValue));
        minValue += stepper;
      }
    }
    final int _index = _valueList
        .indexWhere((String e) => e == _tempDoubleToString(currentValue));
    final int _currentIndex = _index == -1 ? 0 : _index;

    // 组件禁用状态 enable
    bool writable = attribute?.writable ?? false;
    if (_BK3 == device.basicInfo.typeId &&
        _privateBathStatusAttribute?.value == trueValue) {
      writable = false;
    }
    final String onOffStatus = onOffAttribute?.value ?? '';

    final bool disable = !writable ||
        _firewallRunResultsStopHeating ||
        onOffStatus != trueValue ||
        _valueList.isEmpty ||
        deviceOfflineOrPowerOff ||
        loading;

    final ComponentBaseViewModel flexValueListViewModel =
        ExpandValueListViewModel(
            valueList: _valueList,
            currentIndex: _currentIndex,
            unit: '℃',
            enable: !disable,
            noMorePreCallback: (BuildContext context) {
              ToastHelper.showToast('已是最低温度');
            },
            noMoreNextCallback: (BuildContext context) {
              ToastHelper.showToast('已是最高温度');
            },
            checkContinue: () {
              return _checkTargetTemperatureContinue(attribute, !disable);
            },
            valueListEmptyCallback: (BuildContext context) {
              _checkTargetTemperatureContinue(attribute, !disable);
            },
            valueChangeCallback:
                (BuildContext context, String value, int index) {
              checkDeviceState(
                      writable: !disable,
                      writableMsg: _firewallRunResultsStopHeating
                          ? _firewallMsg
                          : _unControlMsg,
                      powerOffMsg: _offStatusMsg)
                  .then((bool pass) {
                if (pass) {
                  final Map<String, String> commands = <String, String>{};
                  commands[attribute?.name ?? ''] = value;
                  quickCtrlLECommand(device.basicInfo.deviceId, commands,
                      onErrorCallback: (String errMsg) {
                    ToastHelper.showToast(errMsg);
                  });
                }
              });
            });

    return flexValueListViewModel;
  }

  // 显示温度转为String且小数部分为0时不显示小数部分
  String _tempDoubleToString(double temp) {
    return temp % 1 == 0 ? temp.toInt().toString() : temp.toStringAsFixed(1);
  }

  Future<bool> _checkTargetTemperatureContinue(
      SmartHomeDeviceAttribute? attribute, bool writable) {
    final bool firewallOn = _firewallRunResultsStopHeating;
    final bool waterOutletOn = _waterOutletShutdownStatusOn;

    String writableMsg = _unControlMsg;
    if (firewallOn) {
      writableMsg = _firewallMsg;
    } else if (waterOutletOn) {
      writableMsg = _waterOutletMsg;
    }
    return checkDeviceState(
        writable: !(!writable || firewallOn || waterOutletOn),
        writableMsg: writableMsg,
        powerOffMsg: _offStatusMsg);
  }

  // 预约用水VM
  ComponentBaseViewModel _resnRunningStatusComponentViewModel() {
    final SmartHomeDeviceAttribute? resn1Attribute =
        _resn1RunningStatusAttribute;
    final SmartHomeDeviceAttribute? resn2Attribute =
        _resn2RunningStatusAttribute;
    final String resn1Value = resn1Attribute?.value ?? '';
    final bool resn1Writable = resn1Attribute?.writable ?? true;

    final String resn2Value = resn2Attribute?.value ?? '';
    final bool resn2Writable = resn2Attribute?.writable ?? true;

    final bool resnOn =
        powerOn && (resn1Value == trueValue || resn2Value == trueValue);
    final bool resnWritable = resn1Writable && resn2Writable;
    final String onOffStatus = onOffAttribute?.value ?? '';
    final bool firewallOn = _firewallRunResultsStopHeating;
    final bool waterOutletOn = _waterOutletShutdownStatusOn;
    String writableMsg = '当前状态无法设置预约用水';
    if (firewallOn) {
      writableMsg = _firewallMsg;
    } else if (waterOutletOn) {
      writableMsg = _waterOutletMsg;
    }
    final bool disable = alarm ||
        !resnWritable ||
        waterOutletOn ||
        firewallOn ||
        onOffStatus != trueValue ||
        deviceOfflineOrPowerOff ||
        loading;

    final ComponentBaseViewModel componentBaseViewModel =
    FixedSwitchIconTextViewModel(
            text: '预约用水',
            icon: 'assets/components/order_water.webp',
            isOn: resnOn,
            enable: !disable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '预约用水');
              checkDeviceState(
                      checkChildLock: false,
                      writable: !disable,
                      writableMsg: writableMsg,
                      powerOffMsg: _offStatusMsg)
                  .then((bool pass) {
                if (pass) {
                  goToPageWithDebounce(
                      'http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=${device.basicInfo.deviceId}&businessType=waterHouseNewOrderDetail');
                }
              });
            });

    return componentBaseViewModel;
  }

  // 定时VM
  ComponentBaseViewModel _eHeatTimeOnOffComponentViewModel() {
    final SmartHomeDeviceAttribute? attribute = _autoTimeOnOffKeyAttribute;
    final String autoOnOff = attribute?.value ?? '';
    final bool writable = attribute?.writable ?? false;
    final bool resnOn = autoOnOff == trueValue;
    final bool firewallOn = _firewallRunResultsStopHeating;
    final bool waterOutletOn = _waterOutletShutdownStatusOn;
    String writableMsg = '当前状态无法设置定时开关';
    if (firewallOn) {
      writableMsg = _firewallMsg;
    } else if (waterOutletOn) {
      writableMsg = _waterOutletMsg;
    }

    final bool disable = alarm ||
        !writable ||
        waterOutletOn ||
        firewallOn ||
        deviceOffline ||
        loading;

    final ComponentBaseViewModel buttonComponentViewModel =
    FixedSwitchIconTextViewModel(
            text: '定时',
            icon: 'assets/components/card_auto_on_off.webp',
            isOn: resnOn,
            enable: !disable,
            clickCallback: (BuildContext? context) {
              gio(cloudProgramName: '定时');
              checkDeviceState(
                      checkPowerOff: false,
                      checkChildLock: false,
                      writable: !disable,
                      writableMsg: writableMsg)
                  .then((bool pass) {
                if (pass) {
                  goToPageWithDebounce(
                      'http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=${device.basicInfo.deviceId}&businessType=waterHouseTimerSwitch');
                }
              });
            });

    return buttonComponentViewModel;
  }

  /// 电热特殊处理
  double _manageValueWith(double defaultValue, ValueType valueType) {
    double min = defaultValue;
    double max = defaultValue;
    double target = defaultValue;

    /// 特殊逻辑
    if (_maxValue75TypeId.contains(device.basicInfo.typeId)) {
      max = 75;
    }
    if (_MAX5 == device.basicInfo.typeId &&
        _sceneAttribute?.value == _value3 &&
        _cloudSmartStatusAttribute?.value == falseValue &&
        _resn1RunningStatusAttribute?.value == falseValue) {
      target = 85;
      max = 85;
    }
    if (_MAX3 == device.basicInfo.typeId &&
        _vastModeAttribute?.value == trueValue) {
      target = 85;
    }
    if (_SMART5 == device.basicInfo.typeId &&
        _sterilizationStatusAttribute?.value == trueValue) {
      min = 35;
      target = 75;
      max = 75;
    }

    if (_DSH == device.basicInfo.typeId ||
        _ZE5 == device.basicInfo.typeId ||
        _AJ3 == device.basicInfo.typeId) {
      if (_waterModeAttribute?.value == _value2) {
        min = 30;
        max = 36;
      }
      if (_waterModeAttribute?.value == _value3) {
        min = 37;
        max = 40;
      }
      if (_waterModeAttribute?.value == _value4) {
        min = 41;
        max = 45;
      }
    }

    if (valueType == ValueType.target) {
      return target;
    }
    if (valueType == ValueType.min) {
      return min;
    }
    if (valueType == ValueType.max) {
      return max;
    }

    return defaultValue;
  }
}

enum ValueType {
  min,
  max,
  target,
}
