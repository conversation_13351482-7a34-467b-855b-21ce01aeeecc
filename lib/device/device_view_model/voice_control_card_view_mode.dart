import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class VoiceControlCardViewModel extends DeviceCardViewModel {
  VoiceControlCardViewModel({required super.device});

  @override
  String get smallCardStatus {
    if (powerOn) {
      return '开';
    }
    if (powerOff) {
      return '关';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    if (powerOn) {
      final double indoorTemperatureValue = double.tryParse(
              device.attributeMap['indoorTemperature']?.value ?? '0') ??
          0;
      if (indoorTemperatureValue >= 0 && indoorTemperatureValue <= 55) {
        return DeviceCardAttribute(
            label: '', value: indoorTemperatureValue.toString(), unit: '℃');
      }

      if (indoorTemperatureValue < 0) {
        return DeviceCardAttribute(label: '', value: '0', unit: '℃');
      }
      return DeviceCardAttribute(label: '', value: '55', unit: '℃');
    }

    return null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    if (powerOn) {
      final String operationModeValue =
          device.attributeMap['operationMode']?.value ?? '';
      final String modeContent = _operationMode[operationModeValue] ?? '';
      return modeContent.isNotEmpty
          ? DeviceCardAttribute(label: '', value: modeContent, unit: '')
          : null;
    }

    return null;
  }

  final Map<String, String> _operationMode = <String, String>{
    '0': '自动',
    '1': '制冷',
    '2': '除湿',
    '4': '制热',
    '6': '送风',
  };
}
