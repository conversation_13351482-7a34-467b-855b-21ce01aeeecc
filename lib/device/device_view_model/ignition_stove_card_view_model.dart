import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class IgnitionStoveCardViewModel extends DeviceCardViewModel {
  IgnitionStoveCardViewModel({required super.device});

  final String leftIgnitionStatusKey = 'leftIgnitionStatus';
  final String rightIgnitionStatusKey = 'rightIgnitionStatus';
  final String middleIgnitionStatusKey = 'middleIgnitionStatus';

  SmartHomeDeviceAttribute? get leftIgnitionStatusAttribute {
    return device.attributeMap[leftIgnitionStatusKey];
  }

  SmartHomeDeviceAttribute? get rightIgnitionStatusAttribute {
    return device.attributeMap[rightIgnitionStatusKey];
  }

  SmartHomeDeviceAttribute? get middleIgnitionStatusAttribute {
    return device.attributeMap[middleIgnitionStatusKey];
  }

  bool _burn(String? value) {
    return value == 'true' || value == '2' || value == '3' || value == '4';
  }

  bool _unBurn(String? value) {
    return value == 'false' || value == '1';
  }

  @override
  bool get runningMode {
    if (_burn(leftIgnitionStatusAttribute?.value) ||
        _burn(rightIgnitionStatusAttribute?.value) ||
        _burn(middleIgnitionStatusAttribute?.value)) {
      return true;
    }
    return false;
  }

  @override
  String get smallCardStatus {
    if (_burn(leftIgnitionStatusAttribute?.value) ||
        _burn(rightIgnitionStatusAttribute?.value)) {
      return '有火';
    } else if (_unBurn(leftIgnitionStatusAttribute?.value) &&
        _unBurn(rightIgnitionStatusAttribute?.value)) {
      return '无火';
    }
    return '';
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    final String value = leftIgnitionStatusAttribute?.value ?? '';
    final String attr = assignAttrValue(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '左灶', value: attr, unit: '')
        : null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    final String value = rightIgnitionStatusAttribute?.value ?? '';
    final String attr = assignAttrValue(value);
    return attr.isNotEmpty
        ? DeviceCardAttribute(label: '右灶', value: attr, unit: '')
        : null;
  }

  String assignAttrValue(String value) {
    switch (value) {
      case '1':
        return '无火';
      case '2':
        return '低火';
      case '3':
        return '中火';
      case '4':
        return '高火';
      case 'true':
        return '有火';
      case 'false':
        return '无火';
      default:
        return '';
    }
  }
}
