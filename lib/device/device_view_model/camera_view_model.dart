import 'dart:async';

import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/plugin/logicengine_plugin.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/utils/agg_utils.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_360_player_impl.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_presenter.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_tencent_player_impl.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/device_widget/camera.dart';
import 'package:smart_home/device/store/camera/camera_support.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../component_view_model/component_view_model.dart';
import '../component_view_model/power_on_off_btn_model/power_button_animation_view_model.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../resize_device_card/resize_base_model.dart';
import 'card_base_view_model.dart';

class CameraMsgItem {
  String title;
  String timeStamp;
  String imageUrl;

  CameraMsgItem(this.title, this.timeStamp, this.imageUrl);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CameraMsgItem &&
          title == other.title &&
          imageUrl == other.imageUrl &&
          timeStamp == other.timeStamp;

  @override
  int get hashCode => title.hashCode ^ imageUrl.hashCode ^ timeStamp.hashCode;

  @override
  String toString() {
    return 'CameraMsgItem{title: $title, timeStamp: $timeStamp, imageUrl: $imageUrl}';
  }
}

class CameraMsgVmWrapper {
  final CameraMsgVM vm;
  int vmKey = 0;

  CameraMsgVmWrapper(this.vm, CameraType type) {
    vmKey = vm.hashCodeWithType(type);
  }

  @override
  bool operator ==(Object other) {
    bool falg = other is CameraMsgVmWrapper && other.vmKey == vmKey;
    return falg;
  }

  @override
  int get hashCode => vmKey;

  bool isValid() {
    return vm.isValid();
  }
}

///看家消息VM
class CameraMsgVM {
  final String _tag = 'CameraMsgVM';

  List<CameraMsgItem> msgs = <CameraMsgItem>[];

  String deviceId = '';
  String devicetype = '';
  String devno = '';

  void setGioInfo(String devId, String type, String devNo) {
    deviceId = devId;
    devicetype = type;
    devno = devNo;
  }

  void gioCameraMsgClick() {
    gioTrack(CameraConstant.gioCameraMsgId, <String, dynamic>{
      CameraConstant.gioProName: CameraConstant.tipForCameraMsg,
      CameraConstant.gioDevId: deviceId,
      CameraConstant.gioDevType: devicetype,
      CameraConstant.gioDevno: devno,
    });
  }

  bool isShowTips = false;

  final Map<CameraType, bool> _cameraPlayingMap = <CameraType, bool>{};

  CameraMsgVM() {
    _cameraPlayingMap[CameraType.HOME_CARD] = false;
    _cameraPlayingMap[CameraType.AGGREGATION_CARD] = false;
  }

  bool showBlur(CameraType type) {
    final bool? flag = _cameraPlayingMap[type];
    if (flag == null) {
      return true;
    } else {
      return !flag;
    }
  }

  bool _isOfflineFlag = false;

  void updateValid(CameraType type, bool isOffline, bool isPlaying) {
    DevLogger.info(
        tag: _tag,
        msg:
            'updateValid camera msg $isOffline $isPlaying , current is $_isOfflineFlag,${_cameraPlayingMap[type]}');
    if (isOffline != _isOfflineFlag) {
      _isOfflineFlag = isOffline;
    }
    _cameraPlayingMap[type] = isPlaying;
  }

  @override
  bool operator ==(Object other) {
    bool flag1 = other is CameraMsgVM &&
        listEquals(msgs, other.msgs) &&
        _isOfflineFlag == other._isOfflineFlag &&
        isShowTips == other.isShowTips;
    return flag1;
  }

  @override
  int get hashCode =>
      listHashCode(msgs) ^ _isOfflineFlag.hashCode ^ isShowTips.hashCode;

  int hashCodeWithType(CameraType type) {
    return hashCode ^ (_cameraPlayingMap[type] ?? false).hashCode;
  }

  void goToCameraMsgDetailPage(String deviceId) {
    if (deviceId.isEmpty) {
      DevLogger.info(
          tag: _tag, msg: 'Cannot navigate to detail: empty deviceId');
      return;
    }
    gioCameraMsgClick();
    if (deviceId.isNotEmpty) {
      goToPageWithDebounce('${CameraConstant.detailCameraMsgUrl}$deviceId');
    }
  }

  void goToCameraMsgDetailPageFromAlarmTime(String deviceId, String alarmTime) {
    if (deviceId.isEmpty || alarmTime.isEmpty) {
      DevLogger.info(
          tag: _tag,
          msg: 'Cannot navigate to detail item: empty deviceId or alarmTime');
      return;
    }
    gioCameraMsgClick();
    if (deviceId.isNotEmpty && alarmTime.isNotEmpty) {
      goToPageWithDebounce(
          '${CameraConstant.detailCameraMsgUrl}$deviceId&alarmTime=$alarmTime');
    }
  }

  void updateValue(CameraMsgVM value) {
    msgs = value.msgs;
    isShowTips = value.isShowTips;
  }

  @override
  String toString() {
    return 'cameramsg vm msgs=$msgs';
  }

  bool isValid() {
    return isShowTips && msgs.isNotEmpty;
  }
}

class SecureDeviceViewModel extends DeviceCardViewModel {
  SecureDeviceViewModel({required super.device}) {
    refresh();
  }

  void refresh() {
    refreshTitle();
    refreshSubTitle();
  }

  void refreshTitle() {
    title = deviceName;
    if (title.length > 12) {
      title = '${title.substring(0, 11)}…';
    }
  }

  void refreshSubTitle() {
    subTitle = room;
    if (subTitle.length > 12) {
      subTitle = '${subTitle.substring(0, 11)}…';
    }
  }

  String title = '';
  String subTitle = '';
}

class CameraVMWrapper {
  final CameraDeviceCardViewModel vm;
  int vmKey = 0;
  CameraVMWrapper(this.vm, CameraType type) {
    vmKey = vm.hashCodeByCameraType(type);
  }

  @override
  bool operator ==(Object other) {
    bool falg = other is CameraVMWrapper && other.vmKey == vmKey;
    return falg;
  }

  @override
  int get hashCode => vmKey;
}

class CameraDeviceCardViewModel extends SecureDeviceViewModel {
  @override
  double get maxHeight => _getLargeCardHeight();

  @override
  double get height {
    if (deviceCardType == DeviceCardType.largeCard) {
      return _getLargeCardHeight();
    }
    return super.height;
  }

  double _getLargeCardHeight() {
    const double screenWidth = 375.0;
    const double horizontalPadding = 32.0;
    final double cardWidth = screenWidth.w - horizontalPadding;

    if (_cameraMsgVM.msgs.isNotEmpty && _cameraMsgVM.isShowTips) {
      const double designWidth = 358.0;
      const double designHeight = 280.0;
      return (cardWidth / designWidth * designHeight).floorToDouble();
    }

    const double aspectRatio = 16 / 9;
    return cardWidth / aspectRatio;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    /// 默认展示状态1
    if (isOffline(CameraType.HOME_CARD)) {
      return super.deviceCardAttributeOne();
    }
    if (isSleep) {
      return DeviceCardAttribute(
          label: CameraConstant.tipForSleep, value: '', unit: '');
    }
    return DeviceCardAttribute(
        label: CameraConstant.tipForWakeup, value: '', unit: '');
  }

  @override
  ComponentBaseViewModel? get topRightComponentViewModel {
    if (deviceCardType == DeviceCardType.middleCard) {
      return _createMiddleCardTopRightComponent() ??
          super.topRightComponentViewModel;
    }
    return super.topRightComponentViewModel;
  }

  Future<bool> sleepButtonCheckContinue() {
    return checkDeviceState(
        checkPowerOff: false,
        writable:
            device.attributeMap[CameraConstant.sleepAttr]?.writable ?? false);
  }

  ComponentBaseViewModel? _createMiddleCardTopRightComponent() {
    final SmartHomeDeviceAttribute? attribute =
        device.attributeMap[CameraConstant.sleepAttr];
    if (attribute == null) {
      return null;
    }
    final PowerButtonAnimationViewModel viewModel =
        PowerButtonAnimationViewModel(
            isOn: !isSleep,
            writable: !isOffline(CameraType.HOME_CARD) && attribute!.writable,
            checkContinue: sleepButtonCheckContinue,
            btnClick: (BuildContext? context) {
              if (deviceCardType == DeviceCardType.largeCard) {
                gioTrack(
                    SmartHomeConstant.deviceCardOnOffGio, <String, dynamic>{
                  'deviceid': deviceId,
                  'devicetype': appTypeName,
                  'status': attribute.value == trueValue ? '关机' : '开机',
                  'devno': prodNo,
                });
              } else if (deviceCardType == DeviceCardType.middleCard) {
                gioTrack(
                    SmartHomeConstant.middleCardOnOffGio, <String, dynamic>{
                  'deviceid': deviceId,
                  'devicetype': appTypeName,
                  'status': attribute.value == trueValue ? '关机' : '开机',
                  'devno': prodNo,
                });
              }
              _handleSleepButton(!isSleep, deviceId, tag,
                  showToast:
                      deviceCardType == DeviceCardType.middleCard && !isSleep);
            });
    return viewModel;
  }

  bool enableMiddleCardTopRightButton() {
    return !isOffline(CameraType.HOME_CARD);
  }

  String tag = 'CameraDeviceCardViewModel';

  String pauseUrl = CameraConstant.pauseUrl;

  String offlineIcon = CameraConstant.offlineIcon;

  String offlineTips = CameraConstant.offlineTips;

  String playIcon = CameraConstant.playIcon;

  String loadingTips = CameraConstant.loadingTips;

  String sleepTips = CameraConstant.sleepTips;

  String sleepImageUrl = CameraConstant.sleepImageUrl;

  String rightArrImage = CameraConstant.rightArrImage;

  double get playerHeight => _getCardHeight(9 / 16);
  double get playerWidth => _getCardWidth();

  bool supportFullScreenMode(CameraUIType type) {
    if (_isTencentCameraDevice(device.basicInfo.typeId)) {
      return false;
    }
    return true;
  }

  final CameraMsgVM _cameraMsgVM = CameraMsgVM();

  CameraDeviceCardViewModel(SmartHomeDevice device) : super(device: device) {
    cardType = CardType.cameraCard;
    refresh();
  }

  @override
  bool supportQuickControl() {
    return true;
  }

  @override
  void refresh() {
    super.refresh();
    isSleep = _isSleepMode(device);

    if (isSleep) {
      CameraLiveCoordinator.instance.stopSleepDevice(deviceId);
      _updateAllCameraTypeStyle(null, CameraWidgetStyle.sleep);
    } else {
      if (device.onlineState != SmartHomeDeviceOnlineState.offline) {
        _updateAllCameraTypeStyle(
            CameraWidgetStyle.sleep, CameraWidgetStyle.stop);
      }
      if (device.onlineState == SmartHomeDeviceOnlineState.offline) {
        _updateAllCameraTypeStyle(null, CameraWidgetStyle.offline);
      }
    }
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  ///摄像头有睡眠模式
  bool _isSleepMode(SmartHomeDevice device) {
    return device.attributeMap[CameraConstant.sleepAttr]?.value ==
        SmartHomeConstant.deviceAttrTrue;
  }

  String retryTips = CameraConstant.retryTips;

  String loadingErrImageUrl = CameraConstant.loadingErrImageUrl;

  String bgImageUrl = CameraConstant.bgImageUrl;

  String bgHeaderImageUrl = CameraConstant.bgHeaderImageUrl;

  double _getCardWidth() {
    const double cardMargin = 16;
    const double cardPadding = 0;
    final double screenWidth = ScreenUtil().screenWidth;
    final double v = screenWidth - cardMargin * 2 - cardPadding * 2;
    return v;
  }

  double _getCardHeight(double ratio) {
    final double w = _getCardWidth();
    final double h = w * ratio;
    return h;
  }

  @override
  bool operator ==(Object other) {
    bool flag = other is CameraDeviceCardViewModel &&
        device.basicInfo.deviceId == other.device.basicInfo.deviceId &&
        mapEquals(other.cameraTypeStyleMap, cameraTypeStyleMap) &&
        other.isMute == isMute &&
        other.floor == floor &&
        other.isSelected == isSelected &&
        other.isSleep == isSleep &&
        other.room == room &&
        other.subTitle == subTitle &&
        _cameraMsgVM == other._cameraMsgVM;
    return flag;
  }

  int hashCodeByCameraType(CameraType type) {
    return device.basicInfo.deviceId.hashCode ^
        cameraTypeStyleMap[type].hashCode ^
        getCameraToolsBarWithType(type).hashCode ^
        isSelected.hashCode ^
        isSleep.hashCode ^
        floor.hashCode ^
        room.hashCode ^
        isMute.hashCode ^
        subTitle.hashCode ^
        _cameraMsgVM.hashCode;
  }

  bool getCameraToolsBarWithType(CameraType type) {
    return _cameraToolsBarWithStyle[type] ?? false;
  }

  void closeToolsBarWithType(CameraType type) {
    _cameraToolsBarWithStyle[type] = false;
  }

  void changeToolsBarWithType(CameraType type) {
    if (_cameraToolsBarWithStyle[type] != null) {
      final bool flag = _cameraToolsBarWithStyle[type] ?? true;
      _cameraToolsBarWithStyle[type] = !flag;
    }
  }

  @override
  int get hashCode =>
      device.basicInfo.deviceId.hashCode ^
      mapHashCode(cameraTypeStyleMap) ^
      isSelected.hashCode ^
      isSleep.hashCode ^
      floor.hashCode ^
      room.hashCode ^
      isMute.hashCode ^
      subTitle.hashCode;

  Map<CameraType, CameraWidgetStyle> cameraTypeStyleMap =
      <CameraType, CameraWidgetStyle>{
    CameraType.HOME_CARD: CameraWidgetStyle.offline,
    CameraType.AGGREGATION_CARD: CameraWidgetStyle.offline,
  };

  final Map<CameraType, bool> _cameraToolsBarWithStyle = <CameraType, bool>{
    CameraType.HOME_CARD: false,
    CameraType.AGGREGATION_CARD: false,
  };

  // CameraWidgetStyle style = CameraWidgetStyle.offline; //页面style
  String loadingGifName = CameraConstant.loadImageUrl; // 加载动画
  String muteIcon = CameraConstant.muteImageUrl; // 静音图标
  bool isMute = true; // 静音
  bool isSleep = false;

  @override
  String sortId() {
    return device.basicInfo.deviceId;
  }

  //详情页
  void gotoDetailPage(String deviceId, CameraType cameraType) {
    if (cameraType == CameraType.AGGREGATION_CARD) {
      gioAggDetailBtnClick(
          GioConst.aggDetailBtn,
          AggregationDeviceConstant.gioCameraTitle,
          AggregationDeviceConstant.gioCameraCard);
    }
    goToPageWithDebounce(CameraConstant.getCameraDetailPageUrl(deviceId));
  }

  void _updateAllCameraTypeStyle(
      CameraWidgetStyle? targetStyle, CameraWidgetStyle style) {
    cameraTypeStyleMap.forEach((CameraType key, CameraWidgetStyle value) {
      if (targetStyle == null || cameraTypeStyleMap[key] == targetStyle) {
        cameraTypeStyleMap[key] = style;
      }
    });
  }

  void updateDevice(SmartHomeDevice device) {
    this.device = device;
    refresh();
    DevLogger.info(
        tag: 'cameraReducer',
        msg:
            'cameraReducer updateDevice deviceId is [${device.basicInfo.deviceId}], current style is [$cameraTypeStyleMap] , current online state ${device.onlineState} ');
    if (device.onlineState == SmartHomeDeviceOnlineState.offline) {
      _updateAllCameraTypeStyle(null, CameraWidgetStyle.offline);
    } else {
      _updateAllCameraTypeStyle(
          CameraWidgetStyle.offline, CameraWidgetStyle.stop);
    }

    cameraTypeStyleMap.forEach((CameraType key, CameraWidgetStyle value) {
      updateCameraMsg(key);
    });

    DevLogger.info(
        tag: 'cameraReducer',
        msg:
            'after cameraReducer updateDevice deviceId is [${device.basicInfo.deviceId}], current style is [$cameraTypeStyleMap] ');
  }

  bool isPlaying(CameraType type) {
    return cameraTypeStyleMap[type] == CameraWidgetStyle.playing;
  }

  bool isOffline(CameraType type) {
    return cameraTypeStyleMap[type] == CameraWidgetStyle.offline;
  }

  void updateCameraMsg(CameraType type) {
    _cameraMsgVM.updateValid(type, isOffline(type), isPlaying(type));
  }

  void updateCameraMsgValue(CameraMsgVM value) {
    _cameraMsgVM.updateValue(value);

    cameraTypeStyleMap.forEach((CameraType key, CameraWidgetStyle value) {
      updateCameraMsg(key);
    });
  }

  CameraMsgVM getCameraMsgVM() {
    _cameraMsgVM.setGioInfo(deviceId, appTypeName, prodNo);
    return _cameraMsgVM;
  }

  bool isShowPlayer(CameraType type) {
    final CameraWidgetStyle style = cameraTypeStyleMap[type]!;
    if (style == CameraWidgetStyle.loading ||
        style == CameraWidgetStyle.playing) {
      return true;
    }
    return false;
  }

  CameraWidgetStyle getCurrentTyle(CameraType widgetType) {
    return cameraTypeStyleMap[widgetType]!;
  }

  void updateStyle(CameraType cameraType, CameraWidgetStyle loading) {
    cameraTypeStyleMap[cameraType] = loading;
    updateCameraMsg(cameraType);
  }

  void gioSwitchCameraStream() {
    gio(cloudProgramName: CameraConstant.tipForSwitchStream);
  }

  void gioFullscreenClick() {
    gio(cloudProgramName: CameraConstant.tipForFullScreem);
  }

  void gioPlayCLick() {
    gio(cloudProgramName: CameraConstant.gioPlay);
  }

  void gioPauseCLick() {
    gio(cloudProgramName: CameraConstant.gioStop);
  }

  void gioMuteCLick() {
    gio(cloudProgramName: CameraConstant.gioMute);
  }

  void gioCameraMsgClick() {
    gio(cloudProgramName: CameraConstant.gioMute);
  }
}

class CameraConstant {
  static const String tipForSwitchStream = '切换';

  static const String tipForFullScreem = '全屏';

  static const String tipForCameraMsg = '看家消息';

  static const String gioCameraMsgId = 'MB36982';

  static const String gioProName = 'cloud_program_name';
  static const String gioDevId = 'deviceid';
  static const String gioDevType = 'devicetype';
  static const String gioDevno = 'devno';

  static const String cameraSwitchButtonImagePath =
      'assets/icons/icon_camera_switch_stream.webp';

  ////开流埋点
  static String gioPlayingID = 'MB35134';

  ///Camera 按钮埋点
  static String gioBtnID = 'MB35211';

  static String loadingErrImageUrl = 'assets/images/icon_load_error.webp';

  static String vdnLinkPrefix =
      'http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=';

  static String getCameraDetailPageUrl(String devId) {
    return '$vdnLinkPrefix$devId';
  }

  static String loadImageUrl = 'assets/images/camera_loading_img.json';
  static String muteImageUrl = 'assets/images/icon_mute_on_new.webp';

  static String sleepAttr = 'dormantMode';

  static String pauseUrl = 'assets/images/icon_pause_new.webp';

  static String iconFullScreen = 'assets/images/icon_camera_full_screen.webp';

  static String retryTips = '画面获取失败，点击重试';

  static String bgImageUrl = 'assets/images/bg_blur_big_new.webp';

  static String offlineIcon = 'assets/images/icon_offline_new.webp';

  static String offlineTips = '摄像头离线';

  static String playIcon = 'assets/images/icon_play_new.webp';

  static String loadingTips = '画面获取中...';

  static String sleepTips = '摄像头已休眠，点击唤醒';

  static String sleepImageUrl = 'assets/images/icon_sleep_new.webp';

  static String muteImageUrlOn = 'assets/images/icon_mute_on_new.webp';

  static String muteImageUrlOff = 'assets/images/icon_mute_off_new.webp';

  static String playerKeyPrefix = 'smart_home_device_CameraLivePresenter';

  static String bgHeaderImageUrl = 'assets/images/camera_bg_header.webp';

  static String rightArrImage = 'assets/images/more_arrow_white_new.png';

  static String deviceId = 'deviceId';

  static String typeid = 'typeid';

  static String devno = 'devno';

  static String industryCode = 'industryCode';

  static String model = 'model';

  static String contentType = 'content_type';

  static String buttonName = 'button_name';

  static String contentTypeBigCard = '大卡片';

  static String gioStop = '暂停';

  static String gioPlay = '播放';

  static String gioMute = '静音';

  static String? cmIndustryCode = '17_006';

  static String detailCameraMsgUrl =
      'https://uplus.haier.com/uplusapp/deviceVideo/alarmVideo.html?deviceId=';
  static String defaultCameraDeviceImage =
      'assets/icons/default_device_img.webp';

  static String tipsForUnsupportCamera = '点击查看详情';

  static String tipForSleep = '已休眠';
  static String tipForWakeup = '运行中';
}

enum CameraWidgetStyle {
  offline,
  sleep,
  stop,
  loading,
  retry,
  playing,
}

class BasePresenterManager {
  Map<String, CameraLivePresenter> get presenters => _presenters;

  final Map<String, CameraLivePresenter> _presenters =
      <String, CameraLivePresenter>{};

  void updatePresenters(Set<String> devs) {
    _updatePresenters(_presenters, devs);
  }

  CameraType cameraType() {
    return CameraType.HOME_CARD;
  }

  bool containPresenter(String devId) {
    return presenters.containsKey(devId);
  }

  void onCreatePresenter(String devId) {}

  CameraLivePresenter getPresenterByDevId(String devId) {
    bool isCreate = false;
    if (presenters.containsKey(devId)) {
    } else {
      presenters[devId] = createMultiPresenter(devId, cameraType: cameraType());
      isCreate = true;
      onCreatePresenter(devId);
    }
    DevLogger.debug(
        tag: tag,
        msg:
            'getPresenterByDevId is AggregationCameraPresenterManager create $isCreate $devId obj is ${presenters[devId].hashCode}');
    return presenters[devId]!;
  }

  String get tag => 'BasePresenterManager';

  void lockPlayer(String devId) {
    if (presenters.containsKey(devId)) {
      presenters[devId]?.lockPlayer();
    }
  }

  void unlockPlayer(String devId) {
    if (presenters.containsKey(devId)) {
      presenters[devId]?.unlockPlayer();
    }
  }
}

class CameraLiveCoordinator extends BasePresenterManager {
  @override
  String get tag => 'CameraLiveCoordinator';
  // 私有化构造函数
  CameraLiveCoordinator._internal();

  // 静态实例
  static final CameraLiveCoordinator _instance =
      CameraLiveCoordinator._internal();

  // 提供一个全局访问点
  static CameraLiveCoordinator get instance => _instance;

  // 其他静态属性改为实例属性
  String get playingDeviceId => _playingDeviceId;

  String _playingDeviceId = '';

  void stopSleepDevice(String deviceId) {
    final CameraLivePresenter? presenter = presenters[deviceId];
    presenter?.stopSleepDevice();
  }

  void stopCurrentPlayer({bool dispatchStop = true}) {
    if (playingDeviceId.isEmpty) {
      return;
    }
    DevLogger.debug(
        tag: 'cameraliveCoordinator',
        msg:
            'cameraLiveCoord stop current player ,current is $playingDeviceId');
    final CameraLivePresenter? presenter = presenters[playingDeviceId];
    presenter?.stopCallback?.call(dispatchStop);
    reset();
  }

  void wakeupDeviceAndPlay(String deviceId) {
    DevLogger.debug(
        tag: 'cameraliveCoordinator',
        msg:
            'cameraLiveCoord wakeupDeviceAndPlay current is $playingDeviceId , target is $deviceId');
    if (_playingDeviceId == deviceId) {
      return;
    } else {
      stopCurrentPlayer();
    }
  }

  void reset() {
    _playingDeviceId = '';
  }

  void setPlayingDeviceIdAndPresenter(String currentDeviceId) {
    _playingDeviceId = currentDeviceId;
    DevLogger.debug(
        tag: 'cameraliveCoordinator',
        msg: 'cameraLiveCoord setPlayingDeviceIdAndPresenter $playingDeviceId');
  }

  void stopAllPlayer() {
    _presenters.forEach((String deviceId, CameraLivePresenter presenter) {
      presenter.stopCallback?.call(true);
    });
    reset();
  }
}

void handleSleepButton(bool flag, String deviceId, String tag) {
  final Map<String, String> commandMap = <String, String>{
    'name': CameraConstant.sleepAttr,
    'value': '$flag'
  };
  final List<Command> commands = <Command>[Command.fromMap(commandMap)];
  LogicEnginePlugin.operate(deviceId, commands).catchError((dynamic err) {
    DevLogger.error(
        tag: tag,
        msg:
            'operate sheet command issue exception: deviceId: $deviceId, commands: $commands, err: $err');
  });
}

class AggregationCameraPresenterManager extends BasePresenterManager {
  @override
  String get tag => 'AggregationCameraPresenterManager';
  // 私有化构造函数
  AggregationCameraPresenterManager._internal();

  // 静态实例
  static final AggregationCameraPresenterManager _instance =
      AggregationCameraPresenterManager._internal();

  // 提供一个全局访问点
  static AggregationCameraPresenterManager get instance => _instance;

  List<String> playingList = <String>[];
  int maxSize = 4;

  @override
  void onCreatePresenter(String devId) {
    if (CameraLiveCoordinator.instance.containPresenter(devId) &&
        containPresenter(devId)) {
      final CameraLivePresenter presenter = getPresenterByDevId(devId);

      final CameraLivePresenter currentPresenter =
          CameraLiveCoordinator.instance.getPresenterByDevId(devId);

      presenter.setCameraStream(currentPresenter.getCameraStream());
    }
  }

  @override
  CameraType cameraType() {
    return CameraType.AGGREGATION_CARD;
  }

  CameraDeviceCardViewModel? _getDeviceCard(
      String devId, Map<String, CardBaseViewModel> allCardViewModelMap) {
    if (allCardViewModelMap[devId] is CameraDeviceCardViewModel) {
      final CameraDeviceCardViewModel vm =
          allCardViewModelMap[devId]! as CameraDeviceCardViewModel;
      return vm;
    }
    return null;
  }

  void onCameraPlaying(
      String devId, Map<String, CardBaseViewModel> allCardViewModelMap) {
    final CameraDeviceCardViewModel? tmpDev =
        _getDeviceCard(devId, allCardViewModelMap);

    bool isCurrentTencent = false;
    if (tmpDev != null) {
      if (_isTencentCameraDevice(tmpDev.device.basicInfo.typeId)) {
        isCurrentTencent = true;
      }
    }

    bool isTencentCameraPlay = false;

    playingList.forEach((String element) {
      final CameraDeviceCardViewModel? item =
          _getDeviceCard(element, allCardViewModelMap);
      if (item != null) {
        if (_isTencentCameraDevice(item.device.basicInfo.typeId)) {
          isTencentCameraPlay = true;
        }
      }
    });

    if (isCurrentTencent || isTencentCameraPlay) {
      _stopPlayingListCamera();
    }

    if (playingList.length >= maxSize) {
      final String deviceId = playingList.removeAt(0);
      final CameraLivePresenter? presenter = presenters[deviceId];
      presenter?.stopCallback?.call(true);
    }

    playingList.add(devId);
    DevLogger.debug(
        tag: tag,
        msg:
            'onCameraPlaying current dev is $devId ,isCurrentTencent is $isCurrentTencent  , isTencentCameraPlay = $isTencentCameraPlay , playingList = $playingList');
  }

  void _stopPlayingListCamera() {
    final List<String> tmp = List<String>.from(playingList);
    tmp.forEach((String deviceId) {
      final CameraLivePresenter? presenter = presenters[deviceId];
      presenter?.stopCallback?.call(true);
    });
    playingList.clear();
  }

  void onCameraStop(String deviceId) {
    playingList.remove(deviceId);
  }

  void _stopSinglePresetner(String devId) {
    final CameraLivePresenter? presenter = presenters[devId];
    presenter?.stopAndDistoryPlayer();
  }

  void stopAllPlaying() {
    final List<String> tmp = List<String>.from(playingList);
    tmp.forEach((String deviceId) {
      _stopSinglePresetner(deviceId);
    });
    playingList.clear();
  }

  void dispatchStopAllCamera(Store<SmartHomeState>? store) {
    store?.dispatch(CameraAction(CameraActionType.stopAll, CameraPayload(''),
        CameraType.AGGREGATION_CARD));
  }

  void onFullScreen(String devId) {
    final List<String> tmp = List<String>.from(playingList);
    tmp.forEach((String deviceId) {
      if (deviceId != devId) {
        _stopSinglePresetner(deviceId);
      }
    });
    playingList.clear();
    playingList.add(devId);
  }
}

enum CameraType {
  HOME_CARD,
  AGGREGATION_CARD,
}

CameraLivePresenter createMultiPresenter(String devId,
    {CameraType cameraType = CameraType.HOME_CARD}) {
  final CardBaseViewModel? vm =
      smartHomeStore.state.deviceState.allCardViewModelMap[devId];
  CameraStreamCount cameraStreamCount = CameraStreamCount.one;
  String typeId = '';
  if (vm is DeviceCardViewModel) {
    typeId = vm.device.basicInfo.typeId;
    cameraStreamCount = isSupportMultiCameraStream(vm);
  }
  return CameraLivePresenter(
      createCameraPlayer(devId, typeId, streamCount: cameraStreamCount),
      devId,
      typeId,
      cameraStreamCount,
      cameraType: cameraType);
}

const List<String> _tencentsCameraDeviceType = <String>[
  '201c80c70c50031c1201fe4a45318f000000eee01d5dd9d95501c7902480ac40',
  '201c80c70c50031c1201318facf9710000005cae21a91e9926cad929f83d1140',
  '201c80c70c50031c1201c040c2427100000005c20f206d21185e1676a9b52740'
];

bool _isTencentCameraDevice(String typeId) {
  return _tencentsCameraDeviceType.contains(typeId);
}

SmartHomeCameraPlayerInterface createCameraPlayer(String devId, String typeId,
    {CameraStreamCount streamCount = CameraStreamCount.one}) {
  if (_isTencentCameraDevice(typeId)) {
    return TencentCameraPlayer(devId);
  }
  return Camera360Player(devId, streamCount);
}

void _updatePresenters(
    Map<String, CameraLivePresenter> presenters, Set<String> devs) {
  if (devs.isEmpty) {
    presenters.forEach((String key, CameraLivePresenter value) {
      value.dispose();
    });
    presenters.clear();
  } else {
    final List<String> devsNotExists = <String>[];
    presenters.forEach((String key, CameraLivePresenter value) {
      if (!devs.contains(key)) {
        devsNotExists.add(key);
      }
    });

    if (devsNotExists.isNotEmpty) {
      devsNotExists.forEach((String element) {
        if (presenters.containsKey(element)) {
          presenters[element]?.dispose();
          presenters.remove(element);
        }
      });
    }
  }
}

void _handleSleepButton(bool flag, String deviceId, String tag,
    {bool showToast = false}) {
  // bool flag = !isSleep;
  final Map<String, String> commandMap = <String, String>{
    'name': CameraConstant.sleepAttr,
    'value': '$flag'
  };
  final List<Command> commands = <Command>[Command.fromMap(commandMap)];
  LogicEnginePlugin.operate(deviceId, commands).then((_) {
    if (showToast && flag) {
      ToastHelper.showToast(CameraConstant.tipForSleep);
    }
  }).catchError((dynamic err) {
    DevLogger.error(
        tag: tag,
        msg:
            'operate sheet command issue exception: deviceId: $deviceId, commands: $commands, err: $err');
  });
}
