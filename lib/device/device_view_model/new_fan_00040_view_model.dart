/*
 * 描述：新风机 2008610800820324240201518004824200000000000000000000000000000040
 * 作者：songFJ
 * 创建时间：2025/3/12
 */

import 'package:smart_home/device/device_view_model/new_fan_base_view_model.dart';

import '../component_view_model/component_view_model.dart';

class NewFan00040ViewModel extends NewFanBaseViewModel {
  NewFan00040ViewModel({required super.device});

  @override
  bool get supportHumidity {
    return humidityAdjustAttribute != null ||
        humidificationStatusAttribute != null ||
        dehumidificationStatusAttribute != null;
  }

  @override
  List<ComponentBaseViewModel> get componentOfflineOrPowerOffViewModelList {
    return <ComponentBaseViewModel>[
      defaultModeComponent(),
      defaultWindSpeedComponent,
      defaultHumidityComponent
    ];
  }

  @override
  List<ComponentBaseViewModel> get componentViewModelList {
    return <ComponentBaseViewModel>[
      modeComponent(),
      windSpeedComponent,
      humidityComponent,
    ];
  }
}
