import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class CardSortedInfoResponseModel extends UhomeResponseModel {
  CardSortedInfoResponseModel.fromJson(super.data) : super.fromJson() {
    cardSortedInfoData = CardSortedInfo.fromJson(super.retData);
  }

  CardSortedInfo? cardSortedInfoData;

  @override
  String toString() {
    return 'cardSortedInfoData: $cardSortedInfoData';
  }
}

class CardSortedInfo {
  String timestamp = '';
  List<DeviceCardInfo> sortDeviceList = <DeviceCardInfo>[];
  ShortcutSortModel shortcutSortList = ShortcutSortModel(
    cameraSort: <String>[],
    wholeHouseSort: <String>[],
    deviceBigCardSort: <String>[],
  );
  List<String> sceneSortList = <String>[];

  CardSortedInfo.fromJson(Map<dynamic, dynamic> json) {
    timestamp = json.stringValueForKey('timestamp', '');

    final List<dynamic> deviceList =
        json.listValueForKey('sortDeviceList', <dynamic>[]);
    for (final dynamic element in deviceList) {
      if (element is Map<dynamic, dynamic>) {
        sortDeviceList.add(DeviceCardInfo.fromJson(element));
      }
    }

    shortcutSortList = ShortcutSortModel.fromJson(
        json.mapValueForKey('shortcutSortList', <dynamic, dynamic>{}));

    final List<dynamic> sceneList =
        json.listValueForKey('sceneSortList', <dynamic>[]);
    for (final dynamic element in sceneList) {
      if (element is String) {
        sceneSortList.add(element);
      }
    }
  }
  @override
  String toString() {
    return 'timestamp: $timestamp, sortDeviceList: $sortDeviceList, shortcutSortList: $shortcutSortList, sceneSortList: $sceneSortList';
  }
}

class DeviceCardInfo {
  String deviceId = '';
  String cardStatus = '';

  DeviceCardInfo.fromJson(Map<dynamic, dynamic> json) {
    deviceId = json.stringValueForKey('deviceId', '');
    cardStatus = json.stringValueForKey('cardStatus', '');
  }

  @override
  String toString() {
    return 'deviceId: $deviceId, cardStatus: $cardStatus';
  }
}

class ShortcutSortModel {
  ShortcutSortModel({
    required this.cameraSort,
    required this.wholeHouseSort,
    required this.deviceBigCardSort,
  });
  List<String> cameraSort = <String>[];
  List<String> wholeHouseSort = <String>[];
  List<String> deviceBigCardSort = <String>[];

  ShortcutSortModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> cameraList =
        json.listValueForKey('cameraSort', <dynamic>[]);
    for (final dynamic element in cameraList) {
      if (element is String) {
        cameraSort.add(element);
      }
    }

    final List<dynamic> wholeHouseList =
        json.listValueForKey('wholeHouseSort', <dynamic>[]);
    for (final dynamic element in wholeHouseList) {
      if (element is String) {
        wholeHouseSort.add(element);
      }
    }

    final List<dynamic> deviceBigCardList =
        json.listValueForKey('deviceBigCardSort', <dynamic>[]);
    for (final dynamic element in deviceBigCardList) {
      if (element is String) {
        deviceBigCardSort.add(element);
      }
    }
  }
  @override
  String toString() {
    return 'cameraSort: $cameraSort, wholeHouseSort: $wholeHouseSort, deviceBigCardSort: $deviceBigCardSort';
  }
}
