/*
 * 描述：设备基础信息
 * 作者：songFJ
 * 建立时间: 12/19/22
 */

import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:smart_home/device/constants/display_in_home_page_constant.dart';

class SmartHomeDeviceBasicInfo {
  final String parentId;
  final String appTypeCode;
  final String appTypeName;
  final String imageAddress;
  final String cardPageImg;
  final String cardPageIcon;
  final String communicationMode;
  final String deviceId;
  final String deviceName;
  final String typeId;
  final String bigClass;
  final String middleClass;
  final String model;
  final String prodNo;
  final String deviceRole;
  final String bindTime;
  final int noKeepAlive; // 0：保活设备，1：非保活设备
  final bool isRebind; // true: 可以重新绑定，false：不可以重新绑定
  final String netType;
  final String ownerId;
  final String ucUserId; // 用户中心userId
  final String deviceGroupType;
  final String configType;
  final String brand;
  String faultInformationStateCode;
  final int cardSort;

  /// 是否在首页展示
  int displayedInHomePage;

  /// 卡片状态 0:小卡 1:中卡2:大卡
  int cardStatus = 0;

  /// 一级应用分组
  final String categoryGrouping;

  /// 二级应用分组
  final String twoGroupingName;
  SmartHomeDeviceConfigState configState;
  UpDeviceOnlyConfigState onlyConfigState;
  final SmartHomeRoomInfo roomInfo;
  final SmartHomeOwnerInfo ownerInfo;

  String aggregationParentId;

  /// 设备分组id
  String deviceGroupId = '';

  //是否是共享设备
  bool isSharedDevice = false;

  //是否支持共享
  bool isSupportShare = false;

  /// 附件设备排序码
  int attachmentSortCode;

  /// 接入方式
  String accessType = '';

  SmartHomeDeviceBasicInfo({
    this.parentId = '',
    this.appTypeCode = '',
    this.appTypeName = '',
    this.imageAddress = '',
    this.cardPageImg = '',
    this.cardPageIcon = '',
    this.communicationMode = '',
    this.deviceId = '',
    this.deviceName = '',
    this.typeId = '',
    this.bigClass = '',
    this.middleClass = '',
    this.model = '',
    this.prodNo = '',
    this.deviceRole = '',
    this.bindTime = '',
    this.noKeepAlive = 0,
    this.isRebind = false,
    this.netType = '',
    this.ownerId = '',
    this.ucUserId = '',
    this.deviceGroupType = '',
    this.configType = '',
    this.faultInformationStateCode = '',
    this.categoryGrouping = '',
    this.twoGroupingName = '',
    this.brand = '',
    this.cardSort = 0,
    this.cardStatus = 0,
    this.configState = SmartHomeDeviceConfigState.loading,
    this.onlyConfigState = UpDeviceOnlyConfigState.UN_CONFIGURABLE,
    SmartHomeRoomInfo? roomInfo,
    SmartHomeOwnerInfo? ownerInfo,
    this.aggregationParentId = '',
    this.deviceGroupId = '',
    this.isSharedDevice = false,
    this.isSupportShare = false,
    this.attachmentSortCode = 0,
    this.accessType = '',
    this.displayedInHomePage = DisplayInHomePageConstant.notSupport,
  })  : roomInfo = roomInfo ?? SmartHomeRoomInfo(),
        ownerInfo = ownerInfo ?? SmartHomeOwnerInfo();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDeviceBasicInfo &&
          runtimeType == other.runtimeType &&
          parentId == other.parentId &&
          appTypeCode == other.appTypeCode &&
          appTypeName == other.appTypeName &&
          imageAddress == other.imageAddress &&
          cardPageImg == other.cardPageImg &&
          cardPageIcon == other.cardPageIcon &&
          communicationMode == other.communicationMode &&
          deviceId == other.deviceId &&
          deviceName == other.deviceName &&
          typeId == other.typeId &&
          bigClass == other.bigClass &&
          middleClass == other.middleClass &&
          model == other.model &&
          prodNo == other.prodNo &&
          deviceRole == other.deviceRole &&
          bindTime == other.bindTime &&
          noKeepAlive == other.noKeepAlive &&
          isRebind == other.isRebind &&
          netType == other.netType &&
          ownerId == other.ownerId &&
          ucUserId == other.ucUserId &&
          deviceGroupType == other.deviceGroupType &&
          configType == other.configType &&
          faultInformationStateCode == other.faultInformationStateCode &&
          categoryGrouping == other.categoryGrouping &&
          twoGroupingName == other.twoGroupingName &&
          configState == other.configState &&
          roomInfo == other.roomInfo &&
          ownerInfo == other.ownerInfo &&
          onlyConfigState == other.onlyConfigState &&
          brand == other.brand &&
          cardSort == other.cardSort &&
          cardStatus == other.cardStatus &&
          aggregationParentId == other.aggregationParentId &&
          deviceGroupId == other.deviceGroupId &&
          isSharedDevice == other.isSharedDevice &&
          isSupportShare == other.isSupportShare &&
          attachmentSortCode == other.attachmentSortCode &&
          accessType == other.accessType &&
          displayedInHomePage == other.displayedInHomePage;

  @override
  int get hashCode =>
      parentId.hashCode ^
      appTypeCode.hashCode ^
      appTypeName.hashCode ^
      imageAddress.hashCode ^
      cardPageIcon.hashCode ^
      cardPageImg.hashCode ^
      communicationMode.hashCode ^
      deviceId.hashCode ^
      deviceName.hashCode ^
      typeId.hashCode ^
      bigClass.hashCode ^
      middleClass.hashCode ^
      model.hashCode ^
      prodNo.hashCode ^
      deviceRole.hashCode ^
      bindTime.hashCode ^
      noKeepAlive.hashCode ^
      isRebind.hashCode ^
      netType.hashCode ^
      ownerId.hashCode ^
      ucUserId.hashCode ^
      deviceGroupType.hashCode ^
      configType.hashCode ^
      faultInformationStateCode.hashCode ^
      categoryGrouping.hashCode ^
      twoGroupingName.hashCode ^
      configState.hashCode ^
      roomInfo.hashCode ^
      ownerInfo.hashCode ^
      onlyConfigState.hashCode ^
      brand.hashCode ^
      cardSort.hashCode ^
      cardStatus.hashCode ^
      aggregationParentId.hashCode ^
      deviceGroupId.hashCode ^
      isSharedDevice.hashCode ^
      isSupportShare.hashCode ^
      attachmentSortCode.hashCode ^
      accessType.hashCode ^
      displayedInHomePage.hashCode;

  @override
  String toString() {
    return 'SmartHomeDeviceBasicInfo{parentId: $parentId, appTypeCode: $appTypeCode, appTypeName: $appTypeName, imageAddress: $imageAddress, cardPageIcon: $cardPageIcon, cardPageImg: $cardPageImg, communicationMode: $communicationMode, deviceId: $deviceId, deviceName: $deviceName, typeId: $typeId, bigClass: $bigClass, middleClass: $middleClass, model: $model, prodNo: $prodNo, deviceRole: $deviceRole, bindTime: $bindTime, noKeepAlive: $noKeepAlive, isRebind: $isRebind, netType: $netType, ownerId: $ownerId, ucUserId: $ucUserId, faultInformationStateCode: $faultInformationStateCode, categoryGrouping: $categoryGrouping, twoGroupingName: $twoGroupingName, configState: $configState, roomInfo: $roomInfo, ownerInfo: $ownerInfo, brand: $brand, cardSort: $cardSort, cardStatus: $cardStatus, isSharedDevice: $isSharedDevice, isSupportShare: $isSupportShare, attachmentSortCode:$attachmentSortCode, accessType: $accessType; displayedInHomePage: $displayedInHomePage}';
  }
}

enum SmartHomeDeviceConfigState {
  loading,
  unsupported,
  supported,
  native,
  notNetwork,
}

class SmartHomeOwnerInfo {
  final String ownerId;
  final String mobile;

  SmartHomeOwnerInfo({this.mobile = '', this.ownerId = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeOwnerInfo &&
          runtimeType == other.runtimeType &&
          ownerId == other.ownerId &&
          mobile == other.mobile;

  @override
  int get hashCode => ownerId.hashCode ^ mobile.hashCode;

  @override
  String toString() {
    return 'SmartHomeOwnerInfo{ownerId: $ownerId, mobile: $mobile}';
  }
}

class SmartHomeRoomInfo {
  final String roomId;
  final String roomClass;
  final String roomName;
  final String floorId;
  final String floorName;

  SmartHomeRoomInfo(
      {this.roomId = '',
      this.roomClass = '',
      this.roomName = '',
      this.floorId = '',
      this.floorName = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeRoomInfo &&
          runtimeType == other.runtimeType &&
          roomId == other.roomId &&
          roomClass == other.roomClass &&
          roomName == other.roomName &&
          floorId == other.floorId &&
          floorName == other.floorName;

  @override
  int get hashCode =>
      roomId.hashCode ^
      roomClass.hashCode ^
      roomName.hashCode ^
      floorId.hashCode ^
      floorName.hashCode;

  @override
  String toString() {
    return 'SmartHomeRoomInfo{roomId: $roomId, roomClass: $roomClass, roomName: $roomName, floorId: $floorId, floorName: $floorName}';
  }
}
