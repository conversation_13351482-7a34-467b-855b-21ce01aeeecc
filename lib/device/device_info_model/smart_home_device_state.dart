/*
 * 描述：设备状态枚举
 * 作者：songFJ
 * 建立时间: 12/19/22
 */
enum SmartHomeDeviceOnlineState { onlineReady, onlineNotReady, offline }

enum CommunicationModeType {
  wifi,
  ble,
  blemesh,
  bleBroadcast,
  nb,
  fourthG,
  zigbee,
  networkCable, //网线
  rs,
  unknown
}

CommunicationModeType communicationModeType(String commMode) {
  switch (commMode.toLowerCase()) {
    case 'wifi':
      return CommunicationModeType.wifi;
    case 'ble':
      return CommunicationModeType.ble;
    case 'blebroadcast':
      return CommunicationModeType.bleBroadcast;
    case 'nb-iot':
      return CommunicationModeType.nb;
    case '4g':
      return CommunicationModeType.fourthG;
    case 'zigbee':
    case 'zigbee3.0':
    case 'zigbee1.2':
      return CommunicationModeType.zigbee;
    case 'networkcable':
      return CommunicationModeType.networkCable;
    case 'unknown':
      return CommunicationModeType.unknown;
    default:
      return CommunicationModeType.unknown;
  }
}
