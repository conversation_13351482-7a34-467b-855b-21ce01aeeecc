/*
 * 描述：设备属性信息
 * 作者：songFJ
 * 建立时间: 12/19/22
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:plugin_device/model/common_models.dart';

class SmartHomeDeviceAttribute {
  final String name;
  final String value;
  final bool writable;
  final num source;
  final SmartHomeDeviceAttributeValueRange valueRange;

  SmartHomeDeviceAttribute(
      {this.name = '',
      this.value = '',
      this.writable = true,
      this.source = 0,
      SmartHomeDeviceAttributeValueRange? valueRange})
      : valueRange = valueRange ?? SmartHomeDeviceAttributeValueRange();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDeviceAttribute &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          value == other.value &&
          writable == other.writable &&
          source == other.source &&
          valueRange == other.valueRange;

  @override
  int get hashCode =>
      name.hashCode ^
      value.hashCode ^
      writable.hashCode ^
      source.hashCode ^
      valueRange.hashCode;

  @override
  String toString() {
    return 'SmartHomeDeviceAttribute{name: $name, value: $value, writable: $writable, source: $source, valueRange: $valueRange}';
  }
}

class SmartHomeDeviceAttributeValueRange {
  final SmartHomeDeviceAttributeValueRangeType type;
  final List<SmartHomeDataItem> dataList;
  final SmartHomeDataStep dataStep;
  final SmartHomeDataTime dataTime;
  final SmartHomeDataDate dataDate;

  SmartHomeDeviceAttributeValueRange(
      {this.type = SmartHomeDeviceAttributeValueRangeType.none,
      this.dataList = const <SmartHomeDataItem>[],
      SmartHomeDataStep? dataStep,
      SmartHomeDataTime? dataTime,
      SmartHomeDataDate? dataDate})
      : dataStep = dataStep ?? SmartHomeDataStep(),
        dataTime = dataTime ?? SmartHomeDataTime(),
        dataDate = dataDate ?? SmartHomeDataDate();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDeviceAttributeValueRange &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          listEquals(dataList, other.dataList) &&
          dataStep == other.dataStep &&
          dataTime == other.dataTime &&
          dataDate == other.dataDate;

  @override
  int get hashCode =>
      type.hashCode ^
      listHashCode(dataList) ^
      dataStep.hashCode ^
      dataTime.hashCode ^
      dataDate.hashCode;

  @override
  String toString() {
    return 'SmartHomeDeviceAttributeValueRange{type: $type, dataList: $dataList, dataStep: $dataStep, dataTime: $dataTime, dataDate: $dataDate}';
  }
}

enum SmartHomeDeviceAttributeValueRangeType { none, list, step, time, date }

SmartHomeDeviceAttributeValueRangeType scDeviceAttributeValueRangeType(
    Type type) {
  SmartHomeDeviceAttributeValueRangeType valueRangeType =
      SmartHomeDeviceAttributeValueRangeType.none;
  switch (type) {
    case Type.NONE:
      valueRangeType = SmartHomeDeviceAttributeValueRangeType.none;

    case Type.LIST:
      valueRangeType = SmartHomeDeviceAttributeValueRangeType.list;

    case Type.STEP:
      valueRangeType = SmartHomeDeviceAttributeValueRangeType.step;

    case Type.TIME:
      valueRangeType = SmartHomeDeviceAttributeValueRangeType.time;

    case Type.DATE:
      valueRangeType = SmartHomeDeviceAttributeValueRangeType.date;
  }
  return valueRangeType;
}

class SmartHomeDataItem {
  final String data;
  final String desc;
  final String code;

  SmartHomeDataItem({this.data = '', this.desc = '', this.code = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDataItem &&
          runtimeType == other.runtimeType &&
          data == other.data &&
          desc == other.desc &&
          code == other.code;

  @override
  int get hashCode => data.hashCode ^ desc.hashCode ^ code.hashCode;

  @override
  String toString() {
    return 'SmartHomeDataItem{data: $data, desc: $desc, code: $code}';
  }
}

class SmartHomeDataStep {
  final String dataType;
  final String step;
  final String minValue;
  final String maxValue;
  final SmartHomeTransform transform;

  SmartHomeDataStep(
      {this.dataType = '',
      this.step = '',
      this.minValue = '',
      this.maxValue = '',
      SmartHomeTransform? transform})
      : transform = transform ?? SmartHomeTransform();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDataStep &&
          runtimeType == other.runtimeType &&
          dataType == other.dataType &&
          step == other.step &&
          minValue == other.minValue &&
          maxValue == other.maxValue &&
          transform == other.transform;

  @override
  int get hashCode =>
      dataType.hashCode ^
      step.hashCode ^
      minValue.hashCode ^
      maxValue.hashCode ^
      transform.hashCode;

  @override
  String toString() {
    return 'SmartHomeDataStep{dataType: $dataType, step: $step, minValue: $minValue, maxValue: $maxValue, transform: $transform}';
  }
}

class SmartHomeTransform {
  final String k;
  final String c;

  SmartHomeTransform({this.k = '', this.c = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeTransform &&
          runtimeType == other.runtimeType &&
          k == other.k &&
          c == other.c;

  @override
  int get hashCode => k.hashCode ^ c.hashCode;

  @override
  String toString() {
    return 'SmartHomeTransform{k: $k, c: $c}';
  }
}

class SmartHomeDataTime {
  final String format;
  final num minHour;
  final num maxHour;
  final num minMinute;
  final num maxMinute;
  final num minSecond;
  final num maxSecond;

  SmartHomeDataTime(
      {this.format = '',
      this.minHour = 0,
      this.maxHour = 0,
      this.minMinute = 0,
      this.maxMinute = 0,
      this.minSecond = 0,
      this.maxSecond = 0});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDataTime &&
          runtimeType == other.runtimeType &&
          format == other.format &&
          minHour == other.minHour &&
          maxHour == other.maxHour &&
          minMinute == other.minMinute &&
          maxMinute == other.maxMinute &&
          minSecond == other.minSecond &&
          maxSecond == other.maxSecond;

  @override
  int get hashCode =>
      format.hashCode ^
      minHour.hashCode ^
      maxHour.hashCode ^
      minMinute.hashCode ^
      maxMinute.hashCode ^
      minSecond.hashCode ^
      maxSecond.hashCode;

  @override
  String toString() {
    return 'SmartHomeDataTime{format: $format, minHour: $minHour, maxHour: $maxHour, minMinute: $minMinute, maxMinute: $maxMinute, minSecond: $minSecond, maxSecond: $maxSecond}';
  }
}

class SmartHomeDataDate {
  final String format;
  final String beginDate;
  final String endDate;

  SmartHomeDataDate({this.format = '', this.beginDate = '', this.endDate = ''});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDataDate &&
          runtimeType == other.runtimeType &&
          format == other.format &&
          beginDate == other.beginDate &&
          endDate == other.endDate;

  @override
  int get hashCode => format.hashCode ^ beginDate.hashCode ^ endDate.hashCode;

  @override
  String toString() {
    return 'SmartHomeDataDate{format: $format, beginDate: $beginDate, endDate: $endDate}';
  }
}
