/*
 * 描述：设备卡片UI父类
 * 作者：songFJ
 * 创建时间：2025/1/22
 */

import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/factory/component_factory.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../common/common_network_image_widget.dart';
import '../component_view_model/component_view_model.dart';
import 'device_info.dart';

class DeviceCard extends StatelessWidget {
  const DeviceCard({super.key, required this.deviceCardViewModel});

  final DeviceCardViewModel deviceCardViewModel;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        deviceCardViewModel.cardClick(context);
      },
      child: Stack(
        children: <Widget>[
          cardWidget(context),
          _editWidget(),
        ],
      ),
    );
  }

  Widget cardWidget(BuildContext context) {
    return Container();
  }

  Widget _editWidget() {
    return StoreConnector<SmartHomeState, bool>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.isEditState;
      },
      builder: (BuildContext context, bool edit) {
        if (!edit) {
          return const SizedBox();
        }
        return GestureDetector(
          onTap: () => _handleCardTap(context, edit),
          child: Stack(
            children: <Widget>[
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.transparent,
              ),
              _editHeader(),
            ],
          ),
        );
      },
    );
  }

  void _handleCardTap(BuildContext context, bool edit) {
    if (edit) {
      if (deviceCardViewModel.isAggregation) {
        deviceCardViewModel.cardClick(context);
      } else {
        deviceCardViewModel.selectCardClick();
      }
    }
  }

  Widget _editHeader() {
    return Row(
      children: <Widget>[
        const Expanded(
          child: SizedBox(),
        ),
        GestureDetector(
          onTap: () {
            deviceCardViewModel.selectCardClick();
          },
          child: Container(
            width: 48,
            height: 48,
            color: Colors.transparent,
          ),
        ),
      ],
    );
  }

  /// 设备名称&楼层&房间&状态
  Widget deviceInfo() {
    return StoreConnector<SmartHomeState, DeviceInfoModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return DeviceInfoModel(
          offline: deviceCardViewModel.deviceOffline,
          showFloor: store.state.deviceState.cardShowFloor,
          status:
              store.state.isEditState ? deviceCardViewModel.editStatus : status,
          isShowEditStatus:
              store.state.isEditState && deviceCardViewModel.isAggregation,
        );
      },
      builder: (BuildContext context, DeviceInfoModel infoModel) {
        return DeviceInfoWidget(
          infoModel: infoModel,
          deviceCardViewModel: deviceCardViewModel,
        );
      },
    );
  }
  Widget deviceImage() {
    return StoreConnector<SmartHomeState, int>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.imageRefreshCount;
      },
      builder: (BuildContext context, int num) {
        return AspectRatio(
            aspectRatio: 1,
            child: deviceCardViewModel.isDeviceIconAsset
                ? Image.asset(
                    deviceCardViewModel.deviceIcon,
                    package: 'smart_home',
                  )
                : CommonNetworkRefreshImg(
                    imageUrl:
                        getSmallCardImgModel(deviceCardViewModel.deviceIcon)
                            .deviceIcon,
                    alignment: Alignment.center,
                    errorWidget: Image.asset(
                      'assets/icons/default_device_img.webp',
                      package: 'smart_home',
                      height: double.infinity,
                    ),
                  ));
      },
    );
  }

  Widget deviceTopRightComponent() {
    return StoreConnector<SmartHomeState, DeviceTopRightModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return DeviceTopRightModel(
            isEdit: store.state.isEditState,
            viewModel: store.state.isEditState
                ? deviceCardViewModel.editComponentViewModel
                : deviceCardViewModel.unionTopRightComponentViewModel);
      },
      builder: (BuildContext context, DeviceTopRightModel model) {
        final ComponentBaseViewModel? viewModel = model.viewModel;
        if (viewModel == null) {
          return Container();
        }

        return ComponentFactory.componentWidget(viewModel: viewModel);
      },
    );
  }

  /// 子类重写
  String get status {
    return '';
  }
}

class DeviceTopRightModel {
  final bool isEdit;

  final ComponentBaseViewModel? viewModel;

  DeviceTopRightModel({required this.isEdit, required this.viewModel});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceTopRightModel &&
          runtimeType == other.runtimeType &&
          isEdit == other.isEdit &&
          viewModel == other.viewModel;

  @override
  int get hashCode => isEdit.hashCode ^ viewModel.hashCode;
}

class DeviceInfoModel {
  final bool offline;
  final bool showFloor;
  final String status;
  final bool isShowEditStatus;

  DeviceInfoModel({
    required this.offline,
    required this.showFloor,
    required this.status,
    required this.isShowEditStatus,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceInfoModel &&
          runtimeType == other.runtimeType &&
          offline == other.offline &&
          showFloor == other.showFloor &&
          status == other.status &&
          isShowEditStatus == other.isShowEditStatus;

  @override
  int get hashCode =>
      offline.hashCode ^
      showFloor.hashCode ^
      status.hashCode ^
      isShowEditStatus.hashCode;
}

class DeviceLabelModel {
  final bool showLabel;
  final bool owner;

  DeviceLabelModel({this.showLabel = false, this.owner = false});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceLabelModel &&
          runtimeType == other.runtimeType &&
          showLabel == other.showLabel &&
          owner == other.owner;

  @override
  int get hashCode => showLabel.hashCode ^ owner.hashCode;
}

class SmallCardImgModel {
  final String deviceIcon;

  SmallCardImgModel({required this.deviceIcon});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmallCardImgModel &&
          runtimeType == other.runtimeType &&
          deviceIcon == other.deviceIcon;

  @override
  int get hashCode => deviceIcon.hashCode;

  @override
  String toString() {
    return 'SmallCardImgModel{deviceIcon: $deviceIcon}';
  }
}

SmallCardImgModel getSmallCardImgModel(String deviceIcon) {
  if (deviceIcon.isEmpty) {
    return SmallCardImgModel(deviceIcon: deviceIcon);
  }
  final RegExp regExp = RegExp(r'_(\d+)x(\d+)\.\w+$');
  final Iterable<RegExpMatch> matches = regExp.allMatches(deviceIcon);
  double width = 0;
  double height = 0;
  if (matches.isNotEmpty) {
    final RegExpMatch match = matches.last;

    final String? widthStr = match.group(1);
    final String? heightStr = match.group(2);

    try {
      if (widthStr != null && heightStr != null) {
        width = double.parse(widthStr);
        height = double.parse(heightStr);
        double actualWidth = 0;
        double actualHeight = 0;
        if (height != 0) {
          if (width >= height) {
            actualHeight = 40;
            actualWidth = width * actualHeight / height;
          } else {
            actualWidth = 40;
            actualHeight = height * actualWidth / width;
          }

          final String newUrl =
              '$deviceIcon?resize=p_0,w_${actualWidth.toInt() * 3},h_${actualHeight.toInt() * 3}';

          return SmallCardImgModel(deviceIcon: newUrl);
        }
      }
    } on FormatException {
      // 转换异常
    }
  }
  return SmallCardImgModel(deviceIcon: deviceIcon);
}

