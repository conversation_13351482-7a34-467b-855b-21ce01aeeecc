/*
 * 描述：设备小卡片
 * 作者：songFJ
 * 创建时间：2025/1/20
 */

import 'package:flutter/material.dart';

import 'device_card.dart';

class DeviceCardSmall extends DeviceCard {
  const DeviceCardSmall({super.key, required super.deviceCardViewModel});

  @override
  Widget cardWidget(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(
        Radius.circular(22),
      ),
      child: ColoredBox(
        color: Colors.white,
        child: Stack(
          children: <Widget>[
            Positioned(left: 0, top: 0, right: 0, child: _header()),
          ],
        ),
      ),
    );
  }

  Widget _header() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      height: 60,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, top: 8, right: 8),
        child: Row(
          children: <Widget>[
            Expanded(
                child: Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 8),
              child: _headerLeft(),
            )),
            const SizedBox(width: 16),
            deviceTopRightComponent(),
          ],
        ),
      ),
    );
  }

  Widget _headerLeft() {
    return Row(
      children: <Widget>[
        deviceImage(),
        const SizedBox(width: 8),
        Expanded(child: deviceInfo()),
      ],
    );
  }

  @override
  String get status {
    return deviceCardViewModel.unionSmallCardStatus;
  }
}
