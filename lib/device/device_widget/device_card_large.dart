/*
 * 描述：设备大卡片
 * 作者：songFJ
 * 创建时间：2025/1/20
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_widget/device_card.dart';
import 'package:smart_home/device/factory/component_factory.dart';

import '../../store/smart_home_state.dart';
import '../device_view_model/device_card_view_model.dart';

class DeviceCardLarge extends DeviceCard {
  const DeviceCardLarge({super.key, required super.deviceCardViewModel});

  @override
  Widget cardWidget(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(
        Radius.circular(22),
      ),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Stack(
          children: <Widget>[
            Positioned(left: 0, bottom: 0, right: 0, child: _content()),
            Positioned(left: 0, top: 0, right: 0, child: _header()),
          ],
        ),
      ),
    );
  }

  @override
  String get status {
    return deviceCardViewModel.unionLargeCardStatus;
  }

  Widget _header() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      height: 60,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, top: 8, right: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
                child: Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 8),
              child: _headerLeft(),
            )),
            const SizedBox(width: 16),
            deviceTopRightComponent(),
          ],
        ),
      ),
    );
  }

  Widget _headerLeft() {
    return Row(
      children: <Widget>[
        deviceImage(),
        const SizedBox(width: 8),
        Expanded(child: deviceInfo()),
      ],
    );
  }

  Widget _content() {
    return StoreConnector<SmartHomeState, _UnionFunctionMapModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return _UnionFunctionMapModel(
            functionMap: deviceCardViewModel.unionLargeCardFuncMap,
            selectedFunctionSetKey: deviceCardViewModel.selectedFunctionSetKey,
            editMode: store.state.isEditState);
      },
      builder: (BuildContext context, _UnionFunctionMapModel model) {
        return SizedBox(
          height: 70,
          child: Stack(
            children: <Widget>[
              SizedBox.expand(
                child: _funcList(model),
              ),
              if (model.editMode)
                GestureDetector(
                  onTap: () {
                    deviceCardViewModel.selectCardClick();
                  },
                  child: const SizedBox.expand(),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _funcList(_UnionFunctionMapModel model) {
    final Map<String, LargeDeviceCardFunctionSet> unionLargeCardFuncMap =
        model.functionMap;

    /// 无功能集
    if (unionLargeCardFuncMap.isEmpty) {
      return Container();
    }

    /// 取出某个对应的功能集
    final LargeDeviceCardFunctionSet? functionSet =
        unionLargeCardFuncMap[model.selectedFunctionSetKey];

    /// 功能列表为空
    if (functionSet == null || functionSet.componentViewModelList.isEmpty) {
      return Container();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: functionWidgetList(functionSet),
      ),
    );
  }

  List<Widget> functionWidgetList(LargeDeviceCardFunctionSet functionSet) {
    final List<Widget> list = <Widget>[];

    if (functionSet.componentAlign == ComponentAlign.noInterval) {
      /// 无间隔布局
      functionSet.componentViewModelList
          .forEach((ComponentBaseViewModel element) {
        list.add(ComponentFactory.componentWidget(viewModel: element));
      });
    } else {
      /// 有间隔布局
      list.add(const SizedBox(width: 16));
      functionSet.componentViewModelList
          .forEach((ComponentBaseViewModel element) {
        list.add(ComponentFactory.componentWidget(viewModel: element));
        list.add(const SizedBox(width: 16));
      });
    }

    return list;
  }
}

class _UnionFunctionMapModel {
  Map<String, LargeDeviceCardFunctionSet> functionMap;
  String selectedFunctionSetKey;
  bool editMode;

  _UnionFunctionMapModel(
      {required this.functionMap,
      required this.selectedFunctionSetKey,
      required this.editMode});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _UnionFunctionMapModel &&
          runtimeType == other.runtimeType &&
          selectedFunctionSetKey == other.selectedFunctionSetKey &&
          mapEquals(functionMap, other.functionMap) &&
          editMode == other.editMode;

  @override
  int get hashCode =>
      selectedFunctionSetKey.hashCode ^
      mapHashCode(functionMap) ^
      editMode.hashCode;
}
