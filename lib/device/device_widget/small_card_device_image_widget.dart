

SmallCardImgModel getSmallCardImgModel(String deviceIcon) {
  double imgRatio = 1;
  if (deviceIcon.isEmpty) {
    return SmallCardImgModel(deviceIcon: deviceIcon, imgAspectRatio: imgRatio);
  }
  final RegExp regExp = RegExp(r'_(\d+)x(\d+)\.\w+$');
  final Iterable<RegExpMatch> matches = regExp.allMatches(deviceIcon);
  double width = 0;
  double height = 0;
  if (matches.isNotEmpty) {
    final RegExpMatch match = matches.last;

    final String? widthStr = match.group(1);
    final String? heightStr = match.group(2);

    try {
      if (widthStr != null && heightStr != null) {
        width = double.parse(widthStr);
        height = double.parse(heightStr);
        double actualWidth = 0;
        double actualHeight = 0;
        if (height != 0) {
          imgRatio = width / height;
          if (imgRatio > 84 / 56) {
            actualHeight = 56;
            actualWidth = width * actualHeight / height;
          } else if (imgRatio < 56 / 84) {
            actualWidth = 56;
            actualHeight = height * actualWidth / width;
          } else {
            if (width >= height) {
              actualHeight = 56;
              actualWidth = width * actualHeight / height;
            } else {
              actualWidth = 56;
              actualHeight = height * actualWidth / width;
            }
          }

          final String newUrl =
              '$deviceIcon?resize=p_0,w_${actualWidth.toInt() * 3},h_${actualHeight.toInt() * 3}';

          return SmallCardImgModel(
              deviceIcon: newUrl, imgAspectRatio: imgRatio);
        }
      }
    } on FormatException {
      // 转换异常
    }
  }
  return SmallCardImgModel(deviceIcon: deviceIcon, imgAspectRatio: imgRatio);
}

class SmallCardImgModel {
  final String deviceIcon;
  final double imgAspectRatio;

  SmallCardImgModel({required this.deviceIcon, required this.imgAspectRatio});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmallCardImgModel &&
          runtimeType == other.runtimeType &&
          deviceIcon == other.deviceIcon &&
          imgAspectRatio == other.imgAspectRatio;

  @override
  int get hashCode => deviceIcon.hashCode ^ imgAspectRatio.hashCode;

  @override
  String toString() {
    return 'SmallCardImgModel{deviceIcon: $deviceIcon, imgAspectRatio: $imgAspectRatio}';
  }
}
