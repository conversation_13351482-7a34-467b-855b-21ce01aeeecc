/*
 * 描述：设备中卡片
 * 作者：songFJ
 * 创建时间：2025/1/20
 */

import 'package:flutter/material.dart';
import 'package:smart_home/device/device_widget/device_card.dart';

class DeviceCardMiddle extends DeviceCard {
  const DeviceCardMiddle({super.key, required super.deviceCardViewModel});

  @override
  Widget cardWidget(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(
        Radius.circular(22),
      ),
      child: ColoredBox(
        color: Colors.white,
        child: _content(),
      ),
    );
  }

  @override
  String get status {
    return deviceCardViewModel.unionMiddleCardStatus;
  }

  Widget _content() {
    return Stack(
      children: <Widget>[
        Positioned(left: 16, bottom: 16, right: 16, child: deviceInfo()),
        Positioned(left: 0, top: 0, right: 0, child: _header()),
      ],
    );
  }

  Widget _header() {
    return Container(
      height: 60,
      color: Colors.white,
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, top: 8, right: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: SizedBox(width: 40, height: 40, child: deviceImage()),
            ),
            deviceTopRightComponent(),
          ],
        ),
      ),
    );
  }
}
