import 'package:flutter/material.dart';
import 'package:smart_home/device/device_view_model/report_for_repair_card_view_model.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../common/smart_home_text_widget.dart';
import 'util/launch_analytics.dart';

class ReportForRepairCard extends StatelessWidget {
  final ReportForRepairCardViewModel viewModel;

  const ReportForRepairCard({
    super.key,
    required this.viewModel,
  });

  @override
  StatelessElement createElement() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!smartHomeStore.state.isLogin) {
        LaunchAnalytics.reportLaunchIfNeeded();
      }
    });
    return super.createElement();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !smartHomeStore.state.isEditState,
      child: GestureDetector(
        onTap: () {
          if (!smartHomeStore.state.isEditState) {
            viewModel.cardClick();
          }
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(22),
          ),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SmartHomeText(
                    text: viewModel.title,
                    fontSize: 14,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xff111111),
                  ),
                  const SizedBox(height: 6),
                  SmartHomeText(
                    text: viewModel.subTitle,
                    fontSize: 12,
                    height: 1,
                    color: const Color(0xff999999),
                  ),
                  const Expanded(child: SizedBox()),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: Image.asset(
                      viewModel.imgUrl,
                      height: 72,
                      package: 'smart_home',
                    ),
                  ),
                  // _image(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
