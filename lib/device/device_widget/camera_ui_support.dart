import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';
import 'package:smart_home/device/component_widget/device_edit_component.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_view_model/camera_header_view_model.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_widget/camera.dart';
import 'package:smart_home/store/smart_home_state.dart';

const double _defaultTextHeight = 1.0;
double _titleFontsize = 14;
double _subTitleFontsize = 12;
double _defaultImagesize = 40;
double _defaultPadding = 6;
const double defaultTop = 12;
const double _titleMargin = 16;
const double _titlePadding = 14;
const double _titleBottom = 8;
const double _streamButtonTop = 16;
double _bigCameraSwitchButtonSize = 32;
double _smallCameraSwitchButtonSize = 24;
double _bigCameraSwitchButtonMarginRight = 24.w - 4;
double _smallCameraSwitchButtonMarginRight = 4;
const double _arrowButtonPadding = 4;
const double _selectButtonSize = 28;

const double defaultSmallCardTop = 8;
Widget getCameraDeviceInfo(String title, String subTitle) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: <Widget>[
      SmartHomeText(
          text: breakWord(title),
          fontSize: _titleFontsize,
          fontWeight: FontWeight.w500,
          textAlign: TextAlign.start,
          height: _defaultTextHeight,
          color: Colors.white),
      Container(
        height: _defaultPadding,
      ),
      SmartHomeText(
          text: breakWord(subTitle),
          fontSize: _subTitleFontsize,
          textAlign: TextAlign.start,
          height: _defaultTextHeight,
          color: const Color(0xffCCCCCC)),
    ],
  );
}

Widget getCameraDeviceImage(String url) {
  return AspectRatio(
      aspectRatio: 1.0,
      child: CommonNetworkRefreshImg(
        imageUrl: url,
        alignment: Alignment.center,
        height: _defaultImagesize,
        errorWidget: Image.asset(
          CameraConstant.defaultCameraDeviceImage,
          package: SmartHomeConstant.package,
          height: double.infinity,
        ),
      ));
}

Widget wrapPaddingTop(Widget child, {double top = defaultTop}) {
  return Container(
    margin: EdgeInsets.only(top: top),
    child: child,
  );
}

Widget getCameraHeader(
    CameraUIType uiType, Widget deviceImage, Widget deviceInfo,
    {CameraStreamType? streamType, VoidCallback? onTap, bool isEdit = false}) {
  return Container(
    padding: const EdgeInsets.only(
        left: _titleMargin, bottom: _titleBottom, right: _titleMargin),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        wrapPaddingTop(
          deviceImage,
        ),
        SizedBox(
            width: uiType == CameraUIType.big_card ? defaultSmallCardTop : 0),
        Flexible(
            child: wrapPaddingTop(deviceInfo,
                top: uiType == CameraUIType.middle_card
                    ? defaultSmallCardTop
                    : _titlePadding)),
        SizedBox(
            width: uiType == CameraUIType.middle_card
                ? (isEdit ? 16 : 0)
                : defaultTop),
        const SizedBox(width: _titleBottom),
        wrapPaddingTop(
            Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                buildEditOverlay(
                    child: GestureDetector(
                      child: CameraStreamTypeButton(
                        streamType,
                        size: uiType == CameraUIType.big_card
                            ? _bigCameraSwitchButtonSize
                            : _smallCameraSwitchButtonSize,
                        marginRight: uiType == CameraUIType.big_card
                            ? _bigCameraSwitchButtonMarginRight
                            : _smallCameraSwitchButtonMarginRight,
                      ),
                      onTap: () {
                        onTap?.call();
                      },
                    ),
                    whenEditShow: false),
                SizedBox(
                  width: uiType == CameraUIType.big_card
                      ? 0
                      : _smallCameraSwitchButtonMarginRight,
                ),
                buildEditOverlay(
                    child: const CameraArrowRight(), whenEditShow: false)
              ],
            ),
            top: uiType == CameraUIType.middle_card
                ? defaultSmallCardTop
                : _streamButtonTop),
      ],
    ),
  );
}

Widget buildEditOverlay({required Widget child, bool whenEditShow = true}) {
  return StoreConnector<SmartHomeState, bool>(
    distinct: true,
    converter: (Store<SmartHomeState> store) => store.state.isEditState,
    builder: (BuildContext context, bool isEdit) {
      return Visibility(
        visible: whenEditShow ? isEdit : !isEdit,
        child: child,
      );
    },
  );
}

class CameraArrowRight extends StatelessWidget {
  const CameraArrowRight({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
          left: _arrowButtonPadding,
          top: _arrowButtonPadding,
          bottom: _arrowButtonPadding),
      child: Image.asset(
        'assets/icons/arrow_white.webp',
        width: 16,
        height: 16,
        fit: BoxFit.contain,
        package: SmartHomeConstant.package,
      ),
    );
  }
}

class CameraStreamTypeButton extends StatefulWidget {
  CameraStreamType? streamType;
  double marginRight;
  double size;

  CameraStreamTypeButton(this.streamType,
      {super.key, this.size = 32, this.marginRight = 24});

  @override
  State<StatefulWidget> createState() {
    return CameraStreamTypeButtonState();
  }
}

class CameraStreamTypeButtonState extends State<CameraStreamTypeButton> {
  @override
  Widget build(BuildContext context) {
    if (widget.streamType == null) {
      return const SizedBox.shrink();
    }
    return Container(
      child: Image.asset(
        CameraConstant.cameraSwitchButtonImagePath,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.contain,
        package: SmartHomeConstant.package,
      ),
      margin: EdgeInsets.only(right: widget.marginRight),
    );
  }
}

Widget buildHeaderButtons(
    BuildContext context,
    ComponentBaseViewModel? selectedMode,
    String deviceId,
    CameraUIType cameraType) {
  return Positioned(
    child: SizedBox(
      height: _selectButtonSize,
      width: _selectButtonSize,
      child: _buildHeaderButtonsWrapper(context, selectedMode, deviceId),
    ),
    top: 8,
    right: 8,
  );
}

Widget _buildHeaderButtonsWrapper(
    BuildContext c, ComponentBaseViewModel? selectedMode, String deviceId) {
  if (selectedMode != null) {
    return StoreConnector<SmartHomeState, CameraHeaderButtonViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          final bool isEdit = store.state.isEditState;
          final CardBaseViewModel? cardVm =
              store.state.deviceState.allCardViewModelMap[deviceId];
          final bool isSelect = cardVm?.isSelected ?? false;
          return CameraHeaderButtonViewModel(
              isEdit: isEdit, isSelect: isSelect);
        },
        builder: (BuildContext context, CameraHeaderButtonViewModel vm) {
          (selectedMode as DeviceEditComponentViewModel?)?.isSelected =
              vm.isSelect;
          return Visibility(
              visible: vm.isEdit,
              child: SizedBox(
                width: _selectButtonSize,
                height: _selectButtonSize,
                child: DeviceEditComponent(
                  viewModel: selectedMode as DeviceEditComponentViewModel,
                ),
              ));
        });
  }
  return Container(
    height: 0,
  );
}
