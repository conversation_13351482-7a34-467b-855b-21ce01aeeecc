/*
 * 描述：设备名称&楼层&房间&状态
 * 作者：fancunshuo
 * 建立时间: 2025/6/12
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/edit/util/edit_constant.dart';

import '../../common/constant.dart';
import '../../common/smart_home_text_widget.dart';
import '../../store/smart_home_store.dart';
import '../device_view_model/device_card_view_model.dart';
import 'device_card.dart';
import 'util/launch_analytics.dart';

const double _kCardStatusFontSize = 12.0;

class DeviceInfoWidget extends StatelessWidget {
  const DeviceInfoWidget(
      {super.key, required this.infoModel, required this.deviceCardViewModel});

  final DeviceCardViewModel deviceCardViewModel;

  final DeviceInfoModel infoModel;

  @override
  StatelessElement createElement() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (smartHomeStore.state.isLogin) {
        LaunchAnalytics.reportLaunchIfNeeded();
      }
    });
    return super.createElement();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: <Widget>[
            Flexible(
                child: SmartHomeText(
                    text: deviceCardViewModel.deviceName,
                    fontSize: 14,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    textAlign: TextAlign.start,
                    color: infoModel.offline
                        ? AppSemanticColors.item.secWeaken
                        : AppSemanticColors.item.primary)),
          ],
        ),
        const SizedBox(height: 6),
        if (infoModel.isShowEditStatus)
          _EditStatusWidget(statusText: infoModel.status)
        else
          SmartHomeText(
              text: roomInfo(infoModel, deviceCardViewModel) +
                  (infoModel.status.isNotEmpty
                      ? '${roomInfo(infoModel, deviceCardViewModel).isNotEmpty ? '丨' : ''}${infoModel.status}'
                      : ''),
              fontSize: 12,
              height: 1,
              textAlign: TextAlign.start,
              color: AppSemanticColors.item.secWeaken),
      ],
    );
  }

  String roomInfo(
      DeviceInfoModel infoModel, DeviceCardViewModel deviceCardViewModel) {
    return (infoModel.showFloor &&
                deviceCardViewModel.room != SmartHomeConstant.shareDeviceFlag
            ? deviceCardViewModel.floor
            : '') +
        deviceCardViewModel.room;
  }
}

class _EditStatusWidget extends StatelessWidget {
  static const double _kArrowSpacing = 4.0;
  final String statusText;

  const _EditStatusWidget({
    required this.statusText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        Flexible(
          child: SmartHomeText(
            text: statusText,
            fontSize: _kCardStatusFontSize,
            height: 1,
            textAlign: TextAlign.start,
            color: AppSemanticColors.item.information.primary,
          ),
        ),
        const SizedBox(width: _kArrowSpacing),
        Image.asset(
          EditConstant.editStatusArrowPath,
          package: SmartHomeConstant.package,
          height: _kCardStatusFontSize,
          width: _kCardStatusFontSize,
        ),
      ],
    );
  }
}
