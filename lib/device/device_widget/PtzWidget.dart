import 'dart:math';
import 'dart:ui' as ui show Image;
import 'package:flutter/material.dart';

import '../../common/constant.dart';

class PtzRockerView extends StatefulWidget {
  final void Function(Direction) onDirectionChanged;

  const PtzRockerView({
    super.key,
    required this.onDirectionChanged
  });

  @override
  PtzRockerViewState createState() => PtzRockerViewState();
}

class PtzRockerViewState extends State<PtzRockerView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Size size = const Size(100, 100);

  Offset _centerPosition = const Offset(50, 50);
  Offset _rockerPosition = const Offset(50, 50);
  double _panelRadius = 0;
  double _rockerRadius = 0;
  double _validDistance = 0;

  ui.Image? backgroundImage;
  ui.Image? rockerImage;
  ui.Image? shadowImage;

  bool inited = false;

  Direction _currentDirection = Direction.Center;

  @override
  void initState() {
    super.initState();
    _initSize();
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      if (context.size != null && !context.size!.isEmpty) {
        size = context.size!;
        _initSize();
      }
    });

    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 150));

    const AssetImage bgAst = AssetImage(
      'assets/images/ptz_rocker_bg.webp',
      package: SmartHomeConstant.package,
    );
    const AssetImage rockerAst = AssetImage(
      'assets/images/ptz_rocker.webp',
      package: SmartHomeConstant.package,
    );
    const AssetImage shadowAst = AssetImage(
      'assets/images/ptz_rocker_shadow.webp',
      package: SmartHomeConstant.package,
    );
    bgAst.resolve(ImageConfiguration.empty).addListener(
        ImageStreamListener((ImageInfo image, bool synchronousCall) {
      backgroundImage = image.image;
      _initSize();
    }));
    rockerAst.resolve(ImageConfiguration.empty).addListener(
        ImageStreamListener((ImageInfo image, bool synchronousCall) {
      rockerImage = image.image;
      _initSize();
    }));
    shadowAst.resolve(ImageConfiguration.empty).addListener(
        ImageStreamListener((ImageInfo image, bool synchronousCall) {
      shadowImage = image.image;
      _initSize();
    }));
  }


  @override
  void didChangeDependencies() {

    super.didChangeDependencies();
  }

  @override
  void didUpdateWidget(covariant PtzRockerView oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  bool isReady() {
    return backgroundImage != null &&
        rockerImage != null &&
        shadowImage != null;
  }

  void _initSize() {
    if (isReady() && !inited) {
      inited = true;
      final Size s = size;
      _centerPosition = Offset(s.width / 2, s.height / 2);
      _rockerPosition = _centerPosition;
      _panelRadius = min(s.width, s.height) / 2;
      final double scale = rockerImage!.width.toDouble() / backgroundImage!.width.toDouble();
      _rockerRadius = _panelRadius * scale; // adjust this scale as needed
      _validDistance = _panelRadius / 4;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {

    if (isReady()) {
      return GestureDetector(
        onPanUpdate: _handleMoveEvent,
        onPanStart: _handleTouchDown,
        onPanEnd: _handleTouchUp,
        child: CustomPaint(
          size: const Size(100,100),
          painter: RockerPainter(
              rockerPosition: _rockerPosition,
              centerPosition: _centerPosition,
              rockerRadius: _rockerRadius,
              panelRadius: _panelRadius,
              validDistance: _validDistance,
              currentDirection: _currentDirection,
              backgroundImage: backgroundImage!,
              rockerImage: rockerImage!,
              shadowImage: shadowImage!),
        ),
      );
    } else {
      return const SizedBox.expand();
    }
  }

  void _handleTouchDown(DragStartDetails details) {
    _controller.stop();
    _handleMoveEventReal(details.localPosition);
  }

  void _handleMoveEvent(DragUpdateDetails details) {
    _handleMoveEventReal(details.localPosition);
  }

  void _handleMoveEventReal(Offset localPosition) {
    final double distance = _getDistance(_centerPosition, localPosition);
    final double maxDis = _panelRadius - _rockerRadius + _rockerRadius / 3;

    if (distance < maxDis) {
      _rockerPosition = localPosition;
    } else {
      final double scale = maxDis / distance;
      final double newX = _centerPosition.dx +
          (localPosition.dx - _centerPosition.dx) * scale;
      final double newY = _centerPosition.dy +
          (localPosition.dy - _centerPosition.dy) * scale;
      _rockerPosition = Offset(newX, newY);
    }

    setState(() {
      final Direction newDirection = _getDirection();
      if (newDirection != _currentDirection) {
        _currentDirection = newDirection;
        widget.onDirectionChanged(newDirection);
      }
    });
  }

  void _handleTouchUp(DragEndDetails details) {
    _controller.forward(from: 0);
    setState(() {
      _rockerPosition = _centerPosition;
      _currentDirection = Direction.Center;
      widget.onDirectionChanged(Direction.Center);
    });
  }

  Direction _getDirection() {
    final double distance = _getDistance(_centerPosition, _rockerPosition);
    if (distance > _validDistance) {
      final double radian = _getRadian(_centerPosition, _rockerPosition);
      final int angle = _getAngle(radian);

      if (angle >= 0 && angle <= 44 || angle >= 316 && angle <= 360) {
        return Direction.Right;
      } else if (angle >= 45 && angle <= 134) {
        return Direction.Down;
      } else if (angle >= 135 && angle <= 224) {
        return Direction.Left;
      } else if (angle >= 225 && angle <= 314) {
        return Direction.Up;
      }
    }
    return Direction.Center;
  }

  double _getDistance(Offset A, Offset B) {
    return sqrt(pow(B.dx - A.dx, 2) + pow(B.dy - A.dy, 2));
  }

  double _getRadian(Offset A, Offset B) {
    final double dx = B.dx - A.dx;
    final double dy = B.dy - A.dy;
    final double lenA = sqrt(dx * dx + dy * dy);
    final double cosValue = dx / lenA;
    final double radian = acos(cosValue);
    return dy < 0 ? -radian : radian;
  }

  int _getAngle(double radian) {
    return ((radian / pi * 180).round() + 360) % 360;
  }
}

class RockerPainter extends CustomPainter {
  Offset rockerPosition;
  Offset centerPosition;
  double rockerRadius;
  double panelRadius;
  double validDistance;
  Direction currentDirection;
  ui.Image backgroundImage;
  ui.Image rockerImage;
  ui.Image shadowImage;

  RockerPainter(
      {required this.rockerPosition,
      required this.centerPosition,
      required this.rockerRadius,
      required this.panelRadius,
      required this.validDistance,
      required this.currentDirection,
      required this.backgroundImage,
      required this.rockerImage,
      required this.shadowImage});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()..isAntiAlias = true;
    //Draw the bg
    canvas.drawImageRect(
        backgroundImage,
        Rect.fromLTWH(0, 0, backgroundImage.width.toDouble(),
            backgroundImage.height.toDouble()),
        Rect.fromLTWH(0, 0, size.width, size.height),
        paint);

    // Draw the shadow
    if (currentDirection != Direction.Center) {
      final double angle = _getAngleInRadians(currentDirection);
      canvas.save();
      canvas.translate(size.width / 2, size.height / 2);
      canvas.rotate(angle);

      canvas.drawImageRect(
          shadowImage,
          Rect.fromLTWH(0, 0, shadowImage.width.toDouble(),
              shadowImage.height.toDouble()),
          Rect.fromCenter(center: Offset.zero, width: size.width, height: size.height),
          paint);
      canvas.restore();

    }

    // Draw the rocker
    final Rect dstR = Rect.fromLTRB(
        rockerPosition.dx - rockerRadius,
        rockerPosition.dy - rockerRadius,
        rockerPosition.dx + rockerRadius,
        rockerPosition.dy + rockerRadius);
    canvas.drawImageRect(
        rockerImage,
        Rect.fromLTWH(
            0, 0, rockerImage.width.toDouble(), rockerImage.height.toDouble()),
        dstR,
        paint);

  }

  double _getAngleInRadians(Direction direction) {
    switch (direction) {
      case Direction.Right:
        return -pi;
      case Direction.Up:
        return pi / 2;
      case Direction.Down:
        return -pi / 2;
      case Direction.Left:
        return 0;
      default:
        return 0;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

enum Direction { Center, Up, Right, Down, Left }
