/*
 * 描述：净水机滤芯
 * 作者：songFJ
 * 创建时间：2025/3/21
 */

import 'dart:convert';

import 'package:device_utils/log/log.dart';
import 'package:smart_home/device/purified_consumables_model.dart';
import 'package:storage/storage.dart';

import '../common/constant.dart';
import '../service/http_service.dart';

typedef UpdateFinishCallback = void Function(List<String> prodNoList);

class PurifiedConsumablePresenter {
  /// 耗材缓存key前缀
  static const String consumableStorageKeyPrefix =
      'smart_home_purified_consumable_key_v2_';

  Map<String, List<PurifiedConsumableModel>> purifiedConsumeMap =
      <String, List<PurifiedConsumableModel>>{};

  // 单例
  static PurifiedConsumablePresenter? _instance;

// 内部构造方法
  PurifiedConsumablePresenter._internal();

  factory PurifiedConsumablePresenter.getInstance() {
    _instance ??= PurifiedConsumablePresenter._internal();
    return _instance!;
  }

  String _generateCacheKey(String prodNo) {
    return consumableStorageKeyPrefix + prodNo;
  }

  Future<void> updateConsumables(
      String prodNo, UpdateFinishCallback callback) async {
    if (prodNo.isEmpty || purifiedConsumeMap.containsKey(prodNo)) {
      return;
    }

    final List<PurifiedConsumableModel> cachedList =
        await _getConsumablesCache(prodNo);
    if (cachedList.isNotEmpty) {
      purifiedConsumeMap[prodNo] = cachedList;
      callback.call(purifiedConsumeMap.keys.toList());
      return;
    }

    final PurifiedConsumablesResponseModel? responseModel =
        await HttpService.purifiedConsumables(
      prodNo,
    );
    final PurifiedConsumablesDataModel? data = responseModel?.data;
    if (data == null || data.consumables.isEmpty) {
      return;
    }

    purifiedConsumeMap[prodNo] = data.consumables;
    callback.call(purifiedConsumeMap.keys.toList());

    await _saveConsumablesCache(_generateCacheKey(prodNo), data.consumables);
    DevLogger.info(
        tag: '${SmartHomeConstant.package}_requestedProdNoList',
        msg: '${purifiedConsumeMap.keys}');
  }

  /// 写入耗材缓存
  Future<void> _saveConsumablesCache(
      String key, List<PurifiedConsumableModel> list) async {
    try {
      final String consumablesString = jsonEncode(list);
      final bool result =
          await Storage.setTemporaryStorage(key, consumablesString);
      DevLogger.info(
          tag: '${SmartHomeConstant.package}_saveConsumablesCache',
          msg: 'result:$result key：$key value:$consumablesString');
    } on FormatException catch (err) {
      DevLogger.error(
          tag: '${SmartHomeConstant.package}_saveConsumablesCache',
          msg: 'JSON格式解析错误: $err, key: $key');
    } catch (err) {
      DevLogger.error(
          tag: '${SmartHomeConstant.package}_saveConsumablesCache',
          msg: '未知错误 err $err key:$key value:$list');
    }
  }

  /// 查询耗材缓存
  Future<List<PurifiedConsumableModel>> _getConsumablesCache(
      String prodNo) async {
    final String key = _generateCacheKey(prodNo);
    final String value = await Storage.getTemporaryStorage(key);

    DevLogger.info(
        tag: '${SmartHomeConstant.package}_getConsumablesCache',
        msg: 'value $value');

    if (value.isEmpty) {
      return <PurifiedConsumableModel>[];
    }

    List<dynamic> consumablesList = <dynamic>[];
    try {
      final dynamic decodeValue = jsonDecode(value);
      if (decodeValue is List) {
        consumablesList = decodeValue;
      }
    } on FormatException catch (err) {
      DevLogger.error(
          tag: '${SmartHomeConstant.package}_getConsumablesCache',
          msg: 'JSON格式解析错误: $err, prodNo: $prodNo, value: $value');
      return <PurifiedConsumableModel>[];
    } catch (err) {
      DevLogger.error(
          tag: '${SmartHomeConstant.package}_getConsumablesCache',
          msg: '未知错误: $err, prodNo: $prodNo, value: $value');
      return <PurifiedConsumableModel>[];
    }

    final List<PurifiedConsumableModel> list = consumablesList
        .whereType<Map<dynamic, dynamic>>()
        .map((Map<dynamic, dynamic> e) => PurifiedConsumableModel.fromJson(e))
        .toList();

    return list;
  }
}
