import 'package:device_utils/compare/compare.dart';
import '../../aggregation/utils/agg_utils.dart';

class GuideState {
  List<GuideSettingModel> settingList = <GuideSettingModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GuideState &&
          runtimeType == other.runtimeType &&
          isListEqual(settingList, other.settingList);

  @override
  int get hashCode => listHashCode(settingList);
}

class GuideSettingModel {
  String aggName = '';
  bool aggStatus = false;
  AggTypeEnum aggType = AggTypeEnum.none; // 0-灯光 1-窗帘 2-环境 3-非网器 4-长期离线 5-摄像头

  GuideSettingModel(this.aggName, this.aggStatus, this.aggType);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GuideSettingModel &&
          runtimeType == other.runtimeType &&
          aggName == other.aggName &&
          aggStatus == other.aggStatus &&
          aggType == other.aggType;

  @override
  int get hashCode => aggName.hashCode ^ aggStatus.hashCode ^ aggType.hashCode;
}
