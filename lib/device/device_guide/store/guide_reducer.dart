import 'package:redux/redux.dart';

import '../../../store/smart_home_state.dart';
import '../../aggregation/utils/agg_utils.dart';
import '../guide_cluster_widget.dart';
import '../model/guide_aggregation_model.dart';
import 'guide_action.dart';
import 'guide_state.dart';

final Reducer<SmartHomeState> guideCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateGuideAggregationAction>(
          _updateGuideAggregationState)
      .call,
  TypedReducer<SmartHomeState, UpdateGuideAggregationSwitchAction>(
          _updateGuideAggregationSwitchState)
      .call,
]);

SmartHomeState _updateGuideAggregationState(
    SmartHomeState state, UpdateGuideAggregationAction action) {
  final GuideAggregationSwitchModel model = action.aggregationSwitchModel;
  GuideSettingModel createSettingModel(
      String name, String? status, AggTypeEnum type) {
    return GuideSettingModel(name, status == aggSwitchOn, type);
  }

  final List<GuideSettingModel> settingList = <GuideSettingModel>[
    if (model.lightAggStatus != null)
      createSettingModel('灯光', model.lightAggStatus, AggTypeEnum.lightAgg),
    if (model.curtainAggStatus != null)
      createSettingModel('窗帘', model.curtainAggStatus, AggTypeEnum.curtainAgg),
    if (model.envAggStatus != null)
      createSettingModel('环境', model.envAggStatus, AggTypeEnum.envAgg),
    if (model.cameraAggStatus != null)
      createSettingModel('摄像头', model.cameraAggStatus, AggTypeEnum.cameraAgg),
    if (model.offlineAggStatus != null)
      createSettingModel(
          '长期离线', model.offlineAggStatus, AggTypeEnum.offlineAgg),
    if (model.nonnetAggStatus != null)
      createSettingModel('不可连网', model.nonnetAggStatus, AggTypeEnum.nonnetAgg),
  ];
  state.guideState.settingList = settingList;
  return state;
}

SmartHomeState _updateGuideAggregationSwitchState(
    SmartHomeState state, UpdateGuideAggregationSwitchAction action) {
  final List<GuideSettingModel> settingList = <GuideSettingModel>[];
  for (final GuideSettingModel model in state.guideState.settingList) {
    final GuideSettingModel newModel =
        GuideSettingModel(model.aggName, model.aggStatus, model.aggType);
    if (newModel.aggType == action.aggType) {
      newModel.aggStatus = action.aggStatus;
    }
    settingList.add(newModel);
  }
  state.guideState.settingList = settingList;
  return state;
}
