import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/device_guide/common_slide_widget.dart';
import 'package:vdn/vdn.dart';

import '../../common/constant.dart';
import '../../widget_common/card_text_style.dart';

class GuideFamilyWidget extends StatefulWidget {
  const GuideFamilyWidget({super.key, this.onTap, required this.showJump});

  final void Function(int page)? onTap;
  final bool showJump;

  @override
  State<StatefulWidget> createState() {
    return _GuideFamilyState();
  }
}

class _GuideFamilyState extends State<GuideFamilyWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    final Tween<double> tween = Tween<double>(begin: 0, end: -15);

    _animation = tween.animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.addStatusListener((AnimationStatus status) {
      if (status == AnimationStatus.completed) {
        _controller.reverse();
      } else if (status == AnimationStatus.dismissed) {
        _controller.forward();
      }
    });
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Center(
        child: Column(
          children: <Widget>[
            const SizedBox(height: 94),
            Text(
              '智家升级，功能焕新',
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                  fontSize: 28,
                  height: 1,
                  fontWeight: FontWeight.w500,
                  fontFamilyFallback: fontFamilyFallback(),
                  color: AppSemanticColors.item.primary),
            ),
            const SizedBox(height: 64),
            Text(
              '协作有方，智享时光',
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                height: 1,
                fontSize: 18,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
                color: AppSemanticColors.item.primary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '家庭管理体系升级，邀请全家人一起使用智能设备吧！',
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                  fontSize: 14,
                  height: 1,
                  color: AppSemanticColors.item.secondary),
            ),
            const SizedBox(height: 24),
            _buildFamilyRoleWidget(
                '家庭创建者', '一家之主，家里有事儿都听Ta的', 'icon_family_create'),
            const SizedBox(height: 12),
            _buildFamilyRoleWidget(
                '管理员', '科技先锋，电子设备Ta都精通', 'icon_family_manager'),
            const SizedBox(height: 12),
            _buildFamilyRoleWidget(
                '普通成员', '功能精简，轻松上手，安全省心', 'icon_family_member'),
          ],
        ),
      ),
      if (widget.showJump)
        CommonSlideWidget(animation: _animation, onTap: widget.onTap, page: 1)
      else
        buildBottomJumpWidget(),
      if (widget.showJump) buildCommonJumpWidget()
    ]);
  }

  Widget buildBottomJumpWidget() {
    final double paddingBottom = MediaQuery.of(context).padding.bottom;
    final double bottom =
        paddingBottom == 0 ? ComponentMargin.popup : paddingBottom;
    return Positioned(
      bottom: bottom,
      left: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Vdn.close();
        },
        child: Container(
          alignment: Alignment.center,
          width: double.infinity,
          height: 44,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppSemanticColors.component.primary.fill,
            borderRadius: BorderRadius.circular(22),
          ),
          child: const Text(
            '立即体验',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
        ),
      ),
    );
  }

  Widget _buildFamilyRoleWidget(String role, String roleDesc, String iconName) {
    return Container(
      width: double.infinity,
      height: 90,
      margin: const EdgeInsets.symmetric(horizontal: 34),
      padding: const EdgeInsets.only(left: 16, right: 10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(22)),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _buildRoleTitle(role),
                const SizedBox(height: 8),
                _buildRoleDescription(roleDesc),
              ],
            ),
            Image.asset(
              'assets/icons/$iconName.webp',
              package: SmartHomeConstant.package,
              width: 60,
              height: 66,
            )
          ]),
    );
  }

  Widget _buildRoleTitle(String text) {
    return Text(
      text,
      textHeightBehavior: const TextHeightBehavior(
          leadingDistribution: TextLeadingDistribution.even),
      style: TextStyle(
        fontSize: 16,
        height: 1,
        color: AppSemanticColors.item.primary,
        fontWeight: FontWeight.w500,
        fontFamilyFallback: fontFamilyFallback(),
      ),
    );
  }

  Widget _buildRoleDescription(String text) {
    return Text(
      text,
      textHeightBehavior: const TextHeightBehavior(
          leadingDistribution: TextLeadingDistribution.even),
      style: TextStyle(
        fontSize: 14,
        height: 1,
        color: AppSemanticColors.item.secondary,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
