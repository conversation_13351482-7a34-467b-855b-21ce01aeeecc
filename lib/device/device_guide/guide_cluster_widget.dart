import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_card_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:lottie/lottie.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/loading_helper.dart';
import 'package:smart_home/device/device_guide/store/guide_action.dart';
import 'package:smart_home/device/device_guide/store/guide_state.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:vdn/vdn.dart';

import '../../common/component_constant.dart';
import '../../common/constant.dart';
import '../../store/smart_home_store.dart';
import '../../widget_common/card_text_style.dart';
import '../aggregation/utils/agg_utils.dart';

const String aggSwitchOn = '1';
const String aggSwitchOff = '0';

class GuideClusterWidget extends StatefulWidget {
  const GuideClusterWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return GuideClusterState();
  }
}

class GuideClusterState extends State<GuideClusterWidget> {
  @override
  Widget build(BuildContext context) {
    final double paddingBottom = MediaQuery.of(context).padding.bottom;
    final double bottom =
        paddingBottom == 0 ? ComponentMargin.popup : paddingBottom;
    return Stack(
      children: <Widget>[
        Column(
          children: <Widget>[
            const SizedBox(height: 94),
            Text(
              '同类聚合，批量操控',
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                  height: 1,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  fontFamilyFallback: fontFamilyFallback(),
                  color: AppSemanticColors.item.primary),
            ),
            const SizedBox(height: 12),
            Text(
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              '聚合同类设备，批量操控更方便',
              style: TextStyle(
                  fontSize: 14,
                  height: 1,
                  color: AppSemanticColors.item.secondary),
            ),
            const SizedBox(height: 30),
            SizedBox(
              height: 322,
              child: Lottie.asset('assets/theme/guide_cluster.json',
                  package: SmartHomeConstant.package),
            ),
            Container(
              alignment: Alignment.centerLeft,
              margin: const EdgeInsets.only(left: 38),
              child: Text(
                '聚合编辑',
                textHeightBehavior: const TextHeightBehavior(
                    leadingDistribution: TextLeadingDistribution.even),
                style: TextStyle(
                    fontSize: 16,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    fontFamilyFallback: fontFamilyFallback(),
                    color: AppSemanticColors.item.primary),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              alignment: Alignment.centerLeft,
              margin: const EdgeInsets.symmetric(horizontal: 38),
              child: Text(
                '管理员可点击「智家APP首页-底部编辑按钮」自定义设置',
                textHeightBehavior: const TextHeightBehavior(
                    leadingDistribution: TextLeadingDistribution.even),
                style: TextStyle(
                    fontSize: 11,
                    color: AppSemanticColors.item.secondary,
                    height: 1.2),
              ),
            ),
            const SizedBox(height: 16),
            StoreConnector<SmartHomeState, AggregationSettingViewModel>(
                distinct: true,
                builder:
                    (BuildContext context, AggregationSettingViewModel model) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 38),
                    child: GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemCount: model.settingList.length,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 2.79,
                        ),
                        itemBuilder: (BuildContext context, int index) {
                          final GuideSettingModel aggModel =
                          model.settingList[index];
                          return _buildSettingItem(aggModel);
                        }),
                  );
                },
                converter: (Store<SmartHomeState> store) {
                  return AggregationSettingViewModel(
                      store.state.guideState.settingList);
                }),
          ],
        ),
        Positioned(
            bottom: bottom,
            left: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                gioTrack('MB36000');
                saveUserClusterSwitch();
              },
              child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                height: 44,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: AppSemanticColors.component.primary.fill,
                  borderRadius: BorderRadius.circular(22),
                ),
                child: const Text(
                  '确认',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
              ),
            ))
      ],
    );
  }

  Widget _buildSettingItem(GuideSettingModel aggModel) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(22),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            aggModel.aggName,
            style: TextStyle(
                fontSize: 14, color: AppSemanticColors.item.primary),
          ),
          CustomSwitch(
            value: aggModel.aggStatus,
            activeColor: AppSemanticColors.item.information.primary,
            inactiveColor: Colors.grey.shade300,
            onChanged: (bool value) {
              _settingOnChange(aggModel.aggType, value);
            },
          )
        ],
      ),
    );
  }

  void _settingOnChange(AggTypeEnum aggType, bool value) {
    smartHomeStore.dispatch(UpdateGuideAggregationSwitchAction(aggType, value));
    final Map<String, dynamic> map = <String, dynamic>{
      'value': value ? '开' : '关'
    };
    if (aggType == AggTypeEnum.lightAgg) {
      gioTrack('MB38333', map);
    } else if (aggType == AggTypeEnum.curtainAgg) {
      gioTrack('MB39540', map);
    } else if (aggType == AggTypeEnum.cameraAgg) {
      gioTrack('MB39033', map);
    } else if (aggType == AggTypeEnum.offlineAgg) {
      gioTrack('MB39034', map);
    } else if (aggType == AggTypeEnum.nonnetAgg) {
      gioTrack('MB39035', map);
    }
  }

  Future<void> saveUserClusterSwitch() async {
    final List<GuideSettingModel> list =
        smartHomeStore.state.guideState.settingList;
    if (list.isEmpty) {
      Vdn.close();
      return;
    }
    String? lightAgg;
    String? curtainAgg;
    String? envAgg;
    String? offlineAgg;
    String? nonnetAgg;
    String? cameraAgg;

    for (final GuideSettingModel model in list) {
      final String aggStatus = model.aggStatus ? aggSwitchOn : aggSwitchOff;
      if (model.aggType == AggTypeEnum.lightAgg) {
        lightAgg = aggStatus;
      } else if (model.aggType == AggTypeEnum.curtainAgg) {
        curtainAgg = aggStatus;
      } else if (model.aggType == AggTypeEnum.envAgg) {
        envAgg = aggStatus;
      } else if (model.aggType == AggTypeEnum.offlineAgg) {
        offlineAgg = aggStatus;
      } else if (model.aggType == AggTypeEnum.nonnetAgg) {
        nonnetAgg = aggStatus;
      } else if (model.aggType == AggTypeEnum.cameraAgg) {
        cameraAgg = aggStatus;
      }
    }

    final FamilyAgg familyAgg = FamilyAgg(
        familyId: 'ALL',
        lightAgg: lightAgg,
        curtainAgg: curtainAgg,
        envAgg: envAgg,
        offlineAgg: offlineAgg,
        nonnetAgg: nonnetAgg,
        cameraAgg: cameraAgg);

    final AggregationSwitchModel model = AggregationSwitchModel(
      source: '0',
      familyAgg: <FamilyAgg>[familyAgg],
    );
    LoadingHelper.showLoading(context);
    try {
      await Family.operateAggregationSwitch(model);
    } catch (e) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'operateAggregationSwitch err: $e');
    } finally {
      LoadingHelper.hideLoading();
      Vdn.close();
    }
  }
}

class AggregationSettingViewModel {
  List<GuideSettingModel> settingList = <GuideSettingModel>[];

  AggregationSettingViewModel(this.settingList);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggregationSettingViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(settingList, other.settingList);

  @override
  int get hashCode => listHashCode(settingList);
}
