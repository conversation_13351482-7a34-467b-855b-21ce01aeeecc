import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:lottie/lottie.dart';
import 'package:smart_home/device/device_guide/common_slide_widget.dart';
import '../../common/constant.dart';
import '../../widget_common/card_text_style.dart';

class GuideCardWidget extends StatefulWidget {
  const GuideCardWidget({super.key, required this.onTap});

  final void Function(int page)? onTap;

  @override
  State<StatefulWidget> createState() {
    return GuideCardState();
  }
}

class GuideCardState extends State<GuideCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    final Tween<double> tween = Tween<double>(begin: 0, end: -15);

    _animation = tween.animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.addStatusListener((AnimationStatus status) {
      if (status == AnimationStatus.completed) {
        _controller.reverse();
      } else if (status == AnimationStatus.dismissed) {
        _controller.forward();
      }
    });
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Center(
        child: Column(
          children: <Widget>[
            const SizedBox(height: 94),
            Text(
              '大小切换，个性布局',
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                height: 1,
                fontSize: 18,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
                color: AppSemanticColors.item.primary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '长按设备卡片进行大小切换，目前已有20种设备支\n持大卡片',
              style: TextStyle(
                  fontSize: 14, color: AppSemanticColors.item.secondary),
            ),
            const SizedBox(height: 24),
            SizedBox(
              height: 322,
              child: Lottie.asset('assets/theme/guide_card.json',
                  package: SmartHomeConstant.package),
            )
          ],
        ),
      ),
      CommonSlideWidget(animation: _animation, onTap: widget.onTap, page: 2),
      buildCommonJumpWidget()
    ]);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
