import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class GuideAggregationResponseModel extends UhomeResponseModel {
  GuideAggregationResponseModel.fromJson(super.data) : super.fromJson() {
    data = GuideAggregationSwitchModel.fromJson(super.retData);
  }

  GuideAggregationSwitchModel data =
      GuideAggregationSwitchModel.fromJson(<dynamic, dynamic>{});
}

class GuideAggregationSwitchModel {
  String? lightAggStatus;
  String? curtainAggStatus;
  String? envAggStatus;
  String? offlineAggStatus;
  String? nonnetAggStatus;
  String? cameraAggStatus;

  GuideAggregationSwitchModel.fromJson(Map<dynamic, dynamic> json) {
    lightAggStatus = json.nullableStringValueForKey('lightAggStatus');
    curtainAggStatus = json.nullableStringValueForKey('curtainAggStatus');
    envAggStatus = json.nullableStringValueForKey('envAggStatus');
    offlineAggStatus = json.nullableStringValueForKey('offlineAggStatus');
    nonnetAggStatus = json.nullableStringValueForKey('nonnetAggStatus');
    cameraAggStatus = json.nullableStringValueForKey('cameraAggStatus');
  }

  @override
  String toString() {
    return '{lightAggStatus: $lightAggStatus, curtainAggStatus: $curtainAggStatus, envAggStatus: $envAggStatus, offlineAggStatus: $offlineAggStatus, nonnetAggStatus: $nonnetAggStatus, cameraAggStatus: $cameraAggStatus}';
  }
}
