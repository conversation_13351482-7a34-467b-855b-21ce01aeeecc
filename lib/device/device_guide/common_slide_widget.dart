import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:vdn/vdn.dart';

import '../../common/component_constant.dart';
import '../../common/constant.dart';

class CommonSlideWidget extends StatelessWidget {
  const CommonSlideWidget({
    super.key,
    required this.animation,
    required this.onTap,
    required this.page,
  });

  final Animation<double> animation;

  final void Function(int page)? onTap;

  final int page;

  @override
  Widget build(BuildContext context) {
    final double paddingBottom = MediaQuery.of(context).padding.bottom;
    final double bottom =
        paddingBottom == 0 ? ComponentMargin.popup : paddingBottom;
    return Positioned(
        bottom: bottom,
        left: 0,
        right: 0,
        child: GestureDetector(
          onTap: () {
            if (onTap != null) {
              onTap!(page);
            }
          },
          child: Column(
            children: <Widget>[
              AnimatedBuilder(
                  animation: animation,
                  builder: (BuildContext context, Widget? child) {
                    return Transform.translate(
                        offset: Offset(0, animation.value),
                        child: Image.asset(
                          'assets/icons/icon_guide_slide.png',
                          package: SmartHomeConstant.package,
                          width: 36,
                          height: 36,
                        ));
                  }),
              const SizedBox(height: 10),
              Text(
                '上滑继续',
                style: TextStyle(
                    fontSize: 14,
                    color: AppSemanticColors.item.information.primary),
              )
            ],
          ),
        ));
  }
}

Widget buildCommonJumpWidget() {
  return Positioned(
      right: 20,
      top: 62,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Vdn.close();
        },
        child: Container(
            width: 48,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.5),
              borderRadius: const BorderRadius.all(Radius.circular(12)),
            ),
            child: const Center(
                child: Text('跳过',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                    )))),
      ));
}
