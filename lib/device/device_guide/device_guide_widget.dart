import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smart_home/device/device_guide/guide_card_widget.dart';
import 'package:smart_home/device/device_guide/guide_family_widget.dart';
import 'package:smart_home/device/device_guide/store/guide_action.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../service/http_service.dart';
import '../../store/smart_home_store.dart';
import 'guide_cluster_widget.dart';
import 'model/guide_aggregation_model.dart';

const String ONLY_FAMILY_GUIDE = 'onlyFamilyGuide';

enum GuideType { familyOnly, full }

class DeviceGuideWidget extends StatefulWidget {
  const DeviceGuideWidget({super.key, this.params});

  final Map<String, dynamic>? params;

  @override
  State<StatefulWidget> createState() {
    return _DeviceGuideState();
  }
}

class _DeviceGuideState extends State<DeviceGuideWidget> {
  final PageController _pageController = PageController();
  GuideType guideType = GuideType.familyOnly;

  List<Widget> _buildGuideTabs() {
    return <Widget>[
      GuideFamilyWidget(
        onTap: onTabSlide,
        showJump: true,
      ),
      GuideCardWidget(
        onTap: onTabSlide,
      ),
      const GuideClusterWidget()
    ];
  }

  void onTabSlide(int page) {
    _pageController.animateToPage(page,
        duration: const Duration(milliseconds: 350), curve: Curves.ease);
  }

  @override
  void initState() {
    super.initState();
    final dynamic familyParam = widget.params?[ONLY_FAMILY_GUIDE];
    if (familyParam != null) {
      final bool isOnlyFamily = familyParam is bool
          ? familyParam
          : familyParam is String && familyParam.toLowerCase() == 'true';

      guideType = isOnlyFamily ? GuideType.familyOnly : GuideType.full;
      setState(() {});
    }
    if (guideType == GuideType.full) {
      getGuideAggregation();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: () => StoreProvider<SmartHomeState>(
          store: smartHomeStore,
          child: Scaffold(
            backgroundColor: const Color(0xFFF6F6F6),
            body: Stack(
              children: <Widget>[
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: SizedBox(
                        height: 600,
                        child: Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: <Color>[
                                Color(0xFFB2D9FF),
                                Color(0xFFF5F5F5)
                              ],
                            ),
                          ),
                        ))),
                if (guideType == GuideType.familyOnly)
                  const GuideFamilyWidget(
                    showJump: false,
                  )
                else
                  PageView(
                    scrollDirection: Axis.vertical,
                    controller: _pageController,
                    children: _buildGuideTabs(),
                  )
              ],
            ),
          )),
    );
  }

  Future<void> getGuideAggregation() async {
    final GuideAggregationSwitchModel? model =
        await HttpService.queryGuideAggregationService('ALL');
    if (model != null) {
      smartHomeStore.dispatch(UpdateGuideAggregationAction(model));
    }
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }
}
