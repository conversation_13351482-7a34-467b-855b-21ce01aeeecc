import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';

class _CoordinateOffset {
  final double main, cross;

  _CoordinateOffset(this.main, this.cross);
}

class SpanableSliverGridLayout extends SliverGridLayout {
  /// Creates a layout that uses equally sized and spaced tiles.
  ///
  /// All of the arguments must not be null and must not be negative. The
  /// `crossAxisCount` argument must be greater than zero.
  SpanableSliverGridLayout(this.crossAxisCount, this.childCrossAxisExtent,
      this.crossAxisStride, this.mainAxisSpacing, this.origins)
      : assert(crossAxisCount > 0),
        assert(mainAxisSpacing >= 0),
        assert(childCrossAxisExtent >= 0),
        assert(crossAxisStride >= 0),
        offsets = origins.toPosition(
            crossAxisCount, mainAxisSpacing, crossAxisStride);

  /// The number of children in the cross axis.
  final int crossAxisCount;

  /// The number of pixels from the leading edge of one tile to the trailing
  /// edge of the same tile in the main axis.
  final double mainAxisSpacing;

  /// The number of pixels from the leading edge of one tile to the leading edge
  /// of the next tile in the cross axis.
  final double crossAxisStride;

  /// The number of pixels from the leading edge of one tile to the trailing
  /// edge of the same tile in the cross axis.
  final double childCrossAxisExtent;

  final List<GridTileOrigin> origins;

  final List<Offset> offsets;

  _CoordinateOffset _findOffset(int index) {
    if (index < offsets.length) {
      final Offset offset = offsets[index];
      return _CoordinateOffset(offset.dy, offset.dx);
    } else {
      return _CoordinateOffset(0, 0);
    }
  }

  @override
  int getMinChildIndexForScrollOffset(double scrollOffset) {
    for (int i = 0; i < offsets.length; i++) {
      if (origins[i].mainAxisExtent + offsets[i].dy >= scrollOffset) {
        return i;
      }
    }

    return 0;
  }

  @override
  int getMaxChildIndexForScrollOffset(double scrollOffset) {
    for (int i = origins.length - 1; i >= 0; i--) {
      if (offsets[i].dy <= scrollOffset) {
        return i;
      }
    }
    return 0;
  }

  @override
  SliverGridGeometry getGeometryForChildIndex(int index) {
    // TODO(fcs): 暂时解决异常，后续排查
    if (index >= origins.length) {
      return const SliverGridGeometry(
        scrollOffset: 0,
        crossAxisOffset: 0,
        mainAxisExtent: 0,
        crossAxisExtent: 0,
      );
    }
    final int span = origins[index].crossAxisSpan;
    final double mainAxisExtent = origins[index].mainAxisExtent;
    final _CoordinateOffset offset = _findOffset(index);

    return SliverGridGeometry(
      scrollOffset: offset.main,
      crossAxisOffset: offset.cross,
      mainAxisExtent: mainAxisExtent,
      crossAxisExtent: childCrossAxisExtent + (span - 1) * crossAxisStride,
    );
  }

  @override
  double computeMaxScrollOffset(int childCount) {
    if (childCount <= 0) {
      return 0.0;
    }

    double max = 0;
    for (int i = 0; i < origins.length; i++) {
      max = math.max(max, offsets[i].dy + origins[i].mainAxisExtent);
    }
    return max;
  }
}

abstract class SpanableSliverGridDelegate extends SliverGridDelegate {
  /// Creates a delegate that makes grid layouts with a fixed number of tiles in
  /// the cross axis.
  ///
  /// All of the arguments must not be null. The `mainAxisSpacing` and
  /// `crossAxisSpacing` arguments must not be negative. The `crossAxisCount`
  /// and `childAspectRatio` arguments must be greater than zero.
  SpanableSliverGridDelegate(
    this.crossAxisCount, {
    this.mainAxisSpacing = 0.0,
    this.crossAxisSpacing = 0.0,
    required this.origins,
  })  : assert(crossAxisCount > 0),
        assert(mainAxisSpacing >= 0),
        assert(crossAxisSpacing >= 0);

  /// The number of children in the cross axis.
  final int crossAxisCount;

  /// The number of logical pixels between each child along the main axis.
  final double mainAxisSpacing;

  /// The number of logical pixels between each child along the cross axis.
  final double crossAxisSpacing;

  final List<GridTileOrigin> origins;

  bool _debugAssertIsValid() {
    assert(crossAxisCount > 0);
    assert(mainAxisSpacing >= 0.0);
    assert(crossAxisSpacing >= 0.0);
    return true;
  }

  double? crossAxisStride;

  double? childCrossAxisExtent;

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    assert(_debugAssertIsValid());
    final double usableCrossAxisExtent =
        constraints.crossAxisExtent - crossAxisSpacing * (crossAxisCount - 1);
    childCrossAxisExtent = usableCrossAxisExtent / crossAxisCount;
    crossAxisStride = childCrossAxisExtent! + crossAxisSpacing;
    return SpanableSliverGridLayout(
      crossAxisCount,
      childCrossAxisExtent!,
      childCrossAxisExtent! + crossAxisSpacing,
      mainAxisSpacing,
      origins,
    );
  }

  @override
  bool shouldRelayout(SpanableSliverGridDelegate oldDelegate) {
    return oldDelegate.crossAxisCount != crossAxisCount ||
        oldDelegate.mainAxisSpacing != mainAxisSpacing ||
        oldDelegate.crossAxisSpacing != crossAxisSpacing;
  }
}

extension on List<GridTileOrigin> {
  List<Offset> toPosition(
    int crossAxisCount,
    double mainAxisSpacing,
    double stride,
  ) {
    int computeCrossAxisCellCount(
      GridTileOrigin childParentData,
      int crossAxisCount,
    ) {
      return math.min(
        childParentData.crossAxisSpan,
        crossAxisCount,
      );
    }

    final List<Offset> res = List<Offset>.filled(length, Offset.zero);

    final List<double> offsets = List<double>.filled(crossAxisCount, 0.0);

    for (int i = 0; i < length; i++) {
      final int crossAxisCellCount = computeCrossAxisCellCount(
        this[i],
        crossAxisCount,
      );

      final TileOrigin origin = findBestCandidate(offsets, crossAxisCellCount,
          i, this[i].mainAxisExtent, mainAxisSpacing, res, this);
      final double mainAxisOffset = origin.mainAxisOffset;
      final double crossAxisOffset = origin.crossAxisIndex * stride;
      final Offset offset = Offset(crossAxisOffset, mainAxisOffset);

      res[i] = offset;

      // Don't forget to update the offsets.
      final double nextTileOffset =
          mainAxisOffset + this[i].mainAxisExtent + mainAxisSpacing;
      for (int i = 0; i < crossAxisCellCount; i++) {
        offsets[origin.crossAxisIndex + i] = nextTileOffset;
      }
    }

    return res;
  }
}

TileOrigin findBestCandidate(
    List<double> offsets,
    int crossAxisCount,
    int index,
    double mainAxisExtent,
    double mainAxisSpacing,
    List<Offset> tiles,
    List<GridTileOrigin> origins) {
  if (crossAxisCount == 4) {
    return TileOrigin(0, math.max(offsets[0], offsets[2]));
  }
  if (offsets[0] == offsets[2]) {
    return TileOrigin(0, offsets[0]);
  } else if (offsets[0] < offsets[2]) {
    return TileOrigin(0, offsets[2]);
  } else if (offsets[0] > offsets[2]) {
    if ((offsets[0] - offsets[2]).round() > 152) {
      return TileOrigin(2, tiles[index - 1].dy);
    } else if ((offsets[0] - offsets[2]).round() > 140) {
      return TileOrigin(2, offsets[2]);
    } else if (mainAxisExtent == 140) {
      if (tiles[index - 1].dx == 0) {
        return TileOrigin(2, offsets[2]);
      }
      return TileOrigin(0, offsets[0]);
    } else if (mainAxisExtent == 64) {
      if (tiles[index - 1].dx == 0) {
        return TileOrigin(0, offsets[0]);
      }
      return TileOrigin(2, offsets[2]);
    }
    return TileOrigin(0, math.max(offsets[0], offsets[2]));
  }
  return TileOrigin(0, math.max(offsets[0], offsets[2]));
}

bool _lessOrNearEqual(double a, double b) {
  return a < b || (a - b).abs() < precisionErrorTolerance;
}

class TileOrigin {
  const TileOrigin(this.crossAxisIndex, this.mainAxisOffset);

  final int crossAxisIndex;
  final double mainAxisOffset;
}

class GridTileOrigin {
  int crossAxisSpan;
  double mainAxisExtent;
  Key key;

  GridTileOrigin(this.crossAxisSpan, this.mainAxisExtent, this.key);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GridTileOrigin &&
          runtimeType == other.runtimeType &&
          crossAxisSpan == other.crossAxisSpan &&
          mainAxisExtent == other.mainAxisExtent;

  @override
  int get hashCode => crossAxisSpan.hashCode ^ mainAxisExtent.hashCode;

  @override
  String toString() {
    return 'GridTile{crossAxisSpan: $crossAxisSpan, mainAxisExtent: $mainAxisExtent}';
  }
}
