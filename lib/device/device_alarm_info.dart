/*
 * 描述：设备告警信息
 * 作者：songFJ
 * 创建时间：2024/8/5
 */

import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class AlarmInfoModel extends UhomeResponseModel {
  AlarmInfoModel.fromJson(super.data) : super.fromJson() {
    data = AlarmInfoData.fromJson(super.retData);
  }

  AlarmInfoData data = AlarmInfoData.fromJson(<dynamic, dynamic>{});

  @override
  String toString() {
    return 'AlarmInfoModel{data: $data}';
  }
}

class AlarmInfoData {
  List<DevAlarmInfo> devAlarmInfo = <DevAlarmInfo>[];

  AlarmInfoData.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> list =
        json.listValueForKey('devAlarmInfo', <dynamic>[]);
    list.forEach((dynamic element) {
      if (element is Map) {
        devAlarmInfo.add(DevAlarmInfo.fromJson(element));
      }
    });
  }

  @override
  String toString() {
    return 'AlarmInfoData{devAlarmInfo: $devAlarmInfo}';
  }
}

class DevAlarmInfo {
  String code = '';
  String info = '';

  DevAlarmInfo.fromJson(Map<dynamic, dynamic> json) {
    code = json.stringValueForKey('code', '');
    info = json.stringValueForKey('info', '');
  }

  @override
  String toString() {
    return 'DevAlarmInfo{code: $code, info: $info}';
  }
}
