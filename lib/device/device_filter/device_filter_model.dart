import 'dart:collection';

class DeviceFilterModel {
  String floorName = '';
  String roomName = '';
  String roomId = '';
  LinkedHashSet<String> categorySet = LinkedHashSet<String>();

  DeviceFilterModel(this.floorName, this.roomName, this.roomId, this.categorySet);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceFilterModel &&
          runtimeType == other.runtimeType &&
          floorName == other.floorName &&
          roomName == other.roomName &&
          roomId == other.roomId &&
          categorySet == other.categorySet;

  @override
  int get hashCode =>
      floorName.hashCode ^ roomName.hashCode ^ roomId.hashCode ^ categorySet.hashCode;

  @override
  String toString() {
    return '{floorName: $floorName, roomName: $roomName, roomId: $roomId, categorySet: $categorySet}';
  }
}

class TabFilterModel {
  String floorName = '';
  String roomName = '';

  TabFilterModel(this.floorName, this.roomName);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TabFilterModel &&
          runtimeType == other.runtimeType &&
          floorName == other.floorName &&
          roomName == other.roomName;

  @override
  int get hashCode => floorName.hashCode ^ roomName.hashCode;

  @override
  String toString() {
    return 'TabFilterModel{floorName: $floorName, roomName: $roomName}';
  }
}
