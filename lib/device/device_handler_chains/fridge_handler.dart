import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_handler_chains/device_handler_base.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_presenter.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_foodnum_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_store.dart';

class FridgeFoodNumHandler extends DeviceHandlerBase {
  static const String _tag = 'FridgeFoodNumHandler';

  static const String _fridgeBigClass = '1';

  final List<SmartHomeDevice> _fridgeDeviceList = <SmartHomeDevice>[];

  final DeviceListFetchTriggerType _triggerType;

  FridgeFoodNumHandler(this._triggerType);

  bool get _isQueryFoodNumEnable =>
      _triggerType == DeviceListFetchTriggerType.deviceListChanged ||
      _triggerType == DeviceListFetchTriggerType.pullToRefresh ||
      _triggerType == DeviceListFetchTriggerType.currentFamilyChanged;

  @override
  bool shouldHandle(SmartHomeDevice device) {
    return device.basicInfo.bigClass == _fridgeBigClass &&
        _isQueryFoodNumEnable;
  }

  @override
  Future<void> handle(SmartHomeDevice device) async {
    _fridgeDeviceList.add(device);
  }

  @override
  void dispatcher() {
    if (!_isQueryFoodNumEnable) {
      return;
    }
    final List<SmartHomeDevice> devicesToQuery =
        List<SmartHomeDevice>.from(_fridgeDeviceList);
    _fridgeDeviceList.clear();
    _queryFridgeFoodNums(devicesToQuery);
  }

  Future<void> _queryFridgeFoodNums(
      List<SmartHomeDevice> devicesToQuery) async {
    final FamilyModel familyModel = await Family.getCurrentFamily();
    final FridgeFoodNumRequestModel requestModel = FridgeFoodNumRequestModel(
      familyId: familyModel.familyId,
      deviceList: devicesToQuery
          .map((SmartHomeDevice item) => DeviceItem.from(item.basicInfo))
          .toList(),
    );

    HttpService.queryFridgesFoodNums(requestModel)
        .then((FridgeFoodNumResponseModel? responseModel) {
      if (responseModel == null) {
        DevLogger.error(
            tag: SmartHomeConstant.package,
            msg:
                '$_tag queryFridgesFoodNums responseModel is null, cannot refresh fridge card, return.');
        return;
      }
      smartHomeStore.dispatch(
          FridgeFoodNumsChangedAction(responseModel.foodNumModel.deviceList));
    }).catchError((Object err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: '$_tag queryFridgesFoodNums error: $err');
    });
  }
}
