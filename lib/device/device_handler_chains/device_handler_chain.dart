import 'package:device_utils/log/log.dart';
import 'package:smart_home/device/device_handler_chains/device_handler_base.dart';
import 'package:smart_home/device/device_handler_chains/fridge_handler.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_presenter.dart';

class DeviceHandlerChain {
  final List<DeviceHandlerBase> _handlerList = <DeviceHandlerBase>[];

  void addHandler(DeviceHandlerBase handler) {
    _handlerList.add(handler);
  }

  Future<void> executeHandlers(SmartHomeDevice device) async {
    for (final DeviceHandlerBase handler in _handlerList) {
      if (handler.shouldHandle(device)) {
        try {
          await handler.handle(device);
          return;
        } catch (e, stackTrace) {
          DevLogger.error(
              tag: 'DeviceHandlerChain',
              msg:
                  'executeHandlers handler.handle err:$e, stackTrace:$stackTrace');
        }
      }
    }
  }

  void dispatcher() {
    for (final DeviceHandlerBase handler in _handlerList) {
      handler.dispatcher();
    }
  }
}

class DeviceHandlerChainFactory {
  DeviceHandlerChain createChain(DeviceListFetchTriggerType triggerType) {
    final DeviceHandlerChain chain = DeviceHandlerChain();
    final List<DeviceHandlerBase> handlers =
        _loadHandlersFromConfig(triggerType);
    handlers.forEach((DeviceHandlerBase handler) {
      chain.addHandler(handler);
    });
    return chain;
  }

  List<DeviceHandlerBase> _loadHandlersFromConfig(
      DeviceListFetchTriggerType triggerType) {
    return <DeviceHandlerBase>[
      FridgeFoodNumHandler(triggerType),
      //TODO-zqj 补充其他处理器
    ];
  }
}
