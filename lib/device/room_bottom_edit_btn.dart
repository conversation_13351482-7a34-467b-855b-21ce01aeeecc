import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:library_widgets/common/util.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_constant.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

class RoomBottomEditButton extends StatelessWidget {
  const RoomBottomEditButton({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildBottomEditButton();
  }

  Widget _buildBottomEditButton() {
    return SliverToBoxAdapter(
      child: StoreConnector<SmartHomeState, _SettingViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return _SettingViewModel(
            visible: store.state.isLogin &&
                !store.state.isEditState &&
                store.state.deviceState.selectedRoomId !=
                    SmartHomeConstant.shareDeviceFlag &&
                !isFamilyMemberRole(),
            isWholeHouseTabSelected: store.state.deviceState.selectedRoomId ==
                store.state.familyState.familyId,
          );
        },
        builder: (BuildContext context, _SettingViewModel vm) {
          return Visibility(
            visible: vm.visible,
            child: Padding(
              padding: const EdgeInsets.only(top: 20, bottom: 32),
              child: GestureDetector(
                onTap: () {
                  gioTrack(AggregationSettingConstant.settingClickGio);
                  if (vm.isWholeHouseTabSelected) {
                    _jumpToRoomEditPageFromWholeHouse();
                  } else {
                    // 单空间-编辑房间
                    _jumpToRoomEditPageFromCurRoom();
                  }
                },
                child: Center(
                  child: Container(
                    width: 76,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Center(
                      child: Text(
                        '编辑',
                        textAlign: TextAlign.center,
                        style:
                            TextStyle(fontSize: 14, color: Color(0xFF111111)),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _jumpToRoomEditPageFromWholeHouse() {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final String roomId = smartHomeStore.state.deviceState.selectedRoomId;
    goToPageWithDebounce(
        '${SmartHomeConstant.aggregationSettingPage}?familyId=$familyId&roomId=$roomId');
  }

  void _jumpToRoomEditPageFromCurRoom() {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final String floorName = smartHomeStore.state.deviceState.selectedFloor;
    final String roomId = smartHomeStore.state.deviceState.selectedRoomId;
    final String roomName = smartHomeStore.state.deviceState.selectedRoom;

    final String roomEditUrl =
        'mpaas://familymanage?familyId=$familyId&floorName=$floorName&roomId=$roomId&roomName=$roomName#/deviceList';
    goToPageWithDebounce(roomEditUrl);
  }
}

class _SettingViewModel {
  bool visible = false;
  bool isWholeHouseTabSelected = true;

  _SettingViewModel(
      {this.visible = false, this.isWholeHouseTabSelected = true});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _SettingViewModel &&
          runtimeType == other.runtimeType &&
          visible == other.visible &&
          isWholeHouseTabSelected == other.isWholeHouseTabSelected;

  @override
  int get hashCode => visible.hashCode ^ isWholeHouseTabSelected.hashCode;
}
