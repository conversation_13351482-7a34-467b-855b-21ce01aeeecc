import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';

import 'drag_notification.dart';

/// A widget that animates its child during drag-and-drop operations.
///
/// The [DragItem] widget animates its child using an implicit animation when
/// the child is dragged. It can be used to provide visual feedback during a drag
/// operation.
///
/// - [child]: The child widget that you want to animate during drag-and-drop.
/// - [duration]: The duration of the animation (default is 200 milliseconds).
/// - [onAnimationStatus]: An optional callback to listen for animation status changes.
///
/// To use this widget, wrap it around the widget you want to animate during drag
/// operations, and the animation will be triggered when necessary.
class DragItem extends ImplicitlyAnimatedWidget {
  const DragItem({
    required this.child,
    Duration? duration,
    this.onAnimationStatus,
    super.key,
  }) : super(duration: duration ?? _animationDuration);
  final Widget child;
  final AnimationStatusListener? onAnimationStatus;
  static const Duration _animationDuration = Duration(milliseconds: 200);

  @override
  ImplicitlyAnimatedWidgetState<ImplicitlyAnimatedWidget> createState() =>
      _DragItemState();
}

class _DragItemState extends AnimatedWidgetBaseState<DragItem> {
  RenderAnimationManage renderAnimationManage = RenderAnimationManage();

  @override
  void initState() {
    super.initState();
    renderAnimationManage.controller = controller;
    renderAnimationManage.animation = animation;
    if (widget.onAnimationStatus != null) {
      controller.addStatusListener(widget.onAnimationStatus!);
    }
  }

  @override
  void forEachTween(TweenVisitor<dynamic> visitor) {}

  void update() {
    controller
      ..value = 0.0
      ..forward();
  }

  @override
  Widget build(BuildContext context) {
    return _DragAnimationRender(
      renderAnimationManage,
      animation.value,
      change: () {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          update();
        });
      },
      child: widget.child,
    );
  }
}

class _DragAnimationRender extends SingleChildRenderObjectWidget {
  const _DragAnimationRender(
    this.renderAnimManage,
    this.update, {
    super.child,
    this.change,
  });

  final void Function()? change;
  final RenderAnimationManage renderAnimManage;
  final double update;

  bool get isExecute => !DragNotification.isScroll;

  @override
  _AnimationRenderObject createRenderObject(BuildContext context) {
    return _AnimationRenderObject(renderAnimManage, update, change: change);
  }

  @override
  void updateRenderObject(
      BuildContext context, _AnimationRenderObject renderObject) {
    if (isExecute && renderObject.update != update) {
      renderObject.markNeedsLayout();
    }
  }
}

class _AnimationRenderObject extends RenderShiftedBox {
  _AnimationRenderObject(
    this.renderAnimationManage,
    this.update, {
    RenderBox? child,
    this.change,
  }) : super(child);
  final void Function()? change;
  final RenderAnimationManage renderAnimationManage;
  final double update;

  bool get isExecute => !DragNotification.isScroll;

  @override
  Size computeDryLayout(BoxConstraints constraints) {
    if (child == null) {
      return constraints.constrain(Size.zero);
    }
    final BoxConstraints innerConstraints =
        constraints.deflate(EdgeInsets.zero);
    final Size childSize = child!.getDryLayout(innerConstraints);
    return constraints.constrain(Size(childSize.width, childSize.height));
  }

  @override
  void performLayout() {
    final BoxConstraints constraints = this.constraints;
    if (child == null) {
      size = constraints.constrain(Size.zero);
      return;
    }
    final BoxConstraints innerConstraints =
        constraints.deflate(EdgeInsets.zero);
    child!.layout(innerConstraints, parentUsesSize: true);
    final BoxParentData childParentData = child!.parentData! as BoxParentData;
    childParentData.offset = Offset.zero;
    size = constraints.constrain(Size(
      child!.size.width,
      child!.size.height,
    ));
  }

  void setStart(EdgeInsetsGeometry? begin, EdgeInsetsGeometry? end) {
    renderAnimationManage.tweenOffset =
        EdgeInsetsGeometryTween(begin: begin, end: end);
    change?.call();
  }

  @override
  void paint(PaintingContext context, Offset offset) {
    if (child != null) {
      final BoxParentData childParentData = child!.parentData! as BoxParentData;
      final Offset position = childParentData.offset + offset;
      renderAnimationManage.currentOffset ??=
          EdgeInsets.only(left: position.dx, top: position.dy);
      if (renderAnimationManage.controller.isAnimating &&
          renderAnimationManage.tweenOffset != null) {
        final EdgeInsets geometry = renderAnimationManage.tweenOffset!
            .evaluate(renderAnimationManage.animation) as EdgeInsets;
        context.paintChild(child!, Offset(geometry.left, geometry.top));
        if (renderAnimationManage.currentOffset!.left != position.dx ||
            renderAnimationManage.currentOffset!.top != position.dy) {
          setStart(
              geometry, EdgeInsets.only(left: position.dx, top: position.dy));
        }
        renderAnimationManage.currentOffset =
            EdgeInsets.only(left: position.dx, top: position.dy);
      } else {
        if (isExecute &&
            renderAnimationManage.currentOffset != null &&
            (renderAnimationManage.currentOffset!.left != position.dx ||
                renderAnimationManage.currentOffset!.top != position.dy)) {
          context.paintChild(
              child!,
              Offset(renderAnimationManage.currentOffset!.left,
                  renderAnimationManage.currentOffset!.top));
          setStart(renderAnimationManage.currentOffset,
              EdgeInsets.only(left: position.dx, top: position.dy));
          renderAnimationManage.currentOffset =
              EdgeInsets.only(left: position.dx, top: position.dy);
        } else {
          renderAnimationManage.currentOffset =
              EdgeInsets.only(left: position.dx, top: position.dy);

          context.paintChild(child!, position);
        }
      }
    }
  }
}

/// A class that manages animation and state for [DragItem].
class RenderAnimationManage {
  RenderAnimationManage();

  EdgeInsetsGeometryTween? tweenOffset;
  EdgeInsets? currentOffset;
  late AnimationController controller;
  late Animation<double> animation;
}
