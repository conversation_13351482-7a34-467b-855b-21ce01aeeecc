import 'package:device_utils/log/log.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/plugin/logicengine_plugin.dart';
import 'package:smart_home/device/quick_card/tools/constant.dart';

//逻辑引擎设备开关机命令下发方法
void powerCommandEngine(String device, Map<String, String> cmdMap) {
  DevLogger.info(
      tag: Constant.tagPowerCommand,
      msg: 'powerBtn command, deviceId: $device,cmdMap: $cmdMap');
  final List<Command> commands = <Command>[Command.fromMap(cmdMap)];
  LogicEnginePlugin.operate(device, commands).then((void value) {
    DevLogger.info(
        tag: Constant.tagPowerCommand,
        msg: 'powerBtn command complete: deviceId: $device, '
            'commands: $commands');
  }).catchError((dynamic err) {
    DevLogger.error(
        tag: Constant.tagPowerCommand,
        msg: 'powerBtn command issue exception: deviceId: $device, '
            'commands: $commands, err: $err');
  });
}

void batchPowerCommandEngine(Map<String, List<Command>> groupCommands) {
  if (groupCommands.isEmpty) {
    DevLogger.info(
        tag: Constant.tagPowerCommand,
        msg: '_batchPowerCommandEngine abort, groupCommands is empty, return');
    return;
  }

  DevLogger.info(
      tag: Constant.tagPowerCommand,
      msg:
          '_batchPowerCommandEngine begin, groupCommands-length:${groupCommands.length}, commands deviceIds:${groupCommands.keys.toList()}');

  groupCommands.forEach((String key, List<Command> value) {
    DevLogger.info(
        tag: Constant.tagPowerCommand,
        msg:
            '_batchPowerCommandEngine print all params, deviceId:$key, command:$value');
  });

  LogicEnginePlugin.batchOperate(groupCommands).then((void value) {
    DevLogger.info(
        tag: Constant.tagPowerCommand, msg: '_batchPowerCommandEngine success');
  }).catchError((dynamic e) {
    DevLogger.error(
        tag: Constant.tagPowerCommand,
        msg: '_batchPowerCommandEngine fail: err: $e');
  });
}
