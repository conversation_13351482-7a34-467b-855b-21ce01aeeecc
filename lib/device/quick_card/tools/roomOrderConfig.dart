class RoomNameConstant {
  static const String roomNameAll = '设备';
  static const String livingRoom = '客厅';
  static const String bedroom = '卧室';
  static const String kitchen = '厨房';
  static const String washroom = '卫生间';
  static const String balcony = '阳台';
  static const String diningRoom = '餐厅';
  static const String kidsRoom = '儿童房';
  static const String eldersRoom = '老人房';
  static const String lounge = '休息室';
  static const String studyRoom = '书房';
  static const String recreationRoom = '娱乐室';
  static const String activityRoom = '活动室';
  static const String clockRoom = '衣帽间';
  static const String hallway = '玄关';
  static const String corridor = '走廊';
  static const String carbarn = '车库';
  static const String garden = '花园';
}

//定义每个房间对应的数值大小用于排序
Map<String, int> roomNameMapWithOrderId = <String, int>{
  RoomNameConstant.roomNameAll: 0,
  RoomNameConstant.livingRoom: 1,
  RoomNameConstant.bedroom: 2,
  RoomNameConstant.kitchen: 3,
  RoomNameConstant.washroom: 4,
  RoomNameConstant.balcony: 5,
  RoomNameConstant.diningRoom: 6,
  RoomNameConstant.kidsRoom: 7,
  RoomNameConstant.eldersRoom: 8,
  RoomNameConstant.lounge: 9,
  RoomNameConstant.studyRoom: 10,
  RoomNameConstant.recreationRoom: 11,
  RoomNameConstant.activityRoom: 12,
  RoomNameConstant.clockRoom: 13,
  RoomNameConstant.hallway: 14,
  RoomNameConstant.corridor: 15,
  RoomNameConstant.carbarn: 16,
  RoomNameConstant.garden: 17,
};

int floorDefaultOrderId = -9999;
Map<String, int> floorNameMapWithOrderId = <String, int>{
  '一层': 8,
  '二层': 7,
  '三层': 6,
  '四层': 5,
  '五层': 4,
  '负一层': 3,
  '负二层': 2,
  '负三层': 1
};

class RoomNameNum {
  String roomName = '';
  int roomNum = -1;
}
