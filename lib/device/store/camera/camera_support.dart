import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

void resetMuteIcon(CameraDeviceCardViewModel cameraViewModel) {
  cameraViewModel.muteIcon = cameraViewModel.isMute
      ? CameraConstant.muteImageUrlOn
      : CameraConstant.muteImageUrlOff;
}

// 根据deviceId 获取对应的viewModel
CameraDeviceCardViewModel? findCameraViewModelByDeviceId(
    String deviceId, Map<String, CardBaseViewModel> maps) {
  return maps[deviceId] as CameraDeviceCardViewModel?;
}

List<String> _multiCameraStreamTypeIdList = <String>[
  '201c80c70c50031c1201a3bdfd123b0000007383a02d45b232698cdacef3e840',
  '201c80c70c50031c1201a3bdfd123b0000007013e5f5a7c57e3388bbf605f640'
];

CameraStreamCount isSupportMultiCameraStream(DeviceCardViewModel vm) {
  final String typeId = vm.device.basicInfo.typeId;
  if (_multiCameraStreamTypeIdList.contains(typeId) && typeId.isNotEmpty) {
    return CameraStreamCount.two;
  }
  return CameraStreamCount.one;
}
