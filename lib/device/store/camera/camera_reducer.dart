import 'package:device_utils/log/log.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/store/camera/camera_support.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_state.dart';

SmartHomeState cameraReducer(SmartHomeState state, CameraAction action) {
  final CameraPayload payload = action.payload;
  final String deviceId = payload.deviceId;
  final CameraDeviceCardViewModel? cameraViewModel =
      findCameraViewModelByDeviceId(
          deviceId, state.deviceState.allCardViewModelMap);
  if (cameraViewModel == null) {
    return state;
  }
  DevLogger.info(
      tag: 'cameraReducer',
      msg:
          'cameraReducer deviceId is [$deviceId], action type is [${action.type} , current cache style is ${cameraViewModel.getCurrentTyle(action.cameraType)}]');
  switch (action.type) {
    case CameraActionType.play:
    case CameraActionType.retry:
    case CameraActionType.wakeup:
      cameraViewModel.updateStyle(action.cameraType, CameraWidgetStyle.loading);
      resetMuteIcon(cameraViewModel);
      break;
    case CameraActionType.error:
      cameraViewModel.updateStyle(action.cameraType, CameraWidgetStyle.retry);
      cameraViewModel.isMute = true;
      cameraViewModel.closeToolsBarWithType(action.cameraType);
      resetMuteIcon(cameraViewModel);
      break;
    case CameraActionType.stop:
      if (cameraViewModel.getCurrentTyle(action.cameraType) !=
          CameraWidgetStyle.offline) {
        if (cameraViewModel.getCurrentTyle(action.cameraType) ==
            CameraWidgetStyle.sleep) {
          return state;
        }
        cameraViewModel.updateStyle(action.cameraType, CameraWidgetStyle.stop);
        cameraViewModel.isMute = true;
        cameraViewModel.closeToolsBarWithType(action.cameraType);

        resetMuteIcon(cameraViewModel);
      }
      break;
    case CameraActionType.playing:
      cameraViewModel.updateStyle(action.cameraType, CameraWidgetStyle.playing);

      resetMuteIcon(cameraViewModel);
      break;
    case CameraActionType.changeToolBarStatus:
      cameraViewModel.changeToolsBarWithType(action.cameraType);
      resetMuteIcon(cameraViewModel);
      break;
    case CameraActionType.mute:
      cameraViewModel.isMute = !cameraViewModel.isMute;
      resetMuteIcon(cameraViewModel);
    case CameraActionType.enterFullScreen:
      _closeToolsBar(cameraViewModel, action.cameraType);
      resetMuteIcon(cameraViewModel);
      break;
    case CameraActionType.exitFullScreen:
      cameraViewModel.isMute = action.payload.status;
      resetMuteIcon(cameraViewModel);
      break;
    default:
      break;
  }
  return state;
}

void _closeToolsBar(
    CameraDeviceCardViewModel? cameraViewModel, CameraType type) {
  if (cameraViewModel != null) {
    cameraViewModel.closeToolsBarWithType(type);
  }
}
