import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_state.dart';

bool cameraMiddleware(SmartHomeState state, CameraAction action) {
  final String deviceId = action.payload.deviceId;

  _handleFullScreenActions(action, deviceId);

  switch (action.cameraType) {
    case CameraType.HOME_CARD:
      return _handleHomeCardAction(action, deviceId);
    case CameraType.AGGREGATION_CARD:
      return _handleAggregationCardAction(action, deviceId, state);
    default:
      return false;
  }
}

// 改动: 提取HOME_CARD处理逻辑
bool _handleHomeCardAction(CameraAction action, String deviceId) {
  switch (action.type) {
    case CameraActionType.play:
      {
        if (CameraLiveCoordinator.instance.playingDeviceId.isNotEmpty) {
          CameraLiveCoordinator.instance.stopCurrentPlayer();
          CameraLiveCoordinator.instance.reset();
        }
        CameraLiveCoordinator.instance.setPlayingDeviceIdAndPresenter(deviceId);
      }
    case CameraActionType.wakeup:
      {
        if (CameraLiveCoordinator.instance.playingDeviceId.isNotEmpty) {
          CameraLiveCoordinator.instance.wakeupDeviceAndPlay(deviceId);
        }
      }
    case CameraActionType.retry:
      {
        if (CameraLiveCoordinator.instance.playingDeviceId.isNotEmpty &&
            CameraLiveCoordinator.instance.playingDeviceId != deviceId) {
          CameraLiveCoordinator.instance.stopCurrentPlayer();
          CameraLiveCoordinator.instance.reset();
        }
        CameraLiveCoordinator.instance.setPlayingDeviceIdAndPresenter(deviceId);
      }
    case CameraActionType.error:
      {
        if (CameraLiveCoordinator.instance.playingDeviceId.isNotEmpty &&
            CameraLiveCoordinator.instance.playingDeviceId != deviceId &&
            deviceId.isNotEmpty) {
          AggregationCameraPresenterManager.instance
              .getPresenterByDevId(deviceId)
              .stopAndDestory();
        }
      }
    case CameraActionType.stopAll:
      {
        CameraLiveCoordinator.instance.stopCurrentPlayer();
      }
    default:
      break;
  }
  return false;
}

bool _handleAggregationCardAction(
    CameraAction action, String deviceId, SmartHomeState state) {
  switch (action.type) {
    case CameraActionType.play:
    case CameraActionType.wakeup:
    case CameraActionType.retry:
      {
        AggregationCameraPresenterManager.instance
            .onCameraPlaying(deviceId, state.deviceState.allCardViewModelMap);
      }
    case CameraActionType.error:
      {
        if (deviceId.isNotEmpty) {
          AggregationCameraPresenterManager.instance
              .getPresenterByDevId(deviceId)
              .stopAndDestory();
          AggregationCameraPresenterManager.instance.onCameraStop(deviceId);
        }
      }
    case CameraActionType.stop:
      {
        AggregationCameraPresenterManager.instance.onCameraStop(deviceId);
      }
    case CameraActionType.stopAll:
      {
        AggregationCameraPresenterManager.instance.stopAllPlaying();
        return true;
      }
    case CameraActionType.enterFullScreen:
      {
        if (deviceId.isNotEmpty) {
          AggregationCameraPresenterManager.instance.onFullScreen(deviceId);
        }
      }
    default:
      break;
  }
  return false;
}

bool _handleFullScreenActions(CameraAction action, String deviceId) {
  switch (action.type) {
    case CameraActionType.exitFullScreen:
      CameraLiveCoordinator.instance.unlockPlayer(deviceId);
      AggregationCameraPresenterManager.instance.unlockPlayer(deviceId);
    case CameraActionType.enterFullScreen:
      CameraLiveCoordinator.instance.lockPlayer(deviceId);
      AggregationCameraPresenterManager.instance.lockPlayer(deviceId);
    default:
      break;
  }
  return false;
}
