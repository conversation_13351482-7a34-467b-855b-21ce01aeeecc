import 'package:device_utils/log/log.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_device/utils/util_diff.dart';
import 'package:smart_home/device/store/voice_box/util/voice_constants.dart';
import 'package:whole_house_music/music/api/music_api.dart';
import 'package:whole_house_music/music/model/song_info_model.dart';
import 'package:whole_house_music/web_socket/websocket_status.dart';

typedef SocketReceiveMsgCallBack = void Function(SongInfoModel songInfoModel);
typedef SongInfoCallback = void Function(SongInfoModel songInfoModel);

enum VoiceActionType {
  unknown,
  next, //下一首
  last, //上一首
  pause, //暂停
  resume, //恢复
  play, //播放
  stop, //暂停
  create, //获取当前播放的资源信息
}

class VoiceDeviceSocket implements ConnectResultListener {
  static VoiceDeviceSocket? _instance;
  SocketReceiveMsgCallBack? _receiveMsgCallBack;
  Map<String, SongInfoModel> songInfoModelMap = <String, SongInfoModel>{};
  VoiceDeviceSocket._internal() {
    _instance = this;
  }

  String currentFamilyId = '';
  List<String> currentDeviceIds = <String>[];

  factory VoiceDeviceSocket() => _instance ?? VoiceDeviceSocket._internal();

  bool isNeedReconnect() {
    return MusicApiHelper.getHelper().getSocketConnectStatus() ==
            WebSocketConnectStatus.close ||
        MusicApiHelper.getHelper().getSocketConnectStatus() ==
            WebSocketConnectStatus.closing;
  }

  ///每次reconnect会重新给底层deivceId赋值
  void reconnectWebSocket(String familyId, List<String> deviceIds,
      SocketReceiveMsgCallBack callBack) {
    final bool isDeviceIdNotChange = isListEqual(deviceIds, currentDeviceIds);
    final bool isFamilyNotChanged = familyId == currentFamilyId;
    final bool isNotNeedReconnect = !isNeedReconnect();
    if (isDeviceIdNotChange && isNotNeedReconnect && isFamilyNotChanged) {
      DevLogger.info(
          tag: 'smartHomeDevice',
          msg:
              'reconnect web socket failed  isNotNeedReconnect = $isNotNeedReconnect, isDeviceIdNotChange = $isDeviceIdNotChange, isFamilyNotChanged = $isFamilyNotChanged ,deviceIds = [$deviceIds] , currentDevices = [$currentDeviceIds]');
      return;
    }
    DevLogger.info(
        tag: 'smartHomeDevice',
        msg:
            'reconnect web socket indeed devices =  $deviceIds,familyid = $familyId');
    currentDeviceIds = deviceIds;
    currentFamilyId = familyId;
    _receiveMsgCallBack = callBack;
    _connect(familyId, deviceIds);
  }

  void _connect(String familyId, List<String> deviceIds) {
    MusicApiHelper.getHelper().connect(
      familyId,
      deviceIds,
      this,
    );
    DevLogger.info(
        tag: 'smartHomeDevice',
        msg:
            'className:VoicePresenter methodName:reconnectWebSocket content--familyId:$familyId ,deviceId: $deviceIds ');
  }

  ///由于底层MusicApiHelper::disConnect的deviceId 参数只为打印,所以这里可以使用‘’作为入参
  Future<void> removeWebSocketAllListener() async {
    currentDeviceIds.clear();
    return MusicApiHelper.getHelper().disConnectAll();
  }

  // 暂停/继续/切歌点击事件
  void musicCardClicked(VoiceActionType actionType,
      SongInfoModel? songInfoModel, String cardType, bool isAlarm) {
    DevLogger.info(
      tag: 'smartHomeDevice',
      msg:
          'musicCardClicked type is $actionType , card type is $cardType, songInfo is $songInfoModel, is alarm $isAlarm',
    );
    if (songInfoModel == null) {
      return;
    }

    if (songInfoModel.deviceId.isNotEmpty) {
      if (currentFamilyId.isNotEmpty &&
          currentDeviceIds.isNotEmpty &&
          _receiveMsgCallBack != null) {
        reconnectWebSocket(
            currentFamilyId, currentDeviceIds, _receiveMsgCallBack!);
      }
    }

    if (songInfoModel.resId == '') {
      ToastHelper.showToast(Constant.toastMusicUncontrollable);
      return;
    }

    if (isAlarm) {
      ToastHelper.showToast(Constant.toastDeviceAlarmInfo);
    }
    _voiceBoxPlayCardAction(actionType, songInfoModel);
  }

  // VoiceBox 播放器操作
  void _voiceBoxPlayCardAction(
      VoiceActionType actionType, SongInfoModel songModel) {
    switch (actionType) {
      case VoiceActionType.play:
      case VoiceActionType.resume:
        MusicApiHelper.getHelper().play(songModel);
        break;
      case VoiceActionType.pause:
      case VoiceActionType.stop:
        MusicApiHelper.getHelper().pause(songModel);
        break;
      case VoiceActionType.last:
        MusicApiHelper.getHelper().previous(songModel);
        break;
      case VoiceActionType.next:
        MusicApiHelper.getHelper().next(songModel);
        break;
      default:
        break;
    }
  }

  @override
  void onReceiveMessage(
      WebSocketConnectStatus connectStatus, SongInfoModel musicInfoModel) {
    songInfoModelMap[musicInfoModel.deviceId] = musicInfoModel;
    if (_receiveMsgCallBack != null) {
      _receiveMsgCallBack!(musicInfoModel);
    }
  }
}
