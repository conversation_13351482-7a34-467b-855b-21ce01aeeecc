import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/song_box_card_view_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/device/store/voice_box/server/voice_box_socket_util.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:whole_house_music/music/model/song_info_model.dart';

import 'util/voice_constants.dart';

SmartHomeState websocketVoiceBoxReducer(
    SmartHomeState state, UpdateVoiceBoxDeviceAction action) {
  final SongInfoModel m = action.songInfoModel;
  if (state.deviceState.allCardViewModelMap[m.deviceId] == null) {
    DevLogger.info(
      tag: 'smartHomeDevice',
      msg:
          'websocketVoiceBoxReducer coming check device is $m ,This device not in allCardViewModelMap!',
    );
    state.deviceState.allCardViewModelMap[m.deviceId] =
        VoiceBoxCardViewModel.createFromWebSocket(m);
  } else {
    final CardBaseViewModel c =
        state.deviceState.allCardViewModelMap[m.deviceId]!;
    DevLogger.info(
      tag: 'smartHomeDevice',
      msg:
          'websocketVoiceBoxReducer coming check device is $m ,This device is in allCardViewModelMap! tye  is ${c.runtimeType}',
    );
    if (c is VoiceBoxCardViewModel) {
      state.deviceState.allCardViewModelMap[m.deviceId] =
          c.updateFromWebSocket(m);
    }
  }
  return state;
}

Future<void> releaseVoiceConnection() async {
  DevLogger.info(
      tag: 'smartHomeDevice',
      msg: 'filterSupportMusicDevices removeWebSocketAllListener!');
  VoiceDeviceSocket().removeWebSocketAllListener();
}

Future<void> filterSupportControlDevice(Store<SmartHomeState>? _store,
    List<SmartHomeDevice> deviceInfoMap, String curFamilyId) async {
  if (deviceInfoMap.isEmpty) {
    releaseVoiceConnection();
    return;
  }

  final List<String> supportDevs = <String>[];
  deviceInfoMap.forEach((SmartHomeDevice value) {
    if (Constant.isVoiceDevice(value.basicInfo.typeId)) {
      if (value.basicInfo.deviceId.isNotEmpty) {
        supportDevs.add(value.basicInfo.deviceId);
      }
    }
  });

  DevLogger.info(
      tag: 'smartHomeDevice',
      msg: 'filterSupportMusicDevices size is  ${supportDevs.length}');
  if (supportDevs.isNotEmpty) {
    VoiceDeviceSocket().reconnectWebSocket(curFamilyId, supportDevs,
        (SongInfoModel songInfoModel) {
      _store?.dispatch(UpdateVoiceBoxDeviceAction(songInfoModel));
    });
  } else {
    releaseVoiceConnection();
  }
}
