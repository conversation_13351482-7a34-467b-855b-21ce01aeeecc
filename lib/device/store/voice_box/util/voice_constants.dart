/*
 * 描述：常量定义
 * 作者：songFJ
 * 建立时间: 2/27/23
 */

class Constant {
  static const String toastDeviceAlarmInfo = '设备故障，请检查设备状态';
  static const String toastMusicUncontrollable = '请前往资源界面，点播内容';

  static List<String> voiceLimitList = <String>[
    '2054a0d610c6d2103b01b2d8cd5716000000d059588c29bb04b8657e80fdb040', // CSPK-C500UD
    '2054a0000426cb243b01be99fbb6b2000000ae73dd8d7e728e323eb8a052b140', //HSPK-X30UD （汉玉白）
    '2054a0000426cb243b01b351503e7e0000009ebe6edafddeb12ed7637cacda40', // HSPK-X31UD（星球崛起）
    '2054a0000426cb243b0198d5760c1d000000bd121b216b07e45bcaefe67fcc40' //HSPK-X20UD
  ];

  static bool isVoiceDevice(String typeId) {
    return voiceLimitList.contains(typeId);
  }
}
