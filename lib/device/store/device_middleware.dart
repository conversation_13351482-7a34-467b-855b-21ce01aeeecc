import 'dart:collection';

import 'package:device_utils/log/log.dart';
import 'package:device_utils/typeId_parse/device_type.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/agg_camera/agg_camera_util.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_action.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_constant.dart';
import 'package:smart_home/device/device_filter/device_filter_model.dart';
import 'package:smart_home/device/device_handler_chains/device_handler_chain.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/device_list_util.dart';
import 'package:smart_home/device/device_presenter.dart';
import 'package:smart_home/device/device_view_model/camera/camera_msg_presenter.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/e_heat_time_on_off_presenter.dart';
import 'package:smart_home/device/factory/device_card_factory.dart';
import 'package:smart_home/device/store/camera/camera_middleware.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/device/store/voice_box/util/voice_constants.dart';
import 'package:smart_home/device/store/voice_box/voice_box_reducer_support.dart';
import 'package:smart_home/offline_gio/offline_gio_track.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_action.dart';
import 'package:storage/storage.dart';
import 'package:user/user.dart';
import 'package:wash_device_manager/wash_device_manager.dart';

import '../../common/constant_gio.dart';
import '../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../store/smart_home_state.dart';
import '../../time_cost_analysis/analysis_presenter.dart';
import '../aggregation/aggregation_detail/model/supportDeviceModel.dart';
import '../aggregation/aggregation_detail/utils/aggregation_presenter.dart';
import '../purified_consumable_presenter.dart';

class DeviceMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    if (action is DeviceInfoMapUpdatedAction) {
      _closeFilterBottomSheet(store);
      _updateDeviceInfoMap(store, action);
      decodeDeviceExcludeJson().then((QuickControlExcludeDevice value) {
        final List<String> excludeDeviceList = value.excludeDevice;
        store.dispatch(
            UpdateExcludeDeviceList(excludeDeviceList: excludeDeviceList));
      });
    } else if (action is CameraAction) {
      final bool ifInterceptor = cameraMiddleware(store.state, action);
      if (ifInterceptor) {
        return;
      }
    } else if (action is FetchCameraMsgAction) {
      _fetchCameraMsgAction(store);
      return;
    }

    next(action);
  }

  void _closeFilterBottomSheet(Store<SmartHomeState> store) {
    if (store.state.deviceState.popUpContext != null) {
      Navigator.pop(store.state.deviceState.popUpContext!);
      store.dispatch(UpdatePopupContextAction(null));
    }
  }

  Future<void> _updateDeviceInfoMap(
      Store<SmartHomeState> store, DeviceInfoMapUpdatedAction action) async {
    final Map<String, SmartHomeDevice> tmpSmartHomeDeviceMap =
        <String, SmartHomeDevice>{};
    final Map<String, CardBaseViewModel> tmpAllCardViewModelMap =
        <String, CardBaseViewModel>{};
    List<String> tmpSmallCardSortIdList = <String>[];

    final Set<String> floorSet = <String>{};

    final List<SmartHomeDevice> voiceDevices = <SmartHomeDevice>[];

    final Map<String, int> smallSortMap = <String, int>{};

    final BrandTypeModel brandTypeModel = BrandTypeModel(netDevice: false);

    DevicePresenter.getInstance().washDeviceList.clear();
    DevicePresenter.getInstance().eHeatAndGasDeviceList.clear();
    DevicePresenter.getInstance().purifiedWaterDeviceList.clear();

    /// 所有附件设备 {parentId：List<DeviceInfoModel>}
    final Map<String, Map<String, DeviceCardViewModel>> role3ViewModelMap =
        <String, Map<String, DeviceCardViewModel>>{};

    /// 已聚合设备
    final Map<String, Map<String, DeviceCardViewModel>>
        aggregationViewModelMap = <String, Map<String, DeviceCardViewModel>>{};

    final Map<String, Map<String, LinkedHashSet<String>>> filterMap =
        <String, Map<String, LinkedHashSet<String>>>{};

    final Map<TabFilterModel, List<String>> tabFilterMap =
        <TabFilterModel, List<String>>{};

    final LinkedHashSet<String> shareCategorySet = LinkedHashSet<String>();

    final LinkedHashSet<String> allCategory = LinkedHashSet<String>();
    final Set<String> allCameraDevs = <String>{};
    final Set<String> aggCameraDevs = <String>{};

    final DeviceHandlerChain chain =
        DeviceHandlerChainFactory().createChain(action.triggerType);
    action.originalDeviceInfoMap.forEach((String key, DeviceInfoModel value) {
      // 品牌
      _getBrandType(brandTypeModel, value);
      // 转换设备数据
      SmartHomeDevice? smartHomeDevice = tmpSmartHomeDeviceMap[value.deviceId];
      if (smartHomeDevice == null) {
        smartHomeDevice = _createSmartHomeDevice(value);
        tmpSmartHomeDeviceMap[smartHomeDevice.basicInfo.deviceId] =
            smartHomeDevice;
      }

      //处理设备楼层、房间、设备类型
      _handleDeviceFilter(smartHomeDevice.basicInfo, filterMap, allCategory,
          shareCategorySet, store.state.deviceState.roomIdByFloorRoomNameMap);
      // 洗衣机、热水器逻辑
      _handleWashAndEHeaterDevice(smartHomeDevice);

      /// 厨下净水器
      _handlePurifiedWaterDevice(smartHomeDevice);
      // 过滤音箱
      if (Constant.isVoiceDevice(smartHomeDevice.basicInfo.typeId)) {
        voiceDevices.add(smartHomeDevice);
      }

      if (smartHomeDevice.basicInfo.deviceRole != '3') {
        floorSet.add(smartHomeDevice.basicInfo.roomInfo.floorName);
      }
      // 大小卡viewmodel
      _handleDeviceCardViewModel(
          smartHomeDevice,
          role3ViewModelMap,
          tmpAllCardViewModelMap,
          smallSortMap,
          tmpSmallCardSortIdList,
          allCameraDevs);
      // 更新聚合设备数据viewmodel, 暂时本地用的附件设备调试
      handleDeviceViewModelForAggregation(
        smartHomeDevice,
        aggregationViewModelMap,
        tmpAllCardViewModelMap,
      );

      //筛选摄像头聚合设备
      filterAggCameraDeices(
          tmpAllCardViewModelMap, smartHomeDevice, aggCameraDevs);

      chain.executeHandlers(smartHomeDevice);
    });

    chain.dispatcher();

    updateAggCameraDeviceCard(
        tmpAllCardViewModelMap, aggCameraDevs, store, action.familyId);

    final FamilyModel familyModel = await Family.getCurrentFamily();
    smartHomeStore.dispatch(UpdateDeviceFilterAction(filterMap, allCategory,
        shareCategorySet, familyModel.familyId, familyModel.floorInfos));
    gioTrack(DevListGioEvent.t2PresenterDispatchDeviceMapAction, <String, int>{
      DevListGioEvent.sizeAllDevList: tmpSmartHomeDeviceMap.length,
    });

    final int traceId = action.traceInfo.traceId;
    final TraceType traceType = action.traceInfo.traceType;
    TimeConsumeStatisticTracker.trace(
        traceId: traceId, loc: TraceLocation.t3, traceType: traceType);

    // 列表排序
    tmpSmallCardSortIdList =
        handleCardSortList(smallSortMap, tmpSmallCardSortIdList);

    for (final String sortId in tmpSmallCardSortIdList) {
      final SmartHomeDevice? device = tmpSmartHomeDeviceMap[sortId];

      if (device is SmartHomeDevice) {
        String roomName = device.basicInfo.roomInfo.roomName;
        String floorName = device.basicInfo.roomInfo.floorName;

        if (device.basicInfo.isSharedDevice) {
          roomName = SmartHomeConstant.shareDeviceFlag;
          floorName = '';
        }

        final TabFilterModel filterModel = TabFilterModel(floorName, roomName);

        if (tabFilterMap[filterModel] == null) {
          tabFilterMap[filterModel] = <String>[];
        }
        tabFilterMap[TabFilterModel(floorName, roomName)]?.add(sortId);
      }

      tabFilterMap[TabFilterModel(SmartHomeConstant.deviceFilterSelectAll,
          SmartHomeConstant.deviceFilterSelectAll)] = <String>[
        ...tmpSmallCardSortIdList
      ];
    }

    // 聚合房间排序和聚合设备排序
    final Map<EditPageType, String> aggregationIdPrefixes =
    <EditPageType, String>{
      EditPageType.light: AggregationSettingConstant.agg_light_id,
      EditPageType.curtain: AggregationSettingConstant.agg_curtain_id,
      EditPageType.env: AggregationSettingConstant.env_id,
    };

      sortAggregationRoomAddDevice(tmpAllCardViewModelMap, smallSortMap,
          smartHomeStore.state.deviceState.cardShowFloor);
    if (aggregationIdPrefixes
        .containsKey(EditPresenterManager.currentPageForEdit)) {
      final String deviceId =
          '${aggregationIdPrefixes[EditPresenterManager.currentPageForEdit]}${action.familyId}';
      _sortAggDeviceWithServerData(tmpAllCardViewModelMap, deviceId);
    }
    TimeConsumeStatisticTracker.trace(
        traceId: traceId,
        loc: TraceLocation.t4,
        traceType: traceType,
        deviceCount: tmpSmartHomeDeviceMap.length);

    smartHomeStore.dispatch(DeviceMapUpdateAction(
        tmpSmartHomeDeviceMap,
        tmpAllCardViewModelMap,
        tmpSmallCardSortIdList,
        floorSet,
        tabFilterMap));

    gioTrack(DevListGioEvent.t3MiddleWareDispatchDeviceMapAction, <String, int>{
      DevListGioEvent.sizeAllDevList: tmpSmartHomeDeviceMap.length,
      DevListGioEvent.sizeSmallCard: -1,
      DevListGioEvent.sizeLargeCard: -1,
      DevListGioEvent.sizeCameraCard: -1,
      DevListGioEvent.sizeWholeHouseCard: -1,
      DevListGioEvent.curNaviTab:
          smartHomeStore.state.deviceState.deviceTabIndex ?? 999,
    });

    DevLogger.info(
        tag: 'SmartHome',
        msg:
            'guide-check DeviceMiddleware isExistDevice-Status:${smartHomeStore.state.deviceState.deviceStatus}');

    _setBrandUITypeToStorage(brandTypeModel);
    filterSupportControlDevice(smartHomeStore, voiceDevices, action.familyId);

    // 设备列表变化，更新运行中的设备信息
    smartHomeStore.dispatch(const UpdateRunningDeviceInfoAction());

    DevicePresenter.getInstance()
        .subscribeDeviceAttribute(familyId: action.familyId);

    _dealCameraDeviceUpdate(store, allCameraDevs, action.familyId);

    OfflineGioTrack().collectDeviceData();
  }

  Future<void> _sortAggDeviceWithServerData(
      Map<String, CardBaseViewModel> tmpAllCardViewModelMap,
      String deviceId) async {
    final AggSortResModel? sortResModel =
        await AggregationPresenter.queryAggSortData(<String>[deviceId]);
    smartHomeStore.dispatch(
        UpdateAggDeviceVmSortAction(sortResModel?.data ?? AggDeviceData()));
  }

  void _dealCameraDeviceUpdate(
      Store<SmartHomeState> store, Set<String> devs, String familyId) {
    CameraLiveCoordinator.instance.updatePresenters(devs);
    if (devs.isNotEmpty) {
      _dealFetchCameraMsgAction(store, devs);
    }
  }

  void _fetchCameraMsgAction(Store<SmartHomeState> store) {
    final Set<String> devs = <String>{};
    store.state.deviceState.allCardViewModelMap
        .forEach((String key, CardBaseViewModel value) {
      if (value is CameraDeviceCardViewModel) {
        devs.add(value.deviceId);
      }
    });
    if (devs.isNotEmpty) {
      _dealFetchCameraMsgAction(store, devs);
    }
  }

  void _dealFetchCameraMsgAction(Store<SmartHomeState> store, Set<String> devs,
      {String? familyId}) {
    familyId ??= smartHomeStore.state.familyState.familyId;
    CameraMsgPresenter.instance.getCameraMsgList(devs, (
      Map<String, CameraMsgVM> cameraMsgList,
    ) {
      if (cameraMsgList.isNotEmpty) {
        store.dispatch(UpdateCameraMsgAction(cameraMsgList));
      }
    }, familyId);
  }

  //处理设备楼层、房间、设备类型
  void _handleDeviceFilter(
      SmartHomeDeviceBasicInfo basicInfo,
      Map<String, Map<String, LinkedHashSet<String>>> filterMap,
      LinkedHashSet<String> allCategory,
      LinkedHashSet<String> shareCategorySet,
      Map<String, String> roomIdByFloorRoomNameMap) {
    if (basicInfo.deviceRole == '3') {
      return;
    }
    String floorName = basicInfo.roomInfo.floorName;
    String roomName = basicInfo.roomInfo.roomName;
    final String roomId = basicInfo.roomInfo.roomId;
    // 楼层名+房间名 -> 房间id
    roomIdByFloorRoomNameMap[floorName + roomName] = roomId;
    String categoryName = basicInfo.twoGroupingName;
    if (categoryName.isEmpty) {
      categoryName = basicInfo.categoryGrouping;
    }
    if (allCategory.isEmpty) {
      allCategory.add(SmartHomeConstant.deviceFilterSelectAll);
    }
    allCategory.add(categoryName);

    if (basicInfo.isSharedDevice) {
      roomName = SmartHomeConstant.shareDeviceFlag;
      floorName = '';
      if (shareCategorySet.isEmpty) {
        shareCategorySet.add(SmartHomeConstant.deviceFilterSelectAll);
      }
      shareCategorySet.add(categoryName);
      return;
    }

    if (floorName.isEmpty && roomName.isEmpty) {
      return;
    }

    final Map<String, LinkedHashSet<String>> roomListMap =
        filterMap[floorName] ?? <String, LinkedHashSet<String>>{};
    final LinkedHashSet<String> categorySet =
        roomListMap[roomName] ?? LinkedHashSet<String>();
    if (categorySet.isEmpty) {
      categorySet.add(SmartHomeConstant.deviceFilterSelectAll);
    }
    categorySet.add(categoryName);
    roomListMap[roomName] = categorySet;
    filterMap[floorName] = roomListMap;
  }

  void _handleWashAndEHeaterDevice(SmartHomeDevice smartHomeDevice) {
    if (DeviceType.washDevice(smartHomeDevice.basicInfo.typeId)) {
      DevicePresenter.getInstance().washDeviceList.add(smartHomeDevice);
      WashDeviceManager.getInstance()
          .loadWashDeviceBasicInfo(smartHomeDevice.basicInfo.typeId);
      WashDeviceManager.getInstance().loadWashDeviceUserPrefer(
          smartHomeDevice.basicInfo.typeId,
          User.getOauthDataSync()?.uhome_user_id ?? '',
          smartHomeDevice.basicInfo.deviceId,
          smartHomeDevice.basicInfo.ownerId);
    } else if (EHeatTimeOnOffPresenter.getInstance()
        .needUpdateDevice(smartHomeDevice)) {
      DevicePresenter.getInstance().eHeatAndGasDeviceList.add(smartHomeDevice);
      EHeatTimeOnOffPresenter.getInstance()
          .updateTimeOnOffStatus(smartHomeDevice,
              (String deviceId, SmartHomeDeviceAttribute attribute) {
        smartHomeStore
            .dispatch(UpdateCustomAttributeAction(deviceId, attribute));
      });
    }
  }

  /// 厨下净水
  void _handlePurifiedWaterDevice(SmartHomeDevice smartHomeDevice) {
    final String bigMiddleNumber =
        '${smartHomeDevice.basicInfo.bigClass}#${smartHomeDevice.basicInfo.middleClass}';
    if (bigMiddleNumber == '22#4') {
      DevicePresenter.getInstance()
          .purifiedWaterDeviceList
          .add(smartHomeDevice);
      final SmartHomeDeviceBasicInfo basicInfo = smartHomeDevice.basicInfo;
      PurifiedConsumablePresenter.getInstance()
          .updateConsumables(basicInfo.prodNo, (List<String> prodNoList) {
        smartHomeStore.dispatch(PurifiedConsumeLoadFinish(prodNoList));
      });
    }
  }

  void _handleDeviceCardViewModel(
      SmartHomeDevice smartHomeDevice,
      Map<String, Map<String, DeviceCardViewModel>> role3ViewModelMap,
      Map<String, CardBaseViewModel> tmpAllCardViewModelMap,
      Map<String, int> smallSortMap,
      List<String> tmpSmallCardSortIdList,
      Set<String> cameraDevs) {
    if (smartHomeDevice.basicInfo.deviceRole == '3') {
      /// 父设备id
      final String parentId = smartHomeDevice.basicInfo.parentId;

      /// 创建 cardViewModel
      final DeviceCardViewModel cardViewModel =
          _createDeviceCardViewModel(smartHomeDevice);

      if (cardViewModel.cardType == CardType.cameraCard) {
        cameraDevs.add(smartHomeDevice.basicInfo.deviceId);
      }
      tmpAllCardViewModelMap[cardViewModel.sortId()] = cardViewModel;
      final Map<String, DeviceCardViewModel> viewModelMap =
          role3ViewModelMap[parentId] ?? <String, DeviceCardViewModel>{};
      viewModelMap[cardViewModel.sortId()] = cardViewModel;
      role3ViewModelMap[parentId] = viewModelMap;

      /// 获取父设备
      final CardBaseViewModel? parentViewModel =
          tmpAllCardViewModelMap[parentId];
      if (parentViewModel is DeviceCardViewModel) {
        parentViewModel.childViewModelMap = viewModelMap;
      }
    } else if (smartHomeDevice.basicInfo.deviceRole != '3') {
      final DeviceCardViewModel cardVm =
          _createDeviceCardViewModel(smartHomeDevice);

      if (cardVm.cardType == CardType.cameraCard) {
        cameraDevs.add(smartHomeDevice.basicInfo.deviceId);
      }

      /// 附件设备赋值
      cardVm.childViewModelMap = role3ViewModelMap[cardVm.sortId()];

      tmpAllCardViewModelMap[cardVm.sortId()] = cardVm;

      smallSortMap[smartHomeDevice.basicInfo.deviceId] =
          smartHomeDevice.basicInfo.cardSort;
      tmpSmallCardSortIdList.add(smartHomeDevice.basicInfo.deviceId);
    }
  }

  SmartHomeDevice _createSmartHomeDevice(DeviceInfoModel value) {
    final SmartHomeDevice smartHomeDevice;
    if (smartHomeStore.state.deviceState.smartHomeDeviceMap[value.deviceId] !=
        null) {
      smartHomeStore.state.deviceState.smartHomeDeviceMap[value.deviceId]!
          .updateDeviceInfo(value);
      smartHomeDevice =
          smartHomeStore.state.deviceState.smartHomeDeviceMap[value.deviceId]!;
    } else {
      smartHomeDevice = SmartHomeDevice.fromDeviceInfoModel(value);
    }

    return smartHomeDevice;
  }

  DeviceCardViewModel _createDeviceCardViewModel(
      SmartHomeDevice smartHomeDevice) {
    final DeviceCardViewModel deviceCardViewModel;
    if (smartHomeStore.state.deviceState
            .allCardViewModelMap[smartHomeDevice.basicInfo.deviceId] !=
        null) {
      deviceCardViewModel = smartHomeStore.state.deviceState
              .allCardViewModelMap[smartHomeDevice.basicInfo.deviceId]!
          as DeviceCardViewModel;
      deviceCardViewModel.deviceCardType =
          convertFromCardStatus(smartHomeDevice.basicInfo.cardStatus);
    } else {
      deviceCardViewModel =
          DeviceCardFactory.viewModelFromSmartHomeDevice(smartHomeDevice);
    }

    if ((smartHomeDevice.basicInfo.deviceId == getLightAggregationKey() ||
            smartHomeDevice.basicInfo.deviceId == getCurtainAggregationKey() ||
            smartHomeDevice.basicInfo.deviceId ==
                getAggregationKey(AggregationSettingConstant.env_id)) &&
        deviceCardViewModel is AggregationBaseViewModel) {
      deviceCardViewModel.clearCache();
    }

    return deviceCardViewModel;
  }

  void _getBrandType(BrandTypeModel brandTypeModel, DeviceInfoModel value) {
    if (value.netType == 'device') {
      brandTypeModel.netDevice = true;
    }
  }

  void _setBrandUITypeToStorage(BrandTypeModel brandTypeModel) {
    Storage.putIntegerValue(
        SmartHomeConstant.netType, brandTypeModel.netDevice ? 1 : 0);
  }
}

List<String> handleCardSortList(
    Map<String, int> sortMap, List<String> keysList) {
  keysList
      .sort((String keys1, String keys2) => sortMap[keys1]! - sortMap[keys2]!);
  return keysList;
}

class BrandTypeModel {
  bool netDevice;

  BrandTypeModel({required this.netDevice});
}
