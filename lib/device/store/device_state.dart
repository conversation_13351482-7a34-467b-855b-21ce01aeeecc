import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_filter/device_filter_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';

import '../component_view_model/popup_component_view_model.dart';
import '../device_view_model/card_base_view_model.dart';

class DeviceState {
  bool isDeviceSmallCardUnfiltered = true;
  bool cardShowFloor = false;
  Map<String, SmartHomeDevice> smartHomeDeviceMap = <String, SmartHomeDevice>{};

  // 楼层名+房间名 -> 房间id， 全屋的时候 房间id = familyId
  // 用于通过楼层和房间名称快速查找对应的房间标识符
  Map<String, String> roomIdByFloorRoomNameMap = <String, String>{};

  List<String> excludeDeviceList = <String>[];

  /// 所有卡片viewModel集合<sortId, viewModel>
  Map<String, CardBaseViewModel> allCardViewModelMap =
      <String, CardBaseViewModel>{};

  /// 筛选后的小卡片顺序 <sortId>
  List<String> smallCardSortIdList = <String>[];

  /// 所有小卡片顺序 <sortId>
  List<String> allSmallCardSortIdList = <String>[];

  /// 报装报修、添加设备、新手礼包卡片集合，不参与拖拽排序
  List<String> unsortedIdList = <String>[];

  Map<TabFilterModel, List<String>> tabFilterMap =
      <TabFilterModel, List<String>>{};

  PopupComponentViewModel? popupComponentViewModel;

  String selectedRoom = '';
  String selectedFloor = '';
  String selectedDeviceCategory = '';
  String selectedRoomId = '';
  LinkedHashMap<String, DeviceFilterModel> deviceFilterMap =
      LinkedHashMap<String, DeviceFilterModel>();

  String filterText = SmartHomeConstant.filter;
  bool isFilterExpanded = false;
  int? deviceTabIndex;
  BuildContext? popUpContext;
  DeviceStatus deviceStatus = DeviceStatus.unknow;

  /// 洗衣机基础数据准备OK的typeId集合
  Set<String> washInfoPreparedTypeIdSet = <String>{};

  /// 洗衣机用户偏好数据准备OK的deviceId集合
  Set<String> washPreferPreparedDeviceIdSet = <String>{};
}

enum DeviceStatus { unknow, hasDevice, noDevice }
