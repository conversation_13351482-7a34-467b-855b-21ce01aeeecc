/*
 * 描述：一键冲洗大按钮
 * 作者：songFJ
 * 创建时间：2025/2/5
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class FlushLargeComponentViewModel extends ComponentThrottlerViewModel {
  final String text;
  final String icon;
  final String packageName;
  final bool isOn;
  final bool enable;
  final void Function(BuildContext context)? clickCallback;

  FlushLargeComponentViewModel(
      {required this.text,
      required this.isOn,
      required this.icon,
      required this.enable,
      this.packageName = 'smart_home',
      this.clickCallback,
      super.throttlerMillionSeconds = 1000,
      super.expandFlex});

  @override
  ComponentType componentType() {
    return ComponentType.flushLarge;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is FlushLargeComponentViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          icon == other.icon &&
          packageName == other.packageName &&
          isOn == other.isOn &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      icon.hashCode ^
      packageName.hashCode ^
      isOn.hashCode ^
      enable.hashCode;
}
