/*
 * 描述：标签类组件
 * 作者：songFJ
 * 创建时间：2025/2/5
 */

import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandLabelViewModel extends ComponentBaseViewModel {
  final String value;
  final String unit;
  final String desc;
  final bool enable;
  final bool isNumberValue;

  @override
  ComponentType componentType() {
    return ComponentType.expandLabel;
  }

  ExpandLabelViewModel(
      {this.value = '--',
      this.unit = '',
      this.desc = '',
      this.enable = true,
      super.expandFlex = 1}): isNumberValue = _isNumber(value);

  static bool _isNumber(String value) {
    final double? doubleValue = double.tryParse(value);
    return doubleValue != null;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is ExpandLabelViewModel &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          unit == other.unit &&
          desc == other.desc &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      value.hashCode ^
      unit.hashCode ^
      desc.hashCode ^
      enable.hashCode;
}
