/*
 * 描述：自适应宽度单文本开关类组件
 * 作者：songFJ
 * 创建时间：2025/2/10
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandSwitchTextViewModel extends ComponentThrottlerViewModel {
  final String text;
  final double textFontSize;
  final bool isOn;
  final bool enable;
  final Alignment alignment;
  final TextAlign textAlign;
  final void Function(BuildContext context)? clickCallback;

  ExpandSwitchTextViewModel(
      {required this.text,
      this.textFontSize = 14,
      required this.isOn,
      required this.enable,
      this.alignment = Alignment.center,
      this.textAlign = TextAlign.center,
      this.clickCallback,
      super.throttlerMillionSeconds,
      super.expandFlex});

  @override
  ComponentType componentType() {
    return ComponentType.expandSwitchText;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is ExpandSwitchTextViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          textFontSize == other.textFontSize &&
          isOn == other.isOn &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      textFontSize.hashCode ^
      isOn.hashCode ^
      enable.hashCode;
}
