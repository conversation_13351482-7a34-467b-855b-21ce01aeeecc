/*
 * 描述：自适应弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/3/12
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

import 'component_throttler_view_model.dart';

class ExpandPopupTextTextUnitViewModel extends ComponentThrottlerViewModel {
  final String text;
  final String unit;
  final String desc;
  final bool isOn;
  final bool enable;
  final void Function(BuildContext context)? clickCallback;

  ExpandPopupTextTextUnitViewModel(
      {required this.text,
      this.unit = '',
      required this.desc,
      this.isOn = false,
      required this.enable,
      this.clickCallback,
      super.expandFlex,
      super.popupComponentViewModel});

  @override
  ComponentType componentType() {
    return ComponentType.expandPopupTextTextUnit;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ExpandPopupTextTextUnitViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          unit == other.unit &&
          desc == other.desc &&
          isOn == other.isOn &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      unit.hashCode ^
      desc.hashCode ^
      isOn.hashCode ^
      enable.hashCode;
}
