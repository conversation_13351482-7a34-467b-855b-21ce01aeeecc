import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

// 区间颜色，前开后闭
class PercentageColorRule {
  final int start; // 区间开始
  final int end; // 区间结束
  final Color color; // 区间颜色

  const PercentageColorRule(this.start, this.end, this.color);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PercentageColorRule &&
          runtimeType == other.runtimeType &&
          start == other.start &&
          end == other.end &&
          color == other.color;

  @override
  int get hashCode => start.hashCode ^ end.hashCode ^ color.hashCode;
}

// 圆环百分比组件
class CircularPercentageModel extends ComponentBaseViewModel {
  final String percentage;
  final int sortId;
  final String title;
  final String subTitle;
  final bool enable;
  final String package;
  final void Function(BuildContext? context)? clickCallback;

  CircularPercentageModel(
      {this.percentage = '',
      this.sortId = 0,
      this.title = '',
      this.subTitle = '',
      this.package = SmartHomeConstant.package,
      this.enable = true,
      this.clickCallback,
      super.expandFlex});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is CircularPercentageModel &&
          runtimeType == other.runtimeType &&
          percentage == other.percentage &&
          sortId == other.sortId &&
          title == other.title &&
          subTitle == other.subTitle &&
          enable == other.enable &&
          package == other.package;

  @override
  int get hashCode =>
      super.hashCode ^
      percentage.hashCode ^
      sortId.hashCode ^
      title.hashCode ^
      subTitle.hashCode ^
      enable.hashCode ^
      package.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.circularPercentage;
  }
}
