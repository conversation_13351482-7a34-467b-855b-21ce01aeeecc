/*
 * 描述：固定宽度弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/2/7
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class FixedPopupTextViewModel extends ComponentThrottlerViewModel {
  final String text;
  final bool enable;
  final bool isOn;
  final Alignment alignment;
  final TextAlign textAlign;
  final void Function(BuildContext context)? clickCallback;

  FixedPopupTextViewModel(
      {required this.text,
      required this.enable,
      this.isOn = false,
      this.alignment = Alignment.center,
      this.textAlign = TextAlign.center,
      this.clickCallback,
        super.throttlerMillionSeconds,
      super.popupComponentViewModel});

  @override
  ComponentType componentType() {
    return ComponentType.fixedTextPopup;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is FixedPopupTextViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          enable == other.enable &&
          isOn == other.isOn &&
          alignment == other.alignment &&
          textAlign == other.textAlign;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      enable.hashCode ^
      isOn.hashCode ^
      alignment.hashCode ^
      textAlign.hashCode;
}
