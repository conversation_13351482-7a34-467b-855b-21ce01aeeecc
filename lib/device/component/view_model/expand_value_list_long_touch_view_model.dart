import 'package:device_utils/compare/compare.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

/// 支持长按的音量、温度等调节组件
class ExpandValueListLongTouchViewModel extends ComponentBaseViewModel {
  final List<String> valueList;
  final int currentIndex;
  final String? unit;
  final bool enable;
  final String preValueIcon;
  final String nextValueIcon;
  final String packageName;

  // 是否需要更新按钮状态，默认为true,false时按钮一直是可点击状态
  final bool needUpdateBtnState;
  final double valueTextFontSize;
  final FontWeight valueTextFontWeight;
  final Future<bool> Function(bool isLongTouchEvent,bool isFirstTouchRun)? checkContinue;

  final int periodicTime;// 长按触发间隔的时间
  final void Function(BuildContext context)? valueListEmptyCallback;
  final void Function(BuildContext context)? noMorePreCallback;
  final void Function(BuildContext context)? noMoreNextCallback;
  final void Function(BuildContext context, String value, int index)?
      valueChangeCallback;
  //isLongTouchEvent : true 表示长按事件，false表示点击事件
  final void Function(BuildContext context,bool isLongTouchEvent,bool isFirstTouchRun)? btnClickPreCallback;
  final void Function(BuildContext context,bool isLongTouchEvent,bool isFirstTouchRun)? btnClickNextCallback;

  @override
  ComponentType componentType() {
    return ComponentType.expandValueLongTouchList;
  }

  ExpandValueListLongTouchViewModel(
      {required this.valueList,
      required this.currentIndex,
      required this.enable,
      this.unit,
      this.preValueIcon = 'assets/icons/pre_icon.webp',
      this.nextValueIcon = 'assets/icons/next_icon.webp',
      this.packageName = 'smart_home',
      this.needUpdateBtnState = true,
      this.valueTextFontSize = 24,
      this.valueTextFontWeight = FontWeight.w600,
      this.periodicTime=150,
      this.checkContinue,
      this.valueListEmptyCallback,
      this.noMorePreCallback,
      this.noMoreNextCallback,
      this.valueChangeCallback,
      this.btnClickPreCallback,
      this.btnClickNextCallback,
      super.expandFlex});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is ExpandValueListLongTouchViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(valueList, other.valueList) &&
          currentIndex == other.currentIndex &&
          unit == other.unit &&
          enable == other.enable &&
          preValueIcon == other.preValueIcon &&
          nextValueIcon == other.nextValueIcon &&
          packageName == other.packageName;

  @override
  int get hashCode =>
      super.hashCode ^
      listHashCode(valueList) ^
      currentIndex.hashCode ^
      unit.hashCode ^
      enable.hashCode ^
      preValueIcon.hashCode ^
      nextValueIcon.hashCode ^
      packageName.hashCode;
}
