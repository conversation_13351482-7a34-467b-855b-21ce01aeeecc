/*
 * 描述：+-调节组件
 * 作者：songFJ
 * 创建时间：2025/3/25
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ValueModel {
  final String value;
  final String unit;

  ValueModel({required this.value, required this.unit});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValueModel &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          unit == other.unit;

  @override
  int get hashCode => value.hashCode ^ unit.hashCode;
}

class ExpandAirConditionValueListViewModel extends ComponentBaseViewModel {
  final List<ValueModel> valueList;
  final int currentIndex;
  final bool enable;
  final String preValueIcon;
  final String nextValueIcon;
  final String packageName;
  final Future<bool> Function()? checkContinue;
  final void Function(BuildContext context)? valueListEmptyCallback;
  final void Function(BuildContext context)? noMorePreCallback;
  final void Function(BuildContext context)? noMoreNextCallback;
  final void Function(BuildContext context, String value, int index)?
      valueChangeCallback;

  @override
  ComponentType componentType() {
    return ComponentType.expandAirConditionValueList;
  }

  ExpandAirConditionValueListViewModel(
      {required this.valueList,
      required this.currentIndex,
      required this.enable,
      this.preValueIcon = 'assets/icons/pre_icon.webp',
      this.nextValueIcon = 'assets/icons/next_icon.webp',
      this.packageName = 'smart_home',
      this.checkContinue,
      this.valueListEmptyCallback,
      this.noMorePreCallback,
      this.noMoreNextCallback,
      this.valueChangeCallback,
      super.expandFlex});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is ExpandAirConditionValueListViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(valueList, other.valueList) &&
          currentIndex == other.currentIndex &&
          enable == other.enable &&
          preValueIcon == other.preValueIcon &&
          nextValueIcon == other.nextValueIcon &&
          packageName == other.packageName;

  @override
  int get hashCode =>
      super.hashCode ^
      listHashCode(valueList) ^
      currentIndex.hashCode ^
      enable.hashCode ^
      preValueIcon.hashCode ^
      nextValueIcon.hashCode ^
      packageName.hashCode;
}
