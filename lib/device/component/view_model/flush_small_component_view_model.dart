/*
 * 描述：一键冲洗小按钮
 * 作者：songFJ
 * 创建时间：2025/2/5
 */
import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class FlushSmallComponentViewModel extends ComponentThrottlerViewModel {
  final String icon;
  final String packageName;
  final bool isOn;
  final bool enable;
  final void Function(BuildContext context)? clickCallback;

  @override
  ComponentType componentType() {
    return ComponentType.flushSmall;
  }

  FlushSmallComponentViewModel(
      {required this.icon,
      this.packageName = 'smart_home',
      required this.isOn,
      required this.enable,
      super.throttlerMillionSeconds,
      this.clickCallback});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is FlushSmallComponentViewModel &&
          runtimeType == other.runtimeType &&
          icon == other.icon &&
          packageName == other.packageName &&
          isOn == other.isOn &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      icon.hashCode ^
      packageName.hashCode ^
      isOn.hashCode ^
      enable.hashCode;
}
