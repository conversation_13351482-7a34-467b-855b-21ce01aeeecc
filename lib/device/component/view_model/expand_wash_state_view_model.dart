/*
 * 描述：自适应洗衣机状态组件
 * 作者：songFJ
 * 创建时间：2025/2/11
 */

import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandWashStateViewModel extends ComponentBaseViewModel {
  final String name;
  final String label;
  final String value;
  final String unit;
  final bool enable;
  final double paddingLeft;

  ExpandWashStateViewModel(
      {required this.name,
      required this.label,
      required this.value,
      required this.unit,
      required this.enable,
      this.paddingLeft = 0,
      super.expandFlex});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ExpandWashStateViewModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          label == other.label &&
          value == other.value &&
          unit == other.unit &&
          enable == other.enable &&
          paddingLeft == other.paddingLeft;

  @override
  int get hashCode =>
      super.hashCode ^
      name.hashCode ^
      label.hashCode ^
      value.hashCode ^
      unit.hashCode ^
      enable.hashCode ^
      paddingLeft.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.expandWashState;
  }
}
