/*
 * 描述：固定宽度弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/2/6
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

import 'component_throttler_view_model.dart';

class FixedPopupIconTextViewModel extends ComponentThrottlerViewModel {
  final String icon;
  final String packageName;
  final String text;
  final bool enable;
  final bool isOn;
  final void Function(BuildContext context)? clickCallback;

  FixedPopupIconTextViewModel(
      {required this.icon,
      this.packageName = 'smart_home',
      required this.text,
      required this.enable,
      this.isOn = false,
      this.clickCallback,
      super.throttlerMillionSeconds,
      super.popupComponentViewModel});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is FixedPopupIconTextViewModel &&
          runtimeType == other.runtimeType &&
          icon == other.icon &&
          packageName == other.packageName &&
          text == other.text &&
          enable == other.enable &&
          isOn == other.isOn;

  @override
  int get hashCode =>
      super.hashCode ^
      icon.hashCode ^
      packageName.hashCode ^
      text.hashCode ^
      enable.hashCode ^
      isOn.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.fixedIconTextPopup;
  }
}
