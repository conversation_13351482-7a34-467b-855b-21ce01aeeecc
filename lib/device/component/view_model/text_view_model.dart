/*
 * 描述：文本
 * 作者：songFJ
 * 创建时间：2025/3/26
 */

import 'dart:ui';

import '../../component_view_model/component_view_model.dart';

class TextViewModel extends ComponentBaseViewModel {
  final String text;
  final double fontSize;
  final FontWeight fontWeight;
  final Color textColor;
  final double? height;
  final int? maxLine;

  TextViewModel(
      {required this.text,
      required this.fontSize,
      this.fontWeight = FontWeight.w400,
      this.height,
      this.maxLine,
      required this.textColor});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is TextViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          fontSize == other.fontSize &&
          fontWeight == other.fontWeight &&
          textColor == other.textColor &&
          height == other.height &&
          maxLine == other.maxLine;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      fontSize.hashCode ^
      fontWeight.hashCode ^
      textColor.hashCode ^
      height.hashCode ^
      maxLine.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.text;
  }
}
