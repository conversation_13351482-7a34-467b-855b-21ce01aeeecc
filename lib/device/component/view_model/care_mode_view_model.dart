/*
 * 描述：care模式选择
 * 作者：songFJ
 * 创建时间：2025/2/10
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class CareModeSelectViewModel extends ComponentBaseViewModel {
  final String name;
  final List<String> modeList;
  final int selectedIndex;
  final bool enable;
  final void Function(BuildContext context, int index)? modeSelectCallback;

  CareModeSelectViewModel(
      {this.name = 'Care模式',
      required this.modeList,
      required this.selectedIndex,
      required this.enable,
      this.modeSelectCallback});

  @override
  ComponentType componentType() {
    return ComponentType.careModeSelect;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is CareModeSelectViewModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          listEquals(modeList, other.modeList) &&
          selectedIndex == other.selectedIndex &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      name.hashCode ^
      listHashCode(modeList) ^
      selectedIndex.hashCode ^
      enable.hashCode;
}

class CareModeItemViewModel extends ComponentBaseViewModel {
  final String text;
  final bool isOn;
  final bool enable;
  final void Function(BuildContext context)? itemClickCallback;

  CareModeItemViewModel(
      {required this.text,
      required this.isOn,
      required this.enable,
      this.itemClickCallback});

  @override
  ComponentType componentType() {
    return ComponentType.careModeItem;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is CareModeItemViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          isOn == other.isOn &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^ text.hashCode ^ isOn.hashCode ^ enable.hashCode;
}
