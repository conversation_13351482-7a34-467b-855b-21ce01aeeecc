/*
 * 描述：自适应空白占位组件
 * 作者：songFJ
 * 创建时间：2025/2/8
 */

import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandEmptyViewModel extends ComponentBaseViewModel {
  @override
  ComponentType componentType() {
    return ComponentType.expandEmpty;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ExpandEmptyViewModel &&
          runtimeType == other.runtimeType;

  @override
  int get hashCode => super.hashCode;

  ExpandEmptyViewModel({super.expandFlex});
}
