import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandSwitchIconTextMarkViewModel extends ExpandSwitchIconTextViewModel {
  final String badge;

  ExpandSwitchIconTextMarkViewModel(
      {required super.icon,
      required super.text,
      required super.enable,
      required super.isOn,
      required this.badge,
      super.packageName,
      super.clickCallback,
      super.throttlerMillionSeconds,
      super.expandFlex});

  @override
  ComponentType componentType() {
    return ComponentType.expandIconTextMarkSwitch;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ExpandSwitchIconTextMarkViewModel &&
          runtimeType == other.runtimeType &&
          badge == other.badge;

  @override
  int get hashCode => super.hashCode ^ badge.hashCode;
}
