import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

import 'circular_percentage_view_model.dart';

/// 净水滤芯耗材viewModel
class PurifiedGridViewModel extends ComponentBaseViewModel {
  PurifiedGridViewModel({
    required this.purifiedList,
  });

  /// [purifiedList] 净水滤芯耗材列表
  List<CircularPercentageModel> purifiedList;

  @override
  ComponentType componentType() {
    return ComponentType.purified_grid;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is PurifiedGridViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(purifiedList, other.purifiedList);

  @override
  int get hashCode => super.hashCode ^ listHashCode(purifiedList);

  @override
  String toString() {
    return 'PurifiedGridViewModel{purifiedList: $purifiedList}';
  }
}
