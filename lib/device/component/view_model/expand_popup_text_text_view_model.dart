/*
 * 描述：自适应宽度文本弹出框类组件
 * 作者：songFJ
 * 创建时间：2025/2/13
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandPopupTextTextViewModel extends ComponentThrottlerViewModel {
  final String text;
  final String desc;
  final bool isOn;
  final bool enable;
  final CrossAxisAlignment crossAxisAlignment;
  final void Function(BuildContext context)? clickCallback;

  ExpandPopupTextTextViewModel(
      {required this.text,
      required this.desc,
      this.isOn = false,
      required this.enable,
      this.crossAxisAlignment = CrossAxisAlignment.start,
      this.clickCallback,
      super.throttlerMillionSeconds,
      super.expandFlex,
      super.popupComponentViewModel});

  @override
  ComponentType componentType() {
    return ComponentType.expandTextText;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ExpandPopupTextTextViewModel &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          desc == other.desc &&
          isOn == other.isOn &&
          crossAxisAlignment == other.crossAxisAlignment &&
          enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      desc.hashCode ^
      isOn.hashCode ^
      crossAxisAlignment.hashCode ^
      enable.hashCode;
}
