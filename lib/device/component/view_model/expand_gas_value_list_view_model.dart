/*
 * 描述：燃热温度控制组件
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';

class ExpandGasValueListViewModel extends ComponentBaseViewModel {
  ExpandGasValueListViewModel(
      {required this.valueList,
      required this.enable,
      required this.egwOn,
      required this.egwTitle,
      required this.currentValue,
      this.safetyLockStatus = false,
      this.showBurningAlarm = false,
      this.comeToFirstAlertMsg = '已是最低温度',
      this.comeToEndAlertMsg = '已是最高温度',
      this.unit = '℃',
      this.preValueIcon = 'assets/images/temp_minus_icon.webp',
        this.nextValueIcon = 'assets/images/temp_add_icon.webp',
        this.triggerAlarm,
        this.valueChangeCallback,
        this.burningAlarm,
        this.clickCallback,
        this.checkContinue,
        super.expandFlex});

  bool enable = true;
  bool egwOn = false;
  String egwTitle;
  final String comeToFirstAlertMsg;
  final String comeToEndAlertMsg;
  final List<double> valueList;
  final double currentValue;
  final bool showBurningAlarm;
  final bool safetyLockStatus;
  String unit;
  final String preValueIcon;
  final String nextValueIcon;
  final void Function(BuildContext? context, String value, int index)?
  triggerAlarm;
  final void Function(BuildContext? context, String value, int index)?
  valueChangeCallback;
  final void Function(BuildContext? context)? burningAlarm;
  final void Function(BuildContext? context)? clickCallback;
  final Future<bool> Function(SmartHomeDeviceAttribute? attribute)?
  checkContinue;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is ExpandGasValueListViewModel &&
          runtimeType == other.runtimeType &&
              enable == other.enable &&
              egwOn == other.egwOn &&
              egwTitle == other.egwTitle &&
              comeToFirstAlertMsg == other.comeToFirstAlertMsg &&
              comeToEndAlertMsg == other.comeToEndAlertMsg &&
              listEquals(valueList, other.valueList) &&
              currentValue == other.currentValue &&
              showBurningAlarm == other.showBurningAlarm &&
              safetyLockStatus == other.safetyLockStatus &&
              unit == other.unit &&
              preValueIcon == other.preValueIcon &&
              nextValueIcon == other.nextValueIcon;

  @override
  int get hashCode =>
      super.hashCode ^
      enable.hashCode ^
      egwOn.hashCode ^
      egwTitle.hashCode ^
      comeToFirstAlertMsg.hashCode ^
      comeToEndAlertMsg.hashCode ^
      listHashCode(valueList) ^
      currentValue.hashCode ^
      showBurningAlarm.hashCode ^
      safetyLockStatus.hashCode ^
      unit.hashCode ^
      preValueIcon.hashCode ^
      nextValueIcon.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.expandGasValueList;
  }
}
