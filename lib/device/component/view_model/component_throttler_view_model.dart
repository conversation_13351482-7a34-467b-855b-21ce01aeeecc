/*
 * 描述：只第一次生效时长控制
 * 作者：songFJ
 * 创建时间：2025/2/13
 */

import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ComponentThrottlerViewModel extends ComponentBaseViewModel {
  final int throttlerMillionSeconds;

  @override
  ComponentType componentType() {
    return ComponentType.throttler;
  }

  ComponentThrottlerViewModel(
      {this.throttlerMillionSeconds = 1000,
      super.expandFlex,
      super.popupComponentViewModel});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ComponentThrottlerViewModel &&
          runtimeType == other.runtimeType &&
          throttlerMillionSeconds == other.throttlerMillionSeconds;

  @override
  int get hashCode => super.hashCode ^ throttlerMillionSeconds.hashCode;
}
