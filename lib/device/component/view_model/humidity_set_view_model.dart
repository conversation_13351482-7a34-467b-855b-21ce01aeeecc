/*
 * 描述：新风机湿度设置
 * 作者：songFJ
 * 创建时间：2025/3/12
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class HumiditySetViewModel extends ComponentBaseViewModel {
  final String humidityTitle;
  final bool humidityOn;
  final int humiditySetCurrentValue;
  final int humiditySetMinValue;
  final int humiditySetMaxValue;
  final int humiditySetStep;
  final bool humidityOnEnable;
  final bool humiditySetEnable;

  final Future<bool> Function()? checkHumidityOnOffContinue;
  final Future<bool> Function()? checkHumiditySetContinue;
  final void Function(BuildContext context, bool humidityOn)?
      humidityOnOffCallback;
  final void Function(BuildContext context, int humiditySetValue)?
      humiditySetCallback;

  HumiditySetViewModel({
    required this.humidityTitle,
    required this.humidityOn,
    required this.humiditySetCurrentValue,
    required this.humiditySetMinValue,
    required this.humiditySetMaxValue,
    required this.humiditySetStep,
    this.humidityOnEnable = false,
    this.humiditySetEnable = false,
    this.checkHumidityOnOffContinue,
    this.checkHumiditySetContinue,
    this.humidityOnOffCallback,
    this.humiditySetCallback,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is HumiditySetViewModel &&
          runtimeType == other.runtimeType &&
          humidityTitle == other.humidityTitle &&
          humidityOn == other.humidityOn &&
          humiditySetCurrentValue == other.humiditySetCurrentValue &&
          humiditySetMinValue == other.humiditySetMinValue &&
          humiditySetMaxValue == other.humiditySetMaxValue &&
          humiditySetStep == other.humiditySetStep &&
          humidityOnEnable == other.humidityOnEnable &&
          humiditySetEnable == other.humiditySetEnable;

  @override
  int get hashCode =>
      super.hashCode ^
      humidityTitle.hashCode ^
      humidityOn.hashCode ^
      humiditySetCurrentValue.hashCode ^
      humiditySetMinValue.hashCode ^
      humiditySetMaxValue.hashCode ^
      humiditySetStep.hashCode ^
      humidityOnEnable.hashCode ^
      humiditySetEnable.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.humiditySet;
  }
}
