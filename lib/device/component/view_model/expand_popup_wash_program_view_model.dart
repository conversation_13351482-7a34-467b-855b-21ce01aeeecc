/*
 * 描述：自适应洗衣机程序弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/2/11
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class ExpandPopupWashProgramViewModel extends ComponentThrottlerViewModel {
  final String name;
  final String value;
  final String unit;
  final bool enable;
  final bool isOn;
  final Alignment alignment;

  final void Function(BuildContext context)? clickCallback;

  ExpandPopupWashProgramViewModel(
      {required this.enable,
      required this.name,
      required this.value,
      required this.unit,
      this.isOn = false,
      this.alignment = Alignment.centerLeft,
      this.clickCallback,
      super.throttlerMillionSeconds,
      super.expandFlex,
      super.popupComponentViewModel});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is ExpandPopupWashProgramViewModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          value == other.value &&
          unit == other.unit &&
          enable == other.enable &&
          isOn == other.isOn &&
          alignment == other.alignment;

  @override
  int get hashCode =>
      super.hashCode ^
      name.hashCode ^
      value.hashCode ^
      unit.hashCode ^
      enable.hashCode ^
      isOn.hashCode ^
      alignment.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.expandPopupWashProgram;
  }
}
