import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/debounce.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component/widget/debounce_throttler/long_touch_widget.dart';

import '../../../store/smart_home_store.dart';
import '../view_model/expand_value_list_long_touch_view_model.dart';

///支持长按的数值 加减类型组件
class ExpandValueListLongTouchWidget extends StatefulWidget {
  const ExpandValueListLongTouchWidget({super.key, required this.viewModel});

  final ExpandValueListLongTouchViewModel viewModel;

  @override
  State<ExpandValueListLongTouchWidget> createState() =>
      _ExpandValueListLongTouchWidgetState();
}

class _ExpandValueListLongTouchWidgetState
    extends State<ExpandValueListLongTouchWidget> {
  bool _manual = false;
  bool _preEnable = true;
  bool _nextEnable = true;
  int _currentIndex = 0;
  String? _unit;
  String _currentValue = '--';
  bool _valueListIsEmpty = false;

  final Debouncer _clickDebouncer = Debouncer(milliseconds: 500);
  final Debouncer _setStateDebouncer = Debouncer(milliseconds: 2000);

  @override
  void dispose() {
    _clickDebouncer.dispose();
    _setStateDebouncer.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _updateDataState();
    _updateBtnState();
  }

  void _updateDataState() {
    _valueListIsEmpty = widget.viewModel.valueList.isEmpty;
    if (!_valueListIsEmpty) {
      _unit = widget.viewModel.unit;
      if (widget.viewModel.currentIndex < 0 ||
          widget.viewModel.currentIndex >= widget.viewModel.valueList.length) {
        _currentIndex = 0;
      } else {
        _currentIndex = widget.viewModel.currentIndex;
      }

      _currentValue = widget.viewModel.valueList[_currentIndex];
    } else {
      _unit = null;
      _currentIndex = 0;
      _currentValue = '--';
    }
  }

  @override
  void didUpdateWidget(covariant ExpandValueListLongTouchWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!_manual) {
      setState(() {
        _updateDataState();
        _updateBtnState();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: widget.viewModel.expandFlex,
      child: Opacity(
        opacity: (widget.viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: GestureDetector(
          onTap: () {},
          child: Container(
            decoration: BoxDecoration(
              color: AppSemanticColors.background.secondary,
              borderRadius: BorderRadius.circular(12),
            ),
            height: 52,
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                _btnWidget(
                    enable: !widget.viewModel.enable ||
                        _preEnable ||
                        smartHomeStore.state.isEditState,
                    icon: widget.viewModel.preValueIcon,
                    packageName: widget.viewModel.packageName,
                    clickCallback: (bool isLongTouchEvent,bool isFirstTouchRun) {
                      if (_valueListIsEmpty) {
                        widget.viewModel.valueListEmptyCallback?.call(context);
                        return;
                      }

                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call(isLongTouchEvent,isFirstTouchRun)
                            .then((bool pass) {
                          if (pass) {
                            _preClick(isLongTouchEvent,isFirstTouchRun);
                          }
                        });
                      } else {
                        _preClick(isLongTouchEvent,isFirstTouchRun);
                      }
                    }),
                RichText(
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  text: SmartHomeSpan.textSpan(
                    text: _floatValue(_currentValue)
                        ? _intPartValue(_currentValue)
                        : _currentValue,
                    fontSize: widget.viewModel.valueTextFontSize,
                    fontWeight: widget.viewModel.valueTextFontWeight,
                    color: AppSemanticColors.item.primary,
                    children: <InlineSpan>[
                      if (_floatValue(_currentValue))
                        SmartHomeSpan.textSpan(
                            text: _floatPartValue(_currentValue),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppSemanticColors.item.primary),
                      if (_unit != null)
                        const WidgetSpan(
                          child: SizedBox(
                            width: 4,
                          ),
                        ),
                      if (_unit != null)
                        SmartHomeSpan.textSpan(
                          text: _unit!,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppSemanticColors.item.primary,
                        ),
                    ],
                  ),
                ),
                _btnWidget(
                    enable: !widget.viewModel.enable ||
                        _nextEnable ||
                        smartHomeStore.state.isEditState,
                    icon: widget.viewModel.nextValueIcon,
                    packageName: widget.viewModel.packageName,
                    clickCallback: (bool isLongTouchEvent,bool isFirstTouchRun) {
                      if (_valueListIsEmpty) {
                        widget.viewModel.valueListEmptyCallback?.call(context);
                        return;
                      }

                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call(isLongTouchEvent,isFirstTouchRun)
                            .then((bool pass) {
                          if (pass) {
                            _nextClick(isLongTouchEvent,isFirstTouchRun);
                          }
                        });
                      } else {
                        _nextClick(isLongTouchEvent,isFirstTouchRun);
                      }
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _preClick(bool isLongTouchEvent,bool isFirstTouchRun) {
    _manual = true;
    if (!_preEnable) {
      widget.viewModel.noMorePreCallback?.call(context);
    } else {
      _valueChange(true);

      // pre按钮点击回调
      widget.viewModel.btnClickPreCallback?.call(context,isLongTouchEvent,isFirstTouchRun);
    }
    _clickDebouncer.run(() {
      widget.viewModel.valueChangeCallback
          ?.call(context, _currentValue, _currentIndex);
    });
    _setStateDebouncer.run(() {
      setState(() {
        _manual = false;
        _updateDataState();
        _updateBtnState();
      });
    });
  }

  void _nextClick(bool isLongTouchEvent,bool isFirstTouchRun) {
    _manual = true;
    if (!_nextEnable) {
      widget.viewModel.noMoreNextCallback?.call(context);
    } else {
      _valueChange(false);

      // next按钮点击回调
      widget.viewModel.btnClickNextCallback?.call(context,isLongTouchEvent,isFirstTouchRun);
    }
    _clickDebouncer.run(() {
      widget.viewModel.valueChangeCallback
          ?.call(context, _currentValue, _currentIndex);
    });
    _setStateDebouncer.run(() {
      setState(() {
        _manual = false;
        _updateDataState();
        _updateBtnState();
      });
    });
  }

  Widget _btnWidget(
      {required bool enable,
      required String icon,
      required String packageName,
      required void Function(bool isLongTouchEvent,bool isFirstTouchRun) clickCallback,}) {
    return LongTouchWidget(
      periodicTime: widget.viewModel.periodicTime,
      clickCallback: (_) {
        clickCallback.call(false,true);
      },
      longTouchCallback: (BuildContext context,bool isFirstLonTouchRun) {
        clickCallback(true,isFirstLonTouchRun);
      },
      child: SizedBox(
        width: 52,
        height: double.infinity,
        child: Opacity(
          opacity: enable ? ComponentOpacity.enable : ComponentOpacity.disable,
          child: ClickEffectCircularWidget(
            enable: enable,
            isOn: false,
            child: Align(
              child: Image.asset(
                icon,
                package: packageName,
                width: 16,
                height: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool _floatValue(String value) {
    return value.contains('.');
    // final double? doubleValue = double.tryParse(value);
    // return value.contains('.') && doubleValue != null;
  }

  String _intPartValue(String value) {
    final int index = value.indexOf('.');
    return value.substring(0, index);
  }

  String _floatPartValue(String value) {
    final int index = value.indexOf('.');
    return value.substring(index);
  }

  void _valueChange(bool pre) {
    if (pre) {
      _currentIndex -= 1;
      if (_currentIndex >= 0) {
        _currentValue = widget.viewModel.valueList[_currentIndex];

        setState(() {
          _updateBtnState();
        });
      }
    } else {
      _currentIndex += 1;
      if (_currentIndex < widget.viewModel.valueList.length) {
        _currentValue = widget.viewModel.valueList[_currentIndex];

        setState(() {
          _updateBtnState();
        });
      } else {}
    }
  }

  void _updateBtnState() {
    if (_currentIndex == 0) {
      _setPreBtnEnable(false);
    } else {
      _setPreBtnEnable(true);
    }
    if (_currentIndex == widget.viewModel.valueList.length - 1) {
      _setNextBtnEnable(false);
    } else {
      _setNextBtnEnable(true);
    }
  }

  void _setPreBtnEnable(bool enable) {
    if (widget.viewModel.needUpdateBtnState) {
      _preEnable = enable;
    } else {
      _preEnable = true;
    }
  }

  void _setNextBtnEnable(bool enable) {
    if (widget.viewModel.needUpdateBtnState) {
      _nextEnable = enable;
    } else {
      _nextEnable = true;
    }
  }
}
