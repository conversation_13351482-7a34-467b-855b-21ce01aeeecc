/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2025/2/8
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/debounce.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/expand_value_list_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';

import '../../../store/smart_home_store.dart';

class ExpandValueListWidget extends StatefulWidget {
  const ExpandValueListWidget({super.key, required this.viewModel});

  final ExpandValueListViewModel viewModel;

  @override
  State<ExpandValueListWidget> createState() => _ExpandValueListWidgetState();
}

class _ExpandValueListWidgetState extends State<ExpandValueListWidget> {
  bool _manual = false;
  bool _preEnable = true;
  bool _nextEnable = true;
  int _currentIndex = 0;
  String _unit = '';
  String _currentValue = '--';
  bool _valueListIsEmpty = false;

  final Debouncer _clickDebouncer = Debouncer(milliseconds: 500);
  final Debouncer _setStateDebouncer = Debouncer(milliseconds: 2000);

  @override
  void dispose() {
    _clickDebouncer.dispose();
    _setStateDebouncer.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _updateDataState();
  }

  void _updateDataState() {
    _valueListIsEmpty = widget.viewModel.valueList.isEmpty;
    if (!_valueListIsEmpty) {
      _unit = widget.viewModel.unit;
      if (widget.viewModel.currentIndex < 0 ||
          widget.viewModel.currentIndex >= widget.viewModel.valueList.length) {
        _currentIndex = 0;
      } else {
        _currentIndex = widget.viewModel.currentIndex;
      }

      _currentValue = widget.viewModel.valueList[_currentIndex];
      if (_currentIndex == 0) {
        _preEnable = false;
      }
      if (_currentIndex == widget.viewModel.valueList.length - 1) {
        _nextEnable = false;
      }
    } else {
      _unit = '';
      _currentIndex = 0;
      _currentValue = '--';
    }
  }

  @override
  void didUpdateWidget(covariant ExpandValueListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!_manual) {
      setState(() {
        _updateDataState();
        _updateBtnState();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: widget.viewModel.expandFlex,
      child: Opacity(
        opacity: (widget.viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable : ComponentOpacity.disable,
        child: GestureDetector(
          onTap: () {},
          child: Container(
            decoration: BoxDecoration(
              color: AppSemanticColors.background.secondary,
              borderRadius: BorderRadius.circular(12),
            ),
            height: 52,
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                _btnWidget(
                    enable: !widget.viewModel.enable ||
                        _preEnable ||
                        smartHomeStore.state.isEditState,
                    icon: widget.viewModel.preValueIcon,
                    packageName: widget.viewModel.packageName,
                    clickCallback: () {
                      if (_valueListIsEmpty) {
                        widget.viewModel.valueListEmptyCallback?.call(context);
                        return;
                      }

                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call()
                            .then((bool pass) {
                          if (pass) {
                            _preClick();
                          }
                        });
                      } else {
                        _preClick();
                      }
                    }),
                RichText(
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  text: SmartHomeSpan.textSpan(
                    text: _floatValue(_currentValue)
                        ? _intPartValue(_currentValue)
                        : _currentValue,
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: AppSemanticColors.item.primary,
                    children: <InlineSpan>[
                      if (_floatValue(_currentValue))
                        SmartHomeSpan.textSpan(
                            text: _floatPartValue(_currentValue),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppSemanticColors.item.primary),
                      const WidgetSpan(
                        child: SizedBox(
                          width: 4,
                        ),
                      ),
                      SmartHomeSpan.textSpan(
                        text: _unit,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppSemanticColors.item.primary,
                      ),
                    ],
                  ),
                ),
                _btnWidget(
                    enable: !widget.viewModel.enable ||
                        _nextEnable ||
                        smartHomeStore.state.isEditState,
                    icon: widget.viewModel.nextValueIcon,
                    packageName: widget.viewModel.packageName,
                    clickCallback: () {
                      if (_valueListIsEmpty) {
                        widget.viewModel.valueListEmptyCallback?.call(context);
                        return;
                      }

                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call()
                            .then((bool pass) {
                          if (pass) {
                            _nextClick();
                          }
                        });
                      } else {
                        _nextClick();
                      }
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _preClick() {
    _manual = true;
    if (!_preEnable) {
      widget.viewModel.noMorePreCallback?.call(context);
    } else {
      _valueChange(true);
    }
    _clickDebouncer.run(() {
      widget.viewModel.valueChangeCallback
          ?.call(context, _currentValue, _currentIndex);
    });
    _setStateDebouncer.run(() {
      setState(() {
        _manual = false;
        _updateDataState();
        _updateBtnState();
      });
    });
  }

  void _nextClick() {
    _manual = true;
    if (!_nextEnable) {
      widget.viewModel.noMoreNextCallback?.call(context);
    } else {
      _valueChange(false);
    }
    _clickDebouncer.run(() {
      widget.viewModel.valueChangeCallback
          ?.call(context, _currentValue, _currentIndex);
    });
    _setStateDebouncer.run(() {
      setState(() {
        _manual = false;
        _updateDataState();
        _updateBtnState();
      });
    });
  }

  Widget _btnWidget(
      {required bool enable,
      required String icon,
      required String packageName,
      required void Function() clickCallback}) {
    return GestureDetector(
      onTap: () {
        clickCallback();
      },
      child: SizedBox(
        width: 52,
        height: double.infinity,
        child: Opacity(
          opacity: enable ? ComponentOpacity.enable : ComponentOpacity.disable,
          child: ClickEffectCircularWidget(
            enable: enable,
            isOn: false,
            child: Align(
              child: Image.asset(
                icon,
                package: packageName,
                width: 16,
                height: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool _floatValue(String value) {
    return value.contains('.');
    // final double? doubleValue = double.tryParse(value);
    // return value.contains('.') && doubleValue != null;
  }

  String _intPartValue(String value) {
    final int index = value.indexOf('.');
    return value.substring(0, index);
  }

  String _floatPartValue(String value) {
    final int index = value.indexOf('.');
    return value.substring(index);
  }

  void _valueChange(bool pre) {
    if (pre) {
      _currentIndex -= 1;
      if (_currentIndex >= 0) {
        _currentValue = widget.viewModel.valueList[_currentIndex];

        setState(() {
          _updateBtnState();
        });
      }
    } else {
      _currentIndex += 1;
      if (_currentIndex < widget.viewModel.valueList.length) {
        _currentValue = widget.viewModel.valueList[_currentIndex];

        setState(() {
          _updateBtnState();
        });
      } else {}
    }
  }

  void _updateBtnState() {
    if (_currentIndex == 0) {
      _preEnable = false;
    } else {
      _preEnable = true;
    }
    if (_currentIndex == widget.viewModel.valueList.length - 1) {
      _nextEnable = false;
    } else {
      _nextEnable = true;
    }
  }
}
