/*
 * 描述：文本组件
 * 作者：songFJ
 * 创建时间：2025/3/26
 */

import 'package:flutter/material.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/text_view_model.dart';

class TextWidget extends StatelessWidget {
  const TextWidget({super.key, required this.viewModel});

  final TextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return SmartHomeText(
        text: viewModel.text,
        fontSize: viewModel.fontSize,
        fontWeight: viewModel.fontWeight,
        maxLines: viewModel.maxLine ?? 1000,
        height: viewModel.height,
        textAlign: TextAlign.start,
        color: viewModel.textColor);
  }
}
