/*
 * 描述：一键冲洗小按钮
 * 作者：songFJ
 * 创建时间：2025/2/5
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/flush_small_component_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/factory/component_factory.dart';

import '../../../store/smart_home_store.dart';
import 'debounce_throttler/throttler_widget.dart';

class FlushSmallWidget extends StatelessWidget {
  const FlushSmallWidget({super.key, required this.viewModel});

  final FlushSmallComponentViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return ThrottlerWidget(
      millionSeconds: viewModel.throttlerMillionSeconds,
      throttlerCallback: (BuildContext context) {
        viewModel.clickCallback?.call(context);
      },
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: Padding(
          padding: const EdgeInsets.only(top: 4.0, right: 4),
          child: Container(
            color: Colors.transparent,
            height: 40,
            width: 40,
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: ClickEffectCircularWidget(
                enable: viewModel.enable && !smartHomeStore.state.isEditState,
                isOn: viewModel.isOn,
                borderRadius: 8,
                child: Align(
                  child: Image.asset(
                    viewModel.icon,
                    package: viewModel.packageName,
                    width: 16,
                    height: 16,
                    color: viewModel.isOn
                        ? AppSemanticColors.item.information.primary
                        : AppSemanticColors.item.primary,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
