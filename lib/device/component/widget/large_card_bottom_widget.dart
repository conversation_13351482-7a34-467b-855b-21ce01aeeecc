import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart' as smart_home;

const double _itemHeight = 52;
const double _iconSize = 32;
const double _fontSize = 16;

class LargeCardBottomWidget extends StatelessWidget {
  late LargeCardBottomWidgetViewModel viewModel;
  LargeCardBottomWidget({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: Container(
      height: _itemHeight,
      width: double.infinity,
      padding: viewModel.margin,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Align(
              alignment: Alignment.centerLeft,
              child: SmartHomeText(
                text: viewModel.description,
                fontSize: _fontSize,
                color: AppSemanticColors.item.primary,
                fontWeight: FontWeight.w500,
              )),
          if (viewModel.icon.isEmpty)
            Container(
              height: 0,
            )
          else
            Align(
                alignment: Alignment.centerRight,
                child: Image.asset(
                  viewModel.icon,
                  width: _iconSize,
                  height: _iconSize,
                  package: SmartHomeConstant.package,
                )),
        ],
      ),
    ));
  }
}

class LargeCardBottomWidgetViewModel extends smart_home.ComponentBaseViewModel {
  int onlineCount = 0;

  LargeCardBottomWidgetViewModel(String desc, String? png) {
    description = desc;
    icon = png ?? '';
  }

  double height = 0;
  EdgeInsets margin = const EdgeInsets.only(left: 24, right: 24);
  String description = '2个摄像头开启';
  String icon = 'assets/icons/aggr_large_bttom_camera.webp';

  @override
  smart_home.ComponentType componentType() {
    return smart_home.ComponentType.largeCardBottomArea;
  }
}
