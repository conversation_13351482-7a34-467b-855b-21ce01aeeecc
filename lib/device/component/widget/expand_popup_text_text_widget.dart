/*
 * 描述：自适应宽度文本弹出框类组件
 * 作者：songFJ
 * 创建时间：2025/2/13
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_text_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_popup_circular_widget.dart';
import 'package:smart_home/device/component/widget/debounce_throttler/throttler_widget.dart';

import '../../../store/smart_home_store.dart';

class ExpandPopupTextTextWidget extends StatelessWidget {
  const ExpandPopupTextTextWidget({super.key, required this.viewModel});

  final ExpandPopupTextTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState) ?
          ComponentOpacity.enable : ComponentOpacity.disable,
        child: SizedBox(
          width: double.infinity,
          height: 52,
          child: ThrottlerWidget(
            throttlerCallback: (BuildContext context) {
              viewModel.clickCallback?.call(context);
            },
            millionSeconds: viewModel.throttlerMillionSeconds,
            child: ClickEffectPopupCircularWidget(
              enable: viewModel.enable && !smartHomeStore.state.isEditState,
              isOn: viewModel.isOn,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: viewModel.crossAxisAlignment,
                  children: <Widget>[
                    SmartHomeText(
                      text: viewModel.text,
                      fontSize: 12,
                      height: 16.0 / 12.0,
                      fontWeight: FontWeight.w600,
                      color: viewModel.isOn
                          ? AppSemanticColors.item.information.primary
                          : AppSemanticColors.item.primary,
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    SmartHomeText(
                      text: viewModel.desc,
                      fontSize: 10,
                      height: 1.2,
                      color: viewModel.isOn
                          ? AppSemanticColors.item.information.primary
                          : AppSemanticColors.item.primary,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
