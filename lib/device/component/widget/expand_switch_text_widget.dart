/*
 * 描述：自适应宽度单文本开关类组件
 * 作者：songFJ
 * 创建时间：2025/2/10
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_switch_text_view_model.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';
import 'click_effect_circular_widget.dart';
import 'debounce_throttler/throttler_widget.dart';

class ExpandSwitchTextWidget extends StatelessWidget {
  const ExpandSwitchTextWidget({super.key, required this.viewModel});

  final ExpandSwitchTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: SizedBox(
        width: double.infinity,
        height: 41,
        child: ThrottlerWidget(
          throttlerCallback: (BuildContext context) {
            viewModel.clickCallback?.call(context);
          },
          millionSeconds: viewModel.throttlerMillionSeconds,
          child: Opacity(
            opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
                ? ComponentOpacity.enable
                : ComponentOpacity.disable,
            child: ClickEffectCircularWidget(
              enable: viewModel.enable && !smartHomeStore.state.isEditState,
              isOn: viewModel.isOn,
              showBorder: true,
              offColor: AppSemanticColors.item.invert,
              child: Align(
                alignment: viewModel.alignment,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: SmartHomeText(
                    text: viewModel.text,
                    fontSize: viewModel.textFontSize,
                    textAlign: viewModel.textAlign,
                    color: viewModel.isOn
                        ? AppSemanticColors.item.information.primary
                        : AppSemanticColors.item.primary,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
