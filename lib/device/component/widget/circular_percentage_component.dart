import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import '../../../store/smart_home_store.dart';
import '../view_model/circular_percentage_view_model.dart';

class CircularPercentageComponent extends StatelessWidget {
  const CircularPercentageComponent({super.key, required this.viewModel});

  final CircularPercentageModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState) ?
          ComponentOpacity.enable : ComponentOpacity.disable,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            if (viewModel.clickCallback != null) {
              viewModel.clickCallback?.call(context);
            }
          },
          child: SizedBox(
            width: double.infinity,
            height: 52,
            child: Row(
              children: <Widget>[
                Stack(
                  children: <Widget>[
                    Positioned(
                      child: CustomPaint(
                        size: const Size(50, 50),
                        painter: CircularProgressPainter(
                          percentage: int.tryParse(viewModel.percentage) ?? 0,
                          lineWidth: 2,
                          progressColor: _getProgressColor(),
                          backgroundColor: AppSemanticColors.background.secondary,
                        ),
                      ),
                    ),
                    if (viewModel.percentage.isNotEmpty)
                      Positioned.fill(
                        child: Center(
                          child: RichText(
                            maxLines: 1,
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            text: TextSpan(
                              text: viewModel.percentage,
                              style: TextStyle(
                                  fontSize: 16,
                                  color: _getPercentNumberColor(),
                                  fontWeight: FontWeight.w600
                              ),
                              children: <InlineSpan>[
                                if (int.tryParse(viewModel.percentage) != null)
                                  WidgetSpan(
                                    child: Transform.translate(
                                      offset: const Offset(0, -1),
                                      child: Text(
                                        '％',
                                        style: TextStyle(
                                            fontSize: 8,
                                            height: 1.8,
                                            color: _getPercentSymbolColor(),
                                            fontWeight: FontWeight.w500
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 5),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          viewModel.title,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppSemanticColors.item.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (viewModel.subTitle.isNotEmpty) Column(
                          children: <Widget>[
                            const SizedBox(height: 2,),
                            Text(
                              viewModel.subTitle,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppSemanticColors.item.secWeaken,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )
        ),
      )
    );
  }

  Color _getProgressColor() {
    Color _color = AppSemanticColors.item.success.primary;
    final int intValue = int.tryParse(viewModel.percentage) ?? 0;
    if (intValue == 0) {
      return _color;
    }
    final List<PercentageColorRule> colorRules = <PercentageColorRule>[
      PercentageColorRule(0, 20, AppSemanticColors.item.remind.secondary),
      PercentageColorRule(20, 100, AppSemanticColors.item.success.primary),
    ];
    colorRules.forEach((PercentageColorRule element) {
      if (intValue > element.start && intValue <= element.end) {
        _color = element.color;
      }
    });
    return _color;
  }

  Color _getPercentNumberColor() {
    if (viewModel.percentage == '--') {
      return AppSemanticColors.item.primary;
    }
    final int percentValue = int.tryParse(viewModel.percentage) ?? 0;
    if(percentValue <= 20) {
      return AppSemanticColors.item.remind.secondary;
    } else {
      return AppSemanticColors.item.primary;
    }
  }

  Color _getPercentSymbolColor() {
    final int percentValue = int.tryParse(viewModel.percentage) ?? 0;
    if(percentValue <= 20) {
      return AppSemanticColors.item.remind.primary;
    } else {
      return AppSemanticColors.item.primary;
    }
  }

}

class CircularProgressPainter extends CustomPainter {
  final int percentage; // 百分比
  final double lineWidth; // 线宽
  final Color progressColor; // 进度条颜色
  final Color backgroundColor; // 背景颜色

  CircularProgressPainter({
    required this.percentage,
    required this.lineWidth,
    required this.progressColor,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round; // 圆角起点和终点

    final Paint progressPaint = Paint()
      ..color = progressColor
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final double radius = (size.width - lineWidth) / 2;
    final Offset center = Offset(size.width / 2, size.height / 2);

    // 绘制背景圆环
    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制进度圆弧
    final double sweepAngle = -2 * pi * (percentage / 100);
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2, // 从顶部开始
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
