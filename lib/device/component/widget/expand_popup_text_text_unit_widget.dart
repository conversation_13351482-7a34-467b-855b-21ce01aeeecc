/*
 * 描述：自适应弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/3/12
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_text_unit_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_popup_circular_widget.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';
import 'debounce_throttler/throttler_widget.dart';

class ExpandPopupTextTextUnitWidget extends StatelessWidget {
  const ExpandPopupTextTextUnitWidget({super.key, required this.viewModel});

  final ExpandPopupTextTextUnitViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: SizedBox(
          width: double.infinity,
          height: 52,
          child: ThrottlerWidget(
            throttlerCallback: (BuildContext context) {
              viewModel.clickCallback?.call(context);
            },
            millionSeconds: viewModel.throttlerMillionSeconds,
            child: ClickEffectPopupCircularWidget(
              enable: viewModel.enable && !smartHomeStore.state.isEditState,
              isOn: viewModel.isOn,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    RichText(
                      text: SmartHomeSpan.textSpan(
                        text: viewModel.text,
                        fontSize: 14,
                        height: 16.0 / 14.0,
                        fontWeight: FontWeight.w600,
                        color: viewModel.isOn
                            ? AppSemanticColors.item.information.primary
                            : AppSemanticColors.item.primary,
                        children: <InlineSpan>[
                          if (viewModel.unit.isNotEmpty)
                            SmartHomeSpan.textSpan(
                              text: viewModel.unit,
                              fontSize: 8,
                              fontWeight: FontWeight.w600,
                              color: viewModel.isOn
                                  ? AppSemanticColors.item.information.primary
                                  : AppSemanticColors.item.primary,
                            )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    SmartHomeText(
                      text: viewModel.desc,
                      fontSize: 10,
                      height: 1.1,
                      color: viewModel.isOn
                          ? AppSemanticColors.item.information.primary
                          : AppSemanticColors.item.primary,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
