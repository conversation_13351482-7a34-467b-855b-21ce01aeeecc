/*
 * 描述：固定宽度弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/2/7
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/fixed_popup_text_view_model.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';
import 'click_effect_popup_circular_widget.dart';
import 'debounce_throttler/throttler_widget.dart';

class FixedPopupTextWidget extends StatelessWidget {
  const FixedPopupTextWidget({super.key, required this.viewModel});

  final FixedPopupTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return ThrottlerWidget(
      throttlerCallback: (BuildContext context) {
        viewModel.clickCallback?.call(context);
      },
      millionSeconds: viewModel.throttlerMillionSeconds,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: SizedBox(
          width: 52,
          height: 52,
          child: ClickEffectPopupCircularWidget(
            enable: viewModel.enable && !smartHomeStore.state.isEditState,
            isOn: viewModel.isOn,
            child: Align(
              alignment: viewModel.alignment,
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: SmartHomeText(
                  text: viewModel.text,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  maxLines: 2,
                  textAlign: viewModel.textAlign,
                  color: viewModel.isOn
                      ? AppSemanticColors.item.information.primary
                      : AppSemanticColors.item.primary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
