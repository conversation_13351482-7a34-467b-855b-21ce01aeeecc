/*
 * 描述：标签类组件
 * 作者：songFJ
 * 创建时间：2025/2/5
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/expand_label_view_model.dart';

import '../../../store/smart_home_store.dart';

class ExpandLabelWidget extends StatelessWidget {
  const ExpandLabelWidget({super.key, required this.viewModel});

  final ExpandLabelViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable, // TODO(sfj): 效果待定
        child: Container(
          width: double.infinity,
          height: 52,
          padding: const EdgeInsets.only(bottom: 7),
          alignment: Alignment.bottomCenter,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              RichText(
                maxLines: 1,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                text: SmartHomeSpan.textSpan(
                  text: viewModel.value,
                  fontSize: viewModel.isNumberValue ? 24 : 20,
                  fontWeight: viewModel.isNumberValue
                      ? FontWeight.w600
                      : FontWeight.w500,
                  color: AppSemanticColors.item.primary,
                  children: <InlineSpan>[
                    const WidgetSpan(
                      child: SizedBox(
                        width: 4,
                      ),
                    ),
                    SmartHomeSpan.textSpan(
                      text: viewModel.unit,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppSemanticColors.item.primary,
                    ),
                  ],
                ),
              ),
              if (viewModel.desc.isNotEmpty)
                SmartHomeText(
                  text: viewModel.desc,
                  fontSize: 10,
                  height: 1.1,
                  color: AppSemanticColors.item.primary,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
