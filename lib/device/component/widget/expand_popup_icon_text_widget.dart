/*
 * 描述：自适应宽度弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/2/7
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_popup_icon_text_view_model.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';
import 'click_effect_popup_circular_widget.dart';
import 'debounce_throttler/throttler_widget.dart';

class ExpandPopupIconTextWidget extends StatelessWidget {
  const ExpandPopupIconTextWidget({super.key, required this.viewModel});

  final ExpandPopupIconTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: <PERSON><PERSON><PERSON><PERSON>(
        width: double.infinity,
        height: 52,
        child: ThrottlerWidget(
          throttlerCallback: (BuildContext context) {
            viewModel.clickCallback?.call(context);
          },
          millionSeconds: viewModel.throttlerMillionSeconds,
          child: Opacity(
            opacity: (viewModel.enable && !smartHomeStore.state.isEditState) ?
              ComponentOpacity.enable : ComponentOpacity.disable,
            child: ClickEffectPopupCircularWidget(
              enable: viewModel.enable && !smartHomeStore.state.isEditState,
              isOn: viewModel.isOn,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Image.asset(
                    viewModel.icon,
                    package: viewModel.packageName,
                    width: 16,
                    height: 16,
                    color: viewModel.isOn
                        ? AppSemanticColors.item.information.primary
                        : AppSemanticColors.item.primary,
                  ),
                  if (viewModel.text.isNotEmpty) const SizedBox(height: 6),
                  if (viewModel.text.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: SmartHomeText(
                        text: viewModel.text,
                        fontSize: 10,
                        height: 1.1,
                        color: viewModel.isOn
                            ? AppSemanticColors.item.information.primary
                            : AppSemanticColors.item.primary,
                      ),
                    )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
