/*
 * 描述：+-调节组件
 * 作者：songFJ
 * 创建时间：2025/3/25
 */
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/debounce.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/expand_air_condition_value_list_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';

import '../../../store/smart_home_store.dart';

class ExpandAirConditionValueListWidget extends StatefulWidget {
  const ExpandAirConditionValueListWidget({super.key, required this.viewModel});

  final ExpandAirConditionValueListViewModel viewModel;

  @override
  State<ExpandAirConditionValueListWidget> createState() =>
      _ExpandAirConditionValueListWidgetState();
}

class _ExpandAirConditionValueListWidgetState
    extends State<ExpandAirConditionValueListWidget> {
  bool _manual = false;
  bool _preEnable = true;
  bool _nextEnable = true;
  int _currentIndex = 0;
  ValueModel _currentValueModel = ValueModel(value: '--', unit: '');

  bool _valueListIsEmpty = false;

  final Debouncer _clickDebouncer = Debouncer(milliseconds: 500);
  final Debouncer _setStateDebouncer = Debouncer(milliseconds: 2000);

  @override
  void dispose() {
    _clickDebouncer.dispose();
    _setStateDebouncer.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _updateDataState();
  }

  void _updateDataState() {
    _valueListIsEmpty = widget.viewModel.valueList.isEmpty;
    if (!_valueListIsEmpty) {
      if (widget.viewModel.currentIndex < 0 ||
          widget.viewModel.currentIndex >= widget.viewModel.valueList.length) {
        _currentIndex = 0;
      } else {
        _currentIndex = widget.viewModel.currentIndex;
      }

      _currentValueModel = widget.viewModel.valueList[_currentIndex];
      if (_currentIndex == 0) {
        _preEnable = false;
      }
      if (_currentIndex == widget.viewModel.valueList.length - 1) {
        _nextEnable = false;
      }
    } else {
      _currentIndex = 0;
      _currentValueModel = ValueModel(value: '--', unit: '');
    }
  }

  @override
  void didUpdateWidget(covariant ExpandAirConditionValueListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!_manual) {
      setState(() {
        _updateDataState();
        _updateBtnState();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: widget.viewModel.expandFlex,
      child: Opacity(
        opacity: (widget.viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: GestureDetector(
          onTap: () {},
          child: Container(
            decoration: BoxDecoration(
              color: AppSemanticColors.background.secondary,
              borderRadius: BorderRadius.circular(12),
            ),
            height: 52,
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                _btnWidget(
                    isPre: true,
                    icon: widget.viewModel.preValueIcon,
                    packageName: widget.viewModel.packageName,
                    clickCallback: () {
                      if (_valueListIsEmpty) {
                        widget.viewModel.valueListEmptyCallback?.call(context);
                        return;
                      }

                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call()
                            .then((bool pass) {
                          if (pass) {
                            _preClick();
                          }
                        });
                      } else {
                        _preClick();
                      }
                    }),
                Expanded(
                  child: RichText(
                    maxLines: 1,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    text: SmartHomeSpan.textSpan(
                      text: _floatValue(_currentValueModel.value)
                          ? _intPartValue(_currentValueModel.value)
                          : _currentValueModel.value,
                      fontSize: _notANumber(_currentValueModel.value) ? 16 : 24,
                      fontWeight: FontWeight.w600,
                      color: AppSemanticColors.item.primary,
                      children: <InlineSpan>[
                        if (_floatValue(_currentValueModel.value))
                          SmartHomeSpan.textSpan(
                              text: _floatPartValue(_currentValueModel.value),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppSemanticColors.item.primary),
                        const WidgetSpan(
                          child: SizedBox(
                            width: 4,
                          ),
                        ),
                        SmartHomeSpan.textSpan(
                          text: _currentValueModel.unit,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          height: 12.0 / 12.0,
                          color: AppSemanticColors.item.primary,
                        ),
                      ],
                    ),
                  ),
                ),
                _btnWidget(
                    isPre: false,
                    icon: widget.viewModel.nextValueIcon,
                    packageName: widget.viewModel.packageName,
                    clickCallback: () {
                      if (_valueListIsEmpty) {
                        widget.viewModel.valueListEmptyCallback?.call(context);
                        return;
                      }

                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call()
                            .then((bool pass) {
                          if (pass) {
                            _nextClick();
                          }
                        });
                      } else {
                        _nextClick();
                      }
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleClick(bool isPre) {
    _manual = true;
    if ((isPre && !_preEnable) || (!isPre && !_nextEnable)) {
      if (isPre) {
        widget.viewModel.noMorePreCallback?.call(context);
      } else {
        widget.viewModel.noMoreNextCallback?.call(context);
      }
    } else {
      _valueChange(isPre);
    }

    _clickDebouncer.run(() {
      widget.viewModel.valueChangeCallback
          ?.call(context, _currentValueModel.value, _currentIndex);
    });

    _setStateDebouncer.run(() {
      setState(() {
        _manual = false;
        _updateDataState();
        _updateBtnState();
      });
    });
  }

  void _preClick() => _handleClick(true);

  void _nextClick() => _handleClick(false);

  Widget _btnWidget(
      {required bool isPre,
      required String icon,
      required String packageName,
      required void Function() clickCallback}) {
    final bool enable = _isButtonEnabled(isPre);

    return GestureDetector(
      onTap: () {
        clickCallback();
      },
      child: SizedBox(
        width: 52,
        height: double.infinity,
        child: Opacity(
          opacity: enable ? ComponentOpacity.enable : ComponentOpacity.disable,
          child: ClickEffectCircularWidget(
            enable: enable,
            isOn: false,
            borderRadius: 12,
            child: Align(
              child: Image.asset(
                icon,
                package: packageName,
                width: 16,
                height: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool _isButtonEnabled(bool isPre) {
    if (!widget.viewModel.enable || smartHomeStore.state.isEditState) {
      return true;
    }
    return isPre ? _preEnable : _nextEnable;
  }

  bool _notANumber(String value) {
    final double? doubleValue = double.tryParse(value);
    return doubleValue == null;
  }

  bool _floatValue(String value) {
    final double? doubleValue = double.tryParse(value);
    return value.contains('.') && doubleValue != null;
  }

  String _intPartValue(String value) {
    final int index = value.indexOf('.');
    return index != -1 ? value.substring(0, index) : value;
  }

  String _floatPartValue(String value) {
    final int index = value.indexOf('.');
    return index != -1 ? value.substring(index) : '';
  }

  void _valueChange(bool pre) {
    if (pre) {
      _currentIndex -= 1;
      if (_currentIndex >= 0) {
        _currentValueModel = widget.viewModel.valueList[_currentIndex];

        setState(() {
          _updateBtnState();
        });
      }
    } else {
      _currentIndex += 1;
      if (_currentIndex < widget.viewModel.valueList.length) {
        _currentValueModel = widget.viewModel.valueList[_currentIndex];

        setState(() {
          _updateBtnState();
        });
      } else {}
    }
  }

  void _updateBtnState() {
    if (_currentIndex == 0) {
      _preEnable = false;
    } else {
      _preEnable = true;
    }
    if (_currentIndex == widget.viewModel.valueList.length - 1) {
      _nextEnable = false;
    } else {
      _nextEnable = true;
    }
  }
}
