/*
 * 描述：蓝牙图标
 * 作者：songFJ
 * 创建时间：2025/2/14
 */
import 'package:flutter/material.dart';
import 'package:smart_home/device/component/view_model/fixed_ble_view_model.dart';

class FixedBleWidget extends StatelessWidget {
  const FixedBleWidget({super.key, required this.viewModel});

  final FixedBleViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: const EdgeInsets.only(top: 10, right: 8),
        child: Image.asset(
          'assets/icons/ble.webp',
          package: 'smart_home',
          width: 16,
          height: 16,
        ),
      ),
    );
  }
}
