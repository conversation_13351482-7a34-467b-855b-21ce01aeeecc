/*
 * 描述：圆角点击效果组件，用于包裹子组件并提供点击效果，根据状态显示不同的背景颜色。
 * 作者：songFJ
 * 创建时间：2025/2/6
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../common/component_constant.dart';

class ClickEffectCircularWidget extends StatefulWidget {
  const ClickEffectCircularWidget({
    super.key,
    required this.child,
    required this.enable,
    required this.isOn,
    this.borderRadius = ComponentRadius.componentSmall,
    this.showBorder = false,
    this.width = double.infinity,
    this.height = double.infinity,
    this.onColor,
    this.onTapDownColor,
    this.onBorderColor,
    this.offColor,
    this.offTapDownColor,
    this.offBorderColor,
  });

  final Widget child; // 子组件
  final bool enable; // 是否禁用状态
  final bool isOn; // 是否开启状态
  final bool showBorder; // 是否显示边框
  final double borderRadius; // 圆角半径
  final double width; // 宽度
  final double height; // 高度
  final Color? onColor; //  开启状态的背景颜色
  final Color? onTapDownColor; // 开启状态的按下时的背景颜色
  final Color? onBorderColor; // 开启状态的边框颜色
  final Color? offColor; // 关闭状态的背景颜色
  final Color? offTapDownColor; // 关闭状态的按下时的背景颜色
  final Color? offBorderColor; // 关闭状态的边框颜色

  @override
  State<ClickEffectCircularWidget> createState() =>
      _ClickEffectCircularWidgetState();
}

class _ClickEffectCircularWidgetState extends State<ClickEffectCircularWidget> {
  bool _tapDown = false;
  Color _onColor = Colors.transparent;
  Color _onTapDownColor = Colors.transparent;
  Color _onBorderColor = Colors.transparent;
  Color _offColor = Colors.transparent;
  Color _offTapDownColor = Colors.transparent;
  Color _offBorderColor = Colors.transparent;

  @override
  void initState() {
    super.initState();
    _onColor = widget.onColor ?? AppSemanticColors.item.information.substrate;
    _onTapDownColor = widget.onTapDownColor ??
        AppSemanticColors.component.information.emphasize;
    _onBorderColor =
        widget.onBorderColor ?? AppSemanticColors.item.information.primary;
    _offColor = widget.offColor ?? AppSemanticColors.background.secondary;
    _offTapDownColor =
        widget.offTapDownColor ?? AppSemanticColors.container.cardClick;
    _offBorderColor = widget.offBorderColor ?? Colors.transparent;
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) {
        if (mounted) {
          setState(() {
            _tapDown = true;
          });
        }
      },
      onPointerCancel: (_) {
        if (mounted) {
          setState(() {
            _tapDown = false;
          });
        }
      },
      onPointerUp: (_) {
        if (mounted) {
          setState(() {
            _tapDown = false;
          });
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(widget.borderRadius)),
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
              color: widget.isOn
                  ? ((_tapDown && widget.enable) ? _onTapDownColor : _onColor)
                  : ((_tapDown && widget.enable)
                      ? _offTapDownColor
                      : _offColor),
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border: !widget.showBorder
                  ? null
                  : Border.all(
                      width: .5,
                      color: (widget.isOn && widget.enable)
                          ? _onBorderColor
                          : _offBorderColor)),
          child: widget.child,
        ),
      ),
    );
  }
}
