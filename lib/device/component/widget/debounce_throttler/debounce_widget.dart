/*
 * 描述：只最后一次生效
 * 作者：songFJ
 * 创建时间：2025/2/5
 */

import 'package:flutter/material.dart';
import 'package:smart_home/common/debounce.dart';

class DebounceWidget extends StatefulWidget {
  const DebounceWidget(
      {super.key,
      required this.debounceCallback,
      this.milliseconds = 1000,
      required this.child});

  final Widget child;

  final int milliseconds;

  final void Function(BuildContext context) debounceCallback;

  @override
  State<DebounceWidget> createState() => _DebounceWidgetState();
}

class _DebounceWidgetState extends State<DebounceWidget> {
  final Debouncer _debouncer = Debouncer(milliseconds: 1000);

  @override
  void initState() {
    _debouncer.milliseconds = widget.milliseconds;
    super.initState();
  }

  @override
  void dispose() {
    _debouncer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _debouncer.run(() {
          widget.debounceCallback(context);
        });
      },
      child: widget.child,
    );
  }
}
