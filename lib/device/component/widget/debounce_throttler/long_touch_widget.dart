import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:smart_home/common/debounce.dart';

///支持点击事件、长按事件的组件
class LongTouchWidget extends StatefulWidget {
  final int periodicTime;
  final void Function(BuildContext context)? clickCallback;//点击回调
  final void Function(BuildContext context,bool isLongTouchFirstRun)? longTouchCallback;//长按回调
  const LongTouchWidget({
    super.key,
    required this.child,
    this.periodicTime = 150,
    this.clickCallback,
    this.longTouchCallback,
  });

  final Widget child;

  @override
  State<LongTouchWidget> createState() => _LongTouchWidgetState();
}

class _LongTouchWidgetState extends State<LongTouchWidget>{
  Timer? _timer;
  final Throttler _throttler = Throttler(milliseconds: 1000);
  bool _isLongTouchFirstRun = true;

  void _startTimer(BuildContext context){
    _stopTimer();
    _isLongTouchFirstRun = true;
    _timer = Timer.periodic(Duration(milliseconds: widget.periodicTime ),  (_) {
      widget.longTouchCallback?.call(context,_isLongTouchFirstRun);
      if(_isLongTouchFirstRun){
        _isLongTouchFirstRun = false;
      }
    });
  }

  void  _stopTimer(){
    _timer?.cancel();
    _timer = null;
  }

  @override
  void initState() {
    super.initState();
    _throttler.milliseconds = widget.periodicTime;
  }

  @override
  void dispose() {
    _throttler.dispose();
    _stopTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _throttler.run((){
          widget.clickCallback?.call(context);
        });
      },
      onLongPressStart: (_) {
        _startTimer(context);
      },
      onLongPressEnd: (_) {
        _stopTimer();
      },
      onLongPressCancel: () {
        _stopTimer();
      },
      child: widget.child,
    );
  }

}
