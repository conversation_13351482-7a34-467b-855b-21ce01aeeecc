/*
 * 描述：只第一次生效
 * 作者：songFJ
 * 创建时间：2025/2/5
 */

import 'package:flutter/material.dart';
import 'package:smart_home/common/debounce.dart';

class ThrottlerWidget extends StatefulWidget {
  const ThrottlerWidget(
      {super.key,
      required this.throttlerCallback,
      this.millionSeconds = 1000,
      required this.child});

  final Widget child;

  final int millionSeconds;

  final void Function(BuildContext context) throttlerCallback;

  @override
  State<ThrottlerWidget> createState() => _ThrottlerWidgetState();
}

class _ThrottlerWidgetState extends State<ThrottlerWidget> {
  final Throttler _throttler = Throttler(milliseconds: 1000);

  @override
  void initState() {
    super.initState();
    _throttler.milliseconds = widget.millionSeconds;
  }

  @override
  void dispose() {
    _throttler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _throttler.run(() {
          widget.throttlerCallback(context);
        });
      },
      child: widget.child,
    );
  }
}
