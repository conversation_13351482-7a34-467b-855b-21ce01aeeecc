/*
 * 描述：自适应洗衣机程序弹窗类组件
 * 作者：songFJ
 * 创建时间：2025/2/11
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_popup_wash_program_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_popup_circular_widget.dart';
import 'package:smart_home/device/component/widget/debounce_throttler/throttler_widget.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';

class ExpandPopupWashProgramWidget extends StatelessWidget {
  const ExpandPopupWashProgramWidget({super.key, required this.viewModel});

  final ExpandPopupWashProgramViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: SizedBox(
          height: 52,
          width: double.infinity,
          child: ThrottlerWidget(
            throttlerCallback: (BuildContext context) {
              viewModel.clickCallback?.call(context);
            },
            millionSeconds: viewModel.throttlerMillionSeconds,
            child: ClickEffectPopupCircularWidget(
                enable: viewModel.enable && !smartHomeStore.state.isEditState,
                isOn: viewModel.isOn,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Align(
                    alignment: viewModel.alignment,
                    child: RichText(
                      maxLines: 1,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      text: SmartHomeSpan.textSpan(
                        text: viewModel.name,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: viewModel.isOn
                            ? AppSemanticColors.item.information.primary
                            : AppSemanticColors.item.primary,
                        children: <InlineSpan>[
                          const WidgetSpan(child: SizedBox(width: 8)),
                          SmartHomeSpan.textSpan(
                            text: viewModel.value,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: viewModel.isOn
                                ? AppSemanticColors.item.information.primary
                                : AppSemanticColors.item.primary,
                          ),
                          SmartHomeSpan.textSpan(
                            text: viewModel.unit,
                            fontSize: 8,
                            color: viewModel.isOn
                                ? AppSemanticColors.item.information.primary
                                : AppSemanticColors.item.primary,
                          ),
                        ],
                      ),
                    ),
                  ),
                )),
          ),
        ),
      ),
    );
  }
}
