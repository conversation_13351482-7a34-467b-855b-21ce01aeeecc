/*
 * 描述：固定宽度弹窗组件
 * 作者：songFJ
 * 创建时间：2025/2/6
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/fixed_popup_icon_text_view_model.dart';
import 'package:smart_home/device/component/widget/click_effect_popup_circular_widget.dart';
import 'package:smart_home/device/component/widget/debounce_throttler/throttler_widget.dart';

import '../../../store/smart_home_store.dart';

class FixedPopupIconTextWidget extends StatelessWidget {
  const FixedPopupIconTextWidget({super.key, required this.viewModel});

  final FixedPopupIconTextViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return ThrottlerWidget(
      throttlerCallback: (BuildContext context) {
        viewModel.clickCallback?.call(context);
      },
      millionSeconds: viewModel.throttlerMillionSeconds,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: SizedBox(
          width: 52,
          height: 52,
          child: ClickEffectPopupCircularWidget(
            enable: viewModel.enable && !smartHomeStore.state.isEditState,
            isOn: viewModel.isOn,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Image.asset(
                  viewModel.icon,
                  package: viewModel.packageName,
                  width: 16,
                  height: 16,
                  color: viewModel.isOn
                      ? AppSemanticColors.item.information.primary
                      : AppSemanticColors.item.primary,
                ),
                if (viewModel.text.isNotEmpty) const SizedBox(height: 6),
                if (viewModel.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: SmartHomeText(
                      text: viewModel.text,
                      fontSize: 10,
                      height: 1.1,
                      color: viewModel.isOn
                          ? AppSemanticColors.item.information.primary
                          : AppSemanticColors.item.primary,
                    ),
                  )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
