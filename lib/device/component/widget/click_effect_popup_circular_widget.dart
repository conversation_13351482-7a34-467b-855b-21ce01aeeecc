/*
 * 描述：弹窗类圆角点击效果组件，用于包裹子组件并提供点击效果，根据状态显示不同的背景颜色。
 * 作者：songFJ
 * 创建时间：2025/2/6
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../common/component_constant.dart';

class ClickEffectPopupCircularWidget extends StatefulWidget {
  const ClickEffectPopupCircularWidget({
    super.key,
    required this.child,
    required this.enable,
    required this.isOn,
    this.width = double.infinity,
    this.height = double.infinity,
    this.topLeftBorderRadius = ComponentRadius.componentSmall,
    this.topRightBorderRadius = ComponentRadius.componentSmall,
    this.bottomLeftBorderRadius = ComponentRadius.componentSmall,
    this.bottomRightBorderRadius = ComponentRadius.componentLarge,
    this.onColor,
    this.onTapDownColor,
    this.offColor,
    this.offTapDownColor,
  });

  final Widget child; // 子组件
  final bool enable; // 是否启用点击效果
  final bool isOn; // 是否开启状态
  final double width; // 宽度
  final double height; // 高度
  final double topLeftBorderRadius; // 上左圆角
  final double topRightBorderRadius; // 上右圆角
  final double bottomLeftBorderRadius; // 下左圆角
  final double bottomRightBorderRadius; // 下右圆角
  final Color? onColor; // 开启状态时的背景颜色
  final Color? onTapDownColor; // 开启状态点击时的背景颜色
  final Color? offColor; // 关闭状态时的背景颜色
  final Color? offTapDownColor; // 关闭状态点击时的背景颜色

  @override
  State<ClickEffectPopupCircularWidget> createState() =>
      _ClickEffectPopupCircularWidgetState();
}

class _ClickEffectPopupCircularWidgetState
    extends State<ClickEffectPopupCircularWidget> {
  bool _tapDown = false;
  Color _onColor = Colors.transparent;
  Color _onTapDownColor = Colors.transparent;
  Color _offColor = Colors.transparent;
  Color _offTapDownColor = Colors.transparent;

  @override
  void initState() {
    super.initState();
    _onColor = widget.onColor ?? AppSemanticColors.item.information.substrate;
    _onTapDownColor = widget.onTapDownColor ??
        AppSemanticColors.component.information.emphasize;

    _offColor = widget.offColor ?? AppSemanticColors.background.secondary;
    _offTapDownColor =
        widget.offTapDownColor ?? AppSemanticColors.container.cardClick;
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      behavior: HitTestBehavior.translucent,
      onPointerDown: (_) {
        if (mounted) {
          setState(() {
            _tapDown = true;
          });
        }
      },
      onPointerCancel: (_) {
        if (mounted) {
          setState(() {
            _tapDown = false;
          });
        }
      },
      onPointerUp: (_) {
        if (mounted) {
          setState(() {
            _tapDown = false;
          });
        }
      },
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: Stack(
          children: <Widget>[
            Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                color: widget.isOn
                    ? ((_tapDown && widget.enable) ? _onTapDownColor : _onColor)
                    : ((_tapDown && widget.enable)
                        ? _offTapDownColor
                        : _offColor),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(widget.topLeftBorderRadius),
                  topRight: Radius.circular(widget.topRightBorderRadius),
                  bottomLeft: Radius.circular(widget.bottomLeftBorderRadius),
                  bottomRight: Radius.circular(widget.bottomRightBorderRadius),
                ),
              ),
              child: widget.child,
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: Image.asset(
                'assets/images/popup_arr.webp',
                package: 'smart_home',
                width: 7,
                height: 7,
                color: widget.isOn
                    ? ((_tapDown && widget.enable) ? _onTapDownColor : _onColor)
                    : ((_tapDown && widget.enable)
                        ? _offTapDownColor
                        : _offColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
