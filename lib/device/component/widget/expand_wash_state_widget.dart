/*
 * 描述：自适应洗衣机状态组件
 * 作者：songFJ
 * 创建时间：2025/2/11
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_wash_state_view_model.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';

class ExpandWashStateWidget extends StatelessWidget {
  const ExpandWashStateWidget({super.key, required this.viewModel});

  final ExpandWashStateViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    final bool showValue = viewModel.label.isNotEmpty ||
        viewModel.value.isNotEmpty ||
        viewModel.unit.isNotEmpty;
    final bool showName = viewModel.name.isNotEmpty;
    return Expanded(
      flex: viewModel.expandFlex,
      child: Opacity(
        opacity: (viewModel.enable && !smartHomeStore.state.isEditState) ?
          ComponentOpacity.enable : ComponentOpacity.disable,
        child: SizedBox(
            height: 52,
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.only(left: viewModel.paddingLeft),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (showValue)
                    RichText(
                      maxLines: 1,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      text: SmartHomeSpan.textSpan(
                        text: viewModel.label,
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: AppSemanticColors.item.primary,
                        children: <InlineSpan>[
                          if (viewModel.label.isNotEmpty)
                            const WidgetSpan(child: SizedBox(width: 2)),
                          SmartHomeSpan.textSpan(
                            text: viewModel.value,
                            fontSize: (viewModel.label.isEmpty &&
                                    viewModel.unit.isEmpty)
                                ? 14
                                : 20,
                            fontWeight: FontWeight.w600,
                            color: AppSemanticColors.item.primary,
                          ),
                          SmartHomeSpan.textSpan(
                            text: viewModel.unit,
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: AppSemanticColors.item.primary,
                          ),
                        ],
                      ),
                    ),
                  if (showValue && showName)
                    const SizedBox(
                      height: 2,
                    ),
                  if (showName)
                    SmartHomeText(
                      text: viewModel.name,
                      fontSize: (viewModel.label.isNotEmpty ||
                              viewModel.value.isNotEmpty ||
                              viewModel.unit.isNotEmpty)
                          ? 10
                          : 14,
                      fontWeight: FontWeight.w600,
                      color: AppSemanticColors.item.information.primary,
                    ),
                ],
              ),
            )),
      ),
    );
  }
}
