import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:smart_home/device/component/view_model/purified_grid_view_model.dart';

import '../view_model/circular_percentage_view_model.dart';
import 'circular_percentage_component.dart';

/// 净水滤芯耗材组件
class PurifiedGridViewComponent extends StatelessWidget {
  const PurifiedGridViewComponent({super.key, required this.viewModel});

  /// [viewModel] 净水滤芯耗材viewModel
  final PurifiedGridViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: viewModel.purifiedList
              .asMap()
              .entries
              .map((MapEntry<int, CircularPercentageModel> entry) =>
                  CircularPercentageComponent(
                    viewModel: entry.value,
                  ))
              .toList()),
    );
  }
}
