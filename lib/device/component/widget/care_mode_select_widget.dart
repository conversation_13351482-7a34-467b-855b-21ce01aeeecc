/*
 * 描述：care模式选择
 * 作者：songFJ
 * 创建时间：2025/2/10
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/care_mode_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_text_view_model.dart';
import 'package:smart_home/device/component/widget/expand_switch_text_widget.dart';

import '../../../store/smart_home_store.dart';

class CareModeSelectWidget extends StatelessWidget {
  const CareModeSelectWidget({super.key, required this.viewModel});

  final CareModeSelectViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: ComponentMargin.page),
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(color: AppSemanticColors.item.terWeaken),
          borderRadius: BorderRadius.circular(ComponentRadius.card),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: ComponentPadding.textTop),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: ComponentMargin.page),
                child: SmartHomeText(
                  text: viewModel.name,
                  fontSize: 12,
                  color: AppSemanticColors.item.secondary,
                ),
              ),
              const SizedBox(height: ComponentPadding.textTop),
              Row(
                children: _modeList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _modeList() {
    final List<Widget> list = <Widget>[];
    list.add(const SizedBox(width: 16));
    for (int i = 0; i < viewModel.modeList.length; i++) {
      final ExpandSwitchTextViewModel vm = ExpandSwitchTextViewModel(
          text: viewModel.modeList[i],
          textFontSize: 12,
          isOn: i == viewModel.selectedIndex,
          enable: viewModel.enable && !smartHomeStore.state.isEditState,
          clickCallback: (BuildContext context) {
            viewModel.modeSelectCallback?.call(context, i);
          });
      list.add(ExpandSwitchTextWidget(viewModel: vm));
      list.add(const SizedBox(width: 16));
    }
    return list;
  }
}
