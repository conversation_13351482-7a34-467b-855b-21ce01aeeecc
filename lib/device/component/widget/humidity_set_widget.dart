/*
 * 描述：新风机湿度调节
 * 作者：songFJ
 * 创建时间：2025/3/12
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/debounce.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/view_model/humidity_set_view_model.dart';
import 'package:smart_home/device/component_widget/wind_speed_component/common_slider_thumb_shape.dart';

class HumiditySetWidget extends StatefulWidget {
  const HumiditySetWidget({super.key, required this.viewModel});

  final HumiditySetViewModel viewModel;

  @override
  State<HumiditySetWidget> createState() => _HumiditySetWidgetState();
}

class _HumiditySetWidgetState extends State<HumiditySetWidget> {
  bool _humidityOn = false;

  @override
  void initState() {
    super.initState();
    _humidityOn = widget.viewModel.humidityOn;
  }

  @override
  void didUpdateWidget(covariant HumiditySetWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {
      _humidityOn = widget.viewModel.humidityOn;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: ComponentMargin.pageTop),
        child: Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: ComponentPadding.cardMiddle),
              child: Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppSemanticColors.component.divider,
                      width: 0.5,
                    ),
                  ),
                ),
                height: 54,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    SmartHomeText(
                      text: widget.viewModel.humidityTitle,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppSemanticColors.item.primary,
                    ),
                    SizedBox(
                      height: 24,
                      width: 44,
                      child: Transform.scale(
                        scale: 0.77,
                        child: CupertinoSwitch(
                            activeColor:
                                AppSemanticColors.component.primary.fill,
                            value: _humidityOn,
                            onChanged: (bool value) {
                              widget.viewModel.checkHumidityOnOffContinue
                                  ?.call()
                                  .then((bool pass) {
                                if (pass) {
                                  setState(() {
                                    _humidityOn = value;
                                  });
                                  widget.viewModel.humidityOnOffCallback
                                      ?.call(context, value);
                                }
                              });
                            }),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            HumiditySliderWidget(
              enable: _humidityOn &&
                  widget.viewModel.humiditySetEnable &&
                  widget.viewModel.humidityOnEnable,
              value: widget.viewModel.humiditySetCurrentValue,
              minValue: widget.viewModel.humiditySetMinValue,
              maxValue: widget.viewModel.humiditySetMaxValue,
              step: widget.viewModel.humiditySetStep,
              checkHumiditySetContinue:
                  widget.viewModel.checkHumiditySetContinue,
              valueChangeCallback: (_, int value) {
                widget.viewModel.humiditySetCallback?.call(context, value);
              },
            ),
          ],
        ),
      ),
    );
  }
}

class HumiditySliderWidget extends StatefulWidget {
  const HumiditySliderWidget({
    super.key,
    required this.enable,
    required this.value,
    required this.minValue,
    required this.maxValue,
    required this.step,
    this.checkHumiditySetContinue,
    this.valueChangeCallback,
  });

  final int value;
  final int minValue;
  final int maxValue;
  final int step;
  final bool enable;
  final Future<bool> Function()? checkHumiditySetContinue;
  final void Function(BuildContext context, int value)? valueChangeCallback;

  @override
  State<HumiditySliderWidget> createState() => _HumiditySliderWidgetState();
}

class _HumiditySliderWidgetState extends State<HumiditySliderWidget> {
  bool _manual = false;

  int _currentValue = 0;
  int _division = 0;

  final Debouncer _valueChangeDebouncer = Debouncer(milliseconds: 500);
  final Debouncer _setStateDebouncer = Debouncer(milliseconds: 2500);

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
    _division = (widget.maxValue - widget.minValue) ~/ widget.step;
  }

  @override
  void dispose() {
    _valueChangeDebouncer.dispose();
    _setStateDebouncer.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant HumiditySliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_manual) {
      _updateData();
      setState(() {});
    }
  }

  void _updateData() {
    _currentValue = widget.value;
    _division = (widget.maxValue - widget.minValue) ~/ widget.step;
  }

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity:
          widget.enable ? ComponentOpacity.enable : ComponentOpacity.disable,
      child: Column(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              height: 54,
              child: Align(
                alignment: Alignment.centerLeft,
                child: SmartHomeText(
                  text: '目标湿度丨$_currentValue%',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.primary,
                ),
              ),
            ),
          ),
          SizedBox(
            width: double.infinity,
            height: 52,
            child: Stack(
              children: <Widget>[
                SliderTheme(
                  data: SliderThemeData(
                      trackHeight: 8,
                      activeTickMarkColor:
                          AppSemanticColors.item.information.primary,
                      activeTrackColor:
                          AppSemanticColors.item.information.primary,
                      inactiveTrackColor: AppSemanticColors.item.tertiary,
                      inactiveTickMarkColor: AppSemanticColors.item.tertiary,
                      valueIndicatorColor: Colors.transparent,
                      overlayColor: Colors.transparent,
                      thumbColor: Colors.white,
                      thumbShape:
                          const CommonSliderThumbShape(thumbRadius: 16)),
                  child: Slider(
                      min: widget.minValue.toDouble(),
                      max: widget.maxValue.toDouble(),
                      divisions: _division,
                      value: widget.enable
                          ? _currentValue.toDouble()
                          : widget.minValue.toDouble(),
                      onChangeStart: (_) {
                        _manual = true;
                      },
                      onChangeEnd: (_) {
                        _manual = false;
                      },
                      onChanged: (double value) {
                        widget.checkHumiditySetContinue
                            ?.call()
                            .then((bool pass) {
                          if (pass) {
                            setState(() {
                              _currentValue = value.toInt();
                            });
                            _valueChangeDebouncer.run(() {
                              widget.valueChangeCallback
                                  ?.call(context, _currentValue);
                            });
                            _setStateDebouncer.run(() {
                              _manual = false;
                              if (mounted) {
                                _updateData();
                                setState(() {});
                              }
                            });
                          }
                        });
                      }),
                ),
                if (!widget.enable)
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.transparent,
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 12, right: 12, bottom: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SmartHomeText(
                  text: widget.enable ? '${widget.minValue}%' : ' ',
                  fontSize: 12,
                  color: AppSemanticColors.item.primary,
                ),
                SmartHomeText(
                  text: widget.enable ? '${widget.maxValue}%' : ' ',
                  fontSize: 12,
                  color: AppSemanticColors.item.primary,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
