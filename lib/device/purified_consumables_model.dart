import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:upservice/model/uhome_response_model.dart';

class PurifiedConsumablesResponseModel extends UhomeResponseModel {
  PurifiedConsumablesResponseModel.fromJson(super.data) : super.fromJson() {
    data = PurifiedConsumablesDataModel.fromJson(super.retData);
  }

  PurifiedConsumablesDataModel? data;

  @override
  String toString() {
    return 'PurifiedConsumablesResponseModel{data: $data}';
  }
}

class PurifiedConsumablesDataModel {
  List<PurifiedConsumableModel> consumables = <PurifiedConsumableModel>[];

  PurifiedConsumablesDataModel({required this.consumables});

  PurifiedConsumablesDataModel.fromJson(Map<dynamic, dynamic> json) {
    if (json['consumables'] is List) {
      consumables = <PurifiedConsumableModel>[];
      json.listValueForKey('consumables', <dynamic>[]).forEach((dynamic v) {
        consumables.add(PurifiedConsumableModel.fromJson(
            v is Map ? v as Map<String, dynamic> : <String, dynamic>{}));
      });

      consumables.sort((PurifiedConsumableModel a, PurifiedConsumableModel b) {
        return a.filterIndex.compareTo(b.filterIndex);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['consumables'] =
        consumables.map((PurifiedConsumableModel e) => e.toJson()).toList();
    return data;
  }

  @override
  String toString() {
    return 'ConsumablesDataModel{consumableName: $consumables}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurifiedConsumablesDataModel &&
          runtimeType == other.runtimeType &&
          listEquals(consumables, other.consumables);

  @override
  int get hashCode => listHashCode(consumables);
}

class PurifiedConsumableModel {
  String code = '';
  String name = '';
  String attributeKeyPrefix = '';
  String level = '';
  String detailsUrl = '';
  int filterIndex = 0;
  List<PropDesc> propDesc = <PropDesc>[];

  PurifiedConsumableModel.fromJson(Map<dynamic, dynamic> json) {
    code = json.stringValueForKey('code', '');
    name = json.stringValueForKey('name', '');
    detailsUrl = json.stringValueForKey('detailsUrl', '');
    filterIndex = json.intValueForKey('filterIndex', 0);
    if (json['propDesc'] is List) {
      propDesc = <PropDesc>[];
      json.listValueForKey('propDesc', <dynamic>[]).forEach((dynamic v) {
        final PropDesc desc = PropDesc.fromJson(
            v is Map ? v as Map<String, dynamic> : <String, dynamic>{});
        if (desc.name == '滤芯标志位') {
          attributeKeyPrefix = desc.value.replaceAll('Select', '');
        }
        if (desc.name == '滤芯级数') {
          level = desc.value;
        }
        propDesc.add(desc);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['name'] = name;
    data['detailsUrl'] = detailsUrl;
    data['attributeKeyPrefix'] = attributeKeyPrefix;
    data['filterIndex'] = filterIndex;
    data['propDesc'] = propDesc.map((PropDesc e) => e.toJson()).toList();
    return data;
  }

  @override
  String toString() {
    return 'ConsumableModel{code: $code, name: $name, level: $level,detailsUrl: $detailsUrl, attributeKeyPrefix: $attributeKeyPrefix, propDesc: $propDesc}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurifiedConsumableModel &&
          runtimeType == other.runtimeType &&
          code == other.code &&
          name == other.name &&
          filterIndex == other.filterIndex &&
          attributeKeyPrefix == other.attributeKeyPrefix &&
          detailsUrl == other.detailsUrl &&
          listEquals(propDesc, other.propDesc);

  @override
  int get hashCode =>
      code.hashCode ^
      name.hashCode ^
      detailsUrl.hashCode ^
      attributeKeyPrefix.hashCode ^
      filterIndex.hashCode ^
      listHashCode(propDesc);
}

class PropDesc {
  String name = '';
  String value = '';

  PropDesc(this.name, this.value);

  PropDesc.fromJson(Map<dynamic, dynamic> json) {
    name = json.stringValueForKey('name', '').trim();
    value = json.stringValueForKey('value', '').trim();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['value'] = value;
    return data;
  }

  @override
  String toString() {
    return 'PropDesc{name: $name, value: $value}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropDesc &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          value == other.value;

  @override
  int get hashCode => name.hashCode ^ value.hashCode;
}
