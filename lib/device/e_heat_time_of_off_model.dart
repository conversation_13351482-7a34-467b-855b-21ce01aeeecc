/*
 * 描述：定时开关机接口数据模型
 * 作者：songFJ
 * 创建时间：2024/7/22
 */

import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class EHeatTimeOnOffResponseModel extends UhomeResponseModel {
  EHeatTimeOnOffResponseModel.fromJson(super.data) : super.fromJson() {
    data = EHeatTimeOnOffDataModel.fromJson(super.retData);
  }

  EHeatTimeOnOffDataModel? data;

  @override
  String toString() {
    return 'EHeatTimeOnOffResponseModel{data: $data}';
  }
}

class EHeatTimeOnOffDataModel {
  bool? onOffStatus;

  EHeatTimeOnOffDataModel({this.onOffStatus});

  EHeatTimeOnOffDataModel.fromJson(Map<dynamic, dynamic> json) {
    onOffStatus = json.boolValueForKey('onOffStatus', false);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['onOffStatus'] = onOffStatus?.toString();
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EHeatTimeOnOffDataModel && onOffStatus == other.onOffStatus;

  @override
  int get hashCode => onOffStatus.hashCode;

  @override
  String toString() {
    return 'EHeatTimeOnOffDataModel{onOffStatus: $onOffStatus}';
  }
}
