import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component_view_model/wash_program_more_select_view_model.dart';

class WashProgramMoreSelectComponent extends StatelessWidget {
  const WashProgramMoreSelectComponent({super.key, required this.viewModel});

  final WashProgramMoreSelectViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (viewModel.programSelect != null) {
          viewModel.programSelect!(context);
        }
      },
      child: ClickEffectCircularWidget(
        enable: true,
        isOn: false,
        offColor: AppSemanticColors.item.invert,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SmartHomeText(
                text: '更多',
                fontSize: 14,
                color: AppSemanticColors.item.primary),
            Image.asset(
              'assets/icons/wash_select_more.webp',
              width: 12,
              height: 12,
              package: SmartHomeConstant.package,
            ),
          ],
        ),
      ),
    );
  }
}
