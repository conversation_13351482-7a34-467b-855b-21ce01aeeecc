/*
 * 描述：多筒洗衣机筒状态组件
 * 作者：songFJ
 * 创建时间：2024/12/19
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component/widget/debounce_throttler/throttler_widget.dart';
import 'package:smart_home/device/component_view_model/wash_roller_component_view_model.dart';

class WashRollerComponent extends StatelessWidget {
  const WashRollerComponent({super.key, required this.viewModel});

  final WashRollerComponentViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return ThrottlerWidget(
      throttlerCallback: (BuildContext context) {
        viewModel.rollerClickCallback?.call(context);
      },
      child: Opacity(
        opacity: viewModel.enable
            ? ComponentOpacity.enable
            : ComponentOpacity.disable,
        child: ClickEffectCircularWidget(
          enable: viewModel.enable,
          isOn: false,
          offColor: AppSemanticColors.item.invert,
          child: Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: ComponentPadding.cardSmall),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SmartHomeText(
                          text: viewModel.rollerName,
                          fontSize: 14,
                          color: AppSemanticColors.item.primary),
                      const SizedBox(height: 2),
                      Row(
                        children: <Widget>[
                          Flexible(
                            child: SmartHomeText(
                              text: viewModel.rollerState,
                              fontSize: 12,
                              color: AppSemanticColors.item.secondary,
                            ),
                          ),
                          if (viewModel.rollerDesc.isNotEmpty)
                            Container(
                              constraints: const BoxConstraints(maxWidth: 100),
                              child: SmartHomeText(
                                text: '丨${viewModel.rollerDesc}',
                                fontSize: 12,
                                color: AppSemanticColors.item.secondary,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: ComponentGap.component),
                  child: Image.asset(
                    'assets/icons/arrow_black.png',
                    package: SmartHomeConstant.package,
                    color: AppSemanticColors.item.tertiary,
                    height: 16,
                    width: 16,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
