import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import '../../common/component_constant.dart';
import '../../common/constant.dart';
import '../../store/smart_home_store.dart';
import '../component_view_model/circular_switch_view_model.dart';

class CircularSwitchComponent extends StatelessWidget {
  const CircularSwitchComponent({super.key, required this.viewModel});

  final CircularSwitchComponentModel viewModel;

  @override
  Widget build(BuildContext context) {
    final bool isComponentEnabled =
        viewModel.enable && !smartHomeStore.state.isEditState;
    final double opacity =
        isComponentEnabled ? ComponentOpacity.enable : ComponentOpacity.disable;
    final Color imageColor = viewModel.selected
        ? AppSemanticColors.item.information.primary
        : AppSemanticColors.item.primary;
    return Opacity(
      opacity: opacity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          ClickEffectCircularWidget(
              width: ComponentWidth.button,
              height: ComponentHeight.button,
              showBorder: true,
              offColor: AppSemanticColors.background.primary,
              borderRadius: ComponentRadius.window,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  viewModel.clickCallback?.call(context);
                },
                child: Container(
                  height: ComponentHeight.button,
                  width: ComponentWidth.button,
                  alignment: Alignment.center,
                  child: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      imageColor,
                      BlendMode.srcIn,
                    ),
                    child: Image.asset(
                      viewModel.icon.isNotEmpty
                          ? viewModel.icon
                          : 'assets/icons/no_text.png',
                      package: SmartHomeConstant.package,
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
              ),
              enable: isComponentEnabled,
              isOn: viewModel.selected),
          const SizedBox(
            height: ComponentGap.widget,
          ),
          Padding(padding: const EdgeInsets.symmetric(horizontal: 2),
              child: SmartHomeText(
                  text: viewModel.text,
                  fontSize: 12,
                  color: AppSemanticColors.item.primary),)
        ],
      ),
    );
  }
}
