/*
 * 描述：洗衣机筒选择组件
 * 作者：songFJ
 * 创建时间：2025/5/12
 */

import 'package:flutter/material.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component_view_model/grid_view_model.dart';

import '../factory/component_factory.dart';

class GridWidget extends StatelessWidget {
  const GridWidget({super.key, required this.viewModel});

  final GridViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(
          horizontal: 16, vertical: ComponentMargin.pageTop),
      itemCount: viewModel.componentList.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: viewModel.crossAxisCount,
          mainAxisSpacing: viewModel.mainAxisSpacing,
          crossAxisSpacing: viewModel.crossAxisSpacing,
          mainAxisExtent: viewModel.mainAxisExtent),
      itemBuilder: (BuildContext context, int index) {
        return Container(
          alignment: Alignment.center,
          child: ComponentFactory.componentWidget(
              viewModel: viewModel.componentList[index]),
        );
      },
    );
  }
}
