/*
 * 描述：弹出框底部返回按钮
 * 作者：songFJ
 * 创建时间：2025/5/12
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';

import '../../common/component_constant.dart';
import '../../common/smart_home_text_widget.dart';

enum PopupButtonType {
  primary,
  secondary,
}

class PopupBottom extends StatelessWidget {
  const PopupBottom(
      {super.key,
      required this.viewModel,
      this.popupButtonType = PopupButtonType.secondary});

  final PopupComponentViewModel? viewModel;
  final PopupButtonType popupButtonType;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: ComponentPadding.largeTop,
        horizontal: ComponentMargin.page,
      ),
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
          width: double.infinity,
          height: 44,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(ComponentRadius.componentLarge),
            color: popupButtonType == PopupButtonType.primary
                ? AppSemanticColors.component.primary.fill
                : AppSemanticColors.component.secondary.invert,
          ),
          child: Center(
            child: SmartHomeText(
              text: viewModel?.cancelTitle ?? '返回',
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: popupButtonType == PopupButtonType.primary
                  ? AppSemanticColors.container.card
                  : AppSemanticColors.component.secondary.on,
            ),
          ),
        ),
      ),
    );
  }
}
