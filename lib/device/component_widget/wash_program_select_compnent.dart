import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component_view_model/wash_program_select_component_view_model.dart';

class WashProgramSelectComponent extends StatelessWidget {
  const WashProgramSelectComponent({super.key, required this.viewModel});

  final WashProgramSelectComponentViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (viewModel.programSelect != null) {
          viewModel.programSelect!(context);
        }
      },
      child: ClickEffectCircularWidget(
        enable: true,
        showBorder: viewModel.showBorder,
        isOn: viewModel.selected,
        offColor: AppSemanticColors.item.invert,
        child: Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: ComponentPadding.cardMiddle),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              SmartHomeText(
                text: viewModel.title,
                fontSize: 14,
                color: viewModel.selected
                    ? AppSemanticColors.item.information.primary
                    : AppSemanticColors.item.primary,
              ),
              const SizedBox(
                height: 4,
              ),
              SmartHomeText(
                text: viewModel.desc,
                fontSize: 12,
                color: viewModel.selected
                    ? AppSemanticColors.item.information.primary
                    : AppSemanticColors.item.secondary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
