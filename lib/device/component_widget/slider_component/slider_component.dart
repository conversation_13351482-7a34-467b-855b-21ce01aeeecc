import 'dart:async';

import 'package:flutter/material.dart';
import 'package:smart_home/device/component_view_model/slider_component_view_model.dart';
import 'package:smart_home/device/component_widget/slider_component/linear_gradient_slider_track_shape.dart';

class SliderComponent extends StatefulWidget {
  const SliderComponent({super.key, required this.viewModel});

  final SliderComponentViewModel viewModel;

  @override
  _SliderComponentState createState() => _SliderComponentState();
}

class _SliderComponentState extends State<SliderComponent> {
  double currentValue = 0;
  Timer? _timer;
  bool isOperating = false;

  @override
  void initState() {
    super.initState();
    currentValue = widget.viewModel.currentValue;
  }

  @override
  void didUpdateWidget(covariant SliderComponent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!isOperating) {
      setState(() {
        currentValue = widget.viewModel.currentValue;
      });
    }
  }

  void handleValueChange(double value) {
    if (widget.viewModel.isDisabled) {
      return;
    }
    _timer?.cancel();
    if (mounted) {
      setState(() {
        currentValue = value;
        isOperating = true;
      });
    }
  }

  void handleValueChangeEnd() {
    if (widget.viewModel.slideValueChange != null) {
      widget.viewModel.slideValueChange!(currentValue);
    }
    _timer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          currentValue = widget.viewModel.currentValue;
          isOperating = false;
        });
      }
      _timer = null;
    });
  }

  bool panDown = false;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onLongPress: () {},
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: _buildSliderWidget(),
        ),
      ),
    );
  }

  Widget _buildSliderWidget() {
    return Listener(
      onPointerDown: (_) {
        setState(() {
          if (!widget.viewModel.isDisabled) {
            panDown = true;
          }
        });
      },
      onPointerUp: (_) {
        setState(() {
          if (!widget.viewModel.isDisabled) {
            panDown = false;
          }
        });
      },
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
            trackShape: LinearGradientSliderTrackShape(
                widget.viewModel.colorType == SliderColorType.brightness
                    ? widget.viewModel.isDisabled
                        ? <int>[0x19000000, 0x19000000]
                        : <int>[0xFFFFE8A5, 0xFFFFD38A]
                    : <int>[0xFFF5F5F5, 0xFFF5A623],
                radius: panDown ? 18 : 12),
            thumbColor: Colors.white,
            overlayColor: Colors.white,
            overlayShape: const RoundSliderOverlayShape(
              overlayRadius: 0,
            ),
            thumbShape: RoundSliderThumbShape(
                disabledThumbRadius: panDown ? 18 : 12,
                enabledThumbRadius: panDown ? 18 : 12),
            inactiveTickMarkColor: Colors.transparent,
            activeTickMarkColor: Colors.transparent,
            trackHeight: panDown ? 36 : 24),
        child: Slider(
          value: currentValue,
          onChanged: handleValueChange,
          onChangeEnd: (double value) {
            handleValueChangeEnd();
          },
          max: widget.viewModel.maxValue,
          min: widget.viewModel.minValue,
        ),
      ),
    );
  }
}
