import 'package:flutter/material.dart';

class LinearGradientSliderTrackShape extends SliderTrackShape {
  const LinearGradientSliderTrackShape(this.backgroundColor,
      {this.disabledThumbGapWidth = 2.0, this.radius = 0});

  final double disabledThumbGapWidth;
  final double radius;
  final List<int> backgroundColor;

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double overlayWidth = sliderTheme.overlayShape
            ?.getPreferredSize(isEnabled, isDiscrete)
            .width ??
        0;
    final double trackHeight = sliderTheme.trackHeight ?? 0;

    final double trackLeft = offset.dx + overlayWidth / 2;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;

    final double trackWidth = parentBox.size.width - overlayWidth;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isEnabled = false,
    bool isDiscrete = false,
    required TextDirection textDirection,
  }) {
    if (sliderTheme.trackHeight == 0) {
      return;
    }

    final Paint rightTrackPaint = Paint()
      ..color = const Color(0xFFF5F5F5);
    Paint leftTrackPaint = Paint()..strokeCap = StrokeCap.butt;

    double horizontalAdjustment = 0.0;
    if (!isEnabled && sliderTheme.thumbShape != null) {
      final double disabledThumbRadius =
          sliderTheme.thumbShape!.getPreferredSize(false, isDiscrete).width /
              2.0;
      final double gap = disabledThumbGapWidth * (1.0 - enableAnimation.value);
      horizontalAdjustment = disabledThumbRadius + gap;
    }

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    double thumbWidth =
        sliderTheme.thumbShape?.getPreferredSize(isEnabled, isDiscrete).width ??
            0;

    leftTrackPaint.strokeWidth = trackRect.height;

    leftTrackPaint.shader = LinearGradient(
      begin: Alignment.topRight,
      end: Alignment.bottomLeft,
      colors: backgroundColor.length >= 2
          ? backgroundColor.map((int e) => Color(e)).toList()
          : <Color>[Colors.blue, Colors.orangeAccent],
    ).createShader(Rect.fromLTRB(
        trackRect.left, trackRect.top, trackRect.width, trackRect.height));

    final RRect leftTrackSegment = RRect.fromLTRBAndCorners(
      trackRect.left - thumbWidth / 2,
      trackRect.top,
      thumbCenter.dx - horizontalAdjustment,
      trackRect.bottom,
      topLeft: Radius.circular(radius),
      bottomLeft: Radius.circular(radius),
    );
    context.canvas.drawRRect(leftTrackSegment, leftTrackPaint);
    final RRect rightTrackSegment = RRect.fromLTRBAndCorners(
      thumbCenter.dx + horizontalAdjustment,
      trackRect.top,
      trackRect.right + thumbWidth / 2,
      trackRect.bottom,
      topRight: Radius.circular(radius),
      bottomRight: Radius.circular(radius),
    );
    context.canvas.drawRRect(rightTrackSegment, rightTrackPaint);
  }
}
