import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component_view_model/wash_bucket_switch_component_view_model.dart';

class WashBucketSwitchComponent extends StatelessWidget {
  const WashBucketSwitchComponent({super.key, required this.viewModel});

  final WashBucketSwitchComponentViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (viewModel.bucketSelect != null) {
          viewModel.bucketSelect!(context);
        }
      },
      child: ClickEffectCircularWidget(
        enable: true,
        isOn: viewModel.selected,
        showBorder: viewModel.showBorder,
        offColor: AppSemanticColors.item.invert,
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: ComponentPadding.cardMiddle),
            child: SmartHomeText(
              text: viewModel.title,
              fontSize: 14,
              color: viewModel.selected
                  ? AppSemanticColors.item.information.primary
                  : AppSemanticColors.item.primary,
            ),
          ),
        ),
      ),
    );
  }
}
