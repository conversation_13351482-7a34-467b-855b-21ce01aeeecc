/*
 * 描述：燃热卡片纯文本开关类组件
 * 作者：武旭
 * 创建时间：2025/3/27
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';
import 'package:smart_home/device/component/widget/debounce_throttler/throttler_widget.dart';

import '../../../store/smart_home_store.dart';
import '../../common/component_constant.dart';
import '../component_view_model/g_water_heater_fixed_switch_text_view_model.dart';

class GWaterHeaterFixedSwitchTextWidget extends StatelessWidget {
  const GWaterHeaterFixedSwitchTextWidget({super.key, required this.viewModel});

  final GWaterHeaterFixedTextViewModel viewModel;

  Widget _buildContent(BuildContext context) {
    final bool isComponentEnabled =
        viewModel.enable && !smartHomeStore.state.isEditState;
    final double opacity =
        isComponentEnabled ? ComponentOpacity.enable : ComponentOpacity.disable;
    return Opacity(
      opacity: opacity,
      child: ClickEffectCircularWidget(
        enable: isComponentEnabled,
        isOn: viewModel.isOn,
        child: _buildTextContent(),
      ),
    );
  }

  Widget _buildTextContent() {
    final Color textColor = viewModel.isOn
        ? AppSemanticColors.item.information.primary
        : AppSemanticColors.item.primary;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        SmartHomeText(
          text: viewModel.title,
          fontSize: 10,
          fontWeight: FontWeight.w600,
          height: 1.1,
          color: textColor,
        ),
        if (viewModel.subTitle.isNotEmpty) const SizedBox(height: 6),
        if (viewModel.subTitle.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: SmartHomeText(
              text: viewModel.subTitle,
              height: 1.1,
              fontSize: 10,
              color: textColor,
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 52,
      height: 52,
      child: ThrottlerWidget(
          throttlerCallback: (BuildContext context) {
            viewModel.clickCallback?.call(context);
          },
          millionSeconds: viewModel.throttlerMillionSeconds,
          child: _buildContent(context)),
    );
  }
}
