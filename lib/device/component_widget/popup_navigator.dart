/*
 * 描述：弹出框顶部nav
 * 作者：songFJ
 * 创建时间：2025/5/12
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component_widget/static_popup_bottom_sheet.dart';

import '../../common/smart_home_text_widget.dart';
import '../component_view_model/popup_component_view_model.dart';

/// navTop
class PopupNavigator extends StatelessWidget {
  const PopupNavigator({super.key, required this.viewModel});

  final PopupComponentViewModel? viewModel;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 64,
      child: Padding(
        padding: const EdgeInsets.only(
            bottom: 12,
            left: ComponentPadding.cardMiddle,
            right: ComponentPadding.cardMiddle),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 4),
              child: Container(
                width: 32,
                height: 4,
                decoration: BoxDecoration(
                  color: AppSemanticColors.component.secondary.emphasize,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(2),
                  ),
                ),
              ),
            ),

            /// 标题
            Expanded(
              child: Stack(
                children: <Widget>[
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: SmartHomeText(
                        text: viewModel?.title ?? '',
                        fontSize: 17,
                        fontWeight: FontWeight.w500,
                        color: AppSemanticColors.item.primary),
                  ),
                  if (viewModel?.subPopupComponentViewModel != null)
                    Positioned(
                      right: ComponentPadding.textIndent,
                      bottom: 0,
                      child: GestureDetector(
                        onTap: () {
                          final PopupComponentViewModel? subViewModel =
                              viewModel?.subPopupComponentViewModel;
                          if (subViewModel != null) {
                            showStaticPopupBottomSheet(subViewModel, context);
                          }
                        },
                        child: Image.asset(
                          'assets/icons/yiwen.webp',
                          package: 'smart_home',
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
