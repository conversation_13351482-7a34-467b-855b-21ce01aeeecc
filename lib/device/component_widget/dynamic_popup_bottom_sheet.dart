/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2025/5/9
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';
import 'package:smart_home/device/component_widget/popup_bottom.dart';
import 'package:smart_home/device/component_widget/popup_navigator.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../common/smart_home_util.dart';
import '../../store/smart_home_store.dart';
import '../component_view_model/component_view_model.dart';
import '../factory/component_factory.dart';
import '../store/device_action.dart';

void showDynamicPopupBottomSheet(BuildContext context) {
  const String deviceCardFuncSet = 'deviceCardFuncSet';
  final double height = MediaQuery.of(context).size.height;
  showModalBottomSheet<dynamic>(
      barrierColor: AppSemanticColors.container.cover
          .withOpacity(ComponentOpacity.disable),
      backgroundColor: Colors.transparent,
      scrollControlDisabledMaxHeightRatio: (height - 98) / height,
      context: context,
      builder: (BuildContext context) {
        final double paddingBottom = MediaQuery.of(context).padding.bottom;

        return StoreProvider<SmartHomeState>(
            store: smartHomeStore,
            child: Padding(
              padding: EdgeInsets.only(
                  left: 12,
                  right: 12,
                  bottom: (paddingBottom == 0)
                      ? ComponentMargin.popup
                      : paddingBottom),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(ComponentRadius.popup),
                  color: AppSemanticColors.container.popup,
                ),
                child: StoreConnector<SmartHomeState, PopupComponentViewModel?>(
                  distinct: true,
                  converter: (Store<SmartHomeState> store) {
                    return store.state.deviceState.popupComponentViewModel;
                  },
                  builder: (BuildContext context,
                      PopupComponentViewModel? viewModel) {
                    final List<Widget> componentList = <Widget>[];

                    viewModel?.componentList
                        .forEach((ComponentBaseViewModel element) {
                      componentList.add(
                          ComponentFactory.componentWidget(viewModel: element));
                    });

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        PopupNavigator(viewModel: viewModel),

                        /// content
                        ...componentList,

                        /// bottom
                        PopupBottom(
                          viewModel: viewModel,
                        ),
                      ],
                    );
                  },
                ),
              ),
            ));
      }).whenComplete(() {
    Future<void>.delayed(const Duration(milliseconds: 200), () {
      smartHomeStore.dispatch(UpdatePopupComponentViewModelAction(null));
    });
    InterceptSystemBackUtil.cancelInterceptSystemBack(deviceCardFuncSet);
  });

  InterceptSystemBackUtil.interceptSystemBack(
      pageName: deviceCardFuncSet, context: context);
}
