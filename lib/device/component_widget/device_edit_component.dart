import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';

class DeviceEditComponent extends StatelessWidget {
  final DeviceEditComponentViewModel viewModel;

  const DeviceEditComponent({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        viewModel.selectedClick();
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 4, right: 4),
        child: SizedBox(
          width: 24,
          height: double.infinity,
          child: Align(
            alignment: Alignment.topCenter,
            child: Image.asset(
              viewModel.isSelected
                  ? 'assets/icons/icon_edit_select.webp'
                  : 'assets/icons/icon_edit_unselect.webp',
              width: 24,
              height: 24,
              package: SmartHomeConstant.package,
            ),
          ),
        ),
      ),
    );
  }
}
