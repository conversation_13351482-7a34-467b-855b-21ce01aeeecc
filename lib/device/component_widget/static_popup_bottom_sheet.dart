/*
 * 描述：PMV描述弹窗
 * 作者：songFJ
 * 创建时间：2025/3/26
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';
import 'package:smart_home/device/component_widget/popup_bottom.dart';
import 'package:smart_home/device/component_widget/popup_navigator.dart';

import '../../common/component_constant.dart';
import '../component_view_model/component_view_model.dart';
import '../factory/component_factory.dart';

/// 只column布局
void showStaticPopupBottomSheet(
    PopupComponentViewModel viewModel, BuildContext context) {
  final double height = MediaQuery.of(context).size.height;
  showModalBottomSheet<dynamic>(
      barrierColor: AppSemanticColors.container.cover
          .withOpacity(ComponentOpacity.disable),
      backgroundColor: Colors.transparent,
      scrollControlDisabledMaxHeightRatio: (height - 98) / height,
      context: context,
      builder: (BuildContext context) {
        final double paddingBottom = MediaQuery.of(context).padding.bottom;

        final List<Widget> componentList = <Widget>[];

        viewModel.componentList.forEach((ComponentBaseViewModel element) {
          componentList
              .add(ComponentFactory.componentWidget(viewModel: element));
          componentList.add(const SizedBox(
            height: ComponentMargin.pageTop,
          ));
        });

        return Padding(
          padding: EdgeInsets.only(
              left: 12,
              right: 12,
              bottom:
                  (paddingBottom == 0) ? ComponentMargin.popup : paddingBottom),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(ComponentRadius.popup),
              color: AppSemanticColors.container.popup,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                /// navTop
                PopupNavigator(viewModel: viewModel),

                /// column布局组件
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: ComponentPadding.cardLarge),
                  child: Column(
                    children: componentList,
                  ),
                ),

                /// bottom
                PopupBottom(
                  viewModel: viewModel,
                  popupButtonType: PopupButtonType.primary,
                ),
              ],
            ),
          ),
        );
      }).whenComplete(() {});
}
