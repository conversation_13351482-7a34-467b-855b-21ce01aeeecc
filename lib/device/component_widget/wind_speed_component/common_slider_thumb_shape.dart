import 'package:flutter/material.dart';

/// 通用滑块组件(带3层阴影)
class CommonSliderThumbShape extends SliderComponentShape {
  const CommonSliderThumbShape({required this.thumbRadius});

  final double thumbRadius;

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) =>
      Size.fromRadius(thumbRadius);

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter? labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // 叠加多层阴影
    final List<BoxShadow> shadows = <BoxShadow>[
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        offset: const Offset(0, 3),
        blurRadius: 10,
        spreadRadius: 1,
      ),
      BoxShadow(
        color: Colors.black.withOpacity(0.06),
        offset: const Offset(0, 8),
        blurRadius: 10,
        spreadRadius: 1,
      ),
      BoxShadow(
        color: Colors.black.withOpacity(0.10),
        offset: const Offset(0, 5),
        blurRadius: 5,
        spreadRadius: -3,
      ),
    ];

    for (final BoxShadow shadow in shadows) {
      canvas.drawCircle(
        center + shadow.offset,
        thumbRadius + shadow.spreadRadius,
        Paint()
          ..color = shadow.color
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadow.blurRadius),
      );
    }

    canvas.drawCircle(center, thumbRadius, Paint()..color = Colors.white);
  }
}
