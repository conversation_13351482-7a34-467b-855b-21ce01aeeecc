import 'package:flutter/material.dart';

class DivisionSliderThumbShape extends SliderComponentShape {
  const DivisionSliderThumbShape(this.thumbColor);

  final int thumbColor;

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.zero;
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    Paint fillPaint = Paint()
      ..color = Color(thumbColor)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(center.dx, center.dy), 16, fillPaint);
  }
}
