import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class DivisionSliderTrackShape extends SliderTrackShape {
  DivisionSliderTrackShape(this.value, this.labelList, this.activeLabelColor,
      this.inactiveLabelColor,
      {this.radius = 0,
      this.isAlign = true,
      this.disabled = false,
      this.sliderToTop = 24,
      this.textToTop = 52});

  final Color activeLabelColor;
  final bool disabled;
  final bool isAlign;
  final Color inactiveLabelColor;
  final List<String> labelList;
  final double radius;
  final double sliderToTop; // 滚动条距离顶部偏移量
  final double textToTop; // 文本距离顶部偏移量
  final double value;

  // 滑块颜色渐变配置
  static const double _kInitialOpacity = 0.14; // 14%
  static const double _kFinalOpacity = 1.0; // 100%
  static const int _kOpacityBase = 100; // 透明度百分比基数

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double overlayWidth = sliderTheme.overlayShape
            ?.getPreferredSize(isEnabled, isDiscrete)
            .width ??
        0;
    final double trackHeight = sliderTheme.trackHeight ?? 0;

    final double trackLeft = offset.dx + overlayWidth / 2;
    final double trackTop = sliderToTop;

    final double trackWidth = parentBox.size.width - overlayWidth;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isEnabled = false,
    bool isDiscrete = false,
    required TextDirection textDirection,
  }) {
    if (sliderTheme.trackHeight == 0 || this.labelList.length <= 1) {
      return;
    }

    final List<String> labelList = this.labelList;

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    // 初始透明度14，最终透明度100
    // 透明度是 等差数列：根据有多少块进行计算
    final List<Color> rangeColorList = <Color>[
      AppSemanticColors.item.information.primary.withOpacity(_kInitialOpacity),
      AppSemanticColors.item.information.primary
    ];

    // 计算透明度等差数列
    final double delta =
        (_kOpacityBase * _kFinalOpacity - _kOpacityBase * _kInitialOpacity) /
            (labelList.length - 1);
    for (int i = 1; i < labelList.length - 2; i++) {
      final Color color = AppSemanticColors.item.information.primary
          .withOpacity(
              (_kOpacityBase * _kInitialOpacity + i * delta) / _kOpacityBase);
      rangeColorList.insert(i, color);
    }

    final TextPainter textPainter = TextPainter();
    final TextSpan textSpan = TextSpan(
      text: labelList[0],
      style: TextStyle(
          fontSize: 12,
          color: value == 0 ? activeLabelColor : inactiveLabelColor),
    );

    textPainter.text = textSpan;
    textPainter.textDirection = TextDirection.ltr;
    textPainter.textAlign = TextAlign.center;
    textPainter.layout(maxWidth: 50);
    textPainter.paint(
      context.canvas,
      Offset(isAlign ? 0 : -textPainter.width / 2, textToTop),
    );

    final RRect firstTrackSegment = RRect.fromLTRBAndCorners(
      trackRect.left,
      trackRect.top,
      trackRect.right / (labelList.length - 1),
      trackRect.bottom,
      topLeft: Radius.circular(radius),
      bottomLeft: Radius.circular(radius),
    );
    final Paint firstTrackPaint = Paint()
      ..color = disabled ? (const Color(0xFFCCCCCC)) : rangeColorList[0]
      ..style = PaintingStyle.fill;
    context.canvas.drawRRect(firstTrackSegment, firstTrackPaint);

    for (int i = 1; i < labelList.length; i++) {
      final TextPainter textPainter = TextPainter();
      final TextSpan textSpan = TextSpan(
        text: labelList[i],
        style: TextStyle(
            fontSize: 12,
            color: value == i ? activeLabelColor : inactiveLabelColor),
      );

      textPainter.text = textSpan;
      textPainter.textDirection = TextDirection.ltr;
      textPainter.textAlign = TextAlign.center;
      textPainter.layout(maxWidth: 50);

      textPainter.paint(
        context.canvas,
        Offset(
            !isAlign
                ? trackRect.right / (labelList.length - 1) * i -
                    textPainter.width / 2
                : (i == labelList.length - 1
                    ? trackRect.right / (labelList.length - 1) * i -
                        textPainter.width
                    : trackRect.right / (labelList.length - 1) * i -
                        textPainter.width / 2),
            textToTop),
      );

      if (i != labelList.length - 1) {
        final RRect iTrackSegment = RRect.fromLTRBAndCorners(
          trackRect.right / (labelList.length - 1) * i,
          trackRect.top,
          trackRect.right / (labelList.length - 1) * (i + 1),
          trackRect.bottom,
          topRight:
              i == labelList.length - 2 ? Radius.circular(radius) : Radius.zero,
          bottomRight:
              i == labelList.length - 2 ? Radius.circular(radius) : Radius.zero,
        );
        final Paint iTrackPaint = Paint()
          ..color = i < rangeColorList.length
              ? (disabled ? const Color(0xFFCCCCCC) : rangeColorList[i])
              : AppSemanticColors.item.primary
          ..style = PaintingStyle.fill;
        context.canvas.drawRRect(iTrackSegment, iTrackPaint);
      }
    }
  }
}
