import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';

import '../../common/debounce.dart';

final SpringSimulation quickControlSimulation = SpringSimulation(
    const SpringDescription(
        mass: 12, //质量
        stiffness: 1, //硬度
        damping: 1.5 // 阻尼
        ),
    0,
    1,
    40 // 速度
    );

class ButtonControlAnimation extends StatefulWidget {
  const ButtonControlAnimation({
    super.key,
    this.quickControl,
    this.multiClickCallback,
    this.isEdit,
    this.scaleMin,
    this.scaleMax,
    required this.contentWidget,
    this.delayCallback,
  });

  final Widget contentWidget;

  // final Function quickControl;
  final void Function(BuildContext? context)? quickControl;
  final void Function(BuildContext? context)? multiClickCallback;
  final bool? isEdit;
  final double? scaleMin;
  final double? scaleMax;
  final bool? delayCallback; // 延迟1000ms

  @override
  State<ButtonControlAnimation> createState() => _ButtonControlAnimationState();
}

class _ButtonControlAnimationState extends State<ButtonControlAnimation>
    with TickerProviderStateMixin {
  // 动效 改变大小
  double scale = 1.0;
  AnimationController? _quickController1;
  Animation<double>? _quickAnimation1;
  AnimationController? _quickController2;
  Animation<double>? _quickAnimation2;

  final Throttler _throttler = Throttler(milliseconds: 1000);

  void handleQuick() {
    _quickController2?.stop();
    _quickController1?.reset();
    _quickController1?.forward();
  }

  @override
  void initState() {
    // 动效 初始化
    initQuickControlAnimation();
    super.initState();
  }

  // 回弹弹簧动效
  void startQuickControlAnimation() {
    _quickAnimation2 = _quickController2?.drive(Tween<double>(
        begin: widget.scaleMin ?? 0.9, end: widget.scaleMax ?? 1.0));
    _quickController2?.animateWith(quickControlSimulation);
  }

  // 动效
  void initQuickControlAnimation() {
    if (widget.isEdit != null && widget.isEdit!) {
      return;
    }
    try {
      _quickController1 = AnimationController(
          vsync: this, duration: const Duration(milliseconds: 100));
      if (_quickController1 != null) {
        _quickAnimation1 = Tween<double>(
                begin: widget.scaleMax ?? 1.0, end: widget.scaleMin ?? 0.9)
            .animate(CurvedAnimation(
                parent: _quickController1!, curve: Curves.easeInOutCubic));
        // 回弹效果
        _quickController2 = AnimationController.unbounded(vsync: this);
        _quickController2?.addListener(() {
          if (_quickAnimation2 != null) {
            if (_quickAnimation2!.value > 1.0) {
              scale = 1.0;
            } else {
              scale = _quickAnimation2!.value;
            }
          }
          setState(() {});
        });
        _quickController1?.addListener(() {
          scale = _quickAnimation1!.value;
          setState(() {});
        });
        _quickController1?.addStatusListener((AnimationStatus status) {
          if (status == AnimationStatus.completed) {
            startQuickControlAnimation();

            Future<dynamic>.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                _quickController2?.stop();
              }
            });
          }
        });
        _quickController2?.addStatusListener((AnimationStatus status) {
          if (status == AnimationStatus.completed) {}
        });
      }
    } catch (e) {}
  }

  @override
  void dispose() {
    _quickController1?.dispose();
    _quickController2?.dispose();
    _throttler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        handleQuick();
        if (widget.delayCallback ?? false) {
          _throttler.run(() {
            widget.quickControl?.call(context);
          }, multiClickCallback: () {
            widget.multiClickCallback?.call(context);
          });
        } else {
          widget.quickControl?.call(context);
        }
      },
      child: widget.isEdit != null && widget.isEdit!
          ? widget.contentWidget
          : Transform.scale(
              scale: scale,
              child: widget.contentWidget,
            ),
    );
  }
}

Function debounce<T>(T? func,
    [Duration delay = const Duration(milliseconds: 2000),
    bool immediately = true]) {
  Timer? timer;
  void target() {
    if (timer?.isActive ?? false) {
      timer?.cancel();
    }
    // 立即执行
    if (immediately) {
      // 没有定时器，立即执行
      final bool callNow = timer == null;
      // 给定时器赋值
      timer = Timer(delay, () {
        timer!.cancel();
        timer = null;
      });
      if (callNow) {
        if (func is Function) {
          func.call();
        }
      }
    } else {
      timer = Timer(delay, () {
        if (func is Function) {
          func.call();
        }
      });
    }
  }

  return target;
}
