import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component_view_model/power_on_off_btn_model/frame_animation_image_model.dart';

class FrameAnimationImage extends StatefulWidget {
  const FrameAnimationImage(
      {super.key,
      required this.animationImageModel,
      required this.duration,
      this.repeat = true,
      this.visible = false,
      this.reverse = false});

  final FrameAnimationImageModel animationImageModel;
  final bool repeat;
  final Duration duration;
  final bool visible;
  final bool reverse;

  @override
  State<FrameAnimationImage> createState() => _FrameAnimationImageState();
}

class _FrameAnimationImageState extends State<FrameAnimationImage>
    with TickerProviderStateMixin {
  AnimationController? _animationController;
  Animation<int>? _animation;

  @override
  void initState() {
    super.initState();
    _animationController =
        AnimationController(vsync: this, duration: widget.duration);
    if (_animationController != null) {
      if (widget.reverse) {
        _animation = ReverseTween<int>(IntTween(
                begin: widget.animationImageModel.beginIndex,
                end: widget.animationImageModel.endIndex))
            .animate(_animationController!);
      } else {
        _animation = IntTween(
                begin: widget.animationImageModel.beginIndex,
                end: widget.animationImageModel.endIndex)
            .animate(_animationController!);
      }
    }

    if (widget.visible) {
      if (widget.repeat) {
        _animationController?.repeat();
      } else {
        _animationController?.forward();
      }
    }
  }

  @override
  void didUpdateWidget(covariant FrameAnimationImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.visible != widget.visible ||
        oldWidget.repeat != widget.repeat) {
      if (widget.visible) {
        if (widget.repeat) {
          _animationController?.repeat();
        } else {
          if (_animationController?.status == AnimationStatus.completed) {
            _animationController?.reset();
            _animationController?.forward();
          } else {
            _animationController?.forward();
          }
        }
      } else {
        _animationController?.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.animationImageModel.assetsPath.isEmpty ||
        widget.animationImageModel.imagePrefix.isEmpty ||
        widget.animationImageModel.imageType.isEmpty) {
      return Container();
    }
    if (_animation == null) {
      return Container();
    }
    return AnimatedBuilder(
        animation: _animation!,
        builder: (BuildContext context, Widget? child) {
          return Image.asset(
            '${widget.animationImageModel.assetsPath}${widget.animationImageModel.imagePrefix}${_animation?.value}.${widget.animationImageModel.imageType}',
            package: SmartHomeConstant.package,
            gaplessPlayback: true,
            fit: BoxFit.cover,
            scale: _dpr(),
            width: double.infinity,
            height: double.infinity,
          );
        });
  }

  // 获得设备的像素密度
  double _dpr() {
    return ScreenUtil().pixelRatio;
  }
}
