import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';

class DevicePowerImagePreloader {
  static const String logTag = 'DevicePowerImagePreloader';

  static final DevicePowerImagePreloader _instance =
      DevicePowerImagePreloader._internal();
  factory DevicePowerImagePreloader.getInstance() => _instance;
  DevicePowerImagePreloader._internal();

  bool _isPreloaded = false;

  void preloadDevicePowerImages(BuildContext context,
      {required String powerOnImg, required String powerOffImg}) {
    if (_isPreloaded) {
      return;
    }

    _isPreloaded = true;
    DevLogger.debug(
        tag: logTag, msg: 'preloadDevicePowerImages _preloadImage begin');

    Future.wait(<Future<void>>[
      _preloadImage(powerOnImg),
      _preloadImage(powerOffImg),
    ]).then((_) {
      DevLogger.debug(
          tag: logTag, msg: 'preloadDevicePowerImages _preloadImage end');
    }).catchError((dynamic e) {
      DevLogger.error(
          tag: logTag,
          msg: 'preloadDevicePowerImages _preloadImage error, err:$e');
    });
  }

  Future<void> _preloadImage(String imagePath) async {
    final ImageProvider imageProvider = AssetImage(
      imagePath,
      package: SmartHomeConstant.package,
    );

    imageProvider.resolve(ImageConfiguration.empty).addListener(
          ImageStreamListener((ImageInfo image, bool synchronousCall) {
            DevLogger.debug(
                tag: logTag, msg: '_preloadImage end, path: $imagePath');
          }, onError: (dynamic err, StackTrace? stackTrace) {
            DevLogger.error(
                tag: logTag,
                msg: '_preloadImage err, path: $imagePath, err:$err');
          }),
        );
  }
}
