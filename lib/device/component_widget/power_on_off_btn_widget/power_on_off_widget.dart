/*
 * 描述：开关机按钮
 * 作者：songFJ
 * 创建时间：2024/9/5
 */

import 'package:flutter/material.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/debounce.dart';
import 'package:smart_home/device/component_widget/power_on_off_btn_widget/frame_animation_image.dart';
import 'package:smart_home/device/component_widget/power_on_off_btn_widget/power_on_off_preload.dart';
import 'package:upsystem/upsystem.dart';

import '../../../common/constant.dart';
import '../../component_view_model/power_on_off_btn_model/frame_animation_image_model.dart';
import '../../component_view_model/power_on_off_btn_model/power_button_animation_view_model.dart';

class PowerOnOffWidget extends StatefulWidget {
  const PowerOnOffWidget({super.key, required this.viewModel});

  final PowerButtonAnimationViewModel viewModel;

  @override
  State<PowerOnOffWidget> createState() => _PowerOnOffWidgetState();
}

class _PowerOnOffWidgetState extends State<PowerOnOffWidget> {
  bool _isOn = false;
  bool _visible = false;

  final Throttler _throttler = Throttler(milliseconds: 1500);

  static const String _powerOnImg =
      'assets/on_off_animation/on_animation/on_animation_15.webp';
  static const String _powerOffImg =
      'assets/on_off_animation/off_animation/off_animation_9.webp';

  bool _hasPreloaded = false;

  @override
  void initState() {
    super.initState();
    _isOn = widget.viewModel.isOn;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasPreloaded) {
      DevicePowerImagePreloader.getInstance().preloadDevicePowerImages(
        context,
        powerOnImg: _powerOnImg,
        powerOffImg: _powerOffImg,
      );
      _hasPreloaded = true;
    }
  }

  @override
  void dispose() {
    _throttler.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant PowerOnOffWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_visible) {
      _isOn = widget.viewModel.isOn;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final double opacity = widget.viewModel.writable
        ? ComponentOpacity.enable
        : ComponentOpacity.disable;
    return GestureDetector(
      onTap: () {
        _throttler.run(() {
          UpSystem.impactFeedBack();

          widget.viewModel.checkContinue?.call().then((bool pass) {
            if (pass) {
              /// 按钮点击回调
              widget.viewModel.btnClick?.call(context);

              if (mounted) {
                setState(() {
                  _visible = true;
                });
              }
              Future<dynamic>.delayed(const Duration(milliseconds: 1500), () {
                _visible = false;
                _isOn = widget.viewModel.isOn;
                if (mounted) {
                  setState(() {});
                }
              });
            }
          });
        });
      },
      child: Opacity(
        opacity: opacity,
        child: SizedBox(
          width: 52,
          height: 52,
          child: _visible
              ? _animationWidget()
              : Image.asset(
                  _isOn ? _powerOnImg : _powerOffImg,
                  package: SmartHomeConstant.package,
                  height: double.infinity,
                  width: double.infinity,
                ),
        ),
      ),
    );
  }

  Widget _animationWidget() {
    return _isOn
        ? FrameAnimationImage(
            animationImageModel: FrameAnimationImageModel(
              assetsPath: 'assets/on_off_animation/off_animation/',
              imagePrefix: 'off_animation_',
              imageType: 'webp',
              beginIndex: 0,
              endIndex: 9,
            ),
            duration: const Duration(milliseconds: 300),
            repeat: false,
            visible: _visible,
          )
        : FrameAnimationImage(
            animationImageModel: FrameAnimationImageModel(
              assetsPath: 'assets/on_off_animation/on_animation/',
              imagePrefix: 'on_animation_',
              imageType: 'webp',
              beginIndex: 0,
              endIndex: 15,
            ),
            duration: const Duration(milliseconds: 500),
            repeat: false,
            visible: _visible,
          );
  }
}
