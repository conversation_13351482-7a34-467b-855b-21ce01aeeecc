/*
 * 描述：洗衣机筒选择gridView
 * 作者：songFJ
 * 创建时间：2025/5/12
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class GridViewModel extends ComponentBaseViewModel {
  final List<ComponentBaseViewModel> componentList;
  final int crossAxisCount;

  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double mainAxisExtent;

  GridViewModel(
      {required this.componentList,
      this.crossAxisCount = 2,
      this.mainAxisSpacing = 12,
      this.crossAxisSpacing = 12,
      this.mainAxisExtent = 44});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
          other is GridViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(componentList, other.componentList) &&
          crossAxisCount == other.crossAxisCount &&
          mainAxisSpacing == other.mainAxisSpacing &&
          crossAxisSpacing == other.crossAxisSpacing &&
          mainAxisExtent == other.mainAxisExtent;

  @override
  int get hashCode =>
      super.hashCode ^
      listHashCode(componentList) ^
      crossAxisCount.hashCode ^
      mainAxisSpacing.hashCode ^
      crossAxisSpacing.hashCode ^
      mainAxisExtent.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.gridView;
  }
}
