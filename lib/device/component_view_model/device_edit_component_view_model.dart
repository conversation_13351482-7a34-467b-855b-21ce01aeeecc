import 'package:smart_home/device/component_view_model/component_view_model.dart';

class DeviceEditComponentViewModel extends ComponentBaseViewModel {
  DeviceEditComponentViewModel({
    required this.isSelected,
    required this.selectedClick,
  });
  bool isSelected;
  void Function() selectedClick;

  @override
  ComponentType componentType() {
    return ComponentType.editSelect;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is DeviceEditComponentViewModel &&
          runtimeType == other.runtimeType &&
          isSelected == other.isSelected;

  @override
  int get hashCode => super.hashCode ^ isSelected.hashCode;
}
