import 'package:flutter/material.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class WashBucketSwitchComponentViewModel extends ComponentBaseViewModel {
  String title;
  bool selected;
  bool showBorder;
  void Function(BuildContext? context)? bucketSelect;

  WashBucketSwitchComponentViewModel(
      {this.title = '',
      this.selected = false,
      this.showBorder = false,
      this.bucketSelect});

  @override
  ComponentType componentType() {
    return ComponentType.washBucketSwitchComponent;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is WashBucketSwitchComponentViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          selected == other.selected &&
          showBorder == other.showBorder;

  @override
  int get hashCode =>
      super.hashCode ^ title.hashCode ^ selected.hashCode ^ showBorder.hashCode;
}
