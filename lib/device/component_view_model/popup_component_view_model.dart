/*
 * 描述：弹出框快捷功能集合
 * 作者：songFJ
 * 创建时间：2024/7/9
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class PopupComponentViewModel {
  String title;
  String cancelTitle;
  String deviceId;
  String attributeKey;
  List<ComponentBaseViewModel> componentList;

  PopupComponentViewModel? subPopupComponentViewModel;

  String get identification {
    return '${deviceId}_$attributeKey';
  }

  PopupComponentViewModel(
      {this.title = '',
      this.cancelTitle = '返回',
      required this.deviceId,
      required this.attributeKey,
      this.componentList = const <ComponentBaseViewModel>[],
      this.subPopupComponentViewModel});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is PopupComponentViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          cancelTitle == other.cancelTitle &&
          deviceId == other.deviceId &&
          attributeKey == other.attributeKey &&
          listEquals(componentList, other.componentList) &&
          subPopupComponentViewModel == other.subPopupComponentViewModel;

  @override
  int get hashCode =>
      title.hashCode ^
      cancelTitle.hashCode ^
      deviceId.hashCode ^
      attributeKey.hashCode ^
      listHashCode(componentList) ^
      subPopupComponentViewModel.hashCode;

  @override
  String toString() {
    return 'PopupComponentViewModel{title: $title, cancelTitle: $cancelTitle, deviceId: $deviceId, attributeKey: $attributeKey, componentList: $componentList, subPopupComponentViewModel: $subPopupComponentViewModel}';
  }
}
