import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';

class WindSpeedComponentViewModel extends ComponentBaseViewModel {
  WindSpeedComponentViewModel({
    required this.valueList,
    required this.selectedValue,
    required this.onChangeEnd,
    this.enable = true,
    this.isAlign = false,
    this.title,
    this.attribute,
    this.checkContinue,
    super.expandFlex,
  });

  final List<String> valueList;
  final double selectedValue;
  final bool isAlign;
  final String? title;
  final bool enable;
  final void Function(double selectedValue)? onChangeEnd;
  final SmartHomeDeviceAttribute? attribute;
  final Future<bool> Function(SmartHomeDeviceAttribute? attribute)?
      checkContinue;

  @override
  ComponentType componentType() {
    return ComponentType.slider;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is WindSpeedComponentViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(valueList, other.valueList) &&
          selectedValue == other.selectedValue &&
          isAlign == other.isAlign &&
          title == other.title &&
          enable == other.enable &&
          attribute == other.attribute &&
          checkContinue == other.checkContinue;

  @override
  int get hashCode =>
      super.hashCode ^
      listHashCode(valueList) ^
      selectedValue.hashCode ^
      isAlign.hashCode ^
      title.hashCode ^
      enable.hashCode ^
      attribute.hashCode ^
      checkContinue.hashCode;
}
