import 'package:flutter/widgets.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class PowerButtonAnimationViewModel extends ComponentBaseViewModel {
  PowerButtonAnimationViewModel({
    required this.isOn,
    required this.writable,
    this.btnClick,
    this.checkContinue,
    super.expandFlex,
  });

  bool isOn;
  bool writable;
  void Function(BuildContext? context)? btnClick;
  Future<bool> Function()? checkContinue;

  @override
  ComponentType componentType() {
    return ComponentType.powerButton;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is PowerButtonAnimationViewModel &&
          runtimeType == other.runtimeType &&
          isOn == other.isOn &&
          writable == other.writable;

  @override
  int get hashCode => super.hashCode ^ isOn.hashCode ^ writable.hashCode;

  @override
  String toString() {
    return 'PowerButtonAnimationViewModel{isOn: $isOn, enable: $writable}';
  }
}
