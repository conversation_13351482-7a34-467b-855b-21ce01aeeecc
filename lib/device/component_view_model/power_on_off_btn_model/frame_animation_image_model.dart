// 图片命名要求：后缀连续，beginIndex为第0张，endIndex为最后一张
// 例如：
// water_ripple_0.png
// water_ripple_1.png
// water_ripple_2.png
// water_ripple_3.png
// water_ripple_4.png
//
// assetsPath = 'assets/frame_animation/watter_ripple/'， imagePrefix = 'water_ripple'，imageType = 'png'，beginIndex = 0，endIndex = 4
// FrameAnimationImageModel(
//         assetsPath: 'assets/frame_animation/water_ripple/',
//         imagePrefix: 'water_ripple_',
//         imageType: 'png',
//         beginIndex: 0,
//         endIndex: 99,
//       )
class FrameAnimationImageModel {
  String assetsPath = '';
  String imagePrefix = '';
  String imageType = '';
  int beginIndex = 0;
  int endIndex = 0;

  FrameAnimationImageModel(
      {this.assetsPath = '',
      this.imagePrefix = '',
      this.imageType = '',
      this.beginIndex = 0,
      this.endIndex = 0});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FrameAnimationImageModel &&
          runtimeType == other.runtimeType &&
          assetsPath == other.assetsPath &&
          imagePrefix == other.imagePrefix &&
          imageType == other.imageType &&
          beginIndex == other.beginIndex &&
          endIndex == other.endIndex;

  @override
  int get hashCode =>
      assetsPath.hashCode ^
      imagePrefix.hashCode ^
      imageType.hashCode ^
      beginIndex.hashCode ^
      endIndex.hashCode;

  @override
  String toString() {
    return 'FrameAnimationImageModel{assetsPath: $assetsPath, imagePrefix: $imagePrefix, imageType: $imageType, beginIndex: $beginIndex, endIndex: $endIndex}';
  }
}
