import 'package:flutter/material.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

//圆形开关组件
class CircularSwitchComponentModel extends ComponentBaseViewModel {
  String text = '';
  String icon = '';
  bool selected = false;
  bool enable = true;

  void Function(BuildContext? context)? clickCallback;

  CircularSwitchComponentModel(
      {this.text = '',
        this.icon = '',
        this.selected = false,
        this.enable = true,
        this.clickCallback});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          super == other &&
              other is CircularSwitchComponentModel &&
              runtimeType == other.runtimeType &&
              text == other.text &&
              icon == other.icon &&
              selected == other.selected &&
              enable == other.enable;

  @override
  int get hashCode =>
      super.hashCode ^
      text.hashCode ^
      icon.hashCode ^
      selected.hashCode ^
      enable.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.circularSwitch;
  }
}
