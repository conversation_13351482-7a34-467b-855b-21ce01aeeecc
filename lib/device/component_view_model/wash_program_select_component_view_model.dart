import 'package:flutter/material.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class WashProgramSelectComponentViewModel extends ComponentBaseViewModel {
  String title;
  String desc;
  bool selected;
  bool showBorder;
  void Function(BuildContext? context)? programSelect;

  WashProgramSelectComponentViewModel(
      {this.title = '',
      this.desc = '',
      this.selected = false,
      this.showBorder = false,
      this.programSelect});

  @override
  ComponentType componentType() {
    return ComponentType.washProgramSelectComponent;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is WashProgramSelectComponentViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          desc == other.desc &&
          selected == other.selected &&
          showBorder == other.showBorder;

  @override
  int get hashCode =>
      super.hashCode ^
      title.hashCode ^
      desc.hashCode ^
      selected.hashCode ^
      showBorder.hashCode;
}
