/*
 * 描述：多筒洗衣机单筒状态
 * 作者：songFJ
 * 创建时间：2024/12/19
 */

import 'package:flutter/cupertino.dart';

import 'component_view_model.dart';

class WashRollerComponentViewModel extends ComponentBaseViewModel {
  WashRollerComponentViewModel(
      {super.expandFlex,
      super.popupComponentViewModel,
      required this.rollerName,
      required this.rollerState,
      required this.rollerDesc,
      required this.enable,
      this.showBorder = false,
      this.rollerClickCallback});

  String rollerName;
  String rollerState;
  String rollerDesc;
  bool enable;
  bool showBorder;
  void Function(BuildContext? context)? rollerClickCallback;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is WashRollerComponentViewModel &&
          runtimeType == other.runtimeType &&
          rollerName == other.rollerName &&
          rollerState == other.rollerState &&
          rollerDesc == other.rollerDesc &&
          enable == other.enable &&
          showBorder == other.showBorder;

  @override
  int get hashCode =>
      super.hashCode ^
      rollerName.hashCode ^
      rollerState.hashCode ^
      rollerDesc.hashCode ^
      enable.hashCode ^
      showBorder.hashCode;

  @override
  ComponentType componentType() {
    return ComponentType.washRoller;
  }
}
