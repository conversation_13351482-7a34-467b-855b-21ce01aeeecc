import 'package:smart_home/device/component_view_model/component_view_model.dart';

enum SliderColorType {
  brightness,
  temperature,
}

class SliderComponentViewModel extends ComponentBaseViewModel {
  SliderComponentViewModel({
    this.maxValue = 100,
    this.minValue = 0,
    this.currentValue = 0,
    this.colorType = SliderColorType.brightness,
    this.isDisabled = false,
    this.slideValueChange,
  });

  double maxValue;
  double minValue;
  double currentValue;
  SliderColorType colorType;
  bool isDisabled;
  void Function(double currentValue)? slideValueChange;

  @override
  ComponentType componentType() {
    return ComponentType.progressBar;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is SliderComponentViewModel &&
          runtimeType == other.runtimeType &&
          maxValue == other.maxValue &&
          minValue == other.minValue &&
          currentValue == other.currentValue &&
          colorType == other.colorType &&
          isDisabled == other.isDisabled;

  @override
  int get hashCode =>
      super.hashCode ^
      maxValue.hashCode ^
      minValue.hashCode ^
      currentValue.hashCode ^
      colorType.hashCode ^
      isDisabled.hashCode;

  @override
  String toString() {
    return '{maxValue: $maxValue, minValue: $minValue, currentValue: $currentValue, colorType: $colorType, isDisabled: $isDisabled, slideValueChange: $slideValueChange}';
  }
}
