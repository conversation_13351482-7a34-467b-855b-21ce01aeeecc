/*
 * 描述：燃热卡片纯文本开关类组件ViewModel
 * 作者：武旭
 * 创建时间：2025/3/27
 */

import 'package:flutter/cupertino.dart';
import 'package:smart_home/device/component/view_model/component_throttler_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';

class GWaterHeaterFixedTextViewModel extends ComponentThrottlerViewModel {
  final String title;
  final String subTitle;
  final bool enable;
  final bool isOn;
  final void Function(BuildContext context)? clickCallback;

  GWaterHeaterFixedTextViewModel(
      {required this.title,
      required this.subTitle,
      required this.enable,
      required this.isOn,
      super.throttlerMillionSeconds,
      this.clickCallback});

  @override
  ComponentType componentType() {
    return ComponentType.gWaterHeaterSwitchText;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is GWaterHeaterFixedTextViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          subTitle == other.subTitle &&
          enable == other.enable &&
          isOn == other.isOn;

  @override
  int get hashCode =>
      super.hashCode ^
      title.hashCode ^
      subTitle.hashCode ^
      enable.hashCode ^
      isOn.hashCode;
}
