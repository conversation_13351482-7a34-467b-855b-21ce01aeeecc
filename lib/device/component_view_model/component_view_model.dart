/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/6/26
 */

import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';

enum ComponentType {
  attribute_abnormal_state,
  switchSelect,
  expandLabel,
  expandEmpty,
  expandValueList,
  expandValueLongTouchList,
  fixedIconTextPopup,
  expandIconTextPopup,
  fixedIconTextSwitch,
  expandIconTextSwitch,
  expandIconTextMarkSwitch,
  fixedTextPopup,
  expandTextPopup,
  flushLarge,
  flushSmall,
  careModeSelect,
  careModeItem,
  expandSwitchText,
  expandPopupWashProgram,
  expandWashState,
  expandTextText,
  expandAirConditionValueList,
  expandPopupTextTextUnit,
  humiditySet,
  throttler,
  ble,
  text,
  labelNew,
  washState,
  washProgramItem,
  washLabel,
  washRoller,
  button,
  titleButton,
  powerButton,
  slider,
  editSelect,
  progressBar,
  unknown,
  popupLabel,
  washProgramSelectComponent,
  washBucketSwitchComponent,
  gridView,
  washProgramMoreSelectComponent,
  circularSwitch,
  circularPercentage,

  /// 净水滤芯耗材类型
  purified_grid,
  expandGasValueList,
  aggregation_btn,
  expandGridView,
  aggregationEmpty,

  /// popup弹窗-按钮列表条目
  expandPopupButtonItem,
  gWaterHeaterSwitchText,
  largeCardBottomArea
}

abstract class ComponentBaseViewModel {
  ComponentBaseViewModel({this.expandFlex = 1, this.popupComponentViewModel});

  PopupComponentViewModel? popupComponentViewModel;

  int expandFlex;

  ComponentType componentType();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ComponentBaseViewModel &&
          runtimeType == other.runtimeType &&
          expandFlex == other.expandFlex &&
          popupComponentViewModel == other.popupComponentViewModel;

  @override
  int get hashCode => expandFlex.hashCode ^ popupComponentViewModel.hashCode;
}
