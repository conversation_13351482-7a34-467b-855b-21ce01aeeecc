import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_util.dart';

class AggregationDisplayedUtil {
  /// 允许首页展示的聚合卡片
  static bool isDisplayHomeAggregation(String aggregationParentId) {
    return isDeviceLightAggregation(aggregationParentId) ||
        isDeviceCurtainAggregation(aggregationParentId) ||
        isEnvAgg(aggregationParentId) ||
        isCameraAgg(aggregationParentId);
  }
}
