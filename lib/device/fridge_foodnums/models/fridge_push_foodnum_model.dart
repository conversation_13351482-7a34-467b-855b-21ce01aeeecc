import 'dart:convert';

import 'package:device_utils/log/log.dart';
import 'package:upservice/map_analysis_extension/map_analysis_extension.dart';

class FridgePushFoodNumModel {
  FridgePushFoodNumModel.fromJson(Map<String, dynamic> json) {
    if (json['body'] is Map) {
      body = FridgePushFoodNumBodyModel.fromJson(
          json.mapValueForKey('body', <dynamic, dynamic>{}));
    }
  }

  FridgePushFoodNumBodyModel body =
      FridgePushFoodNumBodyModel.fromJson(const <dynamic, dynamic>{});
}

class FridgePushFoodNumBodyModel {
  FridgePushFoodNumExtDataModel extData =
      FridgePushFoodNumExtDataModel.fromJson(const <dynamic, dynamic>{});

  FridgePushFoodNumBodyModel.fromJson(Map<dynamic, dynamic> json) {
    extData = FridgePushFoodNumExtDataModel.fromJson(
        json.mapValueForKey('extData', <dynamic, dynamic>{}));
  }
}

class FridgePushFoodNumExtDataModel {
  FridgePushFoodNumDeliverDataModel deliverData =
      FridgePushFoodNumDeliverDataModel.fromJson(const <dynamic, dynamic>{});

  FridgePushFoodNumExtDataModel.fromJson(Map<dynamic, dynamic> json) {
    final String jsonStr = json.stringValueForKey('deliverData', '');
    Map<dynamic, dynamic> jsonMap = <dynamic, dynamic>{};
    try {
      jsonMap = convertType<Map<dynamic, dynamic>>(
          jsonDecode(jsonStr), <dynamic, dynamic>{});
    } catch (e, i) {
      DevLogger.error(
          tag: 'FridgePushFoodNumModel',
          msg: 'FridgePushFoodNumExtDataModel.fromJson err:$e, stack:$i');
    }

    deliverData = FridgePushFoodNumDeliverDataModel.fromJson(jsonMap);
  }
}

class FridgePushFoodNumDeliverDataModel {
  String action = '';
  String deviceId = '';
  int sequenceId = 0;

  FridgePushFoodNumDeliverDataModel.fromJson(Map<dynamic, dynamic> json) {
    action = json.stringValueForKey('action', '');
    deviceId = json.stringValueForKey('deviceId', '');
    sequenceId = json.intValueForKey('sequenceId', 0);
  }
}
