import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:upservice/model/uhome_response_model.dart';

class FridgeFoodNumRequestModel {
  final String familyId;
  final List<DeviceItem> deviceList;

  FridgeFoodNumRequestModel({
    required this.familyId,
    required this.deviceList,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'familyId': familyId,
      'deviceList': deviceList,
    };
  }

  @override
  String toString() {
    return 'FridgeFoodNumRequestModel{familyId: $familyId, deviceList: $deviceList}';
  }
}

class DeviceItem {
  final String deviceId;
  final String model;
  final String productCode;
  final String typeId;
  final String brandName;
  final String deviceTypeName;

  DeviceItem._init({
    required this.deviceId,
    required this.model,
    required this.productCode,
    required this.typeId,
    required this.brandName,
    required this.deviceTypeName,
  });

  factory DeviceItem.from(SmartHomeDeviceBasicInfo basicInfo) {
    return DeviceItem._init(
      deviceId: basicInfo.deviceId,
      model: basicInfo.model,
      productCode: basicInfo.prodNo,
      typeId: basicInfo.typeId,
      brandName: basicInfo.brand,
      deviceTypeName: basicInfo.appTypeName,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'deviceId': deviceId,
      'model': model,
      'productCode': productCode,
      'typeId': typeId,
      'brandName': brandName,
      'deviceTypeName': deviceTypeName,
    };
  }

  @override
  String toString() {
    return 'DeviceItem{deviceId: $deviceId, model: $model, productCode: $productCode, typeId: $typeId, brandName: $brandName, deviceTypeName: $deviceTypeName}';
  }
}

class FridgeFoodNumResponseModel extends UhomeResponseModel {
  FridgeFoodNumResponseModel.fromJson(super.data) : super.fromJson() {
    foodNumModel = FoodNumItemListModel.fromJson(super.retData);
  }

  FoodNumItemListModel foodNumModel =
      FoodNumItemListModel.fromJson(const <dynamic, dynamic>{});
}

class FoodNumItemListModel {
  final List<FoodNumItem> deviceList = <FoodNumItem>[];

  FoodNumItemListModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> tmpDeviceList =
        json.listValueForKey('deviceList', <dynamic>[]);
    tmpDeviceList.forEach((dynamic element) {
      if (element is Map) {
        deviceList.add(FoodNumItem.fromJson(element));
      }
    });
  }

  @override
  String toString() {
    return 'FoodNumItemListModel{deviceList: $deviceList}';
  }
}

class FoodNumItem {
  final String deviceId;
  final int clipFoodNotReadCount;
  final String linkUrl;

  FoodNumItem({
    required this.deviceId,
    required this.clipFoodNotReadCount,
    required this.linkUrl,
  });

  factory FoodNumItem.fromJson(Map<dynamic, dynamic> json) {
    return FoodNumItem(
      deviceId: json.stringValueForKey('deviceId', ''),
      clipFoodNotReadCount: json.intValueForKey('clipFoodNotReadCount', 0),
      linkUrl: json.stringValueForKey('linkUrl', ''),
    );
  }

  @override
  String toString() {
    return 'FoodNumItem{deviceId: $deviceId, clipFoodNotReadCount: $clipFoodNotReadCount, linkUrl: $linkUrl}';
  }
}
