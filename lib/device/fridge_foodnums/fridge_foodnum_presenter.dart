import 'dart:async';

import 'package:device_utils/log/log.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_foodnum_model.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_push_foodnum_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_store.dart';

class FridgeFoodNumPresenter {
  static const String _tag = 'FridgeFoodNumPresenter';

  static const String foodNumPushMessageName = 'BX_Default';
  static const String _foodNumPushMessageAction = 'DeviceCardFoodStatusRefresh';

  static const String fridgeFoodNumKey = 'fridgeFoodNumKey';

  static const String fridgeFoodManageUrlKey = 'fridgeFoodManageUrlKey';

  static const String _defaultFoodManageBaseUrl =
      'https://file.linkcook.cn/appDeployment/prod/foodManageMiddle/index.html';

  static String buildFoodManageDefaultPageUrl(String deviceId) {
    final SmartHomeDevice? device =
        smartHomeStore.state.deviceState.smartHomeDeviceMap[deviceId];
    if (device == null) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg:
              '$_tag buildFoodManageDefaultPageUrl deviceId=$deviceId, device is null');
    }

    final String typeId = device?.basicInfo.typeId ?? '';
    final String model = device?.basicInfo.model ?? '';
    final String brand = device?.basicInfo.brand ?? '';
    final String deviceTypeName = device?.basicInfo.appTypeName ?? '';

    final String managePageUrl =
        '$_defaultFoodManageBaseUrl?deviceId=$deviceId&underneathStatusBar=1&typeId=$typeId&model=$model&brandName=$brand&deviceTypeName=$deviceTypeName#/';
    return managePageUrl;
  }

  static Future<void> parseFoodNumPushMessage(
      Map<String, dynamic> messageMap) async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    if (familyId.isEmpty) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg:
              '$_tag parseFoodNumPushMessage familyId.isEmpty, cannot query foodNums, return.');
      return;
    }
    FridgePushFoodNumModel foodNumModel =
        FridgePushFoodNumModel.fromJson(const <String, dynamic>{});
    try {
      foodNumModel = FridgePushFoodNumModel.fromJson(messageMap);
    } catch (e, i) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg:
              '$_tag parseFoodNumPushMessage FridgePushFoodNumModel.fromJson, err:$e, stack:$i');
    }

    final String action = foodNumModel.body.extData.deliverData.action;
    if (action != _foodNumPushMessageAction) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg:
              '$_tag parseFoodNumPushMessage action:($action) not match, cannot query foodNums, return.');
      return;
    }

    final String deviceId = foodNumModel.body.extData.deliverData.deviceId;
    if (!smartHomeStore.state.deviceState.smartHomeDeviceMap
        .containsKey(deviceId)) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg:
              '$_tag parseFoodNumPushMessage deviceId not found, cannot query foodNums, return.');
      return;
    }

    final List<SmartHomeDevice> fridgeDeviceList = <SmartHomeDevice>[
      smartHomeStore.state.deviceState.smartHomeDeviceMap[deviceId]!
    ];

    final FridgeFoodNumRequestModel requestModel = FridgeFoodNumRequestModel(
      familyId: familyId,
      deviceList: fridgeDeviceList
          .map((SmartHomeDevice item) => DeviceItem.from(item.basicInfo))
          .toList(),
    );
    HttpService.queryFridgesFoodNums(requestModel)
        .then((FridgeFoodNumResponseModel? responseModel) {
      if (responseModel == null) {
        DevLogger.error(
            tag: SmartHomeConstant.package,
            msg:
                '$_tag parseFoodNumPushMessage queryFridgesFoodNums responseModel is null, cannot refresh fridge card, return.');
        return;
      }

      smartHomeStore.dispatch(
          FridgeFoodNumsChangedAction(responseModel.foodNumModel.deviceList));
    }).catchError((Object err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg:
              '$_tag parseFoodNumPushMessage queryFridgesFoodNums error: $err');
    });
  }
}
