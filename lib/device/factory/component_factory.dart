/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/6/26
 */

import 'package:flutter/material.dart';
import 'package:smart_home/device/component/view_model/care_mode_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_air_condition_value_list_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_label_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_icon_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_text_unit_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_wash_program_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_mark_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_value_list_long_touch_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_value_list_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_wash_state_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_ble_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_popup_text_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component/view_model/flush_large_component_view_model.dart';
import 'package:smart_home/device/component/view_model/humidity_set_view_model.dart';
import 'package:smart_home/device/component/view_model/purified_grid_view_model.dart';
import 'package:smart_home/device/component/view_model/text_view_model.dart';
import 'package:smart_home/device/component/widget/care_mode_select_widget.dart';
import 'package:smart_home/device/component/widget/expand_air_condition_value_list_widget.dart';
import 'package:smart_home/device/component/widget/expand_empty_widget.dart';
import 'package:smart_home/device/component/widget/expand_label_widget.dart';
import 'package:smart_home/device/component/widget/expand_popup_icon_text_widget.dart';
import 'package:smart_home/device/component/widget/expand_popup_text_text_unit_widget.dart';
import 'package:smart_home/device/component/widget/expand_popup_text_text_widget.dart';
import 'package:smart_home/device/component/widget/expand_popup_text_widget.dart';
import 'package:smart_home/device/component/widget/expand_popup_wash_program_widget.dart';
import 'package:smart_home/device/component/widget/expand_switch_icon_text_mark_widget.dart';
import 'package:smart_home/device/component/widget/expand_switch_icon_text_widget.dart';
import 'package:smart_home/device/component/widget/expand_switch_text_widget.dart';
import 'package:smart_home/device/component/widget/expand_value_list_long_touch_widget.dart';
import 'package:smart_home/device/component/widget/expand_value_list_widget.dart';
import 'package:smart_home/device/component/widget/expand_wash_state_widget.dart';
import 'package:smart_home/device/component/widget/fixed_ble_widget.dart';
import 'package:smart_home/device/component/widget/fixed_popup_icon_text_widget.dart';
import 'package:smart_home/device/component/widget/fixed_popup_text_widget.dart';
import 'package:smart_home/device/component/widget/fixed_switch_icon_text_widget.dart';
import 'package:smart_home/device/component/widget/flush_large_widget.dart';
import 'package:smart_home/device/component/widget/flush_small_widget.dart';
import 'package:smart_home/device/component/widget/humidity_set_widget.dart';
import 'package:smart_home/device/component/widget/large_card_bottom_widget.dart';
import 'package:smart_home/device/component/widget/purified_grid_view_component.dart';
import 'package:smart_home/device/component/widget/text_widget.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';
import 'package:smart_home/device/component_view_model/g_water_heater_fixed_switch_text_view_model.dart';
import 'package:smart_home/device/component_view_model/grid_view_model.dart';
import 'package:smart_home/device/component_view_model/power_on_off_btn_model/power_button_animation_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_bucket_switch_component_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_program_more_select_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_program_select_component_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_roller_component_view_model.dart';
import 'package:smart_home/device/component_view_model/wind_speed_component_view_model.dart';
import 'package:smart_home/device/component_widget/device_edit_component.dart';
import 'package:smart_home/device/component_widget/g_water_heater_flex_switch_text_widget.dart';
import 'package:smart_home/device/component_widget/grid_widget.dart';
import 'package:smart_home/device/component_widget/power_on_off_btn_widget/power_on_off_widget.dart';
import 'package:smart_home/device/component_widget/wash_bucket_switch_component.dart';
import 'package:smart_home/device/component_widget/wash_program_more_select_component.dart';
import 'package:smart_home/device/component_widget/wash_program_select_compnent.dart';
import 'package:smart_home/device/component_widget/wash_roller_component.dart';
import 'package:smart_home/device/component_widget/wind_speed_component/wind_speed_component.dart';

import '../aggregation/aggregation_card/view_model/aggregation_btns_view_model.dart';
import '../aggregation/aggregation_card/view_model/aggregation_empty_view_model.dart';
import '../aggregation/aggregation_card/view_model/aggregation_expand_grid_view_model.dart';
import '../aggregation/aggregation_card/widgets/aggregation_btn_list.dart';
import '../aggregation/aggregation_card/widgets/aggregation_empty_view.dart';
import '../aggregation/aggregation_card/widgets/aggregation_expand_grid_view_widget.dart';
import '../component/view_model/circular_percentage_view_model.dart';
import '../component/view_model/expand_gas_value_list_view_model.dart';
import '../component/view_model/fixed_popup_icon_text_view_model.dart';
import '../component/view_model/flush_small_component_view_model.dart';
import '../component/widget/circular_percentage_component.dart';
import '../component/widget/expand_gas_value_list_widget.dart';
import '../component_view_model/circular_switch_view_model.dart';
import '../component_view_model/slider_component_view_model.dart';
import '../component_widget/circular_switch_component.dart';
import '../component_widget/slider_component/slider_component.dart';

typedef ComponentBuilder = Widget Function({
  Key? key,
  required ComponentBaseViewModel viewModel,
});

class ComponentFactory {
  static final Map<ComponentType, ComponentBuilder> _componentMap =
  <ComponentType, ComponentBuilder>{
    ComponentType.expandLabel: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandLabelWidget(
            key: key, viewModel: viewModel as ExpandLabelViewModel),
    ComponentType.flushLarge: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        FlushLargeWidget(viewModel: viewModel as FlushLargeComponentViewModel),
    ComponentType.flushSmall: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        FlushSmallWidget(viewModel: viewModel as FlushSmallComponentViewModel),
    ComponentType.fixedIconTextSwitch: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        FixedSwitchIconTextWidget(
            viewModel: viewModel as FixedSwitchIconTextViewModel),
    ComponentType.expandIconTextSwitch: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandSwitchIconTextWidget(
            viewModel: viewModel as ExpandSwitchIconTextViewModel),
    ComponentType.expandIconTextMarkSwitch: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandSwitchIconTextMarkWidget(
            viewModel: viewModel as ExpandSwitchIconTextMarkViewModel),
    ComponentType.fixedIconTextPopup: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        FixedPopupIconTextWidget(
            viewModel: viewModel as FixedPopupIconTextViewModel),
    ComponentType.expandIconTextPopup: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandPopupIconTextWidget(
            viewModel: viewModel as ExpandPopupIconTextViewModel),
    ComponentType.fixedTextPopup: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        FixedPopupTextWidget(viewModel: viewModel as FixedPopupTextViewModel),
    ComponentType.expandTextPopup: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandPopupTextWidget(viewModel: viewModel as ExpandPopupTextViewModel),
    ComponentType.expandEmpty: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        const ExpandEmptyWidget(),
    ComponentType.expandValueList: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandValueListWidget(
            key: key, viewModel: viewModel as ExpandValueListViewModel),
    ComponentType.expandAirConditionValueList: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandAirConditionValueListWidget(
            key: key,
            viewModel: viewModel as ExpandAirConditionValueListViewModel),
    ComponentType.expandValueLongTouchList: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandValueListLongTouchWidget(
          key: key,
          viewModel: viewModel as ExpandValueListLongTouchViewModel,
        ),
    ComponentType.careModeSelect: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        CareModeSelectWidget(
            key: key, viewModel: viewModel as CareModeSelectViewModel),
    ComponentType.expandSwitchText: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandSwitchTextWidget(
            key: key, viewModel: viewModel as ExpandSwitchTextViewModel),
    ComponentType.expandPopupWashProgram: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandPopupWashProgramWidget(
            key: key, viewModel: viewModel as ExpandPopupWashProgramViewModel),
    ComponentType.expandWashState: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandWashStateWidget(
            key: key, viewModel: viewModel as ExpandWashStateViewModel),
    ComponentType.expandTextText: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandPopupTextTextWidget(
            key: key, viewModel: viewModel as ExpandPopupTextTextViewModel),
    ComponentType.expandPopupTextTextUnit: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandPopupTextTextUnitWidget(
            key: key, viewModel: viewModel as ExpandPopupTextTextUnitViewModel),
    ComponentType.humiditySet: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        HumiditySetWidget(
            key: key, viewModel: viewModel as HumiditySetViewModel),
    ComponentType.ble: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        FixedBleWidget(key: key, viewModel: viewModel as FixedBleViewModel),
    ComponentType.text: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        TextWidget(key: key, viewModel: viewModel as TextViewModel),
    ComponentType.powerButton: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        PowerOnOffWidget(
            key: key, viewModel: viewModel as PowerButtonAnimationViewModel),
    ComponentType.editSelect: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        DeviceEditComponent(
            viewModel: viewModel as DeviceEditComponentViewModel),
    ComponentType.washRoller: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        WashRollerComponent(
            viewModel: viewModel as WashRollerComponentViewModel),
    ComponentType.slider: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        WindSpeedComponent(
            key: key, viewModel: viewModel as WindSpeedComponentViewModel),
    ComponentType.progressBar: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        SliderComponent(
            key: key, viewModel: viewModel as SliderComponentViewModel),
    ComponentType.washProgramSelectComponent: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        WashProgramSelectComponent(
            key: key,
            viewModel: viewModel as WashProgramSelectComponentViewModel),
    ComponentType.washBucketSwitchComponent: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        WashBucketSwitchComponent(
            key: key,
            viewModel: viewModel as WashBucketSwitchComponentViewModel),
    ComponentType.gridView: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        GridWidget(key: key, viewModel: viewModel as GridViewModel),
    ComponentType.washProgramMoreSelectComponent: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        WashProgramMoreSelectComponent(
            key: key, viewModel: viewModel as WashProgramMoreSelectViewModel),
    ComponentType.circularSwitch: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        CircularSwitchComponent(
            key: key, viewModel: viewModel as CircularSwitchComponentModel),
    ComponentType.purified_grid: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        PurifiedGridViewComponent(
            key: key, viewModel: viewModel as PurifiedGridViewModel),
    ComponentType.circularPercentage: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        CircularPercentageComponent(
            key: key, viewModel: viewModel as CircularPercentageModel),
    ComponentType.expandGasValueList: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        ExpandGasValueListWidget(
            key: key, viewModel: viewModel as ExpandGasValueListViewModel),
    ComponentType.aggregation_btn: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        AggregationBtnList(key: key, vm: viewModel as AggregationBtnViewModel),
    ComponentType.expandGridView: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        AggregationExpandGridViewWidget(
            key: key, viewModel: viewModel as AggregationExpandGridViewModel),
    ComponentType.aggregationEmpty: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        AggregationEmptyView(
            key: key, viewModel: viewModel as AggregationEmptyViewModel),
    ComponentType.gWaterHeaterSwitchText: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        GWaterHeaterFixedSwitchTextWidget(
            viewModel: viewModel as GWaterHeaterFixedTextViewModel),
    ComponentType.largeCardBottomArea: (
            {Key? key, required ComponentBaseViewModel viewModel}) =>
        LargeCardBottomWidget(
            key: key, viewModel: viewModel as LargeCardBottomWidgetViewModel),
  };

  static Widget componentWidget({
    Key? key,
    required ComponentBaseViewModel viewModel,
  }) {
    final ComponentBuilder builder = _componentMap[viewModel.componentType()] ??
        ({
          Key? key,
          required ComponentBaseViewModel viewModel,
        }) =>
            Container();

    return builder(key: key, viewModel: viewModel);
  }
}
