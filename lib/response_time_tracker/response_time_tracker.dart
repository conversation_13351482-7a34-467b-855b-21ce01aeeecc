/*
 * 描述：控制响应时长和失败率统计工具类
 * 作者：fancunshuo
 * 建立时间: 2024/2/6
 */
import 'dart:async';

import 'package:device_utils/log/log.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:function_toggle/function_toggle.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:plugin_device/model/device_result.dart';

import '../common/constant.dart';
import 'response_time_tracker_model.dart';

class ResponseTimeTracker {
  factory ResponseTimeTracker() => _getInstance()!;

  ResponseTimeTracker._internal() {
    // 初始化
  }

  static ResponseTimeTracker get instance => _getInstance()!;
  static ResponseTimeTracker? _instance;

  static ResponseTimeTracker? _getInstance() {
    _instance ??= ResponseTimeTracker._internal();
    return _instance;
  }

  final Map<String, List<CMDStateModel>> _cmdCacheModelMap =
      <String, List<CMDStateModel>>{};

  Timer? _timer;

  CmdTrackerConfigModel? _configModel;

  final int _defaultTimeoutDuration = 60;

  Future<void> startTracker() async {
    await getTrackerConfig();

    if (_configModel?.trackerStatus ?? false) {
      _timer?.cancel();
      final int duration =
          _configModel?.timeoutDuration ?? _defaultTimeoutDuration;
      _timer = Timer.periodic(Duration(seconds: duration), (Timer timer) {
        _trackerOperation();
      });
    }
  }

  void stopTracker() {
    _timer?.cancel();
    _timer = null;
  }

  void initOperateStateModel({
    required String deviceId,
    required String appTypeCode,
    required String appTypeName,
    required String faultInformationStateCode,
    required String model,
    required List<Command> commands,
    required Map<String, CMDStateModel> cmdStateModelMap,
  }) {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'initOperateStateModel: $_configModel, $deviceId, $commands, '
            '$cmdStateModelMap');

    if (!(_configModel?.trackerStatus ?? false) || _timer == null) {
      return;
    }

    commands.forEach((Command command) {
      final String attributeKey = command.name;
      final String attributeValue = command.value;
      final String groupKey = '${deviceId}_${attributeKey}_$attributeValue';

      final CMDStateModel cmdStateModel = CMDStateModel.fromDeviceResult(
          deviceId,
          attributeKey,
          attributeValue,
          appTypeCode,
          appTypeName,
          faultInformationStateCode,
          model,
          DeviceResult.formMap(<String, dynamic>{}));
      cmdStateModel.appOperateT1 = DateTime.now().millisecondsSinceEpoch;

      cmdStateModelMap[groupKey] = cmdStateModel;

      _cmdCacheModelMap[groupKey] = <CMDStateModel>[
        ..._cmdCacheModelMap[groupKey] ?? <CMDStateModel>[],
        cmdStateModel,
      ];
    });
  }

  void updateOperateStateModel({
    required String deviceId,
    required List<Command> commands,
    required Map<String, CMDStateModel> cmdStateModelMap,
    required DeviceResult deviceResult,
  }) {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg:
            'updateOperateStateModel: $_configModel, $commands, $cmdStateModelMap');

    if (!(_configModel?.trackerStatus ?? false) || _timer == null) {
      return;
    }

    commands.forEach((Command command) {
      final String attributeKey = command.name;
      final String attributeValue = command.value;
      final String groupKey = '${deviceId}_${attributeKey}_$attributeValue';
      final CMDStateModel? cmdStateModel = cmdStateModelMap[groupKey];

      final CommandResult commandResult =
          CommandResult.fromJson(deviceResult.retData);

      cmdStateModel?.appOperateCallbackT8 =
          DateTime.now().millisecondsSinceEpoch;
      cmdStateModel?.appOperateDuration =
          commandResult.appOperateCallbackTime - commandResult.appOperateTime;
      cmdStateModel?.usdkOperateT2 = commandResult.usdkOperateTime;
      cmdStateModel?.usdkOperateCallbackT7 =
          commandResult.usdkOperateCallbackTime;
      cmdStateModel?.usdkOperateDuration =
          commandResult.usdkOperateCallbackTime - commandResult.usdkOperateTime;
      cmdStateModel?.appReportDuration =
          cmdStateModel.appReportT12 - cmdStateModel.appOperateT1;
      cmdStateModel?.sn1 = commandResult.traceId;
      cmdStateModel?.errorCode = deviceResult.retCode;
      cmdStateModel?.errorInfo = deviceResult.retInfo;
    });
  }

  Future<void> updateReportStateModel(
      Map<String, DeviceAttributeModel> modelMap) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'updateReportStateModel: $_configModel');

    if (!(_configModel?.trackerStatus ?? false) || _timer == null) {
      return;
    }

    modelMap.forEach((String deviceId, DeviceAttributeModel model) {
      for (final Attribute attribute in model.engineAttributes) {
        final String key = attribute.name;
        final String value = attribute.value;
        final String groupKey = '${deviceId}_${key}_$value';

        if (_cmdCacheModelMap.containsKey(groupKey)) {
          final List<CMDStateModel> cmdStateModelList =
              _cmdCacheModelMap[groupKey] ?? <CMDStateModel>[];
          for (final CMDStateModel cmdStateModel in cmdStateModelList) {
            if (cmdStateModel.stage != CMDStateGioType.reported) {
              cmdStateModel.stage = CMDStateGioType.reported;
              cmdStateModel.appReportT12 =
                  DateTime.now().millisecondsSinceEpoch;
              cmdStateModel.appReportDuration =
                  cmdStateModel.appReportT12 - cmdStateModel.appOperateT1;
            }
          }
        }
      }
    });
  }

  Future<void> getTrackerConfig() async {
    if (_configModel is! CmdTrackerConfigModel) {
      _configModel = await FunctionToggle.instance
          .getFunctiontoggleModel<CmdTrackerConfigModel?>(
              'SmartDevice_ResponseTimeTrackerGIO',
              (Map<dynamic, dynamic> result) {
        DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg: 'CmdResponseTracker init success，ret: $result');
        return CmdTrackerConfigModel.fromJson(result);
      }).catchError((dynamic e) {
        DevLogger.error(
            tag: SmartHomeConstant.package,
            msg: 'CmdResponseTracker init exception, err:$e');
        return null;
      });
    }
  }

  void _trackerOperation() {
    final int timeoutDuration =
        _configModel?.timeoutDuration ?? _defaultTimeoutDuration;

    for (final List<CMDStateModel> stateModelList in _cmdCacheModelMap.values) {
      for (final CMDStateModel stateModel in stateModelList) {
        if (stateModel.stage == CMDStateGioType.reported) {
          if (stateModel.appReportDuration < timeoutDuration * 1000) {
            _gioTrack(stateModel);
          } else {
            stateModel.timeout = stateModel.appReportDuration;
            _gioTrackTimeout(stateModel);
          }
        } else {
          final int reportDuration =
              DateTime.now().millisecondsSinceEpoch - stateModel.appOperateT1;
          if (reportDuration > timeoutDuration * 1000) {
            stateModel.timeout = reportDuration;
            _gioTrackTimeout(stateModel);
          }
        }
      }
    }
    _removeCMDStateModel();
  }

  void _gioTrack(CMDStateModel stateModel) {
    gioTrack('MB37113', stateModel.toNormalGioJson());
    stateModel.stage = CMDStateGioType.uploaded;
  }

  void _gioTrackTimeout(CMDStateModel stateModel) {
    gioTrack('MB37114', stateModel.toTimeoutGioJson());
    stateModel.stage = CMDStateGioType.uploaded;
  }

  void _removeCMDStateModel() {
    _cmdCacheModelMap.removeWhere((String key, List<CMDStateModel> value) =>
        value.every((CMDStateModel element) =>
            element.stage == CMDStateGioType.uploaded));
    for (final List<CMDStateModel> cmdCacheModelList
        in _cmdCacheModelMap.values) {
      cmdCacheModelList.removeWhere((CMDStateModel cmdCacheModel) =>
          cmdCacheModel.stage == CMDStateGioType.uploaded);
    }
  }
}
