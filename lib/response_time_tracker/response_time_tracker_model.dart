/*
 * 描述：控制响应时长和失败率统计模型
 * 作者：fancunshuo
 * 建立时间: 2024/2/18
 */

import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:plugin_device/model/device_result.dart';

enum CMDStateGioType { operate, reported, uploaded }

const int defaultTimeoutDuration = 60;

class CmdTrackerConfigModel {
  CmdTrackerConfigModel.fromJson(Map<dynamic, dynamic> json) {
    timeoutDuration =
        json.intValueForKey('timeoutDuration', defaultTimeoutDuration);
    trackerStatus = json.boolValueForKey('trackerStatus', false);
  }

  int timeoutDuration = defaultTimeoutDuration;
  bool trackerStatus = false;

  @override
  String toString() {
    return 'CmdTrackerConfigModel{timeoutDuration: $timeoutDuration, trackerStatus: $trackerStatus}';
  }
}

class CMDStateModel {
  CMDStateModel();

  CMDStateModel.fromDeviceResult(
      this.deviceId,
      this.attrKey,
      this.attrValue,
      this.appTypeCode,
      this.appTypeName,
      this.faultInformationStateCode,
      this.model,
      DeviceResult deviceResult) {
    final CommandResult commandResult =
        CommandResult.fromJson(deviceResult.retData);

    appOperateT1 = commandResult.appOperateTime;
    appOperateCallbackT8 = commandResult.appOperateCallbackTime;
    appOperateDuration =
        commandResult.appOperateCallbackTime - commandResult.appOperateTime;
    usdkOperateT2 = commandResult.usdkOperateTime;
    usdkOperateCallbackT7 = commandResult.usdkOperateCallbackTime;
    usdkOperateDuration =
        commandResult.usdkOperateCallbackTime - commandResult.usdkOperateTime;
    sn1 = commandResult.traceId;
    errorCode = deviceResult.retCode;
    errorInfo = deviceResult.retInfo;
  }

  String deviceId = '';
  String attrKey = '';
  String attrValue = '';
  String appTypeCode = '';
  String appTypeName = '';
  String faultInformationStateCode = '';
  String model = '';
  int appOperateT1 = 0;
  int appOperateCallbackT8 = 0;
  int appOperateDuration = 0;
  int usdkOperateT2 = 0;
  int usdkOperateCallbackT7 = 0;
  int usdkOperateDuration = 0;
  int usdkReportT11 = 0;
  int appReportT12 = 0;
  int appReportDuration = 0;
  int timeout = 0;
  String errorCode = '';
  String errorInfo = '';
  CMDStateGioType stage = CMDStateGioType.operate;
  String sn1 = '';
  String sn2 = '';

  Map<String, dynamic> toNormalGioJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['deviceId'] = deviceId;
    data['attrKey'] = attrKey;
    data['attrValue'] = attrValue;
    data['appTypeCode'] = appTypeCode;
    data['appTypeName'] = appTypeName;
    data['faultInformationStateCode'] = faultInformationStateCode;
    data['model'] = model;
    data['appOperateT1'] = appOperateT1;
    data['appOperateCallbackT8'] = appOperateCallbackT8;
    data['appOperateDuration'] = appOperateDuration;
    data['usdkOperateT2'] = usdkOperateT2;
    data['usdkOperateCallbackT7'] = usdkOperateCallbackT7;
    data['usdkOperateDuration'] = usdkOperateDuration;
    data['usdkReportT11'] = usdkReportT11;
    data['appReportT12'] = appReportT12;
    data['appReportDuration'] = appReportDuration;
    data['SN1'] = sn1;
    data['SN2'] = sn2;
    return data;
  }

  Map<String, dynamic> toTimeoutGioJson() {
    final Map<String, dynamic> data = toNormalGioJson();
    data['stage'] = stage.name;
    data['timeoutNew'] = timeout;
    data['errorCode'] = errorCode;
    data['errorInfo'] = errorInfo;
    return data;
  }
}

class CommandResult {
  CommandResult(this.appOperateTime, this.appOperateCallbackTime,
      this.usdkOperateTime, this.usdkOperateCallbackTime, this.traceId);

  CommandResult.fromJson(dynamic json) {
    if (json is Map<dynamic, dynamic>) {
      appOperateTime = json.intValueForKey('appOperateTime', 0);
      appOperateCallbackTime = json.intValueForKey('appOperateCallbackTime', 0);
      usdkOperateTime = json.intValueForKey('usdkOperateTime', 0);
      usdkOperateCallbackTime =
          json.intValueForKey('usdkOperateCallbackTime', 0);
      traceId = json.stringValueForKey('traceId', '');
    }
  }

  int appOperateTime = 0;
  int appOperateCallbackTime = 0;
  int usdkOperateTime = 0;
  int usdkOperateCallbackTime = 0;
  String traceId = '';

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = <String, dynamic>{};
    map['appOperateTime'] = appOperateTime;
    map['appOperateCallbackTime'] = appOperateCallbackTime;
    map['usdkOperateTime'] = usdkOperateTime;
    map['usdkOperateCallbackTime'] = usdkOperateCallbackTime;
    map['traceId'] = traceId;
    return map;
  }
}
