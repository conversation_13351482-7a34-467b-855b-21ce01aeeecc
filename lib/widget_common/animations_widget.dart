import 'package:flutter/material.dart';

class EaseOutCubicAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  Alignment? alignment;

  EaseOutCubicAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.curve = const Cubic(0.32, 0.66, 0.6, 1.0),
    this.alignment,
  });

  @override
  State<EaseOutCubicAnimation> createState() => _EaseOutCubicAnimationState();
}

class _EaseOutCubicAnimationState extends State<EaseOutCubicAnimation>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  Animation<double>? _scaleAnimation;
  Animation<double>? _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller!,
        curve: widget.curve,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller!,
        curve: widget.curve,
      ),
    );

    _controller!.forward();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller!,
      builder: (BuildContext context, Widget? child) {
        return Opacity(
          opacity: _opacityAnimation?.value ?? 1,
          child: Transform.scale(
            scale: _scaleAnimation?.value ?? 1,
            alignment: widget.alignment ?? Alignment.center,
            child: widget.child,
          ),
        );
      },
    );
  }
}

class DragWidgetScaleAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;

  const DragWidgetScaleAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 200),
    this.curve = const Cubic(0.32, 0.66, 0.6, 1.0),
  });

  @override
  State<DragWidgetScaleAnimation> createState() =>
      _DragWidgetScaleAnimationState();
}

class _DragWidgetScaleAnimationState extends State<DragWidgetScaleAnimation>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  Animation<double>? _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _scaleAnimation = Tween<double>(begin: 1, end: 1.05).animate(
      CurvedAnimation(
        parent: _controller!,
        curve: widget.curve,
      ),
    );

    _controller!.forward();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller!,
      builder: (BuildContext context, Widget? child) {
        return Transform.scale(
          scale: _scaleAnimation?.value ?? 1,
          child: widget.child,
        );
      },
    );
  }
}
