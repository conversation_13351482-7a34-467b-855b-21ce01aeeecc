import 'package:flutter/material.dart';

class DividerVertical8Widget extends StatelessWidget {
  const DividerVertical8Widget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 8,
    );
  }
}

class DividerVertical12Widget extends StatelessWidget {
  const DividerVertical12Widget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 12,
    );
  }
}

class DividerVertical16Widget extends StatelessWidget {
  const DividerVertical16Widget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 16,
    );
  }
}
