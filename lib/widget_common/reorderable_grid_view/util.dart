import 'dart:ui' as ui show Image;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

typedef DragFinishCallback = void Function(
    List<Widget> children, List<dynamic> data);

/// Control the scroll speed if drag over the boundary.
/// We can pass time here??
/// [timeInMilliSecond] is the time passed.
/// [overPercentage] is the scroll over the boundary percentage
/// [overSize] is the pixel drag over the boundary
/// [itemSize] is the drag item size
/// Maybe you need decide the scroll speed by the given param.
/// return how many pixels when scroll in 14ms(maybe a frame). 5 is the default
typedef ScrollSpeedController = double Function(
    int timeInMilliSecond, double overSize, double itemSize);

/// build the target placeholder
typedef PlaceholderBuilder = Widget Function(
    int dropIndex, int dropInddex, Widget dragWidget);

/// The drag and drop life cycle.
typedef OnDragStart = void Function(int dragIndex);

/// Called when the position of the dragged widget changes.
///
/// [dragIndex] is the index of the item that is dragged.
/// [position] is the current position of the pointer in the
/// global coordinate system. [delta] is the offset of the current
/// position relative to the position of the last drag update call.
typedef OnDragUpdate = void Function(
    int dragIndex, Offset position, Offset delta);

typedef LongPressCallback = void Function(int index);

Future<ui.Image?> takeScreenShot(State state) async {
  final RenderObject? renderObject = state.context.findRenderObject();
  // var renderObject = item.context.findRenderObject();
  if (renderObject is RenderRepaintBoundary) {
    final RenderRepaintBoundary renderRepaintBoundary = renderObject;
    // not good at here
    final double devicePixelRatio =
        MediaQuery.of(state.context).devicePixelRatio;
    return renderRepaintBoundary.toImage(pixelRatio: devicePixelRatio);
  }
  return null;
}
