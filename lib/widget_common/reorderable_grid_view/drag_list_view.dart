import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/reorderable_sliver_grid_view.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/util.dart';

import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';
import '../animations_widget.dart';

class DragGridView extends StatelessWidget {
  DragGridView({
    super.key,
    required this.children,
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.childAspectRatio,
    required this.data,
    required this.padding,
    required this.longPressCallback,
    required this.dragFinishCallback,
    this.isAllowDraggable,
    this.footer,
  });

  ///拖拽widget列表
  final List<Widget> children;

  ///生成拖拽widget列表的源数据
  final List<dynamic> data;

  ///每一行的列数
  final int crossAxisCount;

  ///行间距
  final double mainAxisSpacing;

  ///列间距
  final double crossAxisSpacing;

  ///组件宽高比
  final double childAspectRatio;

  ///是否允许长按拖拽
  bool? isAllowDraggable;

  ///列表底部不可拖拽排序的widget集合
  List<Widget>? footer;

  ///长按回调
  final LongPressCallback longPressCallback;

  ///结束拖拽回调
  final DragFinishCallback dragFinishCallback;

  ///item边距
  final EdgeInsetsGeometry padding;

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
        padding: padding,
        sliver: SliverSafeArea(
            key: UniqueKey(),
            top: false,
            bottom: false,
            sliver: ReorderableSliverGridView.count(
              crossAxisSpacing: crossAxisSpacing,
              mainAxisSpacing: mainAxisSpacing,
              crossAxisCount: crossAxisCount,
              children: children,
              footer: footer,
              onReorder: (int oldIndex, int newIndex) {
                if (oldIndex > data.length) {
                  throw FlutterError(
                      '当前访问下标$oldIndex data数组长度${data.length} 发生数组越界异常');
                }
                if (newIndex > data.length) {
                  throw FlutterError(
                      '当前访问下标$newIndex data数组长度${data.length} 发生数组越界异常');
                }
                if (oldIndex > children.length) {
                  throw FlutterError(
                      '当前访问下标$oldIndex children数组长度${children.length} 发生数组越界异常');
                }
                if (newIndex > children.length) {
                  throw FlutterError(
                      '当前访问下标$newIndex children数组长度${children.length} 发生数组越界异常');
                }

                ///结束拖拽后更新数据
                final dynamic element = data.removeAt(oldIndex);
                data.insert(newIndex, element);
                final Widget childElement = children.removeAt(oldIndex);
                children.insert(newIndex, childElement);
                dragFinishCallback(children, data);
              },
              dragWidgetBuilder: (int index, Widget child,
                  ImageProvider<Object>? imageProvider) {
                ///生成拖拽视图
                return StoreProvider<SmartHomeState>(
                    store: smartHomeStore,
                    child: Material(
                        color: Colors.transparent,
                        child: DragWidgetScaleAnimation(child: child)));
              },
              isAllowDraggable: isAllowDraggable ?? false,
              longPressCallback: longPressCallback,
              childAspectRatio: childAspectRatio,
            )));
  }
}
