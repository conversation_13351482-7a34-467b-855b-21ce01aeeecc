import 'package:flutter/cupertino.dart';

/// Build the drag widget under finger when dragging.
/// The index here represents the index of current dragging widget
/// The child here represents the current index widget
/// [dragWidgetScreenshot] If you pass screenshotDragWidget true, then will take a screenshot of the drag widget.
/// deprecated , use DragWidgetBuilderV2 instead
@Deprecated('')
typedef DragWidgetBuilder = Widget Function(
    int index, Widget child, ImageProvider? screenshot);

class DragWidgetBuilderV2 {
  /// if ture, will create a screenshot fo the drag widget
  final bool isScreenshotDragWidget;

  /// [screenshot] will not null if you provide isTakeScreenshotDragWidget = ture.
  final Widget Function(int index, Widget child, ImageProvider? screenshot)
      builder;

  DragWidgetBuilderV2(
      {this.isScreenshotDragWidget = false, required this.builder});

  /// a helper method to covert deprecated build to current builder
  static DragWidgetBuilderV2? createByOldBuilder9(
      DragWidgetBuilder? oldBuilder) {
    if (oldBuilder == null) {
      return null;
    }
    return DragWidgetBuilderV2(
        isScreenshotDragWidget: true,
        builder: (int index, Widget child, ImageProvider? screenshot) =>
            oldBuilder(index, child, screenshot));
  }
}
