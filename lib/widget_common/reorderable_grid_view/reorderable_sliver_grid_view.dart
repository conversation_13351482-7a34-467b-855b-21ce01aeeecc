import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/reorderable_item.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/reorderable_wrapper_widget.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/sliver_grid_with_reorderable_pos_delegate.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/util.dart';

import 'drag_widget_builder_v2.dart';

class ReorderableSliverGridView extends StatelessWidget {
  final List<Widget> children;
  final List<Widget>? header;
  final List<Widget>? footer;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;

  final ReorderCallback onReorder;
  final DragWidgetBuilderV2? dragWidgetBuilderV2;
  final ScrollSpeedController? scrollSpeedController;
  final PlaceholderBuilder? placeholderBuilder;
  final OnDragStart? onDragStart;
  final OnDragUpdate? onDragUpdate;
  final Duration dragStartDelay;
  final bool dragEnabled;
  final bool? isAllowDraggable; //是否允许长按拖拽

  final LongPressCallback longPressCallback; //长按回调

  const ReorderableSliverGridView({
    super.key,
    this.children = const <Widget>[],
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.childAspectRatio,
    required this.onReorder,
    this.dragWidgetBuilderV2,
    this.header,
    this.footer,
    this.dragStartDelay = kLongPressTimeout,
    this.scrollSpeedController,
    this.placeholderBuilder,
    this.onDragStart,
    this.onDragUpdate,
    this.dragEnabled = true,
    this.isAllowDraggable = true,
    required this.longPressCallback,
  });

  factory ReorderableSliverGridView.count(
      {Key? key,
      required int crossAxisCount,
      required ReorderCallback onReorder,
      DragWidgetBuilder? dragWidgetBuilder,
      DragWidgetBuilderV2? dragWidgetBuilderV2,
      List<Widget>? footer,
      List<Widget>? header,
      OnDragStart? onDragStart,
      OnDragUpdate? onDragUpdate,
      double mainAxisSpacing = 0.0,
      double crossAxisSpacing = 0.0,
      double childAspectRatio = 1,
      Duration dragStartDelay = kLongPressTimeout,
      List<Widget> children = const <Widget>[],
      bool dragEnabled = true,
      bool isAllowDraggable = true,
      required LongPressCallback longPressCallback,
      PlaceholderBuilder? placeholderBuilder}) {
    return ReorderableSliverGridView(
      key: key,
      onReorder: onReorder,
      children: children,
      footer: footer,
      header: header,
      crossAxisCount: crossAxisCount,
      dragWidgetBuilderV2: dragWidgetBuilderV2 ??
          DragWidgetBuilderV2.createByOldBuilder9(dragWidgetBuilder),
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      childAspectRatio: childAspectRatio,
      onDragStart: onDragStart,
      onDragUpdate: onDragUpdate,
      dragStartDelay: dragStartDelay,
      dragEnabled: dragEnabled,
      isAllowDraggable: isAllowDraggable,
      longPressCallback: longPressCallback,
      placeholderBuilder: placeholderBuilder,
    );
  }

  @override
  Widget build(BuildContext context) {
    final SliverGridWithReorderablePosDelegate child =
        SliverGridWithReorderablePosDelegate.count(
            key: key,
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: mainAxisSpacing,
            crossAxisSpacing: crossAxisSpacing,
            childAspectRatio: childAspectRatio,
            children: ReorderableItemView.wrapMeList(header, children, footer));

    return ReorderableWrapperWidget(
      onReorder: onReorder,
      dragWidgetBuilder: dragWidgetBuilderV2,
      scrollSpeedController: scrollSpeedController,
      placeholderBuilder: placeholderBuilder,
      onDragStart: onDragStart,
      onDragUpdate: onDragUpdate,
      isSliver: true,
      child: child,
      isAllowDraggable: isAllowDraggable,
      longPressCallback: longPressCallback,
    );
  }
}
