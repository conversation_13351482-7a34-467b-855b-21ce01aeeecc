import 'dart:ui' as ui show Image;

import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';

Future<ui.Image?> takeScreenShot(State state) async {
  final RenderObject? renderObject = state.context.findRenderObject();
  // var renderObject = item.context.findRenderObject();
  if (renderObject is RenderRepaintBoundary) {
    final RenderRepaintBoundary renderRepaintBoundary = renderObject;
    // not good at here
    final double devicePixelRatio =
        MediaQuery.of(state.context).devicePixelRatio;
    return renderRepaintBoundary.toImage(pixelRatio: devicePixelRatio);
  }
  return null;
}
