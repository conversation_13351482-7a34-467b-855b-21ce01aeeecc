import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/reorderable_item.dart';
import 'package:smart_home/widget_common/reorderable_grid_view/util.dart';

import 'drag_widget_builder_v2.dart';

typedef DragItemUpdate = void Function(
    DragInfo item, Offset position, Offset delta);

typedef DragItemCallback = void Function(DragInfo item);

/// callback that draw widget ready build(most time take a screenshot)
typedef DragWidgetReadyCallback = void Function();

// Strange that you are create at onStart?
// It's boring that pass you so many params
class DragInfo extends Drag {
  late int index;
  final DragItemUpdate? onUpdate;
  final DragItemCallback? onCancel;
  final DragItemCallback? onEnd;
  final ScrollSpeedController? scrollSpeedController;
  final DragWidgetReadyCallback readyCallback;

  final TickerProvider tickerProvider;
  final GestureMultiDragStartCallback onStart;

  final DragWidgetBuilderV2? dragWidgetBuilder;
  late Size _itemSize;
  late Widget _child;
  late ScrollableState _scrollable;

  // Drag position always is the finger position in global
  Offset dragPosition;

  // dragOffset is the position finger pointer in local(renderObject's left top is (0, 0))
  // how to get the center of dragInfo in global.
  late Offset _dragOffset;

  // = renderBox.size.height
  late double _dragExtent;
  late Size _dragSize;
  // late GlobalKey screenshotKey;
  late ReorderableItemViewState item;

  AnimationController? _proxyAnimationController;

  // Give to _Drag?? You want more control of the drag??
  OverlayEntry? _overlayEntry;
  BuildContext context;
  OverlayState? overlay;
  bool hasEnd = false;

  // zero pos in global, offset to navigation.
  // Fix issue #49
  Offset? _zeroOffset;

  ImageProvider? dragWidgetScreenShot;

  bool _autoScrolling = false;

  int _scrollBeginTime = 0;

  Widget? _dragWidget;

  static const int defaultScrollDuration = 14;

  bool isAllowDragable;

  DragInfo({
    required this.readyCallback,
    required this.item,
    required this.tickerProvider,
    required this.onStart,
    required this.dragPosition,
    required this.context,
    this.overlay,
    this.scrollSpeedController,
    this.dragWidgetBuilder,
    this.onUpdate,
    this.onCancel,
    this.onEnd,
    this.isAllowDragable = true,
  }) {
    index = item.index;
    _child = item.widget.child;
    _itemSize = item.context.size!;
    // screenshotKey = item.repaintKey;

    // why global to is is zero??
    _zeroOffset = (_getOverlay().context.findRenderObject()! as RenderBox)
        .globalToLocal(Offset.zero);

    final RenderBox renderBox = item.context.findRenderObject()! as RenderBox;
    _dragOffset = renderBox.globalToLocal(dragPosition);
    _dragExtent = renderBox.size.height;
    _dragSize = renderBox.size;

    // you can not delete !, because in some flutter version is option.
    _scrollable = Scrollable.of(item.context);
  }

  NavigatorState? findNavigator(BuildContext context) {
    NavigatorState? navigator;
    if (context is StatefulElement && context.state is NavigatorState) {
      navigator = context.state as NavigatorState;
    }
    navigator = navigator ?? context.findAncestorStateOfType<NavigatorState>();
    return navigator;
  }

  Offset getCenterInGlobal() {
    return getPosInGlobal() + _dragSize.center(Offset.zero);
  }

  Offset getPosInGlobal() {
    return dragPosition - _dragOffset;
  }

  void dispose() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _dragWidget = null;
    _proxyAnimationController?.dispose();
    _proxyAnimationController = null;
  }

  Widget createProxy(BuildContext context) {
    Offset position = dragPosition - _dragOffset;
    if (_zeroOffset != null) {
      position = position + _zeroOffset!;
    }

    _dragWidget ??= dragWidgetBuilder != null
        ? dragWidgetBuilder!.builder(index, _child, dragWidgetScreenShot)
        : Material(elevation: 1.0, child: _defaultDragWidget(context));
    return Positioned(
      top: position.dy,
      left: position.dx,
      child: SizedBox(
        width: _itemSize.width,
        height: _itemSize.height,
        child: _dragWidget,
      ),
    );
  }

  Widget _createDragByChild(BuildContext context) {
    return _child;
  }

  Widget _defaultDragWidget(BuildContext context) {
    // return child;
    final Widget rst = _createDragByChild(context);
    return rst;
  }

  OverlayState _getOverlay() {
    // you can not delete !, because in some flutter version is option.
    return overlay ?? Overlay.of(context);
  }

  void startDrag(ImageProvider? screenshot) {
    readyCallback();
    dragWidgetScreenShot = screenshot;
    _overlayEntry = OverlayEntry(builder: createProxy);
    //
    // // Can you give the overlay to _Drag?
    final OverlayState overlay = _getOverlay();
    overlay.insert(_overlayEntry!);
    _scrollIfNeed();
  }

  @override
  void update(DragUpdateDetails details) {
    dragPosition += details.delta;
    onUpdate?.call(this, dragPosition, details.delta);

    _overlayEntry?.markNeedsBuild();
    _scrollIfNeed();
  }

  Future<void> _scrollIfNeed() async {
    if (hasEnd) {
      _scrollBeginTime = 0;
      return;
    }
    if (hasEnd) {
      return;
    }
    if (!isAllowDragable) {
      return;
    }

    if (!_autoScrolling) {
      double? newOffset;
      bool needScroll = false;
      final ScrollPosition position = _scrollable.position;
      final RenderBox scrollRenderBox =
          _scrollable.context.findRenderObject()! as RenderBox;

      final ui.Offset scrollOrigin = scrollRenderBox.localToGlobal(Offset.zero);
      final double scrollStart = scrollOrigin.dy;

      final double scrollEnd = scrollStart + scrollRenderBox.size.height;

      final double dragInfoStart = getPosInGlobal().dy;
      final double dragInfoEnd = dragInfoStart + _dragExtent;

      // scroll bottom
      final bool overBottom = dragInfoEnd > scrollEnd;
      final bool overTop = dragInfoStart < scrollStart;

      final bool needScrollBottom =
          overBottom && position.pixels < position.maxScrollExtent;
      final bool needScrollTop =
          overTop && position.pixels > position.minScrollExtent;

      const double oneStepMax = 15;
      double scroll = oneStepMax;

      double overSize = 0;

      if (needScrollBottom) {
        overSize = dragInfoEnd - scrollEnd;
        scroll = min(overSize, oneStepMax);
      } else if (needScrollTop) {
        overSize = scrollStart - dragInfoStart;
        scroll = min(overSize, oneStepMax);
      }

      void calcOffset() {
        if (needScrollBottom) {
          newOffset = min(position.maxScrollExtent, position.pixels + scroll);
        } else if (needScrollTop) {
          newOffset = max(position.minScrollExtent, position.pixels - scroll);
        }
        needScroll =
            newOffset != null && (newOffset! - position.pixels).abs() >= 1.0;
      }

      calcOffset();

      if (needScroll && scrollSpeedController != null) {
        if (_scrollBeginTime <= 0) {
          _scrollBeginTime = DateTime.now().millisecondsSinceEpoch;
        }

        scroll = scrollSpeedController!(
          DateTime.now().millisecondsSinceEpoch - _scrollBeginTime,
          overSize,
          _itemSize.height,
        );

        calcOffset();
      }

      if (needScroll) {
        _autoScrolling = true;
        await position.animateTo(newOffset!,
            duration: const Duration(milliseconds: defaultScrollDuration),
            curve: Curves.linear);
        _autoScrolling = false;
      } else {
        // don't need scroll
        _scrollBeginTime = 0;
      }
    }
  }

  @override
  void end(DragEndDetails details) {
    onEnd?.call(this);

    _endOrCancel();
  }

  @override
  void cancel() {
    onCancel?.call(this);

    _endOrCancel();
  }

  void _endOrCancel() {
    hasEnd = true;
  }
}
