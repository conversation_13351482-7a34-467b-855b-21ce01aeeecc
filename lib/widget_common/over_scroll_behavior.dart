import 'dart:io';

import 'package:flutter/material.dart';

class NoneOverScrollBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    if (Platform.isAndroid || Platform.isFuchsia) {
      return GlowingOverscrollIndicator(
        child: child,
        //不显示头部水波纹
        showLeading: false,
        //不显示尾部水波纹
        showTrailing: false,
        axisDirection: details.direction,
        color: Theme.of(context).colorScheme.secondary,
      );
    } else {
      return child;
    }
  }
}
