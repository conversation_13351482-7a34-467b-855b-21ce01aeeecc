import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_list_util.dart';

import '../../store/smart_home_state.dart';
import 'giftpack_action.dart';

final Reducer<SmartHomeState> giftPackCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateNewUserPackListAction>(
          _updateNewUserPackList)
      .call,
]);

SmartHomeState _updateNewUserPackList(
    SmartHomeState state, UpdateNewUserPackListAction action) {
  if (action.location == SmartHomeConstant.unLoginNewUserPackLocation) {
    state.giftPackState.unloginNewUserPackModelList = action.itemList;
  } else if (action.location == SmartHomeConstant.loginNewUserPackLocation) {
    state.giftPackState.newUserPackModelList = action.itemList;
    addNewUserCardViewModelToAllCardViewModelMap(action.itemList);
  }
  return state;
}
