import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class NewUserPackResponseModel extends UhomeResponseModel {
  NewUserPackResponseModel.fromJson(super.data) : super.fromJson() {
    data = NewUserPackServerModel.fromJson(super.retData);
  }

  NewUserPackServerModel? data;

  @override
  String toString() {
    return 'NewUserPackResponseModel{data: $data}';
  }
}

class NewUserPackServerModel {
  List<NewUserPackItem> slideList = <NewUserPackItem>[];

  NewUserPackServerModel.fromJson(Map<dynamic, dynamic> json) {
    if (json['slideList'] is List<dynamic>) {
      for (final dynamic v in json['slideList'] as List<dynamic>) {
        if (v is Map<dynamic, dynamic>) {
          slideList.add(NewUserPackItem.fromJson(v));
        }
      }
    }
  }

  @override
  String toString() {
    return 'NewUserPackServerModel{slideList: $slideList}';
  }
}

/// https://stp.haier.net/project/191/interface/api/98653
class NewUserPackItem {
  String pictureUrl = ''; // 图片地址
  String title = ''; // 标题
  String subtitle = ''; // 副标题
  String detailsUrl = ''; // 详情地址

  NewUserPackItem(this.pictureUrl, this.title, this.subtitle, this.detailsUrl);

  NewUserPackItem.fromJson(Map<dynamic, dynamic> json) {
    pictureUrl = json.stringValueForKey('pictureUrl', '');
    title = json.stringValueForKey('title', '');
    subtitle = json.stringValueForKey('subtitle', '');
    detailsUrl = json.stringValueForKey('detailsUrl', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pictureUrl'] = pictureUrl;
    data['title'] = title;
    data['subtitle'] = subtitle;
    data['detailsUrl'] = detailsUrl;
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NewUserPackItem &&
          runtimeType == other.runtimeType &&
          pictureUrl == other.pictureUrl &&
          title == other.title &&
          detailsUrl == other.detailsUrl &&
          subtitle == other.subtitle;

  @override
  int get hashCode =>
      pictureUrl.hashCode ^
      title.hashCode ^
      subtitle.hashCode ^
      detailsUrl.hashCode;

  @override
  String toString() {
    return 'NewUserPackItem{pictureUrl: $pictureUrl, title: $title, subtitle: $subtitle, detailsUrl:$detailsUrl}';
  }
}
