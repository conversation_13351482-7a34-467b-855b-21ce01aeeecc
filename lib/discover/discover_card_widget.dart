import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_usdk/model/discovered_device.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../common/component_constant.dart';

typedef DialogCloseFunction = void Function();
typedef BindJumpFunction = void Function(String type);

class DiscoverCardWidget extends StatefulWidget {
  const DiscoverCardWidget(
      {super.key,
      required this.discoverDevice,
      required this.dialogCloseFun,
      required this.bindJumpFunction});

  final DiscoveredDevice discoverDevice;

  final DialogCloseFunction dialogCloseFun;

  final BindJumpFunction bindJumpFunction;

  @override
  State<StatefulWidget> createState() {
    return _DiscoverCardState();
  }
}

class _DiscoverCardState extends State<DiscoverCardWidget> {
  bool checked = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        const SizedBox(height: ComponentMargin.pageTop),
        GestureDetector(
          onTap: () {
            widget.bindJumpFunction('image');
          },
          child: CommonNetWorkImage(
            url: widget.discoverDevice.imageUrl,
            width: 80,
            height: 80,
            fit: BoxFit.contain,
            errorWidget: getHolderImage(),
            placeHolder: getHolderImage(),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          widget.discoverDevice.deviceName,
          style: TextStyle(
              fontSize: 16,
              color: AppSemanticColors.item.priWeaken,
              fontWeight: FontWeight.w500,
              fontFamilyFallback: fontFamilyFallback()),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: ComponentMargin.pageTop),
        Padding(
          padding:
              const EdgeInsets.symmetric(vertical: ComponentPadding.largeTop),
          child: _filterButtonWidget(),
        )
      ],
    );
  }

  Widget _filterButtonWidget() {
    return DialogComponents.buildBottomActions(<Widget>[
      ButtonFill(
        text: '不再提示',
        type: ButtonType.secondary,
        callback: widget.dialogCloseFun,
      ),
      ButtonFill(
          text: '立即添加',
          callback: () {
            widget.bindJumpFunction('button');
          })
    ]);
  }

  Widget getHolderImage() {
    return Image.asset(
      'assets/images/discover_default_icon.webp',
      package: SmartHomeConstant.package,
      width: 80,
      height: 80,
      fit: BoxFit.contain,
    );
  }
}
