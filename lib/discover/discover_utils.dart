// 自发现GIO打点使用
String getBindType(String bindType) {
  switch (bindType) {
    case 'Nolink':
      return '0';
    case 'SmartLink':
      return '1';
    case 'SoftAP':
      return '2';
    case 'SmartAP':
      return '3';
    case 'Scancode':
      return '5';
    case 'DirectLink':
      return '6';
    case 'BLE&SoftAP':
      return '7';
    case 'BLE':
      return '8';
    case 'QrOauth':
      return '9';
    case 'DevScan':
      return '10';
    case 'BLEMesh':
      return '11';
    case 'safety_tuya_ez':
      return '13';
    case 'gatewayLink':
      return '14';
    case 'BT':
      return '15';
    case 'NewDirectLink@key':
      return '16';
    case 'NewDirectLink@code':
      return '17';
    case 'BLEBroadcast':
      return '18';
    case 'DevScanWithoutAP':
      return '20';
    case 'SoftAP@NewDirectLink@key':
      return '21';
    case 'NewDirectLink@withoutVerification':
      return '22';
    case 'SoftAPOnly':
      return '24';
    case 'DevScanBoth':
      return '25';
    case 'safety_Directlink_ble':
      return '26';
    case 'NB_IOT@softAP':
      return '27';
    case '4G@softAP':
      return '28';
    case 'gatewayLink@safety_Risco':
      return '29';
    case 'gatewayLink@safety_bosheng_music':
      return '30';
    case 'Scancode_NH':
      return '31';
    case 'InfraredLink':
      return '100';
    case 'NonNet':
      return '101';
    case 'NonSupport':
      return '102';
  }

  return '';
}

String getPlayType(String controlState) {
  if (controlState == 'controllable' ||
      controlState == 'controllableAndAuth' ||
      controlState == 'controllableNewFlow') {
    return '1';
  } else {
    return '0';
  }
}

String getStatus(String controlState) {
  switch(controlState){
    case 'none':
      return '0';
    case 'controllable':
      return '1';
    case 'nearControllable':
      return '2';
    case 'controllableAndAuth':
      return '3';
    case 'controllableNewFlow':
      return '4';
  }
  return '';
}
