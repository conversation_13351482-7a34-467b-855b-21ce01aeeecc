import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_usdk/model/common_result.dart';
import 'package:plugin_usdk/model/discovered_device.dart';
import 'package:plugin_usdk/usdk_plugin.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:storage/storage.dart';
import 'package:user/user.dart';

import '../common/smart_home_util.dart';
import 'discover_card_widget.dart';
import 'discover_utils.dart';

class DiscoverNoPromptConstant {
  //仅本生命周期不提示
  static const String ignoreLifecycleNoPrompt = '1';

  //后续不再提示
  static const String ignoreNoPrompt = '2';

  //设置不提示
  static const String discoverSwitchOffNoPrompt = '3';

  //已绑定不提示
  static const String hasBoundNoPrompt = '4';

  //快连30分钟外且无工单不弹框(5)
  static const String noWorkOrderNoPrompt = '5';

  //快连30分钟外且有工单不弹框(6)
  static const String hasWorkOrderNoPrompt = '6';

  //快连30分钟外且有上次已绑定用户(7)
  static const String previouslyBoundNoPrompt = '7';
}

// 自发现弹窗相关逻辑
class DiscoverPresenter {
  static final Dialogs _dialogs = Dialogs();

  static DiscoverPresenter? _instance;
  UsdkPlugin usdkPlugin = UsdkPlugin();

  static const String bindDiscoverSwitchState = 'BindingDiscoverPopSwitch';
  static const String bindDiscoverPermission =
      'ifDiscoverAuthorizedDialogDisplayed';
  //不再提醒
  static const String bindDiscoverStorageIgnore =
      'BindingDiscoverStorageIgnore';

  //上次已绑定
  static const int previouslyBoundStatus = 1;

  //configStatue的状态值
  static const String configStatusAlreadyConfigured = 'already_configured';
  static const String configStatusUpRouterable = 'up_routerable';
  static const String configTypePureBle = 'PureBle';
  static const String configTypeWithoutWifi = 'WithoutWifi';
  static const String controlStateNearControllable = 'nearControllable';

  //自发现开
  static const String discoverOn = '1';

  //自发现关
  static const String discoverOff = '0';

  BuildContext? _context;

  //自发现开关
  bool discoverSwitch = false;

  //当前是否已弹出自发现弹窗
  bool discoverShowing = false;

  //是否在智家Tab
  bool _isSmartHomeTab = false;

  //本次启动忽略
  Set<String> ignoreId = <String>{};

  //不再提示
  Set<String> storageIgnoredId = <String>{};

  //已gio打点的设备
  Set<String> gioTradId = <String>{};

  int discoverCount = 0;

  LinkedHashMap<String, DiscoveredDevice> discoverMap =
      LinkedHashMap<String, DiscoveredDevice>();

  final String bindVdn =
      'https://uplus.haier.com/uplusapp/main/qrcodescan.html?deviceId=';

  factory DiscoverPresenter.getInstance() => _getInstance();

  DiscoverPresenter._internal() {
    getStorageIgnored();
  }

  Future<void> getStorageIgnored() async {
    final String storage =
        await Storage.getStringValue(bindDiscoverStorageIgnore);
    if (storage.isNotEmpty) {
      final List<dynamic> jsonData = jsonDecode(storage) as List<dynamic>;
      storageIgnoredId = jsonData.map((dynamic e) => e as String).toSet();
    }
  }

  static DiscoverPresenter _getInstance() {
    _instance ??= DiscoverPresenter._internal();
    return _instance!;
  }

  void setContextAndSmartHomeTab(BuildContext value, bool homeTab) {
    _context = value;
    _isSmartHomeTab = homeTab;
    checkDiscoverDialog();
  }

  Future<void> addDiscoverDeviceListener() async {
    final String discover = await Storage.getStringValue(
        bindDiscoverSwitchState,
        defaultValue: Platform.isIOS ? discoverOn : discoverOff);
    discoverSwitch = (discover == discoverOn);
    DevLogger.debug(
        tag: SmartHomeConstant.package, msg: 'discoverSwitch - $discover');
    discoverCount = 0;
    usdkPlugin.startBleSearch().then((dynamic value) {
      DevLogger.debug(
          tag: SmartHomeConstant.package, msg: 'startBleSearch: $value');
    });
    usdkPlugin.addDeviceDiscoveryListener(
        onAddDiscoveredDevice: (DiscoveredDevice device) {
      discoverCount = discoverCount + 1;
      handleDiscoverDevice(device);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'onAddDiscoveredDevice - ${device.deviceId} discoverMap:${discoverMap.length} storageIgnoredId:$storageIgnoredId ignoreId:$ignoreId');
      checkDiscoverDialog();
    }, onUpdateDiscoveredDevice: (DiscoveredDevice device) {
      handleDiscoverDevice(device);
      checkDiscoverDialog();
    }, onRemoveDiscoveredDevice: (DiscoveredDevice device) {
      discoverMap.remove(device.deviceId);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'onRemoveDiscoveredDevice - ${device.deviceId} discoverMap:${discoverMap.length} storageIgnoredId:$storageIgnoredId ignoreId:$ignoreId');
      discoverCount = discoverCount - 1;
      gioTradId.remove(device.deviceId);
      checkDiscoverDialog();
    }).then((CommonResult value) {
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'addDeviceDiscoveryListener: $value');
    });
    getDiscoveredDeviceList();
  }

  Future<void> getDiscoveredDeviceList() async {
    discoverMap.clear();
    usdkPlugin.getDiscoveredDeviceList().then((List<DiscoveredDevice> devices) {
      discoverCount = devices.length;
      devices.forEach((DiscoveredDevice device) {
        handleDiscoverDevice(device);
      });
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'getDiscoveredDeviceList - discoverMap:${discoverMap.length} memberType:${smartHomeStore.state.familyState.familyRole}');
      checkDiscoverDialog();
    }).catchError((dynamic value) {
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getDiscoveredDeviceList: $value');
    });
  }

  void removeDiscoverDeviceListener() {
    _isSmartHomeTab = false;
    usdkPlugin.removeDeviceDiscoveryListener();
  }

  //判断是否符合弹窗条件
  void checkDiscoverDialog() {
    if (!discoverShowing &&
        discoverSwitch &&
        _isSmartHomeTab &&
        _context != null &&
        _context!.mounted &&
        discoverMap.isNotEmpty &&
        MediaQuery.of(_context!).orientation == Orientation.portrait &&
        !smartHomeStore.state.isEditState &&
        (User.getLoginStatusSync()?.isLogin ?? false)) {
      final String deviceId = discoverMap.keys.first;
      if (discoverMap[deviceId] != null) {
        showDiscoverCardDialog(_context!, discoverMap[deviceId]!);
      }
    }
  }

  //自发现上报更新或添加时,处理数据
  void handleDiscoverDevice(DiscoveredDevice device) {
    final bool needTrack = !gioTradId.contains(device.deviceId);
    if (!discoverSwitch) {
      if (needTrack) {
        gioIgnoreTrackFunc(device, DiscoverNoPromptConstant.discoverSwitchOffNoPrompt);
      }
      return;
    }

    if (ignoreId.contains(device.deviceId)) {
      if (needTrack) {
        gioIgnoreTrackFunc(
            device, DiscoverNoPromptConstant.ignoreLifecycleNoPrompt);
      }
    } else if (storageIgnoredId.contains(device.deviceId)) {
      if (needTrack) {
        gioIgnoreTrackFunc(device, DiscoverNoPromptConstant.ignoreNoPrompt);
      }
    } else if (device.configType == configTypePureBle &&
        device.configStatus == configStatusAlreadyConfigured) {
      discoverMap.remove(device.deviceId);
      if (needTrack) {
        gioIgnoreTrackFunc(device, DiscoverNoPromptConstant.hasBoundNoPrompt);
      }
    } else if (device.configStatus == configStatusUpRouterable) {
      discoverMap.remove(device.deviceId);
    } else if (device.controlState == controlStateNearControllable) {
      discoverMap.remove(device.deviceId);
      if (needTrack) {
        _deviceNoPromptsGio(device);
      }
    } else {
      discoverMap[device.deviceId] = device;
    }
  }

  void showDiscoverCardDialog(
    BuildContext parentContext,
    DiscoveredDevice discoverDevice,
  ) {
    discoverShowing = true;
    gioTrackFunc(discoverDevice);
    final int dialogStartTime = DateTime.now().millisecondsSinceEpoch;
    _dialogs.showSmartHomeModalBottomSheet(
      context: parentContext,
      isDismissible: false,
      headerConfig: HeaderConfig.titleWithClose(
          title: '发现设备',
          callback: () {
            _dialogs.closeSmartHomeModalBottomSheet();
          }),
      child: (BuildContext context) {
        return DiscoverCardWidget(
            discoverDevice: discoverDevice,
            dialogCloseFun: () {
              notReminder(discoverDevice);
            },
            bindJumpFunction: (String type) {
              jumpToBind(discoverDevice, type);
            });
      },
      afterClose: () {
        bindDialogClose(false, discoverDevice.deviceId);
        discoverMap.remove(discoverDevice.deviceId);
        discoverShowing = false;
        final Map<String, String> map = <String, String>{};
        final int time =
            (DateTime.now().millisecondsSinceEpoch - dialogStartTime) ~/ 1000;
        map['time_length'] = time.toString();
        gioTrack('MB33837', map);
        checkDiscoverDialog();
      },
    );
  }

  // 立即绑定
  void jumpToBind(DiscoveredDevice discoverDevice, String type) {
    bindDialogClose(false, discoverDevice.deviceId);
    if (discoverMap.containsKey(discoverDevice.deviceId)) {
      vdnGoToPageWithoutNetCheck(
          '$bindVdn${discoverDevice.deviceId}&entranceType=discoverDialog&clickType=$type');
      discoverMap.remove(discoverDevice.deviceId);
      deviceBindGioTrack(discoverDevice, type, '1');
    } else {
      ToastHelper.showToast('已失效，请重新添加');
      deviceBindGioTrack(discoverDevice, type, '2');
    }
    _dialogs.closeSmartHomeModalBottomSheet();
  }

  //不再提醒
  void notReminder(DiscoveredDevice discoverDevice) {
    final Map<String, String> map = <String, String>{};
    map['result'] = 'ON';
    gioTrack('MB33835', map);
    bindDialogClose(true, discoverDevice.deviceId); //不再提醒
    discoverMap.remove(discoverDevice.deviceId);
    _dialogs.closeSmartHomeModalBottomSheet();
  }

  Future<void> bindDialogClose(bool checked, String deviceId) async {
    if (checked) {
      storageIgnoredId.add(deviceId);
      await Storage.putStringValue(
          bindDiscoverStorageIgnore, jsonEncode(storageIgnoredId.toList()));
    } else {
      ignoreId.add(deviceId);
    }
  }

  void _deviceNoPromptsGio(DiscoveredDevice device) {
    String result = DiscoverNoPromptConstant.noWorkOrderNoPrompt;
    if (device.workOrder) {
      result = DiscoverNoPromptConstant.hasWorkOrderNoPrompt;
    } else if (device.lastBindStatus == previouslyBoundStatus) {
      result = DiscoverNoPromptConstant.previouslyBoundNoPrompt;
    }
    gioIgnoreTrackFunc(device, result);
  }

  void gioIgnoreTrackFunc(DiscoveredDevice discoverDevice, String result) {
    final Map<String, String> map = getGioMap(discoverDevice);
    map['result'] = result;
    gioTradId.add(discoverDevice.deviceId);
    gioTrack('MB33836', map);
  }

  void gioTrackFunc(DiscoveredDevice discoverDevice) {
    final Map<String, String> map = getGioMap(discoverDevice);
    gioTrack('MB33831', map);
  }

  Map<String, String> getGioMap(DiscoveredDevice discoverDevice) {
    final Map<String, String> map = <String, String>{};
    map['bindtype'] = getBindType(discoverDevice.bindType);
    map['entranceL2'] = '3';
    map['net'] = discoverDevice.configType;
    map['appTypeCode'] = discoverDevice.appTypeCode;
    map['devicetype'] = discoverDevice.appTypeName;
    map['model'] = discoverDevice.model;
    map['devno'] = discoverDevice.productCode;
    return map;
  }

  Future<void> deviceBindGioTrack(
      DiscoveredDevice device, String type, String result) async {
    final Map<String, String> map = <String, String>{};
    final String traceID = await usdkPlugin.createTraceId();
    map['traceID'] = traceID;
    map['bindtype'] = getBindType(device.bindType);
    map['entranceL2'] = '3';
    map['net'] = device.configType;
    map['appTypeCode'] = device.appTypeCode;
    map['devicetype'] = device.appTypeName;
    map['model'] = device.model;
    map['devno'] = device.productCode;
    map['res_type'] = type == 'image' ? '1' : '2';
    map['result'] = result;
    map['play_type'] = getPlayType(device.controlState);
    map['activity_type'] = 'novalue';
    map['status'] = getStatus(device.controlState);
    gioTrack('MB33833', map);
  }
}
