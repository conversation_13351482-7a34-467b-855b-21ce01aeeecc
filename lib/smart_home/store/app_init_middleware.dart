/*
 * 描述：App初始化middleware
 * 作者：fancunshuo
 * 建立时间: 2025/8/3
 */
import 'dart:async';

import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:plugin_device/model/device_msg_model.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/smart_home_utils.dart';
import 'package:smart_home/store/smart_home_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:storage/storage.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/user.dart';
import 'package:wash_device_manager/wash_device_manager.dart';

import '../../cache/model/main_params_model.dart';
import '../../cache/store/cache_action.dart';
import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../device/device_presenter.dart';
import '../../device/fridge_foodnums/fridge_foodnum_presenter.dart';
import '../../discover/discover_presenter.dart';
import '../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../edit/store/edit_action.dart';
import '../../edit/util/edit_manager.dart';
import '../../features/operating_activity/services/activity_service.dart';
import '../../features/operating_activity/store/activity_action.dart';
import '../../navigator/family/family_change_presenter.dart';
import '../../navigator/family/store/family_action.dart';
import '../../navigator/family/widget/family_pop_dialog.dart';
import '../../offline_gio/offline_gio_config.dart';
import '../../offline_gio/offline_gio_track.dart';
import '../../pack_gift/giftpack_presenter.dart';
import '../../scene/scene_presenter.dart';
import '../../scene/switch/switch_presenter.dart';
import '../../store/smart_home_store.dart';
import '../../time_cost_analysis/analysis_presenter.dart';
import '../../user/user_action.dart';
import '../../whole_house/device_consumables/device_consumables_presenter.dart';
import '../../whole_house/whole_house_presenter.dart';

class AppInitMiddleware implements MiddlewareClass<SmartHomeState> {
  Store<SmartHomeState>? _store;

  StreamSubscription<BatchDeviceListChangedEvent>? _deviceListChangeListener;
  StreamSubscription<UserLogoutMessage>? _userLogoutListener;
  StreamSubscription<PushMessage>? _pushMessage;
  StreamSubscription<UserFamilyListChangeMessage>? _familyMapListener;
  StreamSubscription<BackTopRefreshMessage>? _backTopListener;
  StreamSubscription<UserCurrentFamilyChangeMessage>? _currentFamilyListener;
  StreamSubscription<dynamic>? _roomListChangeListener;
  StreamSubscription<UserTokenRefreshedMessage>? _userLoginSuccessListener;
  WholeHousePresenter wholeHousePresenter = WholeHousePresenter();
  ScenePresenter scenePresenter = ScenePresenter();
  DeviceConsumablesPresenter consumablesPresenter =
      DeviceConsumablesPresenter();
  static const String _updateFamilyPositionKey = 'updateFamilyPosition';
  static const int _smartHomeIndex = 0;

  bool _currentFamilyInited = false;

  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    _store = store;
    if (action is AppInitAction) {
      _initAppData(action.mainParamsMap, action.isFirstScreen);
    } else if (action is AppDisposeAction) {
      _removePluginListeners();
    }
    next(action);
  }

  Future<void> _initAppData(
      Map<String, String> mainParamsMap, bool isFirstScreen) async {
    Network.isOnline().then((NetworkStatus networkStatus) {
      final bool isNetAvailable = networkStatus.isOnline;
      _store?.dispatch(UpdateNetworkStateAction(isNetAvailable));
    });
    final MainParamsModel mainParams = MainParamsModel.fromJson(mainParamsMap);
    String familyId = mainParams.familyId;
    bool isLogin = mainParams.isLogin;

    if (isFirstScreen) {
      _store?.dispatch(UpdateAppCacheAction(mainParams));
    }

    _subscribeDeviceList();

    _addPluginListeners();

    isLogin = await _fetchLoginStatus();
    _store?.dispatch(UpdateLoginStatusAction(isLogin: isLogin));

    final FamilyModel familyModel = await _fetchFamilyInfo();
    _updateFamilyInfo(familyModel);
    familyId = familyModel.familyId;

    _fetchFamilyMapData();

    _initServerData(isLogin, familyId);

    _initGioData(familyId, isLogin);
  }

  Future<bool> _fetchLoginStatus() async {
    bool isLogin;
    try {
      final LoginStatus loginStatus = await User.getLoginStatus();
      isLogin = loginStatus.isLogin;
      DevLogger.info(
          tag: 'SmartHome',
          msg: 'initAppData User.getLoginStatus result: loginStatus$isLogin');
    } catch (e) {
      DevLogger.error(
          tag: 'SmartHome', msg: 'initAppData User.getLoginStatus() err:$e');
      isLogin = false;
    }

    return isLogin;
  }

  Future<FamilyModel> _fetchFamilyInfo() async {
    FamilyModel familyModel = FamilyModel.fromJson(<dynamic, dynamic>{});
    try {
      familyModel = await Family.getCurrentFamily();
      DevLogger.info(
          tag: 'SmartHome',
          msg:
              'initAppData Family.getCurrentFamily() result: familyId${familyModel.familyId}, familyName:${familyModel.info.familyName}');

      FamilyChangePresenter.queryFamilyChangeStatus(familyModel.familyId);
    } catch (e) {
      DevLogger.error(
          tag: 'SmartHome',
          msg: 'initAppData Family.getCurrentFamily() err:$e');
    }
    return familyModel;
  }

  void _updateFamilyInfo(FamilyModel familyModel) {
    final FamilyActionModel familyActionModel = FamilyActionModel(
        familyId: familyModel.familyId,
        familyName: familyModel.info.familyName,
        memberType: familyModel.memberType);
    _store?.dispatch(UpdateAppCurrentFamilyInfoAction(familyActionModel));
  }

  Future<void> _fetchFamilyMapData() async {
    try {
      final Map<String, FamilyModel> familyMap = await Family.getFamilyMap();
      DevLogger.debug(
          tag: 'SmartHome',
          msg:
              '__seq__ _fetchFamilyMapSyncData callback, length:${familyMap.length}');
      _store?.dispatch(UpdateAppFamilyMapAction(familyMap));
    } catch (e) {
      DevLogger.error(
          tag: 'SmartHome', msg: '__seq__ _fetchFamilyMapSyncData error $e');
    }
  }

  void _initServerData(bool isLogin, String familyId) {
    if (isLogin) {
      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.loginNewUserPackLocation);
      SwitchPresenter.querySwitchStatus(familyId);
    } else {
      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.unLoginNewUserPackLocation);
    }

    ActivityService.checkSummerActivityNormal().then((bool isActivityOpen) {
      _store?.dispatch(SummerActivityUpdateAction(isActivityOpen));
    });
  }

  void _initGioData(String familyId, bool isLogin) {
    OfflineGioTrack().updateFamilyId(familyId);
    Future<void>.delayed(const Duration(seconds: 3), () {
      OfflineGIOConfig().init();
    });
    gioTrack(GioConst.launchParams, <String, dynamic>{
      DevListGioEvent.familyId: familyId,
      DevListGioEvent.loginStatus: isLogin,
    });
  }

  void _addPluginListeners() {
    // 监听家庭列表变化
    _familyMapListener = Message.listen<UserFamilyListChangeMessage>(
        (UserFamilyListChangeMessage event) async {
      DevLogger.error(
          tag: 'SmartHome',
          msg: '__plugin_msg__ UIMessage.FamilyMapUpdateComplete callback');
      gioTrack(GioConst.familyMapUpdateComplete);
      _fetchFamilyMapData();
      if (!_currentFamilyInited) {
        _currentFamilyInited = true;
        _currentFamilyChangeCallback();
      }
    });
    // 监听当前家庭变化
    _currentFamilyListener = Message.listen<UserCurrentFamilyChangeMessage>(
        (UserCurrentFamilyChangeMessage event) =>
            _currentFamilyChangeCallback());
    // 监听用户登录状态变化
    _userLoginSuccessListener = Message.listen<UserTokenRefreshedMessage>(
        (UserTokenRefreshedMessage event) {
      DevLogger.error(
          tag: 'SmartHome',
          msg: '__plugin_msg__ UIMessage.UserLoginSuccess callback');

      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.loginNewUserPackLocation);
      smartHomeStore.dispatch(UpdateLoginStatusAction(isLogin: true));
      gioTrack(GioConst.userLoginSuccess);
      _fetchFamilyMapData();
    });

    // 退出登陆监听
    _userLogoutListener =
        Message.listen<UserLogoutMessage>((UserLogoutMessage event) {
      _currentFamilyInited = false;
      DevLogger.error(
          tag: 'SmartHome',
          msg: '__plugin_msg__ Message.UserLogoutMessage callback');

      Storage.putStringValue(
          SmartHomeConstant.smartHomeAppBrand, SmartHomeConstant.haier);
      smartHomeStore.dispatch(LogoutAction());
      hideFamilyPopDialog();

      /// 清空仪表盘数据
      wholeHousePresenter.clearWholeHouseData();
      scenePresenter.clearSceneData();
      DevicePresenter.getInstance().clearDeviceList();
      FamilyChangePresenter.isInitFamilyChange = false;
      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.unLoginNewUserPackLocation);
      DevLogger.info(
          tag: 'SmartHomePresenter logout',
          msg: 'SmartHomePresenter logout clear device list');

      gioTrack(GioConst.userLogoutMessage);
    });
    // 网络变化监听
    Network.addNetStateEventListener(_networkStatusChangedCallback);

    // ums推送
    _pushMessage = Message.listen<PushMessage>((PushMessage event) {
      DevLogger.error(
          tag: 'SmartHome',
          msg:
              '__plugin_msg__ Message.PushMessage callback, event:${event.messageMap}');

      final dynamic msgName = event.messageMap['msgName'];
      // 场景推送
      if (msgName == 'SCENE_BSM') {
        scenePresenter
            .fetchSceneData(smartHomeStore.state.familyState.familyId);
      }
      // 耗材推送
      if (msgName == 'APP_Dashboard_Consumable') {
        consumablesPresenter
            .fetchDeviceConsumables(smartHomeStore.state.familyState.familyId);
      }

      /// 冰箱食材数变化推送
      if (msgName == FridgeFoodNumPresenter.foodNumPushMessageName) {
        FridgeFoodNumPresenter.parseFoodNumPushMessage(event.messageMap);
      }
    });

    //修改房间顺序通知
    _roomListChangeListener = Message.listenWithName(
        'UserCurrentFamilyRoomListChange', (dynamic data) {
      DevLogger.error(
          tag: 'SmartHomePresenter',
          msg: '__plugin_msg__ _roomListChangeListener callback');

      DevicePresenter.getInstance().getDeviceList(DeviceListRequestModel(
        familyId: smartHomeStore.state.familyState.familyId,
        triggerType: DeviceListFetchTriggerType.roomListOrderChanged,
      ));
    });

    _backTopListener = UIMessage.sub<BackTopRefreshMessage>(
        (BackTopRefreshMessage event) async {
      DevLogger.error(
          tag: 'SmartHome',
          msg: '__plugin_msg__ UIMessage.BackTopRefreshMessage callback');

      final int? index = event.tabIndex;
      if (index != null && index == _smartHomeIndex) {
        SmartHomeUtils.scrollToTop();
      }
    });

    Storage.addNodeListner(
        _updateFamilyPositionKey, _updateFamilyPositionListener);
  }

  Future<void> _currentFamilyChangeCallback() async {
    DevLogger.error(
        tag: 'SmartHome',
        msg: '__plugin_msg__ UIMessage.CurrentFamilyUpdateComplete callback');

    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      SmartHomeUtils.scrollToTop();
    });
    final int traceId = DateTime.now().millisecondsSinceEpoch;
    TimeConsumeStatisticTracker.trace(
        traceId: traceId,
        loc: TraceLocation.t1_2,
        traceType: TraceType.curFamilyChange);
    DevLogger.info(
        tag: 'SmartHome',
        msg: '__seq__ CurrentFamilyUpdateComplete callback traceId:@$traceId');
    try {
      final FamilyModel familyModel = await Family.getCurrentFamily();

      final bool? isLogin = User.getLoginStatusSync()?.isLogin;
      final FamilyActionModel familyActionModel = FamilyActionModel(
          familyId: familyModel.familyId,
          familyName: familyModel.info.familyName,
          memberType: familyModel.memberType);
      gioTrack(DevListGioEvent.t0CurFamilyChange, <String, dynamic>{
        DevListGioEvent.familyId: familyModel.familyId,
        DevListGioEvent.loginStatus: isLogin,
      });
      smartHomeStore
          .dispatch(UpdateAppCurrentFamilyInfoAction(familyActionModel));

      wholeHousePresenter.getWholeHousePreferenceSetting(familyModel.familyId);
      scenePresenter.fetchSceneData(familyModel.familyId);
      SwitchPresenter.querySwitchStatus(familyModel.familyId);
      FamilyChangePresenter.queryFamilyChangeStatus(familyModel.familyId);
      OfflineGioTrack().updateFamilyId(familyModel.familyId ?? '');
      DiscoverPresenter.getInstance().getDiscoveredDeviceList();
    } catch (err) {
      TimeConsumeStatisticTracker.trace(
          traceId: traceId,
          loc: TraceLocation.t1_2,
          gioEnable: false,
          traceType: TraceType.curFamilyChange);
    }
  }

  void _subscribeDeviceList() {
    UpDevicePlugin.subscribeCurrentFamilyDeviceListChange(
            _deviceListChangeCallback)
        .catchError((dynamic error) {
      DevLogger.error(
          tag: 'SmartHome',
          msg:
              '__plugin_msg__ UpDevicePlugin.subscribeDeviceListChange error $error');
    });
  }

  Future<void> _deviceListChangeCallback(
      Map<String, DeviceInfoModel> currentFamilyDeviceListMap) async {
    final int traceId = DateTime.now().millisecondsSinceEpoch;
    TimeConsumeStatisticTracker.trace(
        traceId: traceId,
        loc: TraceLocation.t1_1,
        traceType: TraceType.devListChange);
    DevLogger.error(
        tag: 'SmartHome',
        msg:
            '__plugin_msg__ UpDevicePlugin.subscribeDeviceListChange callback, beginTime@$traceId');

    WashDeviceManager.getInstance().cancelLoadWashDeviceData();
    // 若当前处于编辑状态则退出编辑模式
    if (smartHomeStore.state.isEditState) {
      smartHomeStore.dispatch(ExitEditStateAction());
    }

    EditPresenterManager.dialogs.closeSmartHomeModalBottomSheet();
    if (SmartHomeEditManager.editDialogContext != null) {
      SmartHomeEditManager.closeEditDialog();
    }

    final FamilyModel curFamily = await Family.getCurrentFamily();
    gioTrack(DevListGioEvent.t0DevListChange, <String, String>{
      DevListGioEvent.familyId: curFamily.familyId,
    });
    final int getFamilySyncEnd = DateTime.now().millisecondsSinceEpoch;
    DevLogger.info(
        tag: 'SmartHome',
        msg:
            '__seq__ UserDeviceListChangeMessage callback, traceId:$traceId, getCurrentFamilySync end:@$getFamilySyncEnd@,curFamilyId:${curFamily?.familyId}, cost:${getFamilySyncEnd - traceId}');
    final String familyId = curFamily.familyId;
    if (familyId.isEmpty) {
      DevLogger.error(
          tag: 'SmartHome',
          msg:
              '__seq__ UserDeviceListChangeMessage callback, traceId:$traceId, getCurrentFamilySync end family is null, return');
      TimeConsumeStatisticTracker.trace(
          traceId: traceId,
          loc: TraceLocation.t1_1,
          traceType: TraceType.devListChange,
          gioEnable: false);
      return;
    }

    DevicePresenter.getInstance().updateDeviceList(
        currentFamilyDeviceListMap,
        DeviceListRequestModel(
          familyId: familyId,
          traceId: traceId,
          triggerType: DeviceListFetchTriggerType.deviceListChanged,
        ));
    wholeHousePresenter.getWholeHouseData(
        triggerType: TriggerType.deviceListChange,
        familyId: curFamily.familyId);
  }

  void _removePluginListeners() {
    _familyMapListener?.cancel();
    _currentFamilyListener?.cancel();
    _userLoginSuccessListener?.cancel();
    _deviceListChangeListener?.cancel();
    _userLogoutListener?.cancel();
    Network.removeNetStateEventListener(_networkStatusChangedCallback);
    _pushMessage?.cancel();
    _roomListChangeListener?.cancel();
    _backTopListener?.cancel();
  }

  void _networkStatusChangedCallback(NetworkStatus networkStatus) {
    DevLogger.error(
        tag: 'SmartHome',
        msg: '__plugin_msg__ _networkStatusChangedCallback: $networkStatus');

    smartHomeStore.dispatch(UpdateNetworkStateAction(networkStatus.isOnline));
    if (networkStatus.isOnline &&
        smartHomeStore.state.editState.isNeedSaveEditedCardListData) {
      EditPresenterManager.onClickDone();
    }
  }

  void _updateFamilyPositionListener(String attrName, String actonType) {
    DevLogger.info(
        tag: 'SmartHome',
        msg:
            'Storage.NodeListner _updateFamilyPositionListener key is $attrName,action is $actonType');
    wholeHousePresenter.getWholeHouseData(
        triggerType: TriggerType.familyPositionChange);
  }
}
