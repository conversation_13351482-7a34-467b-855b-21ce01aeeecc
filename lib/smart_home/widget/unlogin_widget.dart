import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:library_widgets/components/listView/hide_water_ripple_list_view.dart';
import 'package:smart_home/smart_home/widget/unlogin/unlogin_add.dart';
import 'package:smart_home/smart_home/widget/unlogin/unlogin_card_grid.dart';

class UnLoginPage extends StatelessWidget {
  UnLoginPage({super.key, this.physics});

  ScrollPhysics? physics;

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: CustomScrollView(
          physics: physics,
          slivers: const <Widget>[
            SliverToBoxAdapter(
                child: Column(
              children: <Widget>[
                UnLoginAddWidget(),
                UnLoginCardGridWidget(),
              ],
            ))
          ],
        ));
  }
}
