import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/src/store.dart';
import 'package:smart_home/smart_home/widget/unlogin/new_user_pack.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../../pack_gift/model/user_pack_model.dart';

class LoginNoDeviceGridWidget extends StatelessWidget {
  const LoginNoDeviceGridWidget({super.key});

  List<Widget> _buildItemList(NoDeviceGridWidgetViewModel viewModel) {
    final List<Widget> cardList = <Widget>[];
    if (viewModel.newUserPackModelList.isNotEmpty) {
      createCardList(cardList, viewModel.newUserPackModelList, 0);
      if (viewModel.newUserPackModelList.length > 1) {
        createCardList(cardList, viewModel.newUserPackModelList, 1);
      }
    }
    return cardList;
  }

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, NoDeviceGridWidgetViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return NoDeviceGridWidgetViewModel(
            newUserPackModelList:
                store.state.giftPackState.newUserPackModelList);
      },
      builder: (BuildContext context, NoDeviceGridWidgetViewModel viewModel) {
        final List<Widget> cardList = _buildItemList(viewModel);
        return GridView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shrinkWrap: true,
          // 使GridView高度自适应
          physics: const NeverScrollableScrollPhysics(),
          // 禁止GridView滚动
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              mainAxisExtent: 140),
          itemCount: cardList.length,
          itemBuilder: (BuildContext context, int index) {
            return cardList.elementAt(index);
          },
        );
      },
    );
  }

  void createCardList(List<Widget> cardList,
      List<NewUserPackItem> newUserPackModelList, int index) {
    final NewUserPackItem model = newUserPackModelList.elementAt(index);
    cardList.add(NewUserPackWidget(
        viewModel: NewUserPackViewModel(
            bgImg: model.pictureUrl,
            title: model.title,
            subtitle: model.subtitle,
            detailsUrl: model.detailsUrl),
    ));
  }
}

class NoDeviceGridWidgetViewModel {
  NoDeviceGridWidgetViewModel(
      {required this.newUserPackModelList});

  final List<NewUserPackItem> newUserPackModelList;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoDeviceGridWidgetViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(newUserPackModelList, other.newUserPackModelList);

  @override
  int get hashCode =>
      listHashCode(newUserPackModelList);
}
