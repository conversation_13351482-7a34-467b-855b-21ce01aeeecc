import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/room_bottom_edit_btn.dart';
import 'package:smart_home/scene/scene_expand/scene_expand.dart';
import 'package:smart_home/whole_house/whole_house_header_widget.dart';

import '../../common/constant_gio.dart';
import '../../device/device_list_widget.dart';
import '../../device/store/device_action.dart';
import '../../edit/util/edit_manager.dart';
import '../../store/smart_home_store.dart';

class DeviceTabContentWidget extends StatelessWidget {
  final ScrollPhysics? physics;
  final List<String> cardSortIdList;
  final bool dragEnable;
  final String roomId;
  final String roomName;
  final bool filterAll;

  const DeviceTabContentWidget({
    super.key,
    this.physics,
    required this.cardSortIdList,
    this.dragEnable = false,
    this.filterAll = false,
    required this.roomId,
    required this.roomName,
  });

  @override
  Widget build(BuildContext context) {
    final DeviceListWidget deviceList = DeviceListWidget(
      physics: physics,
      cardSortIdList: cardSortIdList,
      dragEnable: dragEnable,
      filterAll: filterAll,
      onReorder: onReorder,
    );
    return CustomScrollView(
      physics: physics,
      shrinkWrap: true,
      slivers: <Widget>[
        // 1 仪表盘组件（横向滚动）
        SliverToBoxAdapter(
          child: WholeHouseHeaderWidget(roomId: roomId, roomName: roomName),
        ),

        // 2 场景组件 + 场景展开按钮
        SceneExpandWidget(roomId: roomId, roomName: roomName),

        // 3 设备列表
        deviceList.buildSliver(context),

        // 4 房间底部编辑按钮
        const RoomBottomEditButton(),
      ],
    );
  }

  void onReorder(List<String> sortedIdList, String roomId) {
    gioTrack(GioConst.gioDragFinished, <String, String>{
      'source': SmartHomeEditManager.getSourceForGio(),
    });
    smartHomeStore.dispatch(SmallCardDragFinishedAction(cardSortIdList));
  }
}
