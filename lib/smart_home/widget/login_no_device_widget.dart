import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:library_widgets/components/listView/hide_water_ripple_list_view.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/smart_home/widget/login_no_device_grid_widget.dart';
import 'package:smart_home/smart_home/widget/unlogin/unlogin_add.dart';

class LoginNoDeviceWidget extends StatelessWidget {
  const LoginNoDeviceWidget({super.key, this.physics});

  final ScrollPhysics? physics;

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: CustomScrollView(
          physics: physics,
          slivers: <Widget>[
            SliverToBoxAdapter(
                child: Column(
              children: <Widget>[
                const UnLoginAddWidget(),
                _buildRepairCard(),
                const LoginNoDeviceGridWidget(),
              ],
            ))
          ],
        ));
  }

  Widget _buildRepairCard() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        gioTrack(SmartHomeConstant.reportForRepairGio);
        goToPageWithDebounce(SmartHomeConstant.urlInstallRepair);
      },
      child: Container(
        height: 80,
        width: double.infinity,
        margin: const EdgeInsets.only(left: 16, right: 16, top: 12),
        decoration: BoxDecoration(
          borderRadius:
              const BorderRadius.all(Radius.circular(ComponentRadius.device)),
          color: AppSemanticColors.background.primary,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SmartHomeText(
                    text: '报装报修',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    height: 1,
                    color: Color(0xff111111),
                  ),
                  SizedBox(height: 6),
                  SmartHomeText(
                    text: '全流程可视化服务',
                    fontSize: 12,
                    height: 1,
                    color: Color(0xff999999),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Image.asset(
                  'assets/images/report_repair.webp',
                  height: double.infinity,
                  package: SmartHomeConstant.package,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
