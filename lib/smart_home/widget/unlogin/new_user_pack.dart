import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../../common/constant_gio.dart';
import '../../../common/smart_home_text_widget.dart';
import '../../../common/smart_home_util.dart';
import '../../../device/resize_device_card/resize_base_model.dart';

class NewUserPackWidget extends StatelessWidget {
  bool unLoginState = false;

  NewUserPackWidget({
    super.key,
    required this.viewModel,
    this.unLoginState = false,
  });

  final NewUserPackViewModel viewModel;

  void _gioCard() {
    if (unLoginState) {
      if (viewModel.sortId() == new_user_guide_card_id) {
        // 未登录状态--- 了解智家卡片
        gioTrack(GioConst.understandApp);
      } else {
        // 未登录状态--- 其他(暂无，预留扩展位置)
      }
      return;
    }
    // 登录状态gio
    if (viewModel.sortId() == new_user_guide_card_id) {
      // 登录状态--- 了解智家卡片
      gioTrack(GioConst.understandAppLogin);
    } else if (viewModel.sortId() == new_user_gift_pack_card_id) {
      // 登录状态--- 新手礼包卡片
      gioTrack(GioConst.newUserPackLogin);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !smartHomeStore.state.isEditState,
      child: GestureDetector(
        onTap: () {
          _gioCard();
          if (!smartHomeStore.state.isEditState) {
            goToPageWithDebounce(viewModel.detailsUrl);
          }
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(22),
          ),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SmartHomeText(
                    text: viewModel.title,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    height: 1,
                    color: const Color(0xff111111),
                  ),
                  const SizedBox(height: 6),
                  SmartHomeText(
                    text: viewModel.subtitle,
                    fontSize: 12,
                    color: const Color(0xff999999),
                  ),
                  const Expanded(child: SizedBox()),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: CommonNetWorkImage(
                      url: viewModel.bgImg,
                      height: 72,
                      errorWidget: const PlaceHolderImage(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// 图片的占位图
class PlaceHolderImage extends StatelessWidget {
  const PlaceHolderImage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.93),
          borderRadius: BorderRadius.circular(12.w),
        ));
  }
}

class NewUserPackViewModel extends CardBaseViewModel {
  final String bgImg; // 背景图片
  final String title; // 标题
  final String subtitle; // 副标题
  final String detailsUrl; // 详情地址
  final String cardSortId;

  NewUserPackViewModel(
      {required this.bgImg,
      required this.title,
      required this.subtitle,
      required this.detailsUrl,
      this.cardSortId = ''})
      : super(ValueKey<String>(cardSortId), DeviceCardType.middleCard);

  @override
  CardType get cardType => CardType.newUserPackCard;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NewUserPackViewModel &&
          runtimeType == other.runtimeType &&
          bgImg == other.bgImg &&
          title == other.title &&
          subtitle == other.subtitle &&
          detailsUrl == other.detailsUrl;

  @override
  int get hashCode =>
      bgImg.hashCode ^ title.hashCode ^ subtitle.hashCode ^ detailsUrl.hashCode;

  @override
  String toString() {
    return 'NewUserPackViewModel{bgImg: $bgImg, title: $title, subtitle: $subtitle, detailsUrl:$detailsUrl}';
  }

  @override
  String sortId() {
    return cardSortId;
  }
}
