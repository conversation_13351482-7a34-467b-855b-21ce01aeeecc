import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/src/store.dart';
import 'package:smart_home/smart_home/widget/unlogin/new_user_pack.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../../device/device_view_model/card_base_view_model.dart';
import '../../../device/device_view_model/report_for_repair_card_view_model.dart';
import '../../../device/device_widget/report_for_repair_card.dart';
import '../../../pack_gift/model/user_pack_model.dart';

class UnLoginCardGridWidget extends StatelessWidget {
  const UnLoginCardGridWidget({super.key});

  List<Widget> _buildItemList(GiftCardGridViewModel viewModel) {
    final List<Widget> cardList = <Widget>[
      ReportForRepairCard(
        viewModel: ReportForRepairCardViewModel(),
      )
    ];
    if (viewModel.newUserPackModelList.isNotEmpty) {
      final NewUserPackItem model = viewModel.newUserPackModelList.elementAt(0);
      cardList.add(NewUserPackWidget(
        viewModel: NewUserPackViewModel(
            bgImg: model.pictureUrl,
            title: model.title,
            subtitle: model.subtitle,
            detailsUrl: model.detailsUrl,
            cardSortId: new_user_guide_card_id),
        unLoginState: true,
      ));
    }
    return cardList;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 12, right: 16),
      child: StoreConnector<SmartHomeState, GiftCardGridViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return GiftCardGridViewModel(
              store.state.giftPackState.unloginNewUserPackModelList);
        },
        builder: (BuildContext context, GiftCardGridViewModel viewModel) {
          final List<Widget> cardList = _buildItemList(viewModel);
          return GridView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2, crossAxisSpacing: 12, mainAxisExtent: 140),
            itemCount: cardList.length,
            itemBuilder: (BuildContext context, int index) {
              return cardList.elementAt(index);
            },
          );
        },
      ),
    );
  }
}

class GiftCardGridViewModel {
  List<NewUserPackItem> newUserPackModelList = <NewUserPackItem>[];

  GiftCardGridViewModel(this.newUserPackModelList);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GiftCardGridViewModel &&
          runtimeType == other.runtimeType &&
          newUserPackModelList == other.newUserPackModelList;

  @override
  int get hashCode => newUserPackModelList.hashCode;
}
