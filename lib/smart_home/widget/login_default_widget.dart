import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/components/listView/hide_water_ripple_list_view.dart';

class LoginDefaultWidget extends StatelessWidget {
  LoginDefaultWidget({super.key, this.physics});

  ScrollPhysics? physics;

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: CustomScrollView(
          physics: physics,
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: SizedBox(
                height: 200.w,
              ),
            )
          ],
        ));
  }
}
