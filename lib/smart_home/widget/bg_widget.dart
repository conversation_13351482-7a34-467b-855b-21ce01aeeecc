import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smart_home/common/constant.dart';

class BackgroundWidget extends StatelessWidget {
  const BackgroundWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      child: ColoredBox(
        color: const Color(0xfff5f5f5),
        child: Column(
          children: <Widget>[
            Image.asset(
              'assets/theme/leader_top.png',
              package: SmartHomeConstant.package,
              width: 375.w,
              height: 375.w,
              fit: BoxFit.fill,
            ),
            const Expanded(child: SizedBox()),
          ],
        ),
      ),
    );
  }
}
