import 'dart:async';
import 'dart:collection';

import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vdn/back.dart';
import 'package:vdn/vdn.dart';

import '../device/device_filter/device_filter_model.dart';
import '../store/smart_home_store.dart';
import 'constant.dart';

bool isFamilyMemberRole() {
  return smartHomeStore.state.familyState.familyRole == FamilyRole.member;
}

bool isFamilyAdminRole() {
  return smartHomeStore.state.familyState.familyRole == FamilyRole.admin;
}

bool isFamilyCreatorRole() {
  return smartHomeStore.state.familyState.familyRole == FamilyRole.creator;
}

int parseTimeToTimestamp(String time, {int defaultValue = 0}) {
  if (time.isEmpty) {
    return defaultValue;
  }

  try {
    return DateTime.parse(time).millisecondsSinceEpoch;
  } catch (e) {
    DevLogger.debug(
        tag: SmartHomeConstant.package, msg: 'parseTimeToTimestamp err:$e');
    return defaultValue;
  }
}

//函数防抖初始时间
int lastTime = 0;

class Debounce {
  static void run(VoidCallback action) {
    final int currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - lastTime > 1000) {
      action();
    }
    lastTime = currentTime;
  }
}

class SmartHomeUtil {
  static Timer? _debounceTimer;
  // 防抖相关变量
  static DateTime? _lastEditLocationClickTime;
  static const int _debounceDelayMs = 1000;

  static void debounce(Duration duration, void Function() callback) {
    if (_debounceTimer != null) {
      _debounceTimer?.cancel();
    }
    _debounceTimer = Timer(duration, () {
      callback.call();
      _debounceTimer?.cancel();
      _debounceTimer = null;
    });
  }

  static Future<Map<dynamic, dynamic>?> gotoPageWithDebounceCallback(
      String url) async {
    final DateTime now = DateTime.now();
    if (_lastEditLocationClickTime != null) {
      final int timeDiff = now.millisecondsSinceEpoch -
          _lastEditLocationClickTime!.millisecondsSinceEpoch;
      if (timeDiff < _debounceDelayMs) {
        DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'gotoPageWithDebounceCB: debounced, timeDiff: ${timeDiff}ms, request ignored',
        );
        // 在防抖时间内，直接返回null，忽略请求
        return null;
      }
    }

    // 更新最后点击时间
    _lastEditLocationClickTime = now;

    DevLogger.debug(
      tag: SmartHomeConstant.package,
      msg: 'gotoPageWithDebounceCB: executing page navigation to $url',
    );

    return Vdn.goToPage(url);
  }

  static String? determineRoomId(
    String selectedFloor,
    String selectedRoom,
    bool cardShowFloor,
    LinkedHashMap<String, DeviceFilterModel> deviceFilterMap,
    String familyId,
  ) {
    if (SmartHomeConstant.deviceFilterSelectAll == selectedFloor &&
        selectedRoom == SmartHomeConstant.deviceFilterSelectAll) {
      return familyId;
    }
    if (cardShowFloor) {
      return deviceFilterMap['$selectedFloor$selectedRoom']?.roomId;
    }
    return deviceFilterMap[selectedRoom]?.roomId;
  }
}

// vdn跳转
Future<void> goToPageWithDebounce(String url,
    {Map<String, dynamic>? params}) async {
  goToPage(url, params: params);
}

//VDN跳转不做网络判断
Future<void> vdnGoToPageWithoutNetCheck(String url,
    {Map<String, dynamic>? params}) async {
  Debounce.run(() async {
    try {
      DevLogger.debug(tag: 'vdnGoToPageWithDebounce', msg: <String, Object>{
        'fn': 'goToPage',
        'data': 'url :$url, params :$params'
      });
      if (url.isNotEmpty) {
        await Vdn.goToPage(url,
            params: <String, dynamic>{...?params, 'checkGuestMode': 1});
      }
    } catch (err) {
      DevLogger.error(
          tag: 'vdnGoToPageWithDebounce',
          msg: <String, Object>{'goToPage': err});
    }
  });
}

double getTextWith(String text, double fontSize) {
  final Text label = Text(
    text,
    style: TextStyle(fontSize: fontSize),
  );
  final TextPainter textPainter = TextPainter(textDirection: TextDirection.ltr);
  textPainter.text = TextSpan(text: label.data, style: label.style);
  textPainter.layout();
  return textPainter.width;
}

double handleTextSize(String text, int maxFontSize, double width) {
  const int minFontSize = 10;
  double fontSize = 10.w;
  for (int i = maxFontSize; i >= minFontSize; i--) {
    if (getTextWith(text, i.w) <= width) {
      fontSize = i.w;
      break;
    }
  }
  return fontSize;
}

class InterceptSystemBackUtil {
  // 注册监听
  static void interceptSystemBack({
    required String pageName,
    bool isNeedNativeBack = false,
    BuildContext? context,
    void Function()? callback,
  }) {
    MessageBackKeyManager.registerMessageChannel();
    MessageBackKeyManager.interceptSystemBack(pageName, false, () {
      if (context != null && context!.mounted) {
        Navigator.of(context).pop();
      }
      if (callback != null) {
        callback();
      }
    });
  }

  // 移除监听
  static void cancelInterceptSystemBack(String pageName) {
    MessageBackKeyManager.cancelInterceptSystemBack(pageName);
  }
}
