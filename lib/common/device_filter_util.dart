import 'package:family/floor_model.dart';
import 'package:family/room_model.dart';

Map<String, int> floorNameMapWithOrderId = <String, int>{
  '全部': 0,
  '1层': 1,
  '2层': 2,
  '3层': 3,
  '4层': 4,
  '5层': 5,
  '6层': 6,
  '7层': 7,
  '8层': 8,
  '负1层': 9,
  '负2层': 10,
  '负3层': 11
};

final Map<String, String> _floorMap = <String, String>{
  '负三层': '负3层',
  '负二层': '负2层',
  '负一层': '负1层',
  '一层': '1层',
  '二层': '2层',
  '三层': '3层',
  '四层': '4层',
  '五层': '5层',
  '六层': '6层',
  '七层': '7层',
  '八层': '8层',
};

String getFloorName(String originName) {
  return _floorMap[originName] ?? originName;
}

class FloorNameNumModel {
  String floorName = '';
  int floorNum = -1;
}

//楼层排序方法
List<String> getFloorOrderList(Set<String> floorNameList) {
  if (floorNameList.length == 1) {
    return floorNameList.toList();
  }
  final List<FloorNameNumModel> floorOrderList = <FloorNameNumModel>[];

  floorNameList.forEach((String floorName) {
    final int floorNum = floorNameMapWithOrderId[floorName] ?? -1;
    if (floorNum != -1) {
      final FloorNameNumModel floorNameNum = FloorNameNumModel();
      floorNameNum.floorName = floorName;
      floorNameNum.floorNum = floorNum;
      floorOrderList.add(floorNameNum);
    } else {
      final FloorNameNumModel floorNameNum = FloorNameNumModel();
      floorNameNum.floorName = floorName;
      floorNameNum.floorNum = 100;
      floorOrderList.add(floorNameNum);
    }
  });

  floorOrderList.sort((FloorNameNumModel left, FloorNameNumModel right) =>
      left.floorNum.compareTo(right.floorNum));

  final List<String> floorOrderListString = <String>[];
  floorOrderList.forEach((FloorNameNumModel element) {
    floorOrderListString.add(element.floorName);
  });
  return floorOrderListString;
}

List<String> getRoomOrderFromFamily(Set<String> roomNameListByDevice,
    List<FloorModel> floorListByFamily, String floorNameByDevice) {
  final List<String> roomList = roomNameListByDevice.toList();
  if (roomList.length == 1) {
    return roomList;
  }

  final Map<String, int> roomNameMapByFamily =
      buildRoomNameMap(floorListByFamily, floorNameByDevice);
  roomList.sort((String a, String b) {
    final int weightA = roomNameMapByFamily[a] ?? 100;
    final int weightB = roomNameMapByFamily[b] ?? 100;
    return weightA.compareTo(weightB);
  });
  return roomList;
}

Map<String, int> buildRoomNameMap(
    List<FloorModel> floorListByFamily, String floorName) {
  final Map<String, int> roomNameMapByFamily = <String, int>{};
  if (floorListByFamily.isEmpty) {
    return roomNameMapByFamily;
  }
  final FloorModel floorModel = floorListByFamily.firstWhere(
      (FloorModel floorInfo) => getFloorName(floorInfo.floorName) == floorName,
      orElse: () => FloorModel.fromJson(<String, dynamic>{}));

  for (final RoomModel roomModel in floorModel.rooms) {
    roomNameMapByFamily[roomModel.roomName] =
        int.tryParse(roomModel.sortCode) ?? 100;
  }
  return roomNameMapByFamily;
}

//该顺序后续要调整
Map<String, int> roomNameMapWithOrderId = <String, int>{
  '全屋': 0,
  '客厅': 1,
  '卧室': 2,
  '厨房': 3,
  '卫生间': 4,
  '阳台': 5,
  '餐厅': 6,
  '儿童房': 7,
  '老人房': 8,
  '休息室': 9,
  '书房': 10,
  '娱乐室': 11,
  '活动室': 12,
  '衣帽间': 13,
  '玄关': 14,
  '走廊': 15,
  '车库': 16,
  '花园': 17,
};

//房间Model
class RoomNameNumModel {
  String roomName = '';
  int roomNum = -1;
}

//房间排序方法
List<String> getRoomOrderList(Set<String> roomNameList) {
  if (roomNameList.length == 1) {
    return roomNameList.toList();
  }
  final List<RoomNameNumModel> roomOrderList = <RoomNameNumModel>[];
  roomNameList.forEach((String roomName) {
    final int roomNum = roomNameMapWithOrderId[roomName] ?? -1;
    if (roomNum != -1) {
      final RoomNameNumModel roomNameNum = RoomNameNumModel();
      roomNameNum.roomName = roomName;
      roomNameNum.roomNum = roomNum;
      roomOrderList.add(roomNameNum);
    } else {
      final RoomNameNumModel roomNameNum = RoomNameNumModel();
      roomNameNum.roomName = roomName;
      roomNameNum.roomNum = 100;
      roomOrderList.add(roomNameNum);
    }
  });

  roomOrderList.sort((RoomNameNumModel left, RoomNameNumModel right) =>
      left.roomNum.compareTo(right.roomNum));
  final List<String> roomNameListString = <String>[];

  roomOrderList.forEach((RoomNameNumModel element) {
    roomNameListString.add(element.roomName);
  });
  return roomNameListString;
}
