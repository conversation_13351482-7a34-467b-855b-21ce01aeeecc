import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';

import 'constant.dart';

class SwitchFamilyBubble extends StatelessWidget {
  final String text;
  final void Function() onSwitch;

  const SwitchFamilyBubble(
      {super.key, required this.text, required this.onSwitch});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        Container(
          constraints: const BoxConstraints(
            maxWidth: 256,
            minHeight: 44,
            maxHeight: 44,
          ),
          padding: const EdgeInsets.only(left: 12, top: 12, bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Flexible(
                child: SmartHomeText(
                  text: text,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.primary,
                ),
              ),
              GestureDetector(
                  onTap: onSwitch,
                  child: Container(
                    width: 52,
                    alignment: Alignment.center,
                    child: SmartHomeText(
                      text: '切换',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppSemanticColors.item.information.primary,
                    ),
                  ))
            ],
          ),
        ),
        // 箭头
        Positioned(
          left: 20,
          top: -9,
          child: Image.asset(
            'assets/images/arrow_up.webp',
            package: SmartHomeConstant.package,
            width: 18,
            height: 9,
          ),
        ),
      ],
    );
  }
}
