/*
 * 描述：设备卡片功能组件相关常量定义
 * 作者：songFJ
 * 创建时间：2025/5/9
 */

/// 圆角
class ComponentRadius {
  static const double popup = 32;
  static const double window = 24;
  static const double device = 22;
  static const double card = 16;
  static const double button = 24;
  static const double cardSecondary = 12;
  static const double componentLarge = 16;
  static const double componentSmall = 12;
}

/// 透明度
class ComponentOpacity {
  static const double enable = 1;
  static const double disable = 0.39;
}

class ComponentMargin {
  static const double popup = 12;
  static const double page = 16;
  static const double pageTop = 12;
  static const double pageBottom = 16;
}

class ComponentPadding {
  static const double textIndent = 4;
  static const double cardSmall = 12;
  static const double middle = 12;
  static const double cardMiddle = 16;
  static const double cardLarge = 20;
  static const double smallTop = 8;
  static const double textTop = 12;
  static const double largeTop = 16;
}

class ComponentGap {
  static const double item = 4;
  static const double widget = 8;
  static const double component = 12;
  static const double group = 20;
  static const double module = 32;
  static const double navIcon = 20;
}

class ComponentWidth {
  static const double button = 48;
}

class ComponentHeight {
  static const double button = 48;
}