class GioScene {
  // MB34506 智家-右上角加号-点击创建场景 {channel_name=智家首页:登录、未登录}
  static const String gioMenuSceneCreate = 'MB34506';
  // MBA38272 智家TAB-右上角加号-点击手动控制 {channel_name=智家首页:登录、未登录}
  static const String gioMenuSceneControl = 'MBA38272';
  // MBA38273  智家TAB-右上角加号-点击自动执行 {channel_name=智家首页:登录、未登录}
  static const String gioMenuSceneAuto = 'MBA38273';

  // MB36938 智家TAB-手动场景-点击  {scene_name:'', plan_type=模版场景/自定义场景, Room_Name=房间名称}
  static const String gioSceneClick = 'MB36938';
  // MBA38274 智家TAB-手动场景-长按卡片 {Room_Name=房间名称}
  static const String gioSceneLongPress = 'MBA38274';
  // MBA38275 智家TAB-手动场景-长按卡片-点击移出  {Room_Name=房间名称}
  static const String gioSceneEditRemove = 'MBA38275';
  // MB38276 智家TAB-手动场景-长按卡片-点击编辑  {Room_Name=房间名称}
  static const String gioSceneEdit = 'MB38276';
  // MBA38277 智家TAB-编辑-手动控制-点击  {Room_Name=房间名称}
  static const String gioSceneControlClick = 'MBA38277';
  // MBA38278 智家TAB-编辑-手动控制-进入 （曝光点）{Room_Name=房间名称}
  static const String gioSceneControlEnter = 'MBA38278';
}
