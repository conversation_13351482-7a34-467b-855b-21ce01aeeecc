import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

import 'constant.dart';

class LoadingHelper {

  static OverlayEntry? _entry;
  static Timer? _timer;
  static OverlayState? _overlay;

  static void showLoading(BuildContext? context) {
    if (context != null) {
      hideLoading();
      _entry = OverlayEntry(
        builder: (_) => loadingWidget(),
      );

      if (_entry != null) {
        _overlay = Overlay.of(context);
        if (_overlay != null) {
          _overlay!.insert(_entry!);
        } else {
          DevLogger.error(tag: 'smart_home', msg: <String, String>{
            'fn': 'showLoading',
            'err': 'overlay is null'
          });
        }
      }

      _timer = Timer(const Duration(seconds: 35), () {
        hideLoading();
      });
    }
  }

  static Widget loadingWidget() {
    return Center(
      child: Container(
        width: 140.w,
        height: 140.w,
        padding: EdgeInsets.symmetric(vertical: 8.w, horizontal: 12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.w)),
          color: const Color.fromRGBO(0, 0, 0, 0.5),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Center(
              child: Lottie.asset(
                'assets/theme/loading.json',
                height: 36.w,
                width: 36.w,
                package: SmartHomeConstant.package,
                key: const Key('common_loading'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void hideLoading() {
    if (_entry != null && _timer != null) {
      _entry?.remove();
      _timer?.cancel();
      _entry = null;
      _timer = null;
    }
  }
}
