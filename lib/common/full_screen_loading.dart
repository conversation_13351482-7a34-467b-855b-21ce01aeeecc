/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2025/4/28
 */

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:lottie/lottie.dart';

import 'constant.dart';

class FullScreenLoading {
  static OverlayEntry? _entry;
  static Timer? _timer;
  static OverlayState? _overlay;

  static void show(BuildContext? context) {
    if (context != null) {
      hide();
      _entry = OverlayEntry(
        builder: (_) => loadingWidget(),
      );

      if (_entry != null) {
        _overlay = Overlay.of(context);
        if (_overlay != null) {
          _overlay!.insert(_entry!);
        } else {
          DevLogger.error(tag: 'smart_home', msg: <String, String>{
            'fn': 'showLoading',
            'err': 'overlay is null'
          });
        }
      }

      _timer = Timer(const Duration(seconds: 35), () {
        hide();
      });
    }
  }

  static Widget loadingWidget() {
    return Center(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: const Color.fromRGBO(0, 0, 0, 0.5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Center(
              child: Lottie.asset(
                'assets/theme/loading.json',
                height: 48,
                width: 48,
                package: SmartHomeConstant.package,
                key: const Key('common_loading'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void hide() {
    if (_entry != null && _timer != null) {
      _entry?.remove();
      _timer?.cancel();
      _entry = null;
      _timer = null;
    }
  }
}
