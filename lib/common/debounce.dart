/*
 * 描述：防抖
 * 作者：songFJ
 * 创建时间：2024/9/4
 */

import 'dart:async';

/// 滞后 milliseconds 之内，只最后一次生效
class Debouncer {
  int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  void run(void Function() action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  void dispose() {
    _timer?.cancel();
  }
}

/// milliseconds 之内，只第一次点击有效
class Throttler {
  int milliseconds;
  Timer? _timer;
  bool _isExecuted = false;

  Throttler({required this.milliseconds});

  void run(void Function() action, {void Function()? multiClickCallback}) {
    if (_isExecuted) {
      multiClickCallback?.call();
      return;
    }
    _timer = Timer(Duration(milliseconds: milliseconds), () {
      _timer?.cancel();
      _isExecuted = false;
    });
    _isExecuted = true;
    action();
  }

  void dispose() {
    _timer?.cancel();
  }
}
