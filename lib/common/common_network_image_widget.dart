import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';

class CommonNetworkRefreshImg extends StatelessWidget {
  const CommonNetworkRefreshImg(
      {super.key,
      required this.imageUrl,
      this.errorWidget,
      this.placeHolder,
      this.width,
      this.height,
      this.fit,
      this.alignment});

  final double? width;
  final double? height;
  final String imageUrl;
  final Widget? errorWidget;
  final Widget? placeHolder;
  final BoxFit? fit;
  final Alignment? alignment;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, CommonNetworkImgViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) =>
            CommonNetworkImgViewModel.fromStore(store),
        builder: (BuildContext context, CommonNetworkImgViewModel vm) {
          return CommonNetWorkImage(
            url: imageUrl,
            width: width,
            height: height,
            fit: fit,
            alignment: alignment,
            needReload: vm.imageRefreshCount,
            errorWidget: errorWidget,
            placeHolder: placeHolder,
          );
        });
  }
}

class CommonNetworkImgViewModel {
  CommonNetworkImgViewModel({required this.imageRefreshCount});

  final int imageRefreshCount;

  static CommonNetworkImgViewModel fromStore(Store<SmartHomeState> store) {
    return CommonNetworkImgViewModel(
      imageRefreshCount: store.state.imageRefreshCount,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonNetworkImgViewModel &&
          runtimeType == other.runtimeType &&
          imageRefreshCount == other.imageRefreshCount;

  @override
  int get hashCode => imageRefreshCount.hashCode;

  @override
  String toString() {
    return 'CommonNetworkImgViewModel{imageRefreshCount: $imageRefreshCount}';
  }
}
