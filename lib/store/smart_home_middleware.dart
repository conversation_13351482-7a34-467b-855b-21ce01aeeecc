import 'package:redux/redux.dart';
import 'package:smart_home/edit/store/edit_middleware.dart';

import '../device/aggregation/agg_store/aggregation_middleware.dart';
import '../device/store/device_middleware.dart';
import '../scene/store/scene_middleware.dart';
import '../smart_home/store/app_init_middleware.dart';
import '../whole_house/store/whole_house_middleware.dart';
import 'smart_home_state.dart';

final List<Middleware<SmartHomeState>> smartHomeMiddleware =
    <Middleware<SmartHomeState>>[
  AppInitMiddleware().call,
  ...wholeHouseMiddleware,
  SceneMiddleware().call,
  DeviceMiddleware().call,
  EditMiddleware().call,
  AggregationMiddleware().call,
];
