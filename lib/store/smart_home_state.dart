import 'package:smart_home/device/device_guide/store/guide_state.dart';

import '../device/aggregation/agg_store/aggregation_state.dart';
import '../device/aggregation/aggregation_setting/aggregation_setting_sheet.dart';
import '../device/store/device_state.dart';
import '../edit/store/edit_state.dart';
import '../features/operating_activity/store/activity_state.dart';
import '../navigator/family/store/family_state.dart';
import '../pack_gift/store/giftpack_state.dart';
import '../scene/store/scene_state.dart';
import '../whole_house/store/whole_house_state.dart';

class SmartHomeState {
  bool isEditState = false;
  // 是否可以滑动切换tab页
  bool isScrollableScrollPhysics = true;

  bool isLogin = false;

  FamilyState familyState = FamilyState();

  WholeHouseState wholeHouseState = WholeHouseState();

  SceneState sceneState = SceneState();

  DeviceState deviceState = DeviceState();

  EditState editState = EditState();

  GiftPackState giftPackState = GiftPackState();
  GuideState guideState = GuideState();

  //下拉刷新
  int imageRefreshCount = 0;

  AggregationState aggregationState = AggregationState();
  AggregationSettingState aggregationSettingState = AggregationSettingState();
  OperatingActivityState operatingActivityState = OperatingActivityState();

  SmartHomeState({
    bool? isEditState,
    bool? isScrollableScrollPhysics,
    bool? isLogin,
    FamilyState? familyState,
    WholeHouseState? wholeHouseState,
    SceneState? sceneState,
    DeviceState? deviceState,
    EditState? editState,
    GiftPackState? giftPackState,
    int? imageRefreshCount,
    AggregationSettingState? aggregationSettingState,
    AggregationState? aggregationState,
    GuideState? guideState,
    OperatingActivityState? operatingActivityState,
  }) {
    this.isEditState = isEditState ?? this.isEditState;
    this.isScrollableScrollPhysics =
        isScrollableScrollPhysics ?? this.isScrollableScrollPhysics;
    this.isLogin = isLogin ?? this.isLogin;
    this.familyState = familyState ?? this.familyState;
    this.wholeHouseState = wholeHouseState ?? this.wholeHouseState;
    this.sceneState = sceneState ?? this.sceneState;
    this.deviceState = deviceState ?? this.deviceState;
    this.editState = editState ?? this.editState;
    this.giftPackState = giftPackState ?? this.giftPackState;
    this.imageRefreshCount = imageRefreshCount ?? this.imageRefreshCount;
    this.aggregationSettingState =
        aggregationSettingState ?? this.aggregationSettingState;
    this.aggregationState = aggregationState ?? this.aggregationState;
    this.guideState = guideState ?? this.guideState;
    this.operatingActivityState =
        operatingActivityState ?? this.operatingActivityState;
  }

  /// 复制并修改部分属性
  SmartHomeState copyWith({
    bool? isEditState,
    bool? isScrollableScrollPhysics,
    bool? isLogin,
    FamilyState? familyState,
    WholeHouseState? wholeHouseState,
    SceneState? sceneState,
    DeviceState? deviceState,
    EditState? editState,
    GiftPackState? giftPackState,
    int? imageRefreshCount,
    AggregationSettingState? aggregationSettingState,
    AggregationState? aggregationState,
    GuideState? guideState,
    OperatingActivityState? operatingActivityState,
  }) {
    return SmartHomeState(
        isEditState: isEditState ?? this.isEditState,
        isScrollableScrollPhysics:
            isScrollableScrollPhysics ?? this.isScrollableScrollPhysics,
        isLogin: isLogin ?? this.isLogin,
        familyState: familyState ?? this.familyState,
        wholeHouseState: wholeHouseState ?? this.wholeHouseState,
        sceneState: sceneState ?? this.sceneState,
        deviceState: deviceState ?? this.deviceState,
        editState: editState ?? this.editState,
        giftPackState: giftPackState ?? this.giftPackState,
        imageRefreshCount: imageRefreshCount ?? this.imageRefreshCount,
        aggregationSettingState:
            aggregationSettingState ?? this.aggregationSettingState,
        aggregationState: aggregationState ?? this.aggregationState,
        guideState: guideState ?? this.guideState,
        operatingActivityState:
            operatingActivityState ?? this.operatingActivityState);
  }
}
