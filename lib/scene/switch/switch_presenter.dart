import 'package:device_utils/log/log.dart';
import 'package:smart_home/scene/switch/switch_status_query_model.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:storage/storage.dart';

import '../../common/constant.dart';
import '../../service/http_service.dart';

class SwitchPresenter {
  static const int switchType = 16;
  static const String switchOpen = '0';
  static const String switchClose = '1';

  static Future<void> querySwitchStatus(String familyId) async {
    final SwitchStatusRequestModel reqModel =
        SwitchStatusRequestModel(switchType: switchType, switchKey: familyId);
    try {
      final SceneSwitchStatusResponseModel? response =
          await HttpService.switchQuery(reqModel);
      if (response?.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        final String switchStatus = response?.data.status ?? switchClose;
        saveSwitchStatusToStorage(familyId, switchStatus);
      }
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'switchQuery err: $err');
    }
  }

  static Future<void> saveSwitchStatusToStorage(
      String familyId, String switchStatus) async {
    final bool switchValue = switchStatus == switchOpen;
    final String switchKey = _buildSwitchStorageKey(familyId);

    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'saveSwitchStatusToStorage begin, key:$switchKey');

    try {
      final bool response =
          await Storage.putBooleanValue(switchKey, switchValue);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'saveSwitchStatusToStorage end, key:$switchKey, response: $response');
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'saveSwitchStatusToStorage err, key:$switchKey, err: $err');
    }
  }

  static Future<bool> querySwitchStatusFromStorage() async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final String switchKey = _buildSwitchStorageKey(familyId);
    try {
      final bool status =
          await Storage.getBooleanValue(switchKey, defaultValue: true);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'querySwitchStatusFromStorage end, key:$switchKey, status: $status');
      return status;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'querySwitchStatusFromStorage err, key:$switchKey, err: $err');
      return true;
    }
  }

  static String _buildSwitchStorageKey(String familyId) {
    return 'switch_scene_lab_$familyId';
  }
}
