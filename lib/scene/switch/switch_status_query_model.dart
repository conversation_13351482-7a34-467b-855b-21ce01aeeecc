import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class SwitchStatusRequestModel {
  SwitchStatusRequestModel({
    required this.switchType,
    required this.switchKey,
  });

  int switchType;
  String switchKey;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['switchType'] = switchType;
    data['switchKey'] = switchKey;
    return data;
  }

  @override
  String toString() {
    return 'SwitchStatusRequestModel{switchType: $switchType, switchKey: $switchKey}';
  }
}

class SceneSwitchStatusResponseModel extends UhomeResponseModel {
  SceneSwitchStatusResponseModel.fromJson(super.data) : super.fromJson() {
    data = SwitchStatusData.fromJson(super.retData);
  }

  SwitchStatusData data = SwitchStatusData.fromJson(<dynamic, dynamic>{});

  @override
  String toString() {
    return 'SceneSwitchStatusResponseModel{data: $data}';
  }
}

class SwitchStatusData {
  SwitchStatusData.fromJson(Map<dynamic, dynamic> json) {
    status = json.stringValueForKey('status', '1');
  }

  // status的值: "0"-打开状态, "1"-关闭状态
  String status = '';

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    return data;
  }

  @override
  String toString() {
    return 'SwitchStatusData{status: $status}';
  }
}
