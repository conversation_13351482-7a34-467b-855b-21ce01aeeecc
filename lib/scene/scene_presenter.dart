/*
 * 描述：场景相关逻辑处理
 * 作者：fancunshuo
 * 建立时间: 2024/7/22
 */

import 'dart:convert';
import 'package:device_utils/log/log.dart';
import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/scene/store/scene_action.dart';
import 'package:smart_home/scene/store/scene_reducer_util.dart';
import 'package:storage/storage.dart';
import 'package:user/user.dart';

import '../common/constant.dart';
import '../service/http_service.dart';
import '../store/smart_home_store.dart';

class ScenePresenter {
  Future<void> fetchSceneData(String familyId) async {
    final ManualSceneRequestModel req = ManualSceneRequestModel(
      familyId: familyId,
      appIconVersion: sceneAppIconVersion,
    );
    HttpService.querySingleRoomSceneList(req)
        .then((ManualSceneResponseModel? responseModel) {
      if (responseModel is ManualSceneResponseModel) {
        smartHomeStore.dispatch(UpdateSceneData(responseModel.scene, null));
        putSceneListListToStorage(familyId, responseModel.scene);
      } else {
        DevLogger.error(
            tag: SmartHomeConstant.package,
            msg: 'fetchSceneData end, err:$responseModel');
      }
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'fetchSceneData err:$err');
    });

    HttpService.queryAllRoomSceneList(req)
        .then((RoomScenesResponseModel? responseModel) {
      if (responseModel is RoomScenesResponseModel) {
        smartHomeStore.dispatch(UpdateSceneData(null, responseModel.data));
      } else {
        DevLogger.error(
            tag: SmartHomeConstant.package,
            msg: 'fetchSceneData-queryAllRoomSceneList end,res:$responseModel');
      }
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'fetchSceneData-queryAllRoomSceneList err:$err');
    });
  }

  void clearSceneData() {
    DevLogger.info(tag: SmartHomeConstant.package, msg: 'clearSceneData');
    smartHomeStore.dispatch(UpdateSceneVisibilityAction(false));
  }

  static SceneViewModel? getSceneListListFromStorage(String sceneListString) {
    DevLogger.info(
        tag: SmartHomeConstant.package,
        msg: 'getSceneListListFromStorage: $sceneListString');
    if (sceneListString.isEmpty) {
      return null;
    }
    try {
      final dynamic jsonData = jsonDecode(sceneListString);
      if (jsonData is Map) {
        return SceneViewModel.fromJson(jsonData);
      } else {
        return null;
      }
    } catch (e) {
      DevLogger.error(
          tag: 'getSceneListListFromStorage', msg: 'jsonDecode error:$e');
      return null;
    }
  }

  static Future<void> putSceneListListToStorage(
      String familyId, ManualSceneServerModel serverModel) async {
    final String userId = User.getOauthDataSync()?.uhome_user_id ?? '';
    final String sceneListKey = 'scene_list_${familyId}_$userId';

    final SceneViewModel viewModel =
        SceneViewModel.fromJson(<dynamic, dynamic>{});

    viewModel.sceneList = SceneReducerUtil.getSceneList(serverModel);
    try {
      Storage.putStringValue(sceneListKey, jsonEncode(viewModel.toJson()));
    } catch (e) {
      DevLogger.error(
          tag: 'putSceneListListFromStorage', msg: 'jsonEncode error:$e');
    }
  }
}
