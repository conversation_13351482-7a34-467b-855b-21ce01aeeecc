/*
 * 描述：智家tab场景server model
 * 作者：fancunshuo
 * 建立时间: 2024/7/22
 */
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class IftttItem {
  String id = '';
  String sceneName = '';
  String sourceId = '';
  String serverIconUrl = '';
  int status = 1;
  String showLabel = '0';
  String triggerType = '';

  IftttItem.fromJson(Map<dynamic, dynamic> json) {
    id = json.stringValueForKey('id', '');
    sceneName = json.stringValueForKey('sceneName', '');
    sourceId = json.stringValueForKey('sourceId', '');
    serverIconUrl = json.stringValueForKey('serverIconUrl', '');
    status = json.intValueForKey('sceneEnableStatus', 1);
    showLabel = json.stringValueForKey('showLabel', '0');
    triggerType = json.stringValueForKey('triggerType', '');
  }
}

class TemplateItem {
  String templateId = '';
  String sceneName = '';
  String serverIconUrl = '';

  TemplateItem.fromJson(Map<dynamic, dynamic> json) {
    templateId = json.stringValueForKey('templateId', '');
    sceneName = json.stringValueForKey('sceneName', '');
    serverIconUrl = json.stringValueForKey('serverIconUrl', '');
  }
}

class SceneServerModel {
  //返回数据的方式
  //1用户有实例化场景，查询用户名下的ifttt场景 ，返回iftttList
  //2用户没有实例化过场景，有推荐场景, 返回templateList
  int type = 2;
  List<IftttItem> iftttList = <IftttItem>[];
  List<TemplateItem> templateList = <TemplateItem>[];
  int sceneCount = 0;

  SceneServerModel.fromJson(Map<dynamic, dynamic> json) {
    type = json.intValueForKey('type', 2);
    if (json['iftttList'] is List<dynamic>) {
      iftttList = <IftttItem>[];
      for (final dynamic v in json['iftttList'] as List<dynamic>) {
        if (v is Map<dynamic, dynamic>) {
          iftttList.add(IftttItem.fromJson(v));
        }
      }
    }
    if (json['templateList'] is List<dynamic>) {
      templateList = <TemplateItem>[];
      for (final dynamic v in json['templateList'] as List<dynamic>) {
        if (v is Map<dynamic, dynamic>) {
          templateList.add(TemplateItem.fromJson(v));
        }
      }
    }
    sceneCount = json.intValueForKey('sceneCount', 0);
  }
}

class SceneResponseModel extends UhomeResponseModel {
  SceneResponseModel.fromJson(super.data) : super.fromJson() {
    scene = SceneServerModel.fromJson(super.retData);
  }

  SceneServerModel scene = SceneServerModel.fromJson(<dynamic, dynamic>{});
}
