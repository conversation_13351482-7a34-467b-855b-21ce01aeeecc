import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio_scene.dart';
import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';
import 'package:smart_home/scene/scene_model/scene_setting_server_model.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import 'package:smart_home/widget_common/over_scroll_behavior.dart';
import 'package:upservice/model/uhome_response_model.dart';
import 'package:vdn/vdn.dart';

enum SceneManageType {
  show,
  unShow,
}

class SceneManagePage extends StatefulWidget {
  const SceneManagePage(
      {super.key, required this.familyId, required this.roomId});

  final String familyId;
  final String roomId;

  @override
  SceneManagePageState createState() => SceneManagePageState();
}

class SceneManagePageState extends State<SceneManagePage> {
  List<SceneItemViewModel> showSceneList = <SceneItemViewModel>[];
  List<SceneItemViewModel> unShowSceneList = <SceneItemViewModel>[];

  Set<String> originShowSceneIdSet = <String>{};
  Set<String> originUnShowSceneIdSet = <String>{};

  @override
  void initState() {
    super.initState();
    ToastHelper.init(context);
    gioTrack(GioScene.gioSceneControlEnter);
    _querySceneList();
  }

  void _querySceneList() {
    final ManualSceneRequestModel req = ManualSceneRequestModel(
      familyId: widget.familyId,
      roomId: widget.familyId == widget.roomId ? null : widget.roomId,
      appIconVersion: sceneAppIconVersion,
    );
    DevLogger.debug(
        tag: 'SceneManagePage',
        msg: 'scene___ manage querySceneList req: $req');
    HttpService.querySingleRoomSceneList(req)
        .then((ManualSceneResponseModel? value) {
      DevLogger.debug(
          tag: 'SceneManagePage',
          msg: 'scene___ manage querySceneList response: $value');
      if (value is ManualSceneResponseModel) {
        value.scene.displaySceneList.forEach((ManualSceneItemModel item) {
          originShowSceneIdSet.add(item.id);
          showSceneList.add(SceneItemViewModel(
            id: item.id,
            name: item.sceneName,
            icon: item.serverIcon,
          ));
        });

        value.scene.noDisplaySceneList.forEach((ManualSceneItemModel item) {
          originUnShowSceneIdSet.add(item.id);
          unShowSceneList.add(SceneItemViewModel(
            id: item.id,
            name: item.sceneName,
            icon: item.serverIcon,
          ));
        });

        if (mounted) {
          setState(() {});
        }
      }
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: 'SceneManagePage',
          msg: 'scene___ manage querySceneList err: $err');
    });
  }

  void _removeFromShow(int index) {
    if (mounted && index >= 0 && index < showSceneList.length) {
      setState(() {
        unShowSceneList.add(showSceneList.removeAt(index));
      });
    }
  }

  void _addToShow(int index) {
    if (mounted && index >= 0 && index < unShowSceneList.length) {
      setState(() {
        showSceneList.add(unShowSceneList.removeAt(index));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppSemanticColors.background.secondary,
      appBar: AppBar(
        leading: GestureDetector(
          onTap: () => Vdn.close(),
          child: _buildSceneCtrlImage('assets/icons/go_back.webp'),
        ),
        centerTitle: true,
        title: _buildTitle(),
        backgroundColor: Colors.transparent,
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        toolbarHeight: 44,
        elevation: 0,
        actions: <Widget>[
          _buildFinishButton(),
        ],
      ),
      body: ScrollConfiguration(
        behavior: NoneOverScrollBehavior(),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // 已展示部分
              _buildSceneListTitle('已展示'),
              if (showSceneList.isEmpty)
                _buildScenePlaceHolderWith('暂无手动控制')
              else
                _buildSceneListWith(SceneManageType.show, showSceneList,
                    (int index) => _removeFromShow(index)),
              const SizedBox(height: 20),

              // 未展示部分
              _buildSceneListTitle('更多'),
              if (unShowSceneList.isEmpty)
                _buildScenePlaceHolderWith('暂无可添加的手动控制')
              else
                _buildSceneListWith(SceneManageType.unShow, unShowSceneList,
                    (int index) => _addToShow(index)),

              _buildBottomDivider(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      '手动控制',
      style: TextStyle(
        fontSize: 17,
        fontFamilyFallback: fontFamilyFallback(),
        fontWeight: FontWeight.w500,
        color: const Color.fromRGBO(38, 38, 38, 1),
      ),
    );
  }

  Widget _buildFinishButton() {
    if (showSceneList.isEmpty && unShowSceneList.isEmpty) {
      return const SizedBox();
    }
    return GestureDetector(
      onTap: _saveSceneList,
      child: Container(
        margin: const EdgeInsets.only(right: 20),
        child: Center(
          child: Text('完成',
              style: TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.w400,
                color: AppSemanticColors.item.information.primary,
              )),
        ),
      ),
    );
  }

  bool isSceneListNoChange() {
    // 获取当前两个列表集合中的场景ID
    final Set<String> currentShowIdSet =
        showSceneList.map((SceneItemViewModel scene) => scene.id).toSet();
    final Set<String> currentUnshowIdSet =
        unShowSceneList.map((SceneItemViewModel scene) => scene.id).toSet();

    // 比较当前集合与初始集合是否相同
    return _setsAreEqual(currentShowIdSet, originShowSceneIdSet) &&
        _setsAreEqual(currentUnshowIdSet, originUnShowSceneIdSet);
  }

  bool _setsAreEqual<T>(Set<T> set1, Set<T> set2) {
    return set1.length == set2.length &&
        set1.containsAll(set2) &&
        set2.containsAll(set1);
  }

  void _saveSceneList() {
    if (isSceneListNoChange()) {
      DevLogger.debug(
          tag: 'SceneManagePage',
          msg:
              'scene___ manage _saveSceneList isSceneListNoChange=true, no need to save, closing VDN container.');
      Vdn.close();
      return;
    }

    final Set<String> currentShowIdSet =
        showSceneList.map((SceneItemViewModel scene) => scene.id).toSet();

    // 计算从unShowSceneList移动到showSceneList的场景
    final Set<String> movedToShow =
        currentShowIdSet.difference(originShowSceneIdSet);

    // 计算从showSceneList移动到unShowSceneList的场景
    final Set<String> movedToUnshow =
        originShowSceneIdSet.difference(currentShowIdSet);

    final ManualSceneSettingRequestModel req = ManualSceneSettingRequestModel(
        familyId: widget.familyId,
        roomId: widget.familyId == widget.roomId ? null : widget.roomId,
        displaySceneIds: movedToShow.toList(),
        noDisplaySceneIds: movedToUnshow.toList());
    HttpService.roomSceneBatchSetting(req).then((UhomeResponseModel? value) {
      if (value is UhomeResponseModel &&
          value.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        DevLogger.debug(
            tag: 'SceneManagePage',
            msg: 'scene___ manage _saveSceneList success');
      } else {
        ToastHelper.showToast('保存失败，请稍后重试');
      }
    }).catchError((dynamic err) {
      ToastHelper.showToast('保存失败，请稍后重试');
    }).whenComplete(() => Vdn.close());
  }

  Widget _buildSceneListWith(SceneManageType type,
      List<SceneItemViewModel> sceneList, void Function(int index)? onPressed) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: sceneList.length,
      itemBuilder: (BuildContext context, int index) {
        final SceneItemViewModel scene = sceneList[index];
        return Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Column(children: <Widget>[
            _buildSceneItemWidget(
                scene, type, onPressed, index, sceneList.length),
            if (index != sceneList.length - 1) _buildItemDivider(),
          ]),
        );
      },
    );
  }

  Widget _buildItemDivider() {
    return const ColoredBox(
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.only(left: 52, right: 16),
        child: Divider(
          height: 1,
          color: Color.fromRGBO(241, 241, 241, 1),
        ),
      ),
    );
  }

  BorderRadius _buildSceneItemBorderRadius(int index, int sceneLength) {
    if (sceneLength == 1) {
      return BorderRadius.circular(16);
    } else if (index == 0) {
      return const BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      );
    } else if (index == sceneLength - 1) {
      return const BorderRadius.only(
        bottomLeft: Radius.circular(16),
        bottomRight: Radius.circular(16),
      );
    }
    return BorderRadius.zero;
  }

  Widget _buildSceneItemWidget(SceneItemViewModel scene, SceneManageType type,
      void Function(int index)? onPressed, int index, int sceneLength) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: _buildSceneItemBorderRadius(index, sceneLength),
      ),
      child: Row(
        children: <Widget>[
          GestureDetector(
            onTap: () => onPressed?.call(index),
            child: _buildSceneCtrlImage(type == SceneManageType.show
                ? 'assets/icons/scene_remove.webp'
                : 'assets/icons/scene_add.webp'),
          ),
          const SizedBox(width: 12),
          _buildSceneWebImage(scene.icon),
          const SizedBox(width: 12),
          _buildSceneName(scene.name),
        ],
      ),
    );
  }

  Widget _buildSceneName(String sceneName) {
    return Expanded(
      child: SizedBox(
        height: 22,
        child: Text(
          sceneName,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 16,
            fontFamilyFallback: fontFamilyFallback(),
            fontWeight: FontWeight.w500,
            height: 22 / 16,
            color: const Color.fromRGBO(38, 38, 38, 1),
          ),
        ),
      ),
    );
  }

  Widget _buildSceneCtrlImage(String imgUrl) {
    return Center(
      child: Image.asset(
        imgUrl,
        width: 24,
        height: 24,
        package: SmartHomeConstant.package,
      ),
    );
  }

  Widget _buildSceneWebImage(String imgUrl) {
    return SizedBox(
      width: 24,
      height: 24,
      child: AspectRatio(
          aspectRatio: 1,
          child: CommonNetWorkImage(
            url: imgUrl,
            alignment: Alignment.center,
            errorWidget: Image.asset(
              'assets/icons/default_scene_img.webp',
              package: SmartHomeConstant.package,
              height: double.infinity,
            ),
          )),
    );
  }

  Widget _buildSceneListTitle(String title) {
    return Container(
      height: 41,
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontFamilyFallback: fontFamilyFallback(),
            color: AppSemanticColors.item.secondary
          ),
        ),
      ),
    );
  }

  Widget _buildScenePlaceHolderWith(String text) {
    return Container(
      height: 100,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
          child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Color.fromRGBO(141, 141, 141, 1),
        ),
      )),
    );
  }

  Widget _buildBottomDivider() {
    return const SizedBox(height: 50);
  }
}

class SceneItemViewModel {
  final String id;
  final String name;
  final String icon;

  SceneItemViewModel({
    required this.id,
    required this.name,
    required this.icon,
  });
}
