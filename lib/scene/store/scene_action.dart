import 'package:flutter/material.dart';
import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';

import '../scene_viewmodel.dart';

class SceneBaseAction {}

class UpdateSceneVisibilityAction extends SceneBaseAction {
  UpdateSceneVisibilityAction(this.visibility);

  bool visibility = false;
}

class UpdateExpansionStatusAction extends SceneBaseAction {
  UpdateExpansionStatusAction(this.expansionStatus);

  ExpansionStatus expansionStatus;
}

class UpdateCardSortAction extends SceneBaseAction {}

class UpdateSceneData extends SceneBaseAction {
  UpdateSceneData(this.wholeHouseSceneListModel, this.allRoomSceneListModel);

  ManualSceneServerModel? wholeHouseSceneListModel;
  RoomSceneListData? allRoomSceneListModel;
}

class SceneExecuteAction extends SceneBaseAction {
  SceneExecuteAction(this.item, this.context);

  SceneItemViewModel item;
  BuildContext context;
}

class SceneDragFinishedAction extends SceneBaseAction {
  SceneDragFinishedAction(this.sceneVmList);

  List<SceneItemViewModel> sceneVmList;
}

class RollbackSceneListAction extends SceneBaseAction {
  RollbackSceneListAction();
}
