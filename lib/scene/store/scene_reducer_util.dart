/*
 * 描述：场景reducer工具
 * 作者：fancunshuo
 * 建立时间: 2024/7/23
 */

import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';

import '../scene_viewmodel.dart';

class SceneReducerUtil {
  static const int valueIsInvalid = 1;

  static const int sceneExpandCloseMaxCount = 4;

  static List<SceneItemViewModel> getSceneList(
      ManualSceneServerModel serverModel) {
    return serverModel.displaySceneList
        .map((ManualSceneItemModel item) => SceneItemViewModel(
              isInvalid: item.status == valueIsInvalid,
              sceneId: item.id,
              type: item.type,
              sceneIcon: item.serverIcon,
              sceneName: item.sceneName,
              templateId: item.templateId,
              appSort: item.appSort,
              dataVersion: item.dataVersion,
            ))
        .toList();
  }

  static List<SceneItemViewModel> getSceneListFromAllRoomScenes(
      RoomScenesDataModel serverModel) {
    return serverModel.scenes
        .map((ManualSceneItemModel item) => SceneItemViewModel(
              isInvalid: item.status == valueIsInvalid,
              sceneId: item.id,
              type: item.type,
              sceneIcon: item.serverIcon,
              sceneName: item.sceneName,
              templateId: item.templateId,
              appSort: item.appSort,
              dataVersion: item.dataVersion,
            ))
        .toList();
  }
}
