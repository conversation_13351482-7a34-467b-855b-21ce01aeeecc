import 'package:redux/redux.dart';
import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/scene/store/scene_reducer_util.dart';

import '../../store/smart_home_state.dart';
import 'scene_action.dart';

final Reducer<SmartHomeState> sceneCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateSceneVisibilityAction>(
          _updateSceneVisibility)
      .call,
  TypedReducer<SmartHomeState, UpdateSceneData>(_updateSceneData).call,
  TypedReducer<SmartHomeState, UpdateExpansionStatusAction>(
          _updateExpansionStatus)
      .call,
  TypedReducer<SmartHomeState, SceneDragFinishedAction>(
          _updateSceneListAfterDragFinish)
      .call,
  TypedReducer<SmartHomeState, RollbackSceneListAction>(_revertSceneList).call,
]);

SmartHomeState _updateSceneVisibility(
    SmartHomeState state, UpdateSceneVisibilityAction action) {
  state.sceneState.visibility = action.visibility;
  if (!action.visibility) {
    state.sceneState.visibility = false;
    state.sceneState.sceneMap = <String, List<SceneItemViewModel>>{};
    state.sceneState.expansionStatusMap = <String, ExpansionStatus>{};
  }
  return state;
}


SmartHomeState _updateSceneData(SmartHomeState state, UpdateSceneData action) {
  final String selectedRoomId = state.deviceState.selectedRoomId;
  final String familyId = state.familyState.familyId;

  final List<SceneItemViewModel> preSceneList =
      state.sceneState.sceneMap[selectedRoomId] ?? <SceneItemViewModel>[];

  // init cur family scene list
  final ManualSceneServerModel? serverModel = action.wholeHouseSceneListModel;
  if (serverModel != null) {
    state.sceneState.sceneMap[familyId] =
        SceneReducerUtil.getSceneList(serverModel);
  }

  // init all rooms scene list
  if (action.allRoomSceneListModel != null) {
    final List<SceneItemViewModel> wholeHouseSceneList =
        state.sceneState.sceneMap[familyId] ?? <SceneItemViewModel>[];
    state.sceneState.sceneMap.clear();
    state.sceneState.sceneMap[familyId] = wholeHouseSceneList;

    action.allRoomSceneListModel?.roomSceneList
        .forEach((RoomScenesDataModel e) {
      state.sceneState.sceneMap[e.roomId] =
          SceneReducerUtil.getSceneListFromAllRoomScenes(e);
    });
  }

  final List<SceneItemViewModel> curSceneList =
      state.sceneState.sceneMap[selectedRoomId] ?? <SceneItemViewModel>[];

  // retain pre scene selected state and edit state
  for (final SceneItemViewModel item in curSceneList) {
    final SceneItemViewModel model = preSceneList.firstWhere(
        (SceneItemViewModel element) => element.sceneId == item.sceneId,
        orElse: () => SceneItemViewModel.fromJson(<dynamic, dynamic>{}));
    item.isSelected = model.isSelected;
    item.isEditState = state.isEditState;
  }
  state.sceneState.visibility = curSceneList.isNotEmpty;
  return state;
}

SmartHomeState _updateExpansionStatus(
    SmartHomeState state, UpdateExpansionStatusAction action) {
  state.sceneState.expansionStatusMap[state.deviceState.selectedRoomId] =
      action.expansionStatus;
  return state;
}

SmartHomeState _updateSceneListAfterDragFinish(
    SmartHomeState state, SceneDragFinishedAction action) {
  final List<SceneItemViewModel> sceneList =
      state.sceneState.sceneMap[state.deviceState.selectedRoomId] ??
          <SceneItemViewModel>[];

  if (sceneList.length == action.sceneVmList.length) {
    state.sceneState.sceneMap[state.deviceState.selectedRoomId] =
        <SceneItemViewModel>[...action.sceneVmList];
  } else {
    final List<SceneItemViewModel> list = <SceneItemViewModel>[];
    list.addAll(sceneList);
    for (int i = 0; i < action.sceneVmList.length; i++) {
      list[i] = action.sceneVmList[i];
    }
    state.sceneState.sceneMap[state.deviceState.selectedRoomId] = list;
  }

  return state;
}

SmartHomeState _revertSceneList(
    SmartHomeState state, RollbackSceneListAction action) {
  state.sceneState.sceneMap[state.deviceState.selectedRoomId] =
      state.editState.cacheSceneCardList.map((SceneItemViewModel scene) {
    return SceneItemViewModel.fromObject(scene);
  }).toList();
  return state;
}
