import 'package:reselect/reselect.dart';
import 'package:smart_home/scene/scene_expand/scene_expansion_button.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/scene/store/scene_reducer_util.dart';
import 'package:smart_home/store/smart_home_state.dart';

/// 场景相关选择器
class SceneSelectors {
  /// 获取场景列表ViewModel
  static Selector<SmartHomeState, SceneViewModel> selectSceneViewModel(
      String roomId) {
    return createSelector5(
      // 输入选择器 - 提取关键依赖数据
      (SmartHomeState state) =>
          state.sceneState.sceneMap[roomId] ?? <SceneItemViewModel>[],
      (SmartHomeState state) =>
          state.sceneState.expansionStatusMap[roomId] ?? ExpansionStatus.close,
      (SmartHomeState state) => state.isEditState,
      (SmartHomeState state) => state.familyState.familyId,
      (SmartHomeState _) => roomId,
      // 结果选择器 - 转换为视图模型
      _createSceneViewModel,
    );
  }

  static bool _isSceneListChanged(
      List<SceneItemViewModel> current, List<String> previousSceneIdList) {
    if (current.length != previousSceneIdList.length) {
      return true;
    }
    for (int i = 0; i < current.length; i++) {
      final SceneItemViewModel tmp = current[i];
      final String previous = previousSceneIdList[i];
      if (tmp.sceneId != previous) {
        return true;
      }
    }
    return false;
  }

  static void _refreshPrevious(List<SceneItemViewModel> current) {
    _previousSceneIdList.clear();
    for (int i = 0; i < current.length; i++) {
      _previousSceneIdList.add(current[i].sceneId);
    }
  }

  static final List<String> _previousSceneIdList = <String>[];

  static ExpansionStatus _previousExpansionStatus = ExpansionStatus.close;

  /// 创建场景vm
  static SceneViewModel _createSceneViewModel(
    List<SceneItemViewModel> sceneList,
    ExpansionStatus expansionStatus,
    bool isEditState,
    String familyId,
    String roomId,
  ) {
    final List<SceneItemViewModel> current =
        _getShowSceneList(sceneList, expansionStatus);
    final bool sceneNameListChanged =
        _isSceneListChanged(current, _previousSceneIdList);
    final bool expansionButtonStatus =
        expansionStatus != _previousExpansionStatus;
    final bool isChanged = sceneNameListChanged || expansionButtonStatus;
    _refreshPrevious(current);
    _previousExpansionStatus = expansionStatus;
    return SceneViewModel(
      current,
      sceneList.isNotEmpty,
      expansionStatus,
      roomId == familyId,
      isEditState,
      isChanged,
    );
  }

  static List<SceneItemViewModel> _getShowSceneList(
      List<SceneItemViewModel> sceneList, ExpansionStatus expansionStatus) {
    if (expansionStatus == ExpansionStatus.close &&
        sceneList.length > SceneReducerUtil.sceneExpandCloseMaxCount) {
      return sceneList.take(SceneReducerUtil.sceneExpandCloseMaxCount).toList();
    }
    return sceneList;
  }

  static Selector<SmartHomeState, SceneExpandViewModel>
      selectExpandBtnViewModel(String roomId) {
    return createSelector2(
      // 输入选择器 - 提取关键依赖数据
      (SmartHomeState state) =>
          state.sceneState.sceneMap[roomId] ?? <SceneItemViewModel>[],
      (SmartHomeState state) =>
          state.sceneState.expansionStatusMap[roomId] ?? ExpansionStatus.close,

      // 结果选择器 - 转换为视图模型
      (List<SceneItemViewModel> sceneList, ExpansionStatus status) =>
          SceneExpandViewModel(
        expandVisibility:
            sceneList.length > SceneReducerUtil.sceneExpandCloseMaxCount,
        expansionStatus: status,
      ),
    );
  }

  /// 获取单个场景ViewModel
  static Selector<SmartHomeState, SceneItemViewModel?> selectSceneItemViewModel(
      String roomId, String sceneId) {
    return createSelector1(
      // 输入选择器 - 提取关键依赖数据
      (SmartHomeState state) =>
          state.sceneState.sceneMap[roomId] ?? <SceneItemViewModel>[],
      // 结果选择器 - 转换为视图模型
      (List<SceneItemViewModel> sceneList) {
        for (int i = 0; i < sceneList.length; i++) {
          if (sceneList[i].sceneId == sceneId) {
            return sceneList[i];
          }
        }
        return null;
      },
    );
  }
}
