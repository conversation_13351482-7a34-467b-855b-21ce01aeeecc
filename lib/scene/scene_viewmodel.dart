import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/template_map.dart';

enum SceneListType { ifttt, template }

class SceneViewModel {
  SceneViewModel(
    List<SceneItemViewModel> sceneList,
    this.visible,
    this.expansionStatus,
    this.draggable,
    this.isEditState,
    this.isChanged,
  ) {
    this.sceneList.addAll(sceneList
        .map((SceneItemViewModel e) => SceneItemViewModel.fromObject(e)));
  }

  List<SceneItemViewModel> sceneList = <SceneItemViewModel>[];
  bool visible = false;
  ExpansionStatus expansionStatus = ExpansionStatus.close;
  bool draggable = true;
  bool isEditState = false;
  bool isChanged = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SceneViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(sceneList, other.sceneList) &&
          expansionStatus == other.expansionStatus &&
          draggable == other.draggable &&
          isEditState == other.isEditState &&
          visible == other.visible &&
          isChanged == other.isChanged;

  @override
  int get hashCode =>
      listHashCode(sceneList) ^
      expansionStatus.hashCode ^
      draggable.hashCode ^
      isEditState.hashCode ^
      visible.hashCode ^
      isChanged.hashCode;

  @override
  String toString() {
    return 'SceneViewModel{ _sceneList: $sceneList, visible: $visible}';
  }

  SceneViewModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> sceneList =
        json.listValueForKey('sceneList', <dynamic>[]);
    for (final dynamic element in sceneList) {
      if (element is Map<dynamic, dynamic>) {
        this.sceneList.add(SceneItemViewModel.fromJson(element));
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sceneList'] = sceneList;
    data['visible'] = visible;
    data['draggable'] = draggable;
    data['expansionStatus'] = expansionStatus.index;
    return data;
  }
}

enum ExpansionStatus {
  open,
  close,
}

class SceneItemViewModel {
  SceneItemViewModel({
    required this.sceneId,
    required this.sceneIcon,
    required this.sceneName,
    this.isInvalid = false,
    this.type = '',
    this.isSelected = false,
    this.isEditState = false,
    this.templateId = '',
    this.appSort = -1,
    this.dataVersion = '',
  });

  String sceneId = '';
  String sceneIcon = '';
  String sceneName = '';

  bool isInvalid = false;
  String type = '';
  bool isSelected = false;
  bool isEditState = false;
  String templateId = '';
  int appSort = -1;
  String dataVersion = ''; // 场景版本-1/2

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SceneItemViewModel &&
          runtimeType == other.runtimeType &&
          isInvalid == other.isInvalid &&
          sceneId == other.sceneId &&
          sceneIcon == other.sceneIcon &&
          sceneName == other.sceneName &&
          type == other.type &&
          isEditState == other.isEditState &&
          isSelected == other.isSelected &&
          templateId == other.templateId &&
          appSort == other.appSort &&
          dataVersion == other.dataVersion;

  @override
  int get hashCode =>
      isInvalid.hashCode ^
      sceneId.hashCode ^
      sceneIcon.hashCode ^
      sceneName.hashCode ^
      type.hashCode ^
      isEditState.hashCode ^
      isSelected.hashCode ^
      templateId.hashCode ^
      appSort.hashCode ^
      dataVersion.hashCode;

  SceneItemViewModel.fromJson(Map<dynamic, dynamic> json) {
    isInvalid = json.boolValueForKey('isInvalid', false);
    sceneId = json.stringValueForKey('sceneId', '');
    sceneIcon = json.stringValueForKey('sceneIcon', '');
    sceneName = json.stringValueForKey('sceneName', '');
    type = json.stringValueForKey('type', '');
    templateId = json.stringValueForKey('templateId', '');
    appSort = json.intValueForKey('appSort', -1);
    dataVersion = json.stringValueForKey('dataVersion', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['isInvalid'] = isInvalid;
    data['sceneId'] = sceneId;
    data['sceneIcon'] = sceneIcon;
    data['sceneName'] = sceneName;
    data['type'] = type;
    data['templateId'] = templateId;
    data['appSort'] = appSort;
    data['dataVersion'] = dataVersion;
    return data;
  }

  SceneItemViewModel.fromObject(SceneItemViewModel model) {
    isInvalid = model.isInvalid;
    sceneId = model.sceneId;
    sceneIcon = model.sceneIcon;
    sceneName = model.sceneName;
    type = model.type;
    isSelected = model.isSelected;
    isEditState = model.isEditState;
    templateId = model.templateId;
    appSort = model.appSort;
    dataVersion = model.dataVersion;
  }

  @override
  String toString() {
    return 'SceneItemViewModel{isInvalid: $isInvalid, sceneId: $sceneId, sceneIcon: $sceneIcon, sceneName: $sceneName, type: $type, isSelected: $isSelected, isEditState: $isEditState, templateId: $templateId, appSort: $appSort, dataVersion: $dataVersion}';
  }
}

enum SceneItemType { ifttt, template }
