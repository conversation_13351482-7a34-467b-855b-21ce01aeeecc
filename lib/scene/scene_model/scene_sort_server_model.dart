/// 场景列表排序
class ManualSceneSortRequestModel {
  String familyId = '';
  List<SceneSortModel> sceneSort = <SceneSortModel>[];

  ManualSceneSortRequestModel({
    required this.familyId,
    required this.sceneSort,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    data['sceneSort'] = sceneSort;
    return data;
  }

  @override
  String toString() {
    return 'ManualSceneSortRequestModel{familyId: $familyId, sceneSort: $sceneSort} ';
  }
}

class SceneSortModel {
  String sceneId = '';
  String type = '';
  int appSort = -1;

  SceneSortModel({
    required this.sceneId,
    required this.type,
    required this.appSort,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sceneId'] = sceneId;
    data['type'] = type;
    data['appSort'] = appSort;
    return data;
  }

  @override
  String toString() {
    return 'SceneSortModel{sceneId: $sceneId, type: $type, appSort: $appSort} ';
  }
}
