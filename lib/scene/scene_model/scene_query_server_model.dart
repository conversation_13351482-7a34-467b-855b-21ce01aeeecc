import 'dart:convert';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/map_analysis_extension/map_analysis_extension.dart'
    show convertType;
import 'package:upservice/model/uhome_response_model.dart';

const String sceneAppIconVersion = 'v3';

/// 手动场景 请求Model
class ManualSceneRequestModel {
  String familyId = '';
  String? roomId = '';
  String appIconVersion = '';

  ManualSceneRequestModel({
    required this.familyId,
    this.roomId,
    this.appIconVersion = '',
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    if (roomId != null) {
      data['roomId'] = roomId;
    }
    data['appIconVersion'] = appIconVersion;

    return data;
  }

  @override
  String toString() {
    return 'ManualSceneRequestModel{familyId: $familyId, roomId: $roomId, appIconVersion: $appIconVersion}';
  }
}

/// 手动场景 列表返回Model
class ManualSceneResponseModel extends UhomeResponseModel {
  ManualSceneResponseModel.fromJson(super.data) : super.fromJson() {
    scene = ManualSceneServerModel.fromJson(super.retData);
  }

  ManualSceneServerModel scene =
      ManualSceneServerModel.fromJson(const <dynamic, dynamic>{});
}

class ManualSceneServerModel {
  List<ManualSceneItemModel> displaySceneList = <ManualSceneItemModel>[];
  List<ManualSceneItemModel> noDisplaySceneList = <ManualSceneItemModel>[];

  ManualSceneServerModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> tmpDisplayList =
        json.listValueForKey('displaySceneList', <dynamic>[]);
    tmpDisplayList.forEach((dynamic element) {
      if (element is Map) {
        displaySceneList.add(ManualSceneItemModel.fromJson(element));
      }
    });

    final List<dynamic> tmpNoDisplayList =
        json.listValueForKey('noDisplaySceneList', <dynamic>[]);
    tmpNoDisplayList.forEach((dynamic element) {
      if (element is Map) {
        noDisplaySceneList.add(ManualSceneItemModel.fromJson(element));
      }
    });
  }
}

/// 所有房间场景列表 返回Model
class RoomScenesResponseModel {
  RoomScenesResponseModel.fromJson(dynamic originData) {
    Map<dynamic, dynamic> json = <dynamic, dynamic>{};
    if (originData is String) {
      json = convertType<Map<dynamic, dynamic>>(
          jsonDecode(originData), <dynamic, dynamic>{});
    } else if (originData is Map) {
      json = originData;
    }
    _retCode = json.stringValueForKey('retCode', '');
    _retInfo = json.stringValueForKey('retInfo', '');
    _retData = json.listValueForKey('data', <dynamic>[]);

    data = RoomSceneListData.fromList(_retData);
  }

  String get retCode => _retCode;
  String _retCode = '';

  String get retInfo => _retInfo;
  String _retInfo = '';

  List<dynamic> get retData => _retData;
  List<dynamic> _retData = <dynamic>[];

  RoomSceneListData data = RoomSceneListData.fromList(<dynamic>[]);

  @override
  String toString() {
    return 'RoomScenesResponseModel: {retCode: $retCode, retInfo: $retInfo, retData: $retData}';
  }
}

class RoomSceneListData {
  List<RoomScenesDataModel> roomSceneList = <RoomScenesDataModel>[];
  RoomSceneListData.fromList(List<dynamic> jsonList) {
    jsonList.forEach((dynamic element) {
      if (element is Map) {
        roomSceneList.add(RoomScenesDataModel.fromJson(element));
      }
    });
  }

  @override
  String toString() {
    return 'RoomSceneListData{roomSceneList: $roomSceneList}';
  }
}

/// 房间场景数据模型
class RoomScenesDataModel {
  String roomId = '';
  List<ManualSceneItemModel> scenes = <ManualSceneItemModel>[];

  RoomScenesDataModel.fromJson(Map<dynamic, dynamic> json) {
    roomId = json.stringValueForKey('roomId', '');

    final List<dynamic> scenesData =
        json.listValueForKey('scenes', <dynamic>[]);

    scenesData.forEach((dynamic element) {
      if (element is Map) {
        scenes.add(ManualSceneItemModel.fromJson(element));
      }
    });
  }

  @override
  String toString() {
    return 'RoomScenesDataModel{roomId: $roomId, scenes: $scenes}';
  }
}

class ManualSceneItemModel {
  String type = '';
  String serverIcon = '';
  int appSort = -1;
  String templateId = '';
  String id = '';
  String sceneName = '';
  String dataVersion = ''; // 场景版本-1/2
  int status = -1; // 0-有效；1-失效；

  ManualSceneItemModel.fromJson(Map<dynamic, dynamic> json) {
    type = json.stringValueForKey('type', '');
    serverIcon = json.stringValueForKey('serverIcon', '');
    appSort = json.intValueForKey('appSort', -1);
    templateId = json.stringValueForKey('templateId', '');
    id = json.stringValueForKey('id', '');
    sceneName = json.stringValueForKey('sceneName', '');
    dataVersion = json.stringValueForKey('dataVersion', '');
    status = json.intValueForKey('status', -1);
  }

  @override
  String toString() {
    return 'ManualSceneItemModel{type: $type, serverIcon: $serverIcon, appSort: $appSort, templateId: $templateId, id: $id, sceneName: $sceneName, dataVersion:$dataVersion, status: $status} ';
  }
}
