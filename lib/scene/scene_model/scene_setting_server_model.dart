class ManualSceneSettingRequestModel {
  String familyId = '';
  String? roomId;
  List<String>? displaySceneIds = <String>[];
  List<String>? noDisplaySceneIds = <String>[];

  ManualSceneSettingRequestModel({
    required this.familyId,
    this.roomId,
    this.displaySceneIds,
    this.noDisplaySceneIds,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    if (roomId != null) {
      data['roomId'] = roomId;
    }
    if (displaySceneIds != null && displaySceneIds is List<String>) {
      data['displaySceneIds'] = displaySceneIds?.map((String v) => v).toList();
    }
    if (noDisplaySceneIds != null && noDisplaySceneIds is List<String>) {
      data['noDisplaySceneIds'] =
          noDisplaySceneIds?.map((String v) => v).toList();
    }
    return data;
  }

  @override
  String toString() {
    return 'ManualSceneSettingRequestModel{familyId: $familyId, '
        'roomId: $roomId, '
        'displaySceneIds: $displaySceneIds, '
        'noDisplaySceneIds: $noDisplaySceneIds} ';
  }
}
