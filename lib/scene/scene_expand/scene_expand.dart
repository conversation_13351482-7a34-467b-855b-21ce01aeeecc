import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:reorderable_plus/reorderable.dart' as reorder;
import 'package:sliver_tools/sliver_tools.dart';
import 'package:smart_home/common/constant_gio_scene.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:smart_home/scene/scene_expand/scene_expansion_button.dart';
import 'package:smart_home/scene/scene_util.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/scene/store/scene_action.dart';
import 'package:smart_home/scene/store/scene_reducer_util.dart';
import 'package:smart_home/scene/store/scene_selectors.dart';
import 'package:smart_home/scene/widgets/scene_card_item_widget.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../common/constant.dart';
import '../../common/smart_home_util.dart';

// const ValueKey<String> sceneListKey = ValueKey<String>('SceneListKey');

class SceneExpandWidget extends StatelessWidget {
  SceneExpandWidget({super.key, required this.roomId, required this.roomName});

  final String roomId;
  final String roomName;

  static const double _kHorizontalPadding = 16.0;
  static const double _kBottomPadding = 12.0;

  ValueKey<String> sceneListKey = const ValueKey<String>('SceneListKey');

  static const int _kGridCrossAxisCount = 2;
  double _kGridChildAspectRatio = 0;
  static const double _kGridChildAspectDefaultRatio = 3.089;
  static const double _kGridMainAxisSpacing = 12.0;
  static const double _kGridCrossAxisSpacing = 12.0;
  static const double _kSceneCardHeight = 56.0;
  final Map<double, double> _aspectRatioCache = <double, double>{};

  double _calcDynamicAspectRatio(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    // use cached ratio
    if (_aspectRatioCache.containsKey(screenWidth)) {
      return _aspectRatioCache[screenWidth] ?? _kGridChildAspectDefaultRatio;
    }

    final double contentWidth = screenWidth - (_kHorizontalPadding * 2);
    final double cardWidth =
        (contentWidth - _kGridCrossAxisSpacing) / _kGridCrossAxisCount;

    // 根据定高和动宽--计算动态宽高比
    final double dynamicAspectRatio = cardWidth / _kSceneCardHeight;
    // cache ratio
    _aspectRatioCache[screenWidth] = dynamicAspectRatio;

    return dynamicAspectRatio;
  }

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, SceneViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return SceneSelectors.selectSceneViewModel(roomId)(store.state);
      },
      builder: (BuildContext context, SceneViewModel viewModel) {
        if (!viewModel.visible) {
          return const SliverToBoxAdapter();
        }
        _kGridChildAspectRatio = _calcDynamicAspectRatio(context);

        return MultiSliver(
          children: <Widget>[
            _buildSceneList(viewModel),
            _buildDivider(),
            SceneExpansionButton(roomId: roomId),
          ],
        );
      },
    );
  }

  Widget _buildDivider() {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          final List<SceneItemViewModel> sceneList =
              store.state.sceneState.sceneMap[roomId] ?? <SceneItemViewModel>[];
          return sceneList.isNotEmpty &&
              sceneList.length <= SceneReducerUtil.sceneExpandCloseMaxCount;
        },
        builder: (BuildContext context, bool showDivider) {
          return SliverToBoxAdapter(
            child: SizedBox(height: showDivider ? _kBottomPadding : 0),
          );
        });
  }

  Widget _buildSceneList(SceneViewModel viewModel) {
    return SliverAnimatedPaintExtent(
      key: sceneListKey,
      duration: Duration(milliseconds: viewModel.isChanged ? 350 : 0),
      child: SliverPadding(
        padding: const EdgeInsets.symmetric(horizontal: _kHorizontalPadding),
        sliver: reorder.SliverReorderableGrid(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _kGridCrossAxisCount,
            childAspectRatio: _kGridChildAspectRatio,
            mainAxisSpacing: _kGridMainAxisSpacing,
            crossAxisSpacing: _kGridCrossAxisSpacing,
          ),
          itemBuilder: (BuildContext context, int index) {
            return _itemBuilder(context, index, viewModel);
          },
          itemCount: viewModel.sceneList.length,
          onReorder: (int oldIndex, int newIndex) {
            viewModel.sceneList
                .insert(newIndex, viewModel.sceneList.removeAt(oldIndex));
            smartHomeStore
                .dispatch(SceneDragFinishedAction(viewModel.sceneList));
          },
          onReorderStart: (int dragIndex) {
            gioTrack(GioScene.gioSceneLongPress, gioRoomNameParam);
            if (!smartHomeStore.state.isEditState) {
              smartHomeStore.dispatch(EnterEditStateBySceneCardAction(
                  id: viewModel.sceneList[dragIndex].sceneId));
            }
          },
          onReorderEnd: (int dragIndex) {},
          proxyDecorator:
              (Widget child, int index, Animation<double> animation) {
            return Material(
              color: Colors.transparent,
              child: StoreProvider<SmartHomeState>(
                store: smartHomeStore,
                child: child,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _itemBuilder(
      BuildContext context, int index, SceneViewModel viewModel) {
    final SceneItemViewModel item = viewModel.sceneList[index];
    return reorder.ReorderableDelayedDragStartListener(
      key: ValueKey<String>(item.sceneId),
      index: index,
      enabled: viewModel.draggable,
      child: _buildSceneItem(item, viewModel, context),
    );
  }

  Widget _buildSceneItem(
      SceneItemViewModel item, SceneViewModel viewModel, BuildContext context) {
    final Widget sceneItemWidget =
        StoreConnector<SmartHomeState, SceneItemViewModel?>(
      distinct: true,
      key: ValueKey<SceneItemViewModel>(item),
      converter: (Store<SmartHomeState> store) {
        return SceneSelectors.selectSceneItemViewModel(roomId, item.sceneId)(
            store.state);
      },
      builder: (BuildContext context, SceneItemViewModel? vm) {
        if (vm == null) {
          return const SizedBox();
        }
        return SceneCardItemWidget(
          item: vm,
          onClick: (SceneItemViewModel item) {
            if (vm.isEditState) {
              smartHomeStore.dispatch(
                  UpdateSceneCardSelectedStatusAction(id: item.sceneId));
            } else {
              gioTrack(GioScene.gioSceneClick, <String, String>{
                'scene_name': item.sceneName,
                'plan_type': item.templateId.isEmpty ? '自定义场景' : '模板场景',
                'Room_Name': smartHomeStore.state.familyState.familyId == roomId
                    ? '全屋'
                    : '其他',
              });
              smartHomeStore.dispatch(SceneExecuteAction(item, context));
            }
          },
          isEditState: vm.isEditState,
        );
      },
    );

    if (viewModel.draggable && !isFamilyMemberRole()) {
      return sceneItemWidget;
    }

    return GestureDetector(
      onLongPress: () {
        gioTrack(GioScene.gioSceneLongPress, gioRoomNameParam);
        if (isFamilyMemberRole()) {
          ToastHelper.showToast(SmartHomeConstant.cardManageWarning);
          return;
        }
        if (smartHomeStore.state.isEditState) {
          return;
        }
        smartHomeStore
            .dispatch(EnterEditStateBySceneCardAction(id: item.sceneId));
      },
      child: sceneItemWidget,
    );
  }
}
