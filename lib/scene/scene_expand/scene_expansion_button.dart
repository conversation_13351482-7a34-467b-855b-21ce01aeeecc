import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/scene/store/scene_action.dart';
import 'package:smart_home/scene/store/scene_selectors.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

/// 场景展开、折叠按钮
class SceneExpansionButton extends StatelessWidget {
  const SceneExpansionButton({super.key, required this.roomId});

  final String roomId;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, SceneExpandViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return SceneSelectors.selectExpandBtnViewModel(roomId)(store.state);
        },
        builder: (BuildContext context, SceneExpandViewModel viewModel) {
          if (!viewModel.expandVisibility) {
            return const SliverToBoxAdapter();
          }
          return SliverToBoxAdapter(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                smartHomeStore.dispatch(UpdateExpansionStatusAction(
                    viewModel.expansionStatus == ExpansionStatus.close
                        ? ExpansionStatus.open
                        : ExpansionStatus.close));
              },
              child: SizedBox(
                height: 44,
                width: double.infinity,
                child: Center(
                  child: Image.asset(
                    viewModel.expansionStatus == ExpansionStatus.close
                        ? 'assets/icons/scene_arrow_down.webp'
                        : 'assets/icons/scene_arrow_up.webp',
                    width: 12,
                    height: 12,
                    package: SmartHomeConstant.package,
                  ),
                ),
              ),
            ),
          );
        });
  }
}

class SceneExpandViewModel {
  final bool expandVisibility;
  final ExpansionStatus expansionStatus;

  SceneExpandViewModel(
      {required this.expandVisibility, required this.expansionStatus});

  @override
  int get hashCode => expandVisibility.hashCode ^ expansionStatus.hashCode;

  @override
  bool operator ==(Object other) {
    return other is SceneExpandViewModel &&
        other.expandVisibility == expandVisibility &&
        other.expansionStatus == expansionStatus;
  }
}
