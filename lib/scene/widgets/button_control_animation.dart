import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';

import '../../device/component_widget/button_control_animation.dart';

final SpringSimulation quickControlSimulation = SpringSimulation(
    const SpringDescription(
        mass: 12, //质量
        stiffness: 1, //硬度
        damping: 1.5 // 阻尼
        ),
    0,
    1,
    40 // 速度
    );

class ButtonControlAnimation extends StatefulWidget {
  const ButtonControlAnimation({
    super.key,
    required this.quickControl,
    required this.contentWidget,
  });

  final Widget contentWidget;
  final void Function() quickControl;

  @override
  State<ButtonControlAnimation> createState() => _ButtonControlAnimationState();
}

class _ButtonControlAnimationState extends State<ButtonControlAnimation>
    with TickerProviderStateMixin {
  // 动效 改变大小
  double scale = 1.0;
  AnimationController? _quickController1;
  Animation<double>? _quickAnimation1;
  AnimationController? _quickController2;
  Animation<double>? _quickAnimation2;

  void handleQuick() {
    if (mounted) {
      widget.quickControl();
    }
    _quickController2?.stop();
    _quickController1?.reset();
    _quickController1?.forward();
  }

  @override
  void initState() {
    // 动效 初始化
    initQuickControlAnimation();
    super.initState();
  }

  // 回弹弹簧动效
  void startQuickControlAnimation() {
    _quickAnimation2 =
        _quickController2?.drive(Tween<double>(begin: 0.95, end: 1.0));
    _quickController2?.animateWith(quickControlSimulation);
  }

  // 动效
  void initQuickControlAnimation() {
    try {
      _quickController1 = AnimationController(
          vsync: this, duration: const Duration(milliseconds: 170));
      if (_quickController1 != null) {
        _quickAnimation1 = Tween<double>(begin: 1.0, end: 0.95).animate(
            CurvedAnimation(
                parent: _quickController1!, curve: Curves.easeInOutCubic));
        // 回弹效果
        _quickController2 = AnimationController.unbounded(vsync: this);
        _quickController2?.addListener(() {
          if (_quickAnimation2 != null) {
            if (_quickAnimation2!.value > 1.0) {
              scale = 1.0;
            } else {
              scale = _quickAnimation2!.value;
            }
          }
          setState(() {});
        });
        _quickController1?.addListener(() {
          scale = _quickAnimation1!.value;
          setState(() {});
        });
        _quickController1?.addStatusListener((AnimationStatus status) {
          if (status == AnimationStatus.completed) {
            startQuickControlAnimation();
            Future<dynamic>.delayed(const Duration(milliseconds: 300), () {
              _quickController2?.stop();
            });
          }
        });
        _quickController2?.addStatusListener((AnimationStatus status) {
          if (status == AnimationStatus.completed) {}
        });
      }
    } catch (e) {
      DevLogger.info(
          tag: 'SmartHome-Scene', msg: 'initQuickControlAnimation err:$e');
    }
  }

  @override
  void dispose() {
    _quickController1?.dispose();
    _quickController2?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: debounce(() {
        handleQuick();
      }, const Duration(milliseconds: 1000)) as void Function()?,
      child: Transform.scale(
        scale: scale,
        child: widget.contentWidget,
      ),
    );
  }
}
