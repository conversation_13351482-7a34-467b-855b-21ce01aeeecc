/*
 * 描述：智家tab场景卡片
 * 作者：fancunshuo
 * 建立时间: 2024/7/4
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import 'button_control_animation.dart';

typedef SceneCardClick = void Function(SceneItemViewModel item);

class SceneCardItemWidget extends StatefulWidget {
  const SceneCardItemWidget({
    super.key,
    required this.item,
    required this.onClick,
    required this.isEditState,
  });

  final SceneItemViewModel item;
  final SceneCardClick onClick;
  final bool isEditState;

  @override
  State<SceneCardItemWidget> createState() => _SceneCardItemState();
}

class _SceneCardItemState extends State<SceneCardItemWidget>
    with TickerProviderStateMixin {
  Decoration get _cardDecoration {
    return BoxDecoration(
      border:
          Border.all(color: const Color.fromRGBO(255, 255, 255, 1), width: 0.5),
      borderRadius: const BorderRadius.all(Radius.circular(22)),
      color: const Color.fromRGBO(255, 255, 255, 1),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Widget content = Stack(
      children: <Widget>[
        Container(
          height: 56,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: _cardDecoration,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Expanded(
                child: Row(
                  children: <Widget>[
                    _buildSceneIcon(),
                    const SizedBox(width: 8),
                    _buildSceneName(),
                    if (widget.isEditState) _buildSelectedIcon(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
    if (widget.isEditState) {
      return GestureDetector(
        onTap: () => widget.onClick(widget.item),
        child: content,
      );
    }
    //TODO-zqj 此处沿用线上点击动效，430版本后按需替换成新动效
    return ButtonControlAnimation(
      quickControl: () {
        widget.onClick(widget.item);
      },
      contentWidget: content,
    );
  }

  Widget _buildSceneIcon() {
    return SizedBox(
      width: 20,
      height: 20,
      child: AspectRatio(
          aspectRatio: 1,
          child: CommonNetworkRefreshImg(
            imageUrl: widget.item.sceneIcon,
            alignment: Alignment.center,
            errorWidget: Image.asset(
              'assets/icons/default_scene_img.webp',
              package: SmartHomeConstant.package,
              height: double.infinity,
            ),
          )),
    );
  }

  Widget _buildSceneName() {
    return Expanded(
      child: Opacity(
        opacity: widget.item.isInvalid ? 0.39 : 1,
        child: ColoredBox(
          color: Colors.white,
          child: Text(
            widget.item.sceneName,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 13,
                fontFamilyFallback: fontFamilyFallback(),
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(17, 17, 17, 1)),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedIcon() {
    return SizedBox(
      width: 24,
      height: 24,
      child: Image.asset(
        widget.item.isSelected
            ? 'assets/icons/icon_edit_select.webp'
            : 'assets/icons/icon_edit_unselect.webp',
        package: SmartHomeConstant.package,
      ),
    );
  }
}
