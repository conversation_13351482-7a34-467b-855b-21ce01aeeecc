import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:path_provider/path_provider.dart';

class CachedNetworkImageBuilder extends StatelessWidget {
  final String url;
  final List<String>? imageExtensions;
  final Widget? placeHolder;
  final Widget? errorWidget;
  final Widget Function(File image) builder;

  const CachedNetworkImageBuilder({
    super.key,
    required this.url,
    this.imageExtensions,
    this.placeHolder,
    this.errorWidget,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
      future: Future<String>.microtask(
        () async {
          final RegExp ieRegEx = RegExp(
            imageExtensions != null
                ? imageExtensions!
                    .map((String e) => "(.${e.replaceAll(RegExp('[. ]'), '')})")
                    .join('|')
                : '(.jpg)|(.jpeg)|(.gif)|(.png)',
          );

          final String imageName = url.split('/').last;
          final Directory directory = await getApplicationDocumentsDirectory();

          final Directory folderDirectory =
              Directory('${directory.path}/cached_images_9_3_0/');

          if (!folderDirectory.existsSync()) {
            await folderDirectory.create(recursive: true);
          }

          final Directory imageDirectory =
              Directory(folderDirectory.path + imageName);

          if (folderDirectory
              .listSync()
              .toString()
              .contains(imageDirectory.path)) {
            return imageDirectory.path;
          } else if (url.startsWith(RegExp('http(s)?://')) &&
              url.contains(ieRegEx)) {
            final Response response = await get(Uri.parse(url));

            if (response.statusCode == 200) {
              if ((response.headers['content-type'] ?? '')
                  .startsWith('image')) {
                final File file = File(imageDirectory.path);
                file.writeAsBytesSync(response.bodyBytes);

                return imageDirectory.path;
              } else {
                return ':error: url does not contain image';
              }
            } else {
              return ':error: Failed to load image : statusCode: ${response.statusCode}';
            }
          } else {
            return ':error: unknown error occured';
          }
        },
      ),
      builder: (_, AsyncSnapshot<String> snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasData) {
          if (snapshot.data!.startsWith(':error:')) {
            return errorWidget ?? Center(child: Text(snapshot.data!));
          } else {
            return builder(File(snapshot.data!));
          }
        } else {
          return placeHolder ??
              const Center(child: CircularProgressIndicator());
        }
      },
    );
  }
}
