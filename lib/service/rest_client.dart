import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';
import 'package:smart_home/device/device_info_model/card_sorted_info_response_model.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_foodnum_model.dart';
import 'package:smart_home/device/purified_consumables_model.dart';
import 'package:smart_home/navigator/family/model/geofencing_response_model.dart';
import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';
import 'package:smart_home/scene/scene_server_model.dart';
import 'package:smart_home/scene/switch/switch_status_query_model.dart';
import 'package:smart_home/whole_house/device_consumables/consumables_server_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/single_consumable_model.dart';
import 'package:upservice/model/uhome_response_model.dart';

import '../device/aggregation/agg_camera/model/agg_sort_response_model.dart';
import '../device/aggregation/aggregation_detail/model/supportDeviceModel.dart';
import '../device/aggregation/aggregation_setting/service/switch_status_response_model.dart';
import '../device/device_alarm_info.dart';
import '../device/device_guide/model/guide_aggregation_model.dart';
import '../device/device_view_model/camera/service/camera_msg_response_model.dart';
import '../device/e_heat_time_of_off_model.dart';
import '../edit/model/change_family_check_model.dart';
import '../features/operating_activity/models/summer_activity_model.dart';
import '../pack_gift/model/user_pack_model.dart';
import '../whole_house/device_env/services/env_devices_response_model.dart';
import '../whole_house/device_fault_alarm/models/device_fault_alarm_response_model.dart';
import '../whole_house/location_weather/services/weather/weather_response_model.dart';
import '../whole_house/preference_setting/models/preference_setting_response_model.dart';

part 'rest_client.g.dart';

@RestApi()
abstract class SmartHomeRestClient {
  factory SmartHomeRestClient(Dio dio, {String baseUrl}) = _SmartHomeRestClient;

  /// 获取环境设备数据
  @POST('/api-gw/wisdomdevice/device/v1/queryEnvPreference')
  Future<EnvDevicesResponseModel> getEnvDevices(
      @Body() Map<String, String> params,
      @Header('appVersion') String appVersion);

  /// 设置环境设备偏好
  @POST('/api-gw/wisdomdevice/device/v1/setEnvPreference')
  Future<UhomeResponseModel> setEnvDevicePreference(
      @Body() Map<String, String> params,
      @Header('appVersion') String appVersion);

  /// 获取室外天气
  @POST('/api-gw/wisdomhouse/house/v2/multifunctional/query')
  Future<WeatherResponseModel> getWholeHouseWeather(
      @Body() Map<String, dynamic> params);

  /// 仪表盘-故障件接口
  @GET('/api-gw/wisdomdevice/device/v1/queryDeviceFaults')
  Future<DeviceFaultAlarmResponseModel> getWholeHouseFaultAlarms(
      @Query('familyId') String familyId);

  /// 仪表盘-偏好设置查询
  @GET('/api-gw/wisdomhouse/house/v1/queryPreference')
  Future<PreferenceSettingResponseModel> getWholeHousePreferenceSetting(
      @Query('familyId') String familyId);

  /// 仪表盘-偏好设置
  @POST('/api-gw/wisdomhouse/house/v1/setPreference')
  Future<UhomeResponseModel> setWholeHousePreferenceSetting(
      @Body() Map<String, String> params);

  // 查询家庭设备耗材列表V8
  // 接口文档详见：https://stp.haier.net/project/79/interface/api/251130
  @POST('/api-gw/wisdomhouse/house/v8/consumables/family/list')
  Future<ConsumableServerResponseModel> queryDeviceConsumables(
      @Body() Map<String, dynamic> params);

  // 查询家庭单设备耗材列表V1
  // 接口文档详见：https://stp.haier.net/project/79/interface/api/243160
  @POST('/api-gw/wisdomhouse/house/v7/device/single/consumable/info')
  Future<ConsumableInfoResponseModel> queryConsumableInfo(
      @Body() Map<String, dynamic> params);

  /// 非高安设备操作数据校验(网器适配家庭重构)
  @POST('/api-gw/wisdomdevice/device/v1/operate/check')
  Future<OperateCheckResponseModel> operateCheck(
      @Body() Map<String, dynamic> params);

  // 设备保养耗材重置
  // 接口文档详见：https://stp.haier.net/project/79/interface/api/11226
  @POST('/api-gw/wisdomhouse/house/v1/device/tob/cleanreset')
  Future<UhomeResponseModel> cleanResetConsumables(
      @Body() Map<String, dynamic> params);

  @GET(
      '/api-gw/wisdomhouse/house/model/v2/elecwaterheater/scheduler/onOffStatus')
  Future<EHeatTimeOnOffResponseModel> eHeatTimeOnOffInfo(
      @Query('deviceId') String deviceId);

  /// 快捷操控 查询厨下净水机耗材信息
  @GET('/api-gw/wisdomhouse/house/model/v2/device/consumables')
  Future<PurifiedConsumablesResponseModel> purifiedConsumables(
      @Query('prodNo') String prodNo);

  @POST('/emuplus/device/getDevAlarmInfo')
  Future<AlarmInfoModel> alarmsInfo(@Body() Map<String, dynamic> params);

  @POST('/api-gw/zjsceneapi/scene/v1/quickScene')
  Future<SceneResponseModel> getQuickScene(
      @Body() Map<String, dynamic> params,
      @Header('apiVersion') String apiVersion,
      @Header('appVersion') String appVersion);

  @POST('/api-gw/zjsceneapi/scene/quickSceneCreate')
  Future<UhomeResponseModel> editQuickScene(
      @Body() Map<String, dynamic> params);

  @POST('/api-gw/zjsceneapi/template/delSceneTemplate')
  Future<UhomeResponseModel> delSceneTemplate(
      @Body() Map<String, String> params);

  @POST('/api-gw/wisdomdevice/device/v1/card/order/query')
  Future<CardSortedInfoResponseModel> getCardSortedInfo(
      @Body() Map<String, dynamic> params);

  @POST('/api-gw/shpmResource/ad/v1/rotation')
  Future<NewUserPackResponseModel> getNewUserPackList(
      @Body() Map<String, String> params);

  // 查询聚合卡片
  @GET('/api-gw/wisdomdevice/device/v3/boot/aggregation/switch/query')
  Future<GuideAggregationResponseModel> queryGuideAggregation(
      @Query('familyId') String familyId,
      @Header('appVersion') String appVersion);

  // 设置聚合卡片
  @POST('/api-gw/wisdomdevice/device/v1/aggregation/switch/operate')
  Future<dynamic> setClusterSwitch(@Body() Map<String, dynamic> params);

  // 查询聚合卡片开关状态
  // 接口文档详见：https://stp.haier.net/project/79/interface/api/228313
  @POST('/api-gw/wisdomdevice/device/v3/aggregation/switch/query')
  Future<SwitchStatusResponseModel> queryAggregationSwitch(
      @Body() Map<String, dynamic> params);

  @GET('/api-gw/wisdomdevice/device/v2/aggdetail')
  Future<AggSortResponseModel> queryAggDetailSort(
    @Query('familyId') String familyId,
    @Query('aggType') String aggType,
  );

  // 查询家庭下某类未聚合设备
  // 接口文档详见：https://stp.haier.net/project/79/interface/api/233587
  @POST('/api-gw/wisdomdevice/device/v2/supportedAggDevices')
  Future<SupportDeviceResModel> querySupportedAggDevices(
      @Body() Map<String, dynamic> params,
      @Header('appVersion') String appVersion);
  // 获取camera的大卡片的最新看家信息
  // 接口文档详见：https://stp.haier.net/project/79/interface/api/232984
  @POST('/api-gw/wisdomdevice/device/v1/latestHomeSecurity/query')
  Future<CameraMsgResponseModel> queryCameraMsgInfo(
      @Body() Map<String, dynamic> params,
      @Header('appVersion') String appVersion);

  // 手动场景列表获取 https://stp.haier.net/project/790/interface/api/234577
  @POST('/api-gw/zjsceneapi/scene/v1/getSceneListHome')
  Future<ManualSceneResponseModel> queryManualSceneList(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 手动场景批量设置 https://stp.haier.net/project/790/interface/api/233875
  @POST('/api-gw/zjsceneapi/scene/v1/frontPageDisplays')
  Future<UhomeResponseModel> sceneBatchSetting(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 场景列表排序 https://stp.haier.net/project/87/interface/api/83820
  @POST('/omssceneapi/scene/v2/updateSceneSort')
  Future<UhomeResponseModel> updateSceneSort(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
    @Header('apiVersion') String apiVersion,
  );

  // 查询开关状态 https://stp.haier.net/project/191/interface/api/61919
  @POST('/api-gw/zjBaseServer/switch/query')
  Future<SceneSwitchStatusResponseModel> switchQuery(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 查询家庭地址围栏 https://stp.haier.net/project/79/interface/api/242210
  @POST('/api-gw/wisdomfamily/family/refactor/v1/filter/by/geofencing')
  Future<GeofencingResponseModel> queryFamilyGeofencing(
    @Body() Map<String, dynamic> params,
  );

  // 查询智家tab所有的房间页显示的手动场景 https://stp.haier.net/project/790/interface/api/242164
  @POST('/api-gw/zjsceneapi/scene/v1/getAllRoomSceneHome')
  Future<RoomScenesResponseModel> queryAllRoomsSceneList(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 查询单房间页显示的手动场景 https://stp.haier.net/project/790/interface/api/242137
  @POST('/api-gw/zjsceneapi/scene/v1/getSceneHome')
  Future<ManualSceneResponseModel> querySingleRoomSceneList(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 手动场景批量设置单房间页显示 https://stp.haier.net/project/790/interface/api/242155
  @POST('/api-gw/zjsceneapi/scene/v1/sceneDisplays')
  Future<UhomeResponseModel> roomSceneBatchSetting(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 批量查询设备卡片食材管理状态列表
  // https://stp.haier.net/project/79/interface/api/243440
  @POST('/api-gw/wisdomdevice/refrigerator/card/food/info')
  Future<FridgeFoodNumResponseModel> queryFridgeFoodNums(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 灯光|窗帘|环境聚合卡片内部单空间设备排序查询 https://stp.haier.net/project/79/interface/api/242845
  @POST('/api-gw/wisdomdevice/device/v1/agg/card/singlespace/sort/query')
  Future<AggSortResModel> queryAggSort(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );

  // 灯光|窗帘|环境聚合卡片内部单空间设备排序查询 https://stp.haier.net/project/79/interface/api/242810
  @POST('/api-gw/wisdomdevice/device/v1/agg/card/singlespace/sort/save')
  Future<UhomeResponseModel> saveAggSort(
    @Body() Map<String, dynamic> params,
    @Header('appVersion') String appVersion,
  );
}
