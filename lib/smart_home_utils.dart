/*
 * 描述：SmartHome工具类
 * 作者：fancunshuo
 * 建立时间: 2025/8/3
 */

import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import 'common/constant.dart';

class SmartHomeUtils {
  // 单例实例
  static final SmartHomeUtils _instance = SmartHomeUtils._internal();

  // 工厂构造函数
  factory SmartHomeUtils() => _instance;

  // 私有构造函数
  SmartHomeUtils._internal();

  static ItemScrollController? itemScrollController;
  static PageController? smartHomePageController;
  static ScrollController? extendScrollController;

  static const int _scrollAnimationDurationMs = 200;

  static void initSmartHomeScrollController(ScrollController extendController,
      PageController? pageController, ItemScrollController? itemController) {
    extendScrollController = extendController;
    smartHomePageController = pageController;
    itemScrollController = itemController;
  }

  static void changeTab(int index) {
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      if (smartHomePageController != null &&
          smartHomePageController!.hasClients) {
        smartHomePageController!.jumpToPage(index);
      }
    });
    DevLogger.info(
        tag: 'get_current_tag',
        msg: 'get_current_tag  current tab index$index');
    if (index == 0) {
      gioTrack(SmartHomeConstant.quickListExposureGio);
    }
  }

  static void scrollToTop() {
    extendScrollController?.animateTo(0,
        duration: const Duration(milliseconds: _scrollAnimationDurationMs),
        curve: Curves.fastOutSlowIn);
  }
}
