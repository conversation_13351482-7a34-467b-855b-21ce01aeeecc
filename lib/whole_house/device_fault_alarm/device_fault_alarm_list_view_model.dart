import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

/// 仪表盘-故障弹窗列表vm
class DeviceFaultAlarmListViewModel {
  DeviceFaultAlarmListViewModel({required this.list});

  /// [list] 故障列表
  final List<DeviceFaultAlarmItemViewModel> list;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceFaultAlarmListViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(list, other.list);

  @override
  int get hashCode => listHashCode(list);

  @override
  String toString() {
    return 'DeviceFaultAlarmListViewModel{list: $list}';
  }
}

class DeviceFaultAlarmItemViewModel {
  DeviceFaultAlarmItemViewModel(
      {required this.deviceName,
      required this.deviceImage,
      required this.deviceRoom,
      required this.faults,
      required this.detailUrl});

  /// [deviceName] 设备名称
  final String deviceName;

  /// [deviceImage] 设备图片
  final String deviceImage;

  /// [deviceRoom] 设备楼层+房间
  final String deviceRoom;

  /// [faults] 故障名称拼接字符串
  final String faults;

  /// [detailUrl] 故障详情链接
  final String detailUrl;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceFaultAlarmItemViewModel &&
          runtimeType == other.runtimeType &&
          deviceName == other.deviceName &&
          deviceImage == other.deviceImage &&
          deviceRoom == other.deviceRoom &&
          faults == other.faults &&
          detailUrl == other.detailUrl;

  @override
  int get hashCode =>
      deviceName.hashCode ^
      deviceImage.hashCode ^
      deviceRoom.hashCode ^
      faults.hashCode ^
      detailUrl.hashCode;

  @override
  String toString() {
    return 'DeviceFaultAlarmItemViewModel{deviceName: $deviceName, deviceImage: $deviceImage, deviceRoom: $deviceRoom, faults: $faults, detailUrl: $detailUrl}';
  }
}
