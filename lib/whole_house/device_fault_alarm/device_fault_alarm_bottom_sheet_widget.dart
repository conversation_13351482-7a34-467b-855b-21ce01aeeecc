import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/whole_house/widgets/whole_house_list_item_widget.dart';

import '../../common/smart_home_util.dart';
import '../../store/smart_home_state.dart';
import '../widgets/whole_house_popup_empty.dart';
import 'device_fault_alarm_list_view_model.dart';
import 'store/selectors.dart';

class DeviceFaultAlarmBottomSheetWidget extends StatelessWidget {
  const DeviceFaultAlarmBottomSheetWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, DeviceFaultAlarmListViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return DeviceFaultAlarmSelectors.selectFaultAlarmListViewModel(
              store.state);
        },
        builder:
            (BuildContext context, DeviceFaultAlarmListViewModel viewModel) {
          if (viewModel.list.isEmpty) {
            return const WholeHousePopupEmptyWidget(
              type: EmptyType.alarm,
              desc: '暂无故障告警',
            );
          }
          return ListView.separated(
            itemCount: viewModel.list.length,
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.symmetric(vertical: 12),
            shrinkWrap: true,
            itemBuilder: (BuildContext context, int index) {
              return _itemBuilder(context, viewModel.list[index]);
            },
            separatorBuilder: (BuildContext context, int index) =>
                const SizedBox(height: 12),
          );
        });
  }

  /// 构建列表项
  Widget _itemBuilder(
      BuildContext context, DeviceFaultAlarmItemViewModel item) {
    return WholeHouseListItemWidget(
      deviceName: item.deviceName,
      deviceImage: item.deviceImage,
      roomName: item.deviceRoom,
      desc: item.faults,
      descColor: AppSemanticColors.item.remind.primary,
      clickCallback: (BuildContext context) {
        gioTrack(GioConst.wholeHouseClickFaultDetail,
            <String, String>{'value': item.faults});
        if (item.detailUrl.isNotEmpty) {
          // 尝试关闭底部弹窗
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          goToPageWithDebounce(item.detailUrl);
        }
      },
    );
  }
}
