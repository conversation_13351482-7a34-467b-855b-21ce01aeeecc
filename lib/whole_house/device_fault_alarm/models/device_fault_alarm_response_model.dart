import 'package:json_annotation/json_annotation.dart';
import 'package:upservice/model/uhome_response_model.dart';

import 'device_fault_alarm_item_model.dart';

part 'device_fault_alarm_response_model.g.dart';

/// 故障接口返回数据model
class DeviceFaultAlarmResponseModel extends UhomeResponseModel {
  DeviceFaultAlarmResponseModel.fromJson(super.json) : super.fromJson() {
    if (super.retData['list'] is List<dynamic>) {
      for (final dynamic v in super.retData['list'] as List<dynamic>) {
        if (v is Map<dynamic, dynamic>) {
          list.add(DeviceFaultAlarmResponseItemModel.fromJson(v));
        }
      }
    }
  }
  List<DeviceFaultAlarmResponseItemModel> list =
      <DeviceFaultAlarmResponseItemModel>[];
}

/// 接口返回的故障列表中每一项的model
@JsonSerializable(explicitToJson: true, anyMap: true)
class DeviceFaultAlarmResponseItemModel {
  DeviceFaultAlarmResponseItemModel({
    required this.deviceInfo,
    this.deviceFaults,
  });

  /// [deviceInfo] 设备信息
  DeviceInfoModel deviceInfo;

  /// [deviceFaults] 故障列表
  @JsonKey(defaultValue: <DeviceFaultModel>[])
  List<DeviceFaultModel>? deviceFaults;

  factory DeviceFaultAlarmResponseItemModel.fromJson(
          Map<dynamic, dynamic> json) =>
      _$DeviceFaultAlarmResponseItemModelFromJson(json);
  Map<String, dynamic> toJson() =>
      _$DeviceFaultAlarmResponseItemModelToJson(this);
}

/// 接口返回的deviceInfo
@JsonSerializable(explicitToJson: true, anyMap: true)
class DeviceInfoModel {
  DeviceInfoModel(
      {required this.mac,
      required this.devName,
      required this.deviceImage,
      required this.devRoomName,
      required this.devFloorName});

  /// [mac] 设备deviceId
  @JsonKey(defaultValue: '')
  String mac;

  /// [devName] 设备名称
  @JsonKey(defaultValue: '')
  String devName;

  /// deviceImage [设备图片]
  @JsonKey(defaultValue: '')
  String deviceImage;

  /// [devRoomName] 房间名称
  @JsonKey(defaultValue: '')
  String devRoomName;

  /// [devFloorName] 楼层名称
  @JsonKey(defaultValue: '')
  String devFloorName;

  factory DeviceInfoModel.fromJson(Map<dynamic, dynamic> json) =>
      _$DeviceInfoModelFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceInfoModelToJson(this);
}
