// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_fault_alarm_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceFaultAlarmItemModel _$DeviceFaultAlarmItemModelFromJson(Map json) =>
    DeviceFaultAlarmItemModel(
      deviceId: json['deviceId'] as String? ?? '',
      deviceName: json['deviceName'] as String? ?? '',
      deviceImage: json['deviceImage'] as String? ?? '',
      floorName: json['floorName'] as String? ?? '',
      roomName: json['roomName'] as String? ?? '',
      deviceFaults: (json['deviceFaults'] as List<dynamic>?)
              ?.map((e) => DeviceFaultModel.fromJson(
                  Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
    );

Map<String, dynamic> _$DeviceFaultAlarmItemModelToJson(
        DeviceFaultAlarmItemModel instance) =>
    <String, dynamic>{
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'deviceImage': instance.deviceImage,
      'floorName': instance.floorName,
      'roomName': instance.roomName,
      'deviceFaults': instance.deviceFaults.map((e) => e.toJson()).toList(),
    };

DeviceFaultModel _$DeviceFaultModelFromJson(Map json) => DeviceFaultModel(
      id: json['id'] as String? ?? '',
      deviceCode: json['deviceCode'] as String? ?? '',
      faultName: json['faultName'] as String? ?? '',
      mpaas: json['mpaas'] as String? ?? '',
    );

Map<String, dynamic> _$DeviceFaultModelToJson(DeviceFaultModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deviceCode': instance.deviceCode,
      'faultName': instance.faultName,
      'mpaas': instance.mpaas,
    };
