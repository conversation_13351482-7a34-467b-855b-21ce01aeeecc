// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_fault_alarm_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceFaultAlarmResponseItemModel _$DeviceFaultAlarmResponseItemModelFromJson(
        Map json) =>
    DeviceFaultAlarmResponseItemModel(
      deviceInfo: DeviceInfoModel.fromJson(json['deviceInfo'] as Map),
      deviceFaults: (json['deviceFaults'] as List<dynamic>?)
              ?.map((e) => DeviceFaultModel.fromJson(
                  Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
    );

Map<String, dynamic> _$DeviceFaultAlarmResponseItemModelToJson(
        DeviceFaultAlarmResponseItemModel instance) =>
    <String, dynamic>{
      'deviceInfo': instance.deviceInfo.toJson(),
      'deviceFaults': instance.deviceFaults?.map((e) => e.toJson()).toList(),
    };

DeviceInfoModel _$DeviceInfoModelFromJson(Map json) => DeviceInfoModel(
      mac: json['mac'] as String? ?? '',
      devName: json['devName'] as String? ?? '',
      deviceImage: json['deviceImage'] as String? ?? '',
      devRoomName: json['devRoomName'] as String? ?? '',
      devFloorName: json['devFloorName'] as String? ?? '',
    );

Map<String, dynamic> _$DeviceInfoModelToJson(DeviceInfoModel instance) =>
    <String, dynamic>{
      'mac': instance.mac,
      'devName': instance.devName,
      'deviceImage': instance.deviceImage,
      'devRoomName': instance.devRoomName,
      'devFloorName': instance.devFloorName,
    };
