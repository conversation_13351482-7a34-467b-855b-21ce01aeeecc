import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:plugin_device/utils/util_diff.dart';

part 'device_fault_alarm_item_model.g.dart';

///
@JsonSerializable(explicitToJson: true, anyMap: true)
class DeviceFaultAlarmItemModel {
  /// 构造函数
  DeviceFaultAlarmItemModel({
    required this.deviceId,
    required this.deviceName,
    required this.deviceImage,
    required this.floorName,
    required this.roomName,
    required this.deviceFaults,
  });

  /// [deviceId] 设备ID
  @JsonKey(defaultValue: '')
  String deviceId;

  /// [deviceName] 设备名称
  @JsonKey(defaultValue: '')
  String deviceName;

  /// [deviceImage] 设备图片
  @JsonKey(defaultValue: '')
  String deviceImage;

  /// [floorName] 楼层名称
  @JsonKey(defaultValue: '')
  String floorName;

  /// [roomName] 房间名称
  @JsonKey(defaultValue: '')
  String roomName;

  /// [deviceFaults] 设备故障列表
  @JsonKey(defaultValue: <DeviceFaultModel>[])
  List<DeviceFaultModel> deviceFaults;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceFaultAlarmItemModel &&
          runtimeType == other.runtimeType &&
          deviceId == other.deviceId &&
          deviceName == other.deviceName &&
          deviceImage == other.deviceImage &&
          floorName == other.floorName &&
          roomName == other.roomName &&
          listEquals(deviceFaults, other.deviceFaults);

  @override
  int get hashCode =>
      deviceId.hashCode ^
      deviceName.hashCode ^
      deviceImage.hashCode ^
      floorName.hashCode ^
      roomName.hashCode ^
      listHashCode(deviceFaults);

  @override
  String toString() {
    return 'DeviceFaultAlarmItemModel{deviceId: $deviceId, deviceName: $deviceName, deviceImage: $deviceImage, floorName: $floorName, roomName: $roomName, deviceFaults: $deviceFaults}';
  }

  factory DeviceFaultAlarmItemModel.fromJson(Map<dynamic, dynamic> json) =>_$DeviceFaultAlarmItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceFaultAlarmItemModelToJson(this);
}

/// 设备故障模型，用于表示设备的故障信息
@JsonSerializable(explicitToJson: true, anyMap: true)
class DeviceFaultModel {
  /// 构造函数
  DeviceFaultModel({
    required this.id,
    required this.deviceCode,
    required this.faultName,
    required this.mpaas,
  });

  /// [id] 故障ID
  @JsonKey(defaultValue: '')
  String id;

  /// [deviceCode] 本机故障码
  @JsonKey(defaultValue: '')
  String deviceCode;

  /// [faultName] 故障名称
  @JsonKey(defaultValue: '')
  String faultName;

  /// [mpaas] 跳转链接标题
  @JsonKey(defaultValue: '')
  String mpaas;

  factory DeviceFaultModel.fromJson(Map<String, dynamic> json) =>_$DeviceFaultModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceFaultModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceFaultModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          deviceCode == other.deviceCode &&
          faultName == other.faultName &&
          mpaas == other.mpaas;

  @override
  int get hashCode =>
      id.hashCode ^ deviceCode.hashCode ^ faultName.hashCode ^ mpaas.hashCode;

  @override
  String toString() {
    return 'DeviceFaultModel{id: $id, deviceCode: $deviceCode, faultName: $faultName, mpaas: $mpaas}';
  }
}
