import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/whole_house/widgets/whole_house_header_icon_widget.dart';

import '../../common/smart_home_util.dart';
import '../../store/smart_home_state.dart';
import '../widgets/animated_visible_fade_widget.dart';
import '../widgets/show_whole_house_modal_bottom_sheet_widget.dart';
import 'device_fault_alarm_bottom_sheet_widget.dart';
import 'store/selectors.dart';
import 'whole_house_header_fault_alarm_view_model.dart';

class WholeHouseHeaderFaultAlarmWidget extends StatelessWidget {
  const WholeHouseHeaderFaultAlarmWidget({super.key, required this.roomId});
  final String roomId;
  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, WholeHouseHeaderFaultAlarmViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) =>
            DeviceFaultAlarmSelectors.selectHeaderFaultViewModelWithRoomId(store.state, roomId),
        builder:
            (BuildContext context, WholeHouseHeaderFaultAlarmViewModel vm) {
          return AnimatedVisibleFadeWidget(
              visible: vm.visible,
              child: _FaultAlarmIconWidget(
                  icon: vm.icon,
                  count: vm.count,
                  detailUrl: vm.detailUrl,
                  faults: vm.faults,
                  roomId: roomId));
        });
  }
}

// _FaultAlarmIconWidget这个组件只有有故障时才会创建
class _FaultAlarmIconWidget extends StatefulWidget {
  const _FaultAlarmIconWidget({
    super.key,
    required this.icon,
    required this.count,
    required this.detailUrl,
    required this.faults,
    required this.roomId,
  });

  /// 图标 url
  final String icon;

  /// 故障设备个数
  final int count;

  /// 跳转链接
  final String detailUrl;

  /// [faults] 故障内容
  final String faults;

  // 房间id
  final String roomId;

  @override
  State<_FaultAlarmIconWidget> createState() => _FaultAlarmIconWidgetState();
}

class _FaultAlarmIconWidgetState extends State<_FaultAlarmIconWidget> {
  @override
  void initState() {
    super.initState();
    gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
      'value': '故障',
    });
  }

  @override
  void didUpdateWidget(covariant _FaultAlarmIconWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.roomId != widget.roomId) {
      // 房间id变化，重新埋点
      gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
        'value': '故障',
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WholeHouseHeaderIconWidget(
        margin: const EdgeInsets.only(right: 8),
        icon: Image.asset(
          widget.icon,
          width: 24,
          height: 24,
          package: SmartHomeConstant.package,
        ),
        count: widget.count,
        clickCallback: (BuildContext context) {
          gioTrack(GioConst.wholeHouseIconClick, <String, String>{
            'value': '故障',
          });
          // 点击操作：一个设备跳转故障详情，多个设备弹窗展示
          if (widget.count == 1 && widget.detailUrl.isNotEmpty) {
            gioTrack(GioConst.wholeHouseClickFaultDetail, <String, String>{
              'value': widget.faults,
            });
            goToPageWithDebounce(widget.detailUrl);
          } else if (widget.count > 1) {
            showWholeHouseModalBottomSheetWidget(context,
                title: '故障告警',
                child: const DeviceFaultAlarmBottomSheetWidget());
          }
        });
  }
}
