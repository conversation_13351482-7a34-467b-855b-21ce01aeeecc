

class WholeHouseHeaderFaultAlarmViewModel {
  WholeHouseHeaderFaultAlarmViewModel(
      {required this.icon,
      required this.count,
      required this.detailUrl,
      required this.visible,
      required this.faults});

  /// [icon] 图标
  final String icon;

  /// [count] 数量
  final int count;

  /// [detailUrl] 跳转链接
  final String detailUrl;

  /// [visible] 是否显示
  final bool visible;

  /// [faults] 故障内容
  final String faults;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WholeHouseHeaderFaultAlarmViewModel &&
          runtimeType == other.runtimeType &&
          icon == other.icon &&
          count == other.count &&
          detailUrl == other.detailUrl &&
          visible == other.visible &&
          faults == other.faults;

  @override
  int get hashCode =>
      icon.hashCode ^
      count.hashCode ^
      detailUrl.hashCode ^
      visible.hashCode ^
      faults.hashCode;

  @override
  String toString() {
    return 'WholeHouseHeaderFaultAlarmViewModel{icon: $icon, count: $count, detailUrl: $detailUrl, visible: $visible, faults: $faults}';
  }
}
