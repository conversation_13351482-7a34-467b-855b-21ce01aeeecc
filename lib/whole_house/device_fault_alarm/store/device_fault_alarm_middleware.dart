import 'package:device_utils/log/log.dart';
import 'package:flutter/foundation.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/store/device_action.dart';

import '../../../device/device_info_model/smart_home_device.dart';
import '../../../device/device_view_model/card_base_view_model.dart';
import '../../../device/device_view_model/device_card_view_model.dart';
import '../../../store/smart_home_state.dart';
import '../device_fault_alarm_presenter.dart';

/// 设备故障警报中间件
/// 负责拦截设备属性更新Action，检查故障变化并触发故障数据获取
class DeviceFaultAlarmMiddleware implements MiddlewareClass<SmartHomeState> {
  DeviceFaultAlarmPresenter presenter = DeviceFaultAlarmPresenter();
  // 已经上报故障数据的字段
  final Map<String, Set<String>> _alreadyReportedFaults =
      <String, Set<String>>{};
  String _previousFamilyId = '';
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    // 先更新设备的属性
    next(action);
    if (action is UpdateDeviceAttributeAction) {
      if (store.state.familyState.familyId != _previousFamilyId) {
        _alreadyReportedFaults.clear();
        _previousFamilyId = store.state.familyState.familyId;
      }

      try {
        // 收集故障数据和没有故障的deviceId
        final (
          Set<String> currentReportedNoFaultSet,
          Map<String, Set<String>> currentReportedFaults
        ) = _collectDeviceFaultStatus(store.state, action.attributeMap);

        // 对比已经上报的故障数据和此次上报的故障，检查是否有变化
        final bool hasFaultChanges = _hasFaultChanges(
            currentReportedNoFaultSet, currentReportedFaults, store.state);
        if (hasFaultChanges) {
          // 检测到故障变化，获取最新故障数据
          presenter.getWholeHouseDeviceFaultAlarmDataWithDebounce();
        }
      } catch (err) {
        DevLogger.error(
            tag: SmartHomeConstant.package,
            msg: 'DeviceFaultAlarmMiddleware err:$err');
      }
    }
  }

  /// 收集上报上来的具有故障的设备及无故障的设备
  /// 返回格式: {设备ID: 故障名称set}
  (Set<String>, Map<String, Set<String>>) _collectDeviceFaultStatus(
      SmartHomeState state, Map<String, DeviceAttributeModel> attributeMap) {
    final Set<String> noFaultSet = <String>{};
    final Map<String, Set<String>> faultsMap = <String, Set<String>>{};

    for (final String deviceId in attributeMap.keys) {
      // 设备对象
      final SmartHomeDevice? device =
          state.deviceState.smartHomeDeviceMap[deviceId];
      final CardBaseViewModel? viewModel =
          state.deviceState.allCardViewModelMap[deviceId];
      // 跳过无效设备或非设备卡片
      if (device is! SmartHomeDevice || viewModel is! DeviceCardViewModel) {
        continue;
      }
      if (viewModel.isSharedDevice) {
        continue;
      }
      // 获取设备故障名称列表
      final Set<String> cautionsName =
          _getDeviceCautionsName(device, viewModel);
      if (cautionsName.isNotEmpty) {
        faultsMap[deviceId] = cautionsName;
      } else {
        noFaultSet.add(deviceId);
      }
    }
    return (noFaultSet, faultsMap);
  }

  /// 获取设备的故障名称列表，排除需忽略的故障
  Set<String> _getDeviceCautionsName(
      SmartHomeDevice device, DeviceCardViewModel viewModel) {
    if (viewModel.engineDevice) {
      // 处理引擎设备故障，排除需要忽略的故障类型
      return viewModel.engineCautionsNoCancel
          .map((Caution e) => e.name)
          .toSet();
    } else {
      // 处理非引擎设备故障，排除需要忽略的故障类型
      return viewModel.cautionsNoCancel
          .map((UpDeviceCaution e) => e.name)
          .toSet();
    }
  }

  /// 对比已经上报的故障与此次上报的故障是否有变化
  /// 返回true表示需要更新，返回false表示无变化
  /// 1 取消故障：上报alarmCancel的deviceId在已经上报的故障设备集合中，从已经上报故障中移除，标记有变化;
  /// 2 新增故障设备：deviceId不在已经上报的故障设备集合中，新的故障加入已经上报的故障中，标记有变化;
  /// 3 对比个数：上报故障的deviceId在已经上报的故障设备集合中, 对比每个deviceId的故障数量，如果有不一样，更新已经上报的故障，标记有变化;
  /// 4 如果个数相同，对比内容：遍历对比上报的告警内容是否有变化，如果有变化，更新已经上报的故障，标记有变化;
  /// 5 循环完成后，如果有变化返回true, 否则返回false
  bool _hasFaultChanges(Set<String> currentReportedNoFault,
      Map<String, Set<String>> currentReportedFaults, SmartHomeState state) {
    bool hasChange = false;
    for (final String deviceId in currentReportedNoFault) {
      if (_alreadyReportedFaults.remove(deviceId) != null) {
        hasChange = true;
      }
    }

    for (final MapEntry<String, Set<String>> entry
        in currentReportedFaults.entries) {
      final String deviceId = entry.key;
      final Set<String> faults = entry.value;

      if (!_alreadyReportedFaults.containsKey(deviceId)) {
        _alreadyReportedFaults[deviceId] = Set<String>.from(faults);
        hasChange = true;
        continue;
      }

      final Set<String> previousFaults =
          _alreadyReportedFaults[deviceId] ?? <String>{};
      if (previousFaults.isNotEmpty && faults.length != previousFaults.length ||
          !setEquals(faults, previousFaults)) {
        _alreadyReportedFaults[deviceId] = Set<String>.from(faults);
        hasChange = true;
      }
    }
    return hasChange;
  }
}
