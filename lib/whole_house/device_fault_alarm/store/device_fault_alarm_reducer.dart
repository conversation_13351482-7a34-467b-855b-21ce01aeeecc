import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';

import 'device_fault_alarm_action.dart';
import 'device_fault_alarm_state.dart';

final Reducer<SmartHomeState> deviceFaultAlarmReducer = TypedReducer<
            SmartHomeState, UpdateWholeHouseDeviceFaultAlarmListStateAction>(
        _updateWholeHouseDeviceFaultAlarmReducer)
    .call;

/// 更新故障警报状态
SmartHomeState _updateWholeHouseDeviceFaultAlarmReducer(SmartHomeState state,
    UpdateWholeHouseDeviceFaultAlarmListStateAction action) {
  // 更新故障警报列表
  state.wholeHouseState.deviceFaultAlarmState.list =
      action.wholeHouseDeviceFaultAlarmList;

  // 更新设备故障映射
  state.wholeHouseState.deviceFaultAlarmState.deviceFaultMap =
      DeviceFaultAlarmState.buildDeviceFaultMap(
          action.wholeHouseDeviceFaultAlarmList);
  return state;
}
