import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../device_fault_alarm_list_view_model.dart';
import '../models/device_fault_alarm_item_model.dart';
import '../whole_house_header_fault_alarm_view_model.dart';

/// 设备故障警报相关的选择器
class DeviceFaultAlarmSelectors {
  /// 获取设备故障警报列表视图模型
  static final Selector<SmartHomeState, DeviceFaultAlarmListViewModel>
      selectFaultAlarmListViewModel = createSelector2(
    // 输入选择器 - 提取关键依赖数据
    (SmartHomeState state) => state.wholeHouseState.deviceFaultAlarmState.list,
    (SmartHomeState state) => state.deviceState.cardShowFloor,

    // 结果选择器 - 转换为视图模型
    _createViewModel,
  );

  static final Selector<SmartHomeState, WholeHouseHeaderFaultAlarmViewModel>
      selectHeaderFaultViewModel = createSelector3(
    (SmartHomeState state) => state.wholeHouseState.deviceFaultAlarmState.list,
    (SmartHomeState state) =>
        state.wholeHouseState.preferenceSettingState.homeSwitch,
    (SmartHomeState state) => state.familyState.familyId,
    _createHeaderFaultViewModel,
  );

  /// 创建视图模型列表
  static DeviceFaultAlarmListViewModel _createViewModel(
    List<DeviceFaultAlarmItemModel> list,
    bool cardShowFloor,
  ) {
    return DeviceFaultAlarmListViewModel(
      list: list.map((DeviceFaultAlarmItemModel item) {
        return DeviceFaultAlarmItemViewModel(
          deviceName: item.deviceName,
          deviceImage: item.deviceImage,
          deviceRoom:
              cardShowFloor ? item.floorName + item.roomName : item.roomName,
          faults: _formatFaultNames(item.deviceFaults),
          detailUrl: item.deviceFaults.isNotEmpty &&
                  item.deviceFaults.first.mpaas.isNotEmpty
              ? item.deviceFaults.first.mpaas
              : '',
        );
      }).toList(),
    );
  }

  /// 创建故障顶部组件vm
  static WholeHouseHeaderFaultAlarmViewModel _createHeaderFaultViewModel(
    List<DeviceFaultAlarmItemModel> deviceFaultList,
    bool homeSwitch,
    String familyId,
  ) {
    return WholeHouseHeaderFaultAlarmViewModel(
      icon: 'assets/icons/fault_icon.webp',
      count: deviceFaultList.length,
      detailUrl: deviceFaultList.isNotEmpty &&
              deviceFaultList[0].deviceFaults.isNotEmpty
          ? deviceFaultList[0].deviceFaults[0].mpaas
          : '',
      // 全屋房间 && 偏好设置打开 && 故障有数据
      visible: homeSwitch && deviceFaultList.isNotEmpty,
      faults: deviceFaultList.isNotEmpty &&
              deviceFaultList[0].deviceFaults.isNotEmpty
          ? _formatFaultNames(deviceFaultList[0].deviceFaults)
          : '',
    );
  }

  static WholeHouseHeaderFaultAlarmViewModel
      selectHeaderFaultViewModelWithRoomId(
          SmartHomeState state, String roomId) {
    final WholeHouseHeaderFaultAlarmViewModel defaultViewModel =
        selectHeaderFaultViewModel(state);

    return WholeHouseHeaderFaultAlarmViewModel(
        visible: roomId == state.familyState.familyId && defaultViewModel.visible,
        icon: defaultViewModel.icon,
        count: defaultViewModel.count,
        detailUrl: defaultViewModel.detailUrl,
        faults: defaultViewModel.faults);
  }

  /// 格式化故障名称列表
  static String _formatFaultNames(List<DeviceFaultModel> faults) {
    if (faults.isEmpty) {
      return '';
    }
    return faults
        .map((DeviceFaultModel fault) => fault.faultName)
        .where((String name) => name.isNotEmpty)
        .join('、');
  }
}
