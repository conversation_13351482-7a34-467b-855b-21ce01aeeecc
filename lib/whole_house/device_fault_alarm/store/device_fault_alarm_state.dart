import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

import '../models/device_fault_alarm_item_model.dart';

class DeviceFaultAlarmState {
  // 故障列表
  List<DeviceFaultAlarmItemModel> list = <DeviceFaultAlarmItemModel>[];

  // 映射：设备ID -> 故障模型, 方便按照 deviceId 查找故障
  Map<String, DeviceFaultAlarmItemModel> deviceFaultMap =
      <String, DeviceFaultAlarmItemModel>{};

  // 生成映射的帮助方法
  static Map<String, DeviceFaultAlarmItemModel> buildDeviceFaultMap(
      List<DeviceFaultAlarmItemModel> list) {
    return <String, DeviceFaultAlarmItemModel>{
      for (final DeviceFaultAlarmItemModel item in list) item.deviceId: item
    };
  }

  /// clear清空状态方法
  void clear() {
    list.clear();
    deviceFaultMap.clear();
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceFaultAlarmState &&
          runtimeType == other.runtimeType &&
          listEquals(list, other.list) &&
          mapEquals(deviceFaultMap, other.deviceFaultMap);

  @override
  int get hashCode => listHashCode(list) ^ mapHashCode(deviceFaultMap);

  @override
  String toString() {
    return 'DeviceFaultAlarmState{list: $list, deviceFaultMap: $deviceFaultMap}';
  }
}
