import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'weather_card_view_model.freezed.dart';

@freezed
class WeatherCardViewModel with _$WeatherCardViewModel {
  const factory WeatherCardViewModel({
    required String icon,
    required String value,
    required String unit,
    required Color backgroundColor,
    required String area,
    required String humidity,
    required String humidityUnit,
    required String pm25,
    required String aqi,
    @Default('') String customBackgroundImage,
  }) = _WeatherCardViewModel;
}
