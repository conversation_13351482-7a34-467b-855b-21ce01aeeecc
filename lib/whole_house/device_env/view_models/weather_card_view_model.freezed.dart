// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'weather_card_view_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WeatherCardViewModel {
  String get icon => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;
  String get unit => throw _privateConstructorUsedError;
  Color get backgroundColor => throw _privateConstructorUsedError;
  String get area => throw _privateConstructorUsedError;
  String get humidity => throw _privateConstructorUsedError;
  String get humidityUnit => throw _privateConstructorUsedError;
  String get pm25 => throw _privateConstructorUsedError;
  String get aqi => throw _privateConstructorUsedError;
  String get customBackgroundImage => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WeatherCardViewModelCopyWith<WeatherCardViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WeatherCardViewModelCopyWith<$Res> {
  factory $WeatherCardViewModelCopyWith(WeatherCardViewModel value,
          $Res Function(WeatherCardViewModel) then) =
      _$WeatherCardViewModelCopyWithImpl<$Res, WeatherCardViewModel>;
  @useResult
  $Res call(
      {String icon,
      String value,
      String unit,
      Color backgroundColor,
      String area,
      String humidity,
      String humidityUnit,
      String pm25,
      String aqi,
      String customBackgroundImage});
}

/// @nodoc
class _$WeatherCardViewModelCopyWithImpl<$Res,
        $Val extends WeatherCardViewModel>
    implements $WeatherCardViewModelCopyWith<$Res> {
  _$WeatherCardViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = null,
    Object? value = null,
    Object? unit = null,
    Object? backgroundColor = null,
    Object? area = null,
    Object? humidity = null,
    Object? humidityUnit = null,
    Object? pm25 = null,
    Object? aqi = null,
    Object? customBackgroundImage = null,
  }) {
    return _then(_value.copyWith(
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      unit: null == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as String,
      backgroundColor: null == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color,
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as String,
      humidity: null == humidity
          ? _value.humidity
          : humidity // ignore: cast_nullable_to_non_nullable
              as String,
      humidityUnit: null == humidityUnit
          ? _value.humidityUnit
          : humidityUnit // ignore: cast_nullable_to_non_nullable
              as String,
      pm25: null == pm25
          ? _value.pm25
          : pm25 // ignore: cast_nullable_to_non_nullable
              as String,
      aqi: null == aqi
          ? _value.aqi
          : aqi // ignore: cast_nullable_to_non_nullable
              as String,
      customBackgroundImage: null == customBackgroundImage
          ? _value.customBackgroundImage
          : customBackgroundImage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WeatherCardViewModelImplCopyWith<$Res>
    implements $WeatherCardViewModelCopyWith<$Res> {
  factory _$$WeatherCardViewModelImplCopyWith(_$WeatherCardViewModelImpl value,
          $Res Function(_$WeatherCardViewModelImpl) then) =
      __$$WeatherCardViewModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String icon,
      String value,
      String unit,
      Color backgroundColor,
      String area,
      String humidity,
      String humidityUnit,
      String pm25,
      String aqi,
      String customBackgroundImage});
}

/// @nodoc
class __$$WeatherCardViewModelImplCopyWithImpl<$Res>
    extends _$WeatherCardViewModelCopyWithImpl<$Res, _$WeatherCardViewModelImpl>
    implements _$$WeatherCardViewModelImplCopyWith<$Res> {
  __$$WeatherCardViewModelImplCopyWithImpl(_$WeatherCardViewModelImpl _value,
      $Res Function(_$WeatherCardViewModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = null,
    Object? value = null,
    Object? unit = null,
    Object? backgroundColor = null,
    Object? area = null,
    Object? humidity = null,
    Object? humidityUnit = null,
    Object? pm25 = null,
    Object? aqi = null,
    Object? customBackgroundImage = null,
  }) {
    return _then(_$WeatherCardViewModelImpl(
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      unit: null == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as String,
      backgroundColor: null == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color,
      area: null == area
          ? _value.area
          : area // ignore: cast_nullable_to_non_nullable
              as String,
      humidity: null == humidity
          ? _value.humidity
          : humidity // ignore: cast_nullable_to_non_nullable
              as String,
      humidityUnit: null == humidityUnit
          ? _value.humidityUnit
          : humidityUnit // ignore: cast_nullable_to_non_nullable
              as String,
      pm25: null == pm25
          ? _value.pm25
          : pm25 // ignore: cast_nullable_to_non_nullable
              as String,
      aqi: null == aqi
          ? _value.aqi
          : aqi // ignore: cast_nullable_to_non_nullable
              as String,
      customBackgroundImage: null == customBackgroundImage
          ? _value.customBackgroundImage
          : customBackgroundImage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$WeatherCardViewModelImpl implements _WeatherCardViewModel {
  const _$WeatherCardViewModelImpl(
      {required this.icon,
      required this.value,
      required this.unit,
      required this.backgroundColor,
      required this.area,
      required this.humidity,
      required this.humidityUnit,
      required this.pm25,
      required this.aqi,
      this.customBackgroundImage = ''});

  @override
  final String icon;
  @override
  final String value;
  @override
  final String unit;
  @override
  final Color backgroundColor;
  @override
  final String area;
  @override
  final String humidity;
  @override
  final String humidityUnit;
  @override
  final String pm25;
  @override
  final String aqi;
  @override
  @JsonKey()
  final String customBackgroundImage;

  @override
  String toString() {
    return 'WeatherCardViewModel(icon: $icon, value: $value, unit: $unit, backgroundColor: $backgroundColor, area: $area, humidity: $humidity, humidityUnit: $humidityUnit, pm25: $pm25, aqi: $aqi, customBackgroundImage: $customBackgroundImage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WeatherCardViewModelImpl &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.humidity, humidity) ||
                other.humidity == humidity) &&
            (identical(other.humidityUnit, humidityUnit) ||
                other.humidityUnit == humidityUnit) &&
            (identical(other.pm25, pm25) || other.pm25 == pm25) &&
            (identical(other.aqi, aqi) || other.aqi == aqi) &&
            (identical(other.customBackgroundImage, customBackgroundImage) ||
                other.customBackgroundImage == customBackgroundImage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      icon,
      value,
      unit,
      backgroundColor,
      area,
      humidity,
      humidityUnit,
      pm25,
      aqi,
      customBackgroundImage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WeatherCardViewModelImplCopyWith<_$WeatherCardViewModelImpl>
      get copyWith =>
          __$$WeatherCardViewModelImplCopyWithImpl<_$WeatherCardViewModelImpl>(
              this, _$identity);
}

abstract class _WeatherCardViewModel implements WeatherCardViewModel {
  const factory _WeatherCardViewModel(
      {required final String icon,
      required final String value,
      required final String unit,
      required final Color backgroundColor,
      required final String area,
      required final String humidity,
      required final String humidityUnit,
      required final String pm25,
      required final String aqi,
      final String customBackgroundImage}) = _$WeatherCardViewModelImpl;

  @override
  String get icon;
  @override
  String get value;
  @override
  String get unit;
  @override
  Color get backgroundColor;
  @override
  String get area;
  @override
  String get humidity;
  @override
  String get humidityUnit;
  @override
  String get pm25;
  @override
  String get aqi;
  @override
  String get customBackgroundImage;
  @override
  @JsonKey(ignore: true)
  _$$WeatherCardViewModelImplCopyWith<_$WeatherCardViewModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
