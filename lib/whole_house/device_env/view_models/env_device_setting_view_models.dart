import 'package:flutter/foundation.dart';
import 'package:smart_home/whole_house/device_env/env_device_constants.dart';

/// 环境设备设置列表视图模型
/// 包含按环境类型分组的设备列表
class EnvDeviceSettingListViewModel {
  final List<EnvDeviceSettingCategoryViewModel> list;
  
  const EnvDeviceSettingListViewModel({
    required this.list,
  });
  
  bool get isEmpty => list.isEmpty;
  
  bool get isNotEmpty => list.isNotEmpty;
  
  int get length => list.length;
  
  EnvDeviceSettingListViewModel copyWith({
    List<EnvDeviceSettingCategoryViewModel>? list,
  }) {
    return EnvDeviceSettingListViewModel(
      list: list ?? this.list,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is EnvDeviceSettingListViewModel &&
           listEquals(other.list, list);
  }
  
  @override
  int get hashCode => Object.hashAll(list);
  
  @override
  String toString() => 'EnvDeviceSettingListViewModel(categories: ${list.length})';
}

/// 环境设备设置分类视图模型
/// 表示一个环境类型（温度、湿度、空气质量）及其包含的设备列表
class EnvDeviceSettingCategoryViewModel {
  final String envName;
  final List<EnvDeviceSettingItemViewModel> items;
  
  const EnvDeviceSettingCategoryViewModel({
    required this.envName,
    required this.items,
  });
  
  /// 创建特定环境类型的分类
  factory EnvDeviceSettingCategoryViewModel.forType(String type, List<EnvDeviceSettingItemViewModel> items) {
    return EnvDeviceSettingCategoryViewModel(
      envName: EnvDeviceConstants.getDisplayName(type),
      items: items,
    );
  }
  
  bool get isEmpty => items.isEmpty;
  
  bool get isNotEmpty => items.isNotEmpty;
  
  int get length => items.length;
  
  /// 获取选中的设备
  EnvDeviceSettingItemViewModel? get selectedDevice {
    try {
      return items.firstWhere((EnvDeviceSettingItemViewModel item) => item.selected);
    } catch (_) {
      return null;
    }
  }
  
  EnvDeviceSettingCategoryViewModel copyWith({
    String? envName,
    List<EnvDeviceSettingItemViewModel>? items,
  }) {
    return EnvDeviceSettingCategoryViewModel(
      envName: envName ?? this.envName,
      items: items ?? this.items,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is EnvDeviceSettingCategoryViewModel &&
           other.envName == envName &&
           listEquals(other.items, items);
  }
  
  @override
  int get hashCode => Object.hash(
    envName,
    Object.hashAll(items),
  );
  
  @override
  String toString() => 'EnvDeviceSettingCategoryViewModel(envName: $envName, items: ${items.length})';
}

/// 环境设备设置项视图模型
/// 表示一个具体的环境设备项，用于列表展示和交互
class EnvDeviceSettingItemViewModel {
  final String deviceId;
  final String deviceName;
  final String deviceImage;
  final String roomName;
  final String floorName;
  final String reportedValue;
  final String unit;
  final bool isOnline;
  final bool selected;
  final String type;
  
  const EnvDeviceSettingItemViewModel({
    required this.deviceId,
    required this.deviceName,
    required this.roomName,
    required this.reportedValue,
    required this.unit,
    required this.type,
    this.deviceImage = '',
    this.floorName = '',
    this.isOnline = true,
    this.selected = false,
  });
  
  
  EnvDeviceSettingItemViewModel copyWith({
    String? deviceId,
    String? deviceName,
    String? deviceImage,
    String? roomName,
    String? floorName,
    String? reportedValue,
    String? unit,
    bool? isOnline,
    bool? selected,
    String? type,
  }) {
    return EnvDeviceSettingItemViewModel(
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      deviceImage: deviceImage ?? this.deviceImage,
      roomName: roomName ?? this.roomName,
      floorName: floorName ?? this.floorName,
      reportedValue: reportedValue ?? this.reportedValue,
      unit: unit ?? this.unit,
      isOnline: isOnline ?? this.isOnline,
      selected: selected ?? this.selected,
      type: type ?? this.type,
    );
  }
  
  /// 切换选中状态
  EnvDeviceSettingItemViewModel toggleSelected() {
    return copyWith(selected: !selected);
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is EnvDeviceSettingItemViewModel &&
           other.deviceId == deviceId &&
           other.deviceName == deviceName &&
           other.deviceImage == deviceImage &&
           other.roomName == roomName &&
           other.floorName == floorName &&
           other.reportedValue == reportedValue &&
           other.unit == unit &&
           other.isOnline == isOnline &&
           other.selected == selected &&
           other.type == type;
  }
  
  @override
  int get hashCode => Object.hash(
    deviceId,
    deviceName,
    deviceImage,
    roomName,
    floorName,
    reportedValue,
    unit,
    isOnline,
    selected,
    type,
  );
  
  @override
  String toString() => 'EnvDeviceSettingItemViewModel(deviceName: $deviceName, type: $type, selected: $selected)';
}
