import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';

/// 环境设备相关常量
class EnvDeviceConstants {
  static const String TYPE_TEMPERATURE = 'temperature';
  static const String TYPE_HUMIDITY = 'humidity';
  static const String TYPE_PM25 = 'pm25';
  
  static const Map<String, String> TYPE_DISPLAY_NAMES = <String, String>{
    TYPE_TEMPERATURE: '温度',
    TYPE_HUMIDITY: '湿度',
    TYPE_PM25: AggregationDeviceConstant.aggEnvLabelDescPm25,
  };
  
  static const Map<String, String> TYPE_UNITS = <String, String>{
    TYPE_TEMPERATURE: '°C',
    TYPE_HUMIDITY: '%',
    TYPE_PM25: '',
  };
  
  static const Map<String, String> TYPE_ICONS = <String, String>{
    TYPE_TEMPERATURE: 'assets/images/env_device/temperature.webp',
    TYPE_HUMIDITY: 'assets/images/env_device/humidity.webp',
    TYPE_PM25: 'assets/images/env_device/pm2_5.webp',
  };
  
  static const String DEFAULT_ICON = 'assets/images/env_device/default.webp';
  
  static String getDisplayName(String type) {
    return TYPE_DISPLAY_NAMES[type.toLowerCase()] ?? type;
  }
  
  static String getUnit(String type) {
    return TYPE_UNITS[type.toLowerCase()] ?? '';
  }
  
  static String getIcon(String type) {
    return TYPE_ICONS[type.toLowerCase()] ?? DEFAULT_ICON;
  }
  
  static List<String> getAllTypes() {
    return <String>[TYPE_TEMPERATURE, TYPE_HUMIDITY, TYPE_PM25];
  }
} 
