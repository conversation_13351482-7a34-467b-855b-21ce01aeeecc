import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/whole_house/device_env/services/env_devices_response_model.dart';

import 'utils.dart';

/// 环境设备状态模型，包含环境设备相关信息
class EnvDeviceState {
  /// 空间环境信息映射表，键为空间ID，值为对应的空间环境模型
  final Map<String, SpaceEnvironmentModel> spaces;

  /// 构造函数
  /// 
  /// 接收空间环境模型列表，并自动转换为以空间ID为键的映射表
  EnvDeviceState({
    List<SpaceEnvironmentModel> spacesList = const <SpaceEnvironmentModel>[],
  }) : spaces = _convertToMap(spacesList);
  
  /// 将空间环境模型列表转换为映射表
  static Map<String, SpaceEnvironmentModel> _convertToMap(List<SpaceEnvironmentModel> spacesList) {
    final Map<String, SpaceEnvironmentModel> result = <String, SpaceEnvironmentModel>{};
    for (final SpaceEnvironmentModel space in spacesList) {
      if (space.spaceId.isNotEmpty) {
        result[space.spaceId] = space;
      }
    }
    return result;
  }

  factory EnvDeviceState.fromJson(Map<String, dynamic> json) {
    final List<SpaceEnvironmentModel> spacesList = <SpaceEnvironmentModel>[];
    
    final List<dynamic> spacesJson = json.listValueForKey('spaces', <dynamic>[]);
    for (final dynamic spaceJson in spacesJson) {
      if (spaceJson is Map<dynamic, dynamic>) {
        final String spaceId = spaceJson.stringValueForKey('spaceId', '');
        if (spaceId.isNotEmpty) {
          spacesList.add(SpaceEnvironmentModel(
            spaceId: spaceId,
          ));
        }
      }
    }
    
    return EnvDeviceState(
      spacesList: spacesList,
    );
  }

  Map<String, dynamic> toJson() {
    final List<Map<String, String>> spacesList = spaces.entries
        .map<Map<String, String>>((MapEntry<String, SpaceEnvironmentModel> entry) => 
            <String, String>{'spaceId': entry.key})
        .toList();
    
    return <String, dynamic>{
      'spaces': spacesList,
    };
  }

  EnvDeviceState copyWith({
    List<SpaceEnvironmentModel>? spacesList,
  }) {
    return EnvDeviceState(
      spacesList: spacesList ?? this.spacesList,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnvDeviceState &&
        mapEquals(other.spaces, spaces);
  }

  @override
  int get hashCode => Object.hashAll(spaces.entries);

  @override
  String toString() {
    final StringBuffer buffer = StringBuffer('EnvDeviceState{\n');
    spaces.forEach((String key, SpaceEnvironmentModel value) {
      buffer.write('  $key: $value,\n');
    });
    buffer.write('}');
    return buffer.toString();
  }
  
  List<SpaceEnvironmentModel> get spacesList => spaces.values.toList();
  
  SpaceEnvironmentModel? getSpace(String spaceId) => spaces[spaceId];
  
  bool get isEmpty => spaces.isEmpty;
  
  bool get isNotEmpty => spaces.isNotEmpty;
  
  int get length => spaces.length;
  
  /// 判断指定空间是否有在线的环境设备，三种设备都要在线
  /// 
  /// 如果该空间的温度、湿度、PM2.5三种环境设备中，
  /// 至少有一种设备存在且在线，则返回true
  bool hasEnvDevice(String spaceId) {
    final SpaceEnvironmentModel? space = getSpace(spaceId);
    if (space == null) {
      return false;
    }
    
    final bool hasTemperature = space.temperature.any((EnvDeviceItemModel device) => device.isOnline);
    
    final bool hasHumidity = space.humidity.any((EnvDeviceItemModel device) => device.isOnline);
    
    final bool hasPM25 = space.pm25.any((EnvDeviceItemModel device) => device.isOnline);
    
    return hasTemperature && hasHumidity && hasPM25;
  }
  
  /// 判断是否有任意空间拥有环境设备
  bool get hasAnyEnvDevice => spaces.keys.any((String spaceId) => hasEnvDevice(spaceId));
  
  /// 判断指定空间是否有在线且被选中的环境设备
  /// 任一种类型的设备存在且在线且选中，返回true
  bool hasAnyOnlineDevice(String spaceId) {
    final SpaceEnvironmentModel? space = getSpace(spaceId);
    if (space == null) {
      return false;
    }
    return anyDeviceOnlineAndSelected(space.temperature) ||
        anyDeviceOnlineAndSelected(space.humidity) ||
        anyDeviceOnlineAndSelected(space.pm25);
  }

  /// 判断是否有所有空间拥有在线且被选中的环境设备
  bool get hasAnyOnlineDeviceAllRoom {
    return spaces.values.any((SpaceEnvironmentModel space) =>
        anyDeviceOnlineAndSelected(space.temperature) ||
        anyDeviceOnlineAndSelected(space.humidity) ||
        anyDeviceOnlineAndSelected(space.pm25));
  }
  
  /// 判断特定空间下是否有设备不满足"选中且在线"的条件
  /// 
  /// [spaceId] 需要检查的空间ID
  /// 
  /// 如果该空间有任何设备满足"未选中"或"离线"条件，返回true
  /// 如果该空间所有设备都"选中且在线"或没有设备，返回false
  bool hasAnyDeviceNotSelectedOrOffline(String spaceId) {
    final SpaceEnvironmentModel? space = getSpace(spaceId);
    
    if (space == null) {
      return false;
    }
    
    // 收集所有设备
    final List<EnvDeviceItemModel> allDevices = <EnvDeviceItemModel>[
      ...space.temperature,
      ...space.humidity,
      ...space.pm25
    ];
    
    if (allDevices.isEmpty) {
      return false;
    }
    
    // 检查是否有任何设备未选中或离线（"选中且在线"的补集）
    return allDevices.any((EnvDeviceItemModel device) => 
        !device.selected || !device.isOnline
    );
  }
  
  /// 判断特定空间下是否有任何环境设备
  /// 
  /// [spaceId] 需要检查的空间ID
  /// 
  /// 如果该空间至少有一个环境设备（包括离线设备），返回true
  /// 如果该空间没有任何环境设备，返回false
  bool hasAnyEnvDevices(String spaceId) {
    final SpaceEnvironmentModel? space = getSpace(spaceId);
    
    if (space == null) {
      return false;
    }
    
    return space.temperature.isNotEmpty || 
           space.humidity.isNotEmpty || 
           space.pm25.isNotEmpty;
  }
}
