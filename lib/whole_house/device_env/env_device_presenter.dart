import 'package:smart_home/store/smart_home_store.dart';

import 'actions/env_devices_actions.dart';
import 'services/env_devices_response_model.dart';
import 'services/env_devices_service.dart';
import 'services/env_devices_service_provider.dart';

/// 环境设备Presenter
///
/// 负责获取环境设备数据并更新到Store
class EnvDevicePresenter {
  static final EnvDevicePresenter _instance = EnvDevicePresenter._internal();

  factory EnvDevicePresenter() => _instance;

  EnvDevicePresenter._internal();

  /// 获取全屋环境设备数据
  Future<void> getWholeHouseEnvDeviceData() async {
    final String familyId = smartHomeStore.state.familyState.familyId;

    // 获取环境设备服务
    final EnvDevicesService service =
        EnvDevicesServiceProvider.instance.getService();

    final EnvDevicesData? envDevicesData =
        await service.getWholeHouseEnvDeviceSettingData(familyId);

    // 更新Store
    if (envDevicesData != null) {
      smartHomeStore.dispatch(
          UpdateWholeHouseEnvDeviceStateAction(envDevicesData.spaces));
    }
  }

  /// 获取全屋环境设备数据(使用协调器)
  Future<void> getWholeHouseEnvDeviceDataWithCoordinator(String familyId) async {

    // 获取环境设备服务
    final EnvDevicesService service =
        EnvDevicesServiceProvider.instance.getService();

    final EnvDevicesData? envDevicesData =
        await service.getWholeHouseEnvDeviceSettingData(familyId);

    if (envDevicesData != null) {
      smartHomeStore.dispatch(
          UpdateWholeHouseEnvDeviceStateAction(envDevicesData.spaces));
    }
  }
}
