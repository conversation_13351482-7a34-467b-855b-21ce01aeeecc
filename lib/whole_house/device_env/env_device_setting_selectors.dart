import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/env_device_constants.dart';
import 'package:smart_home/whole_house/device_env/env_device_state.dart';
import 'package:smart_home/whole_house/device_env/services/env_devices_response_model.dart';
import 'package:smart_home/whole_house/device_env/view_models/env_device_setting_view_models.dart';

/// 环境设备设置选择器
/// 
/// 用于从Redux状态中提取视图模型数据
class EnvDeviceSettingSelectors {
  /// 选择特定空间的环境设备设置列表视图模型
  static Selector<SmartHomeState, EnvDeviceSettingListViewModel> createSelectEnvDeviceSettingListViewModel(String spaceId) {
    return createSelector2(
      (SmartHomeState state) => state.wholeHouseState.envDeviceState,
      (SmartHomeState _) => spaceId,
      _createEnvDeviceSettingListViewModel,
    );
  }
  
  static EnvDeviceSettingListViewModel _createEnvDeviceSettingListViewModel(
    EnvDeviceState envDeviceState,
    String spaceId
  ) {
    final SpaceEnvironmentModel? spaceModel = envDeviceState.getSpace(spaceId);
    
    if (spaceModel == null) {
      return const EnvDeviceSettingListViewModel(list: <EnvDeviceSettingCategoryViewModel>[]);
    }
    
    final List<EnvDeviceSettingCategoryViewModel> categoryList = <EnvDeviceSettingCategoryViewModel>[];
    
    if (spaceModel.temperature.isNotEmpty) {
      categoryList.add(_createCategoryViewModel(EnvDeviceConstants.TYPE_TEMPERATURE, spaceModel.temperature));
    }
    
    if (spaceModel.humidity.isNotEmpty) {
      categoryList.add(_createCategoryViewModel(EnvDeviceConstants.TYPE_HUMIDITY, spaceModel.humidity));
    }
    
    if (spaceModel.pm25.isNotEmpty) {
      categoryList.add(_createCategoryViewModel(EnvDeviceConstants.TYPE_PM25, spaceModel.pm25));
    }
    
    return EnvDeviceSettingListViewModel(list: categoryList);
  }
  
  static EnvDeviceSettingCategoryViewModel _createCategoryViewModel(
    String type,
    List<EnvDeviceItemModel> devices
  ) {
    final List<EnvDeviceSettingItemViewModel> itemList = devices.map((EnvDeviceItemModel device) {
      final String unit = EnvDeviceConstants.getUnit(type);
      
      return EnvDeviceSettingItemViewModel(
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        roomName: device.roomName,
        floorName: device.floorName,
        reportedValue: device.reportedValue,
        unit: unit,
        type: type,
        deviceImage: device.cardPageImg,
        isOnline: device.isOnline,
        selected: device.selected,
      );
    }).toList();
    
    return EnvDeviceSettingCategoryViewModel.forType(type, itemList);
  }
}
