import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/actions/env_devices_actions.dart';
import 'package:smart_home/whole_house/device_env/view_models/env_device_setting_view_models.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import 'env_device_constants.dart';

/// 列表组件WholeHouseListCheckItemWidget(环境设备)
///
/// 用于显示环境设备设置页面中的设备项
/// 包含设备图片、设备名称、房间名称、环境值及单位、选中状态
/// 支持显示离线状态，使用单选控件(Radio)
class WholeHouseListCheckItemWidget extends StatelessWidget {
  const WholeHouseListCheckItemWidget({
    super.key,
    this.deviceId = '',
    this.deviceImage = '',
    this.deviceName = '',
    this.roomName = '',
    this.floorName = '',
    this.value = '',
    this.unit = '',
    this.checked = false,
    this.offline = false,
    this.deviceType = '',
    this.spaceId = '',
    this.hideTopRadius = false,
    this.hideBottomRadius = false,
    required this.onChanged,
  });

  factory WholeHouseListCheckItemWidget.fromViewModel({
    Key? key,
    required BuildContext context,
    required EnvDeviceSettingItemViewModel viewModel,
    String spaceId = '',
    required bool isWholeHouse,//当前房间是否为全屋
    bool hideTopRadius = false,
    bool hideBottomRadius = false,
  }) {
    return WholeHouseListCheckItemWidget(
      key: key,
      deviceId: viewModel.deviceId,
      deviceImage: viewModel.deviceImage,
      deviceName: viewModel.deviceName,
      roomName: viewModel.roomName,
      floorName: viewModel.floorName,
      value: viewModel.reportedValue,
      unit: viewModel.unit,
      checked: viewModel.selected,
      offline: !viewModel.isOnline,
      deviceType: viewModel.type,
      spaceId: spaceId,
      hideTopRadius: hideTopRadius,
      hideBottomRadius: hideBottomRadius,
      onChanged: (bool selected) {
        gioTrack(GioConst.wholeHouseEnvSettings, <String, String>{
          'page_type': isWholeHouse ? '全屋环境管理页' : '房间环境管理页',
        });
        // 分发Action到Redux Store
        final Store<SmartHomeState> store =
            StoreProvider.of<SmartHomeState>(context);
        store.dispatch(SetWholeHouseEnvDeviceSettingAction(
          deviceId: viewModel.deviceId,
          selected: selected,
          deviceType: viewModel.type,
          spaceId: spaceId,
        ));
      },
    );
  }

  final String deviceId;
  final String deviceImage;
  final String deviceName;
  final String roomName;
  final String floorName;
  final String value;
  final String unit;
  final bool checked;
  final bool offline;
  final String deviceType;
  final String spaceId;
  final bool hideTopRadius;
  final bool hideBottomRadius;
  final ValueChanged<bool> onChanged;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        // 只在未选中状态时才触发变更事件
        if (!checked) {
          onChanged(true);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        height: 72,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(hideTopRadius ? 0 : 16),
            topRight: Radius.circular(hideTopRadius ? 0 : 16),
            bottomLeft: Radius.circular(hideBottomRadius ? 0 : 16),
            bottomRight: Radius.circular(hideBottomRadius ? 0 : 16),
          ),
        ),
        child: Row(
          children: <Widget>[
            _buildDeviceImage(),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  _buildDeviceInfo(),
                  const SizedBox(height: 2),
                  _buildRoomOfflineStatus(),
                ],
              ),
            ),
            const SizedBox(height: 4),
            _ValueUnitWidget(
              value: value,
              unit: unit,
              offline: offline,
              deviceType: deviceType,
            ),
            const SizedBox(width: 8),
            _CircularCheckboxWidget(checked: checked),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceImage() {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: SizedBox(
          width: 40,
          height: 40,
          child: AspectRatio(
              aspectRatio: 1,
              child: CommonNetworkRefreshImg(
                imageUrl: deviceImage,
                alignment: Alignment.center,
                errorWidget: Image.asset(
                  'assets/icons/default_device_img.webp',
                  package: 'smart_home',
                  height: double.infinity,
                ),
              ))),
    );
  }

  Widget _buildDeviceInfo() {
    return Row(
      children: <Widget>[
        Expanded(
          child: Opacity(
            opacity: offline ? 0.39 : 1.0,
            child: Text(
              deviceName,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF111111),
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
          ),
        ),
        const SizedBox(width: 4),
      ],
    );
  }

  Widget _buildRoomOfflineStatus() {
    
    // 如果value为空，则不显示副标题
    if (value.isEmpty) {
      return const SizedBox.shrink();
    }

    String displayText = '$floorName$roomName';
    
    // 如果离线，添加离线状态
    if (offline) {
      displayText = '$displayText | 离线';
    }

    return Padding(
      padding: const EdgeInsets.only(top: 0),
      child: Opacity(
        opacity: offline ? 0.39 : 1.0,
        child: Text(
          displayText,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF666666),
            fontFamilyFallback: fontFamilyFallback(),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

/// 环境值及单位
class _ValueUnitWidget extends StatelessWidget {
  const _ValueUnitWidget({
    super.key,
    required this.value,
    required this.unit,
    this.offline = false,
    this.deviceType = '',
  });

  final String value;
  final String unit;
  final bool offline;
  final String deviceType;

  @override
  Widget build(BuildContext context) {
    const Color textColor = Color(0xFF111111);

    // 离线状态下不显示值和单位
    if (offline) {
      return const SizedBox.shrink();
    }

    return Text.rich(
      TextSpan(
        children: <InlineSpan>[
          TextSpan(
            text: value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
              color: textColor,
              fontFamilyFallback: fontFamilyFallback(),
            ),
          ),
          // 仅在不是pm25类型且单位不为空时显示单位
          if (deviceType != EnvDeviceConstants.TYPE_PM25 && unit.isNotEmpty)
            const WidgetSpan(child: SizedBox(width: 4)),
          if (deviceType != EnvDeviceConstants.TYPE_PM25 && unit.isNotEmpty)
            TextSpan(
              text: unit,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: textColor,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
        ],
      ),
    );
  }
}

/// 一个圆形checkbox
class _CircularCheckboxWidget extends StatelessWidget {
  const _CircularCheckboxWidget({super.key, this.checked = false});

  final bool checked;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: 24.w,
        height: 24.w,
        child: Image.asset(
          checked
              ? 'assets/images/env_device/icon_env_device_selected.webp'
              : 'assets/images/env_device/icon_env_device_unselected.webp',
          package: SmartHomeConstant.package,
        ));
  }
}
