import 'package:device_utils/typeId_parse/template_map.dart';

/// 环境信息偏好设置请求模型
/// 用于向服务器发送用户选择的环境设备偏好
class EnvDevicePreferenceRequestModel {
  /// 家庭ID
  final String familyId;
  
  /// 空间ID
  final String spaceId;
  
  /// 选中的温度设备ID（若未选择则为null）
  final String? temperature;
  
  /// 选中的湿度设备ID（若未选择则为null）
  final String? humidity;
  
  /// 选中的PM2.5设备ID（若未选择则为null）
  final String? pm25;
  
  /// 构造函数
  const EnvDevicePreferenceRequestModel({
    required this.familyId,
    required this.spaceId,
    this.temperature,
    this.humidity,
    this.pm25,
  });
  
  /// 从JSON创建请求模型
  factory EnvDevicePreferenceRequestModel.fromJson(Map<dynamic, dynamic> json) {
    final String temperatureId = json.stringValueForKey('temperature', '');
    final String humidityId = json.stringValueForKey('humidity', '');
    final String pm25Id = json.stringValueForKey('pm25', '');
    
    return EnvDevicePreferenceRequestModel(
      familyId: json.stringValueForKey('familyId', ''),
      spaceId: json.stringValueForKey('spaceId', ''),
      temperature: temperatureId.isEmpty ? null : temperatureId,
      humidity: humidityId.isEmpty ? null : humidityId,
      pm25: pm25Id.isEmpty ? null : pm25Id,
    );
  }
  
  /// 转换为JSON格式用于API请求
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'familyId': familyId,
      'spaceId': spaceId,
    };
    
    // 仅当设备ID有效时添加到请求中
    _addIfNotEmpty(json, 'temperature', temperature);
    _addIfNotEmpty(json, 'humidity', humidity);
    _addIfNotEmpty(json, 'pm25', pm25);
    
    return json;
  }
  
  /// 辅助方法：当值非空且非空字符串时添加到JSON
  void _addIfNotEmpty(Map<String, dynamic> json, String key, String? value) {
    if (value != null && value.isNotEmpty) {
      json[key] = value;
    }
  }
  
  @override
  String toString() {
    return 'EnvDevicePreferenceRequestModel{'
      'familyId: $familyId, '
      'spaceId: $spaceId, '
      'temperature: $temperature, '
      'humidity: $humidity, '
      'pm25: $pm25'
    '}';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is EnvDevicePreferenceRequestModel &&
           other.familyId == familyId &&
           other.spaceId == spaceId &&
           other.temperature == temperature &&
           other.humidity == humidity &&
           other.pm25 == pm25;
  }
  
  @override
  int get hashCode => Object.hash(
    familyId,
    spaceId,
    temperature,
    humidity,
    pm25,
  );
} 
