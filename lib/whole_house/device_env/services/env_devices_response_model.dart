import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/whole_house/device_env/env_device_constants.dart';
import 'package:upservice/model/uhome_response_model.dart';

/// 环境设备接口响应模型
class EnvDevicesResponseModel extends UhomeResponseModel {

  EnvDevicesData environmentData = EnvDevicesData.fromJson(<dynamic, dynamic>{});


  EnvDevicesResponseModel.fromJson(super.data) : super.fromJson() {
      environmentData = EnvDevicesData.fromJson(super.retData);
  }

  @override
  String toString() => 'EnvDevicesResponseModel{environmentData: $environmentData}';
}

/// 环境数据模型
class EnvDevicesData {
  List<SpaceEnvironmentModel> spaces = <SpaceEnvironmentModel>[];

  EnvDevicesData.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> spacesJson = json.listValueForKey('spaces', <dynamic>[]);
    
    spaces = spacesJson
    .whereType<Map<dynamic, dynamic>>()
    .map((Map<dynamic, dynamic> item) => _createSpaceModel(item))
    .toList();
  }
  
  // 从JSON数据创建空间环境模型
  SpaceEnvironmentModel _createSpaceModel(Map<dynamic, dynamic> spaceJson) {
    final String spaceId = spaceJson.stringValueForKey('spaceId', '');
    final SpaceEnvironmentModel space = SpaceEnvironmentModel(spaceId: spaceId);
    
    final Map<String, List<dynamic>> devicesByType = _getDevicesByType(spaceJson);
    _processDevicesByType(devicesByType, space);
    
    return space;
  }
  
  // 从JSON获取按类型分组的设备列表
  Map<String, List<dynamic>> _getDevicesByType(Map<dynamic, dynamic> spaceJson) {
    return <String, List<dynamic>>{
      EnvDeviceConstants.TYPE_TEMPERATURE: spaceJson.listValueForKey(
          EnvDeviceConstants.TYPE_TEMPERATURE, <dynamic>[]),
      EnvDeviceConstants.TYPE_HUMIDITY: spaceJson.listValueForKey(
          EnvDeviceConstants.TYPE_HUMIDITY, <dynamic>[]),
      EnvDeviceConstants.TYPE_PM25: spaceJson.listValueForKey(
          EnvDeviceConstants.TYPE_PM25, <dynamic>[]),
    };
  }
  
  // 处理按类型分组的设备并添加到空间模型中
  void _processDevicesByType(
      Map<String, List<dynamic>> devicesByType, SpaceEnvironmentModel space) {
    devicesByType.forEach((String deviceType, List<dynamic> devicesJson) {
      final List<EnvDeviceItemModel> devices = devicesJson
          .whereType<Map<dynamic, dynamic>>()
          .map((Map<dynamic, dynamic> item) => EnvDeviceItemModel.fromJson(item))
          .toList();
          
      for (final EnvDeviceItemModel device in devices) {
        _assignDeviceToCategory(device, deviceType, space);
      }
    });
  }
  
  // 根据设备类型或设备ID将设备分配到相应类别
  void _assignDeviceToCategory(
      EnvDeviceItemModel device, String deviceType, SpaceEnvironmentModel space) {
    switch (deviceType) {
      case EnvDeviceConstants.TYPE_TEMPERATURE:
        space.temperature.add(device);
      case EnvDeviceConstants.TYPE_HUMIDITY:
        space.humidity.add(device);
      case EnvDeviceConstants.TYPE_PM25:
        space.pm25.add(device);
      default:
        _assignDeviceByIdPattern(device, space);
    }
  }
  
  // 根据设备ID特征判断设备类型并分配
  void _assignDeviceByIdPattern(EnvDeviceItemModel device, SpaceEnvironmentModel space) {
    final String deviceId = device.deviceId.toLowerCase();
    
    if (deviceId.contains('temp')) {
      space.temperature.add(device);
    } else if (deviceId.contains('humid')) {
      space.humidity.add(device);
    } else if (deviceId.contains('pm25') || deviceId.contains('pm2.5')) {
      space.pm25.add(device);
    } else {
      space.temperature.add(device);
    }
  }

  @override
  String toString() {
    final StringBuffer buffer = StringBuffer('EnvDevicesData {\n');
    buffer.write('  spaces: [\n');
    
    for (int i = 0; i < spaces.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${spaces[i].toString()}');
    }
    
    buffer.write('\n  ]\n}');
    return buffer.toString();
  }
}

/// 房间环境信息
class SpaceEnvironmentModel {
  final String spaceId;
  final List<EnvDeviceItemModel> temperature;
  final List<EnvDeviceItemModel> humidity;
  final List<EnvDeviceItemModel> pm25;
  
  SpaceEnvironmentModel({
    required this.spaceId,
    List<EnvDeviceItemModel>? temperature,
    List<EnvDeviceItemModel>? humidity,
    List<EnvDeviceItemModel>? pm25,
  }) : 
    temperature = temperature ?? <EnvDeviceItemModel>[],
    humidity = humidity ?? <EnvDeviceItemModel>[],
    pm25 = pm25 ?? <EnvDeviceItemModel>[];

  factory SpaceEnvironmentModel.fromJson(Map<dynamic, dynamic> json) {
    return SpaceEnvironmentModel(
      spaceId: json.stringValueForKey('spaceId', ''),
      temperature: json
          .listValueForKey('temperature', <dynamic>[])
          .map((dynamic e) => EnvDeviceItemModel.fromJson(
              Map<String, dynamic>.from(e as Map<dynamic, dynamic>)))
          .toList(),
      humidity: json
          .listValueForKey('humidity', <dynamic>[])
          .map((dynamic e) => EnvDeviceItemModel.fromJson(
              Map<String, dynamic>.from(e as Map<dynamic, dynamic>)))
          .toList(),
      pm25: json
          .listValueForKey('pm25', <dynamic>[])
          .map((dynamic e) => EnvDeviceItemModel.fromJson(
              Map<String, dynamic>.from(e as Map<dynamic, dynamic>)))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['spaceId'] = spaceId;
    data['temperature'] = temperature;
    data['humidity'] = humidity;
    data['pm25'] = pm25;
    return data;
  }
    
  @override
  String toString() {
    final StringBuffer buffer = StringBuffer('SpaceEnvironmentModel {\n');
    buffer.write('  spaceId: $spaceId,\n');
    
    // 打印温度设备详情
    buffer.write('  temperature: [\n');
    for (int i = 0; i < temperature.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${temperature[i].toString()}');
    }
    buffer.write('\n  ],\n');
    
    // 打印湿度设备详情
    buffer.write('  humidity: [\n');
    for (int i = 0; i < humidity.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${humidity[i].toString()}');
    }
    buffer.write('\n  ],\n');
    
    // 打印PM2.5设备详情
    buffer.write('  pm25: [\n');
    for (int i = 0; i < pm25.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${pm25[i].toString()}');
    }
    buffer.write('\n  ]\n}');
    
    return buffer.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SpaceEnvironmentModel &&
        other.spaceId == spaceId &&
        listEquals(other.temperature, temperature) &&
        listEquals(other.humidity, humidity) &&
        listEquals(other.pm25, pm25);
  }

  @override
  int get hashCode => Object.hash(
        spaceId,
        Object.hashAll(temperature),
        Object.hashAll(humidity),
        Object.hashAll(pm25),
      );
}

/// 环境设备数据模型
class EnvDeviceItemModel {
  final bool selected;
  final String deviceId;
  final String deviceName;
  final String roomName;
  final String floorName;
  final String cardPageImg;
  final String reportedValue;
  final bool isOnline;

  EnvDeviceItemModel({
    this.selected = false,
    required this.deviceId,
    required this.deviceName,
    required this.roomName,
    this.floorName = '',
    this.cardPageImg = '',
    required this.reportedValue,
    this.isOnline = false,
  });

  /// 格式化上报值，四舍五入并只保留整数部分
  static String formatReportedValue(String value) {

    final double? doubleValue = double.tryParse(value);
    
    // 如果解析成功，进行四舍五入并转为整数
    if (doubleValue != null) {
      final int roundedValue = doubleValue.round();
      return roundedValue.toString();
    }
    
    return value;
  }

  factory EnvDeviceItemModel.fromJson(Map<dynamic, dynamic> json) {

    final String reportedValue = formatReportedValue(
      json.stringValueForKey('reportedValue', '')
    );
    
    return EnvDeviceItemModel(
      selected: json.boolValueForKey('selected', false),
      deviceId: json.stringValueForKey('deviceId', ''),
      deviceName: json.stringValueForKey('deviceName', ''),
      roomName: json.stringValueForKey('roomName', ''),
      floorName: json.stringValueForKey('floorName', ''),
      cardPageImg: json.stringValueForKey('cardPageImg', ''),
      reportedValue: reportedValue,
      isOnline: json.boolValueForKey('isOnline', false),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['selected'] = selected;
    data['deviceId'] = deviceId;
    data['deviceName'] = deviceName;
    data['roomName'] = roomName;
    data['floorName'] = floorName;
    data['cardPageImg'] = cardPageImg;
    data['reportedValue'] = reportedValue;
    data['isOnline'] = isOnline;
    return data;
  }
  
  @override
  String toString() => 'EnvDeviceItemModel{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    reportedValue: $reportedValue,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnvDeviceItemModel &&
        other.deviceId == deviceId &&
        other.deviceName == deviceName &&
        other.roomName == roomName &&
        other.floorName == floorName &&
        other.cardPageImg == cardPageImg &&
        other.reportedValue == reportedValue &&
        other.isOnline == isOnline &&
        other.selected == selected;
  }

  @override
  int get hashCode => Object.hash(
        deviceId,
        deviceName,
        roomName,
        floorName,
        cardPageImg,
        reportedValue,
        isOnline,
        selected,
      );
}

class TemperatureDevice extends EnvDeviceItemModel {
  TemperatureDevice({
    required super.deviceId,
    required super.roomName,
    required super.reportedValue,
    required super.deviceName,
    super.selected,
    super.floorName,
    super.cardPageImg,
    super.isOnline,
  });
  
  factory TemperatureDevice.fromJson(Map<dynamic, dynamic> json) {
    // 获取并格式化reportedValue
    final String reportedValue = EnvDeviceItemModel.formatReportedValue(
      json.stringValueForKey('reportedValue', '')
    );
    
    return TemperatureDevice(
      selected: json.boolValueForKey('selected', false),
      deviceId: json.stringValueForKey('deviceId', ''),
      deviceName: json.stringValueForKey('deviceName', ''),
      roomName: json.stringValueForKey('roomName', ''),
      floorName: json.stringValueForKey('floorName', ''),
      cardPageImg: json.stringValueForKey('cardPageImg', ''),
      reportedValue: reportedValue,
      isOnline: json.boolValueForKey('isOnline', false),
    );
  }
  
  double get temperature => double.tryParse(reportedValue) ?? 0.0;
  
  @override
  String toString() => 'TemperatureDevice{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    temperature: $reportedValue°C,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';
}

class HumidityDevice extends EnvDeviceItemModel {
  HumidityDevice({
    required super.deviceId,
    required super.roomName,
    required super.reportedValue,
    required super.deviceName,
    super.selected,
    super.floorName,
    super.cardPageImg,
    super.isOnline, 
  });
  
  factory HumidityDevice.fromJson(Map<dynamic, dynamic> json) {
    // 获取并格式化reportedValue
    final String reportedValue = EnvDeviceItemModel.formatReportedValue(
      json.stringValueForKey('reportedValue', '')
    );
    
    return HumidityDevice(
      selected: json.boolValueForKey('selected', false),
      deviceId: json.stringValueForKey('deviceId', ''),
      deviceName: json.stringValueForKey('deviceName', ''),
      roomName: json.stringValueForKey('roomName', ''),
      floorName: json.stringValueForKey('floorName', ''),
      cardPageImg: json.stringValueForKey('cardPageImg', ''),
      reportedValue: reportedValue,
      isOnline: json.boolValueForKey('isOnline', false),
    );
  }
  
  int get humidity => int.tryParse(reportedValue) ?? 0;
  
  @override
  String toString() => 'HumidityDevice{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    humidity: $reportedValue%,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';
}

class PM25Device extends EnvDeviceItemModel {
  PM25Device({
    required super.deviceId,
    required super.roomName,
    required super.reportedValue,
    required super.deviceName,
    super.selected,
    super.floorName,
    super.cardPageImg,
    super.isOnline,
  });
  
  factory PM25Device.fromJson(Map<dynamic, dynamic> json) {
    // 获取并格式化reportedValue
    final String reportedValue = EnvDeviceItemModel.formatReportedValue(
      json.stringValueForKey('reportedValue', '')
    );
    
    return PM25Device(
      selected: json.boolValueForKey('selected', false),
      deviceId: json.stringValueForKey('deviceId', ''),
      deviceName: json.stringValueForKey('deviceName', ''),
      roomName: json.stringValueForKey('roomName', ''),
      floorName: json.stringValueForKey('floorName', ''),
      cardPageImg: json.stringValueForKey('cardPageImg', ''),
      reportedValue: reportedValue,
      isOnline: json.boolValueForKey('isOnline', false),
    );
  }
  
  int get pm25 => int.tryParse(reportedValue) ?? 0;
  
  @override
  String toString() => 'PM25Device{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    pm25: $reportedValue μg/m³,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';
} 
