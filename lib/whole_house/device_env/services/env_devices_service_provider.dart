import 'env_devices_service.dart';
import 'real_env_devices_service.dart' as real;

/// 环境设备服务提供者
class EnvDevicesServiceProvider {
  
  EnvDevicesServiceProvider._();
  
  static final EnvDevicesServiceProvider _instance = EnvDevicesServiceProvider._();
  
  static EnvDevicesServiceProvider get instance => _instance;
  
  /// 删除mock数据,只返回真实数据源
  EnvDevicesService getService() {
      return real.RealEnvironmentService.instance;
  }
} 
