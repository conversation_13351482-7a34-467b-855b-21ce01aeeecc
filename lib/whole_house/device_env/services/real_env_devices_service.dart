import 'dart:async';

import 'package:device_utils/log/log.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/service/http_service.dart';
import 'env_devices_response_model.dart';
import 'env_devices_service.dart';

/// 实际环境设备服务实现
class RealEnvironmentService implements EnvDevicesService {
  
  RealEnvironmentService._();

  static final RealEnvironmentService _instance = RealEnvironmentService._(); 

  static RealEnvironmentService get instance => _instance; 
 
  @override
  Future<EnvDevicesData?> getWholeHouseEnvDeviceSettingData(String familyId) async {
    try {

      final EnvDevicesResponseModel? responseModel = await HttpService.getEnvDevices(familyId);
      if (responseModel == null) {
        DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: '获取环境设备数据失败: 响应为空',
        );
        return null;
      }

      return responseModel.environmentData;
    } catch (e) {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: '获取环境设备数据失败: $e',
      );
      return null;
    }
  }
}
