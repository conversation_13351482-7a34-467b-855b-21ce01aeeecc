import 'package:smart_home/whole_house/device_env/services/env_devices_response_model.dart';

/// 更新全屋环境设备状态的Action
class UpdateWholeHouseEnvDeviceStateAction {
  /// 空间环境模型列表
  final List<SpaceEnvironmentModel> spaces;

  /// 构造函数
  const UpdateWholeHouseEnvDeviceStateAction(this.spaces);

  @override
  String toString() {
    return 'UpdateWholeHouseEnvDeviceStateAction{spaces: $spaces}';
  }
}

/// 设置环境设备偏好的Action
/// 
/// 当用户在环境设备设置页面中选择设备时触发此Action
class SetWholeHouseEnvDeviceSettingAction {
  final String deviceId;
  
  final bool selected;
  
  /// 设备类型 ('temperature', 'humidity', 'pm25')
  final String deviceType;
  
  final String spaceId;
  
  const SetWholeHouseEnvDeviceSettingAction({
    required this.deviceId,
    required this.selected,
    required this.deviceType,
    required this.spaceId,
  });
  
  @override
  String toString() {
    return 'SetWholeHouseEnvDeviceSettingAction{deviceId: $deviceId, selected: $selected, deviceType: $deviceType, spaceId: $spaceId}';
  }
}
