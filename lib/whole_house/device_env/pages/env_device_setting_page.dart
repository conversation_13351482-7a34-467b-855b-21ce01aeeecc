import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:reselect/reselect.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_env/env_device_constants.dart';
import 'package:smart_home/whole_house/device_env/env_device_setting_selectors.dart';
import 'package:smart_home/whole_house/device_env/view_models/env_device_setting_view_models.dart';
import 'package:smart_home/whole_house/device_env/whole_house_list_check_item_widget.dart';
import 'package:smart_home/whole_house/location_weather/widgets/accuracy_location_modal_tools.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import 'package:smart_home/widget_common/ui_components.dart';

import '../env_device_presenter.dart';

/// 环境设备设置页面
///
/// 展示当前空间下所有可用环境设备并支持选择
/// 设备按温度、湿度、空气质量分类展示
class EnvDeviceSettingPage extends StatefulWidget {
  static const String pageName =
      '${SmartHomeConstant.package}/env_device_setting_page';

  const EnvDeviceSettingPage({
    super.key,
    required this.spaceId,
    this.spaceName = '',
    this.showWeatherCard = false,
  });

  final String spaceId;
  final String spaceName;
  final bool showWeatherCard;

  @override
  EnvDeviceSettingPageState createState() => EnvDeviceSettingPageState();
}

class EnvDeviceSettingPageState extends State<EnvDeviceSettingPage> {
  static const String _pageName = 'envDeviceSettingPage';
  String spaceName = '';
  bool isWholeHouse = false;

  @override
  void initState() {
    super.initState();
    // 注册物理返回拦截监听
    _registerBackButtonHandler();

    // 获取空间名称
    _initSpaceInfo();

    // 查询环境设备数据
    EnvDevicePresenter().getWholeHouseEnvDeviceData();
  }

  @override
  void dispose() {
    // 移除物理返回拦截监听
    InterceptSystemBackUtil.cancelInterceptSystemBack(_pageName);
    super.dispose();
  }

  /// 注册返回按钮处理
  void _registerBackButtonHandler() {
    InterceptSystemBackUtil.interceptSystemBack(
      pageName: _pageName,
      callback: _handleBackNavigation,
    );
  }

  /// 处理返回导航
  void _handleBackNavigation() {
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  /// 初始化空间信息
  void _initSpaceInfo() {
    final Store<SmartHomeState> store = smartHomeStore;
    if (store.state.deviceState.selectedRoomId ==
        store.state.familyState.familyId) {
      spaceName = '全屋';
      isWholeHouse = true;
    } else {
      final String floor = store.state.deviceState.cardShowFloor
          ? store.state.deviceState.selectedFloor
          : '';
      spaceName = '$floor${store.state.deviceState.selectedRoom}';
      isWholeHouse = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StoreProvider<SmartHomeState>(
      store: smartHomeStore,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// 构建应用栏
  AppBar _buildAppBar() {
    return AppBar(
      toolbarHeight: 44,
      title: _buildTitle(),
      centerTitle: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      systemOverlayStyle: const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
      leading: _buildBackButton(),
    );
  }

  /// 构建返回按钮
  Widget _buildBackButton() {
    return GestureDetector(
      onTap: _handleBackNavigation,
      child: Center(
        child: Image.asset(
          'assets/icons/navi_back.webp',
          package: SmartHomeConstant.package,
          height: 24,
          width: 24,
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return StoreConnector<SmartHomeState, EnvDeviceSettingListViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        final Selector<SmartHomeState, EnvDeviceSettingListViewModel> selector =
            EnvDeviceSettingSelectors.createSelectEnvDeviceSettingListViewModel(
                widget.spaceId);
        return selector(store.state);
      },
      builder: (BuildContext context, EnvDeviceSettingListViewModel viewModel) {
        return viewModel.isEmpty
            ? _buildEmptyContent(context)
            : _buildContentWithList(context, viewModel);
      },
    );
  }

  Widget _buildEmptyContent(BuildContext context) {
    return Stack(
      children: <Widget>[
        // 空状态在整个屏幕居中显示
        _buildEmptyState(context),
        // 天气卡片在顶部显示（如果需要）
        if (widget.showWeatherCard)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildWeatherCard(),
          ),
      ],
    );
  }

  Widget _buildContentWithList(
      BuildContext context, EnvDeviceSettingListViewModel viewModel) {
    final double bottomPadding = MediaQuery.of(context).padding.bottom;
    return CustomScrollView(
      slivers: <Widget>[
        if (widget.showWeatherCard)
          SliverToBoxAdapter(child: _buildWeatherCard()),
        _buildSliverListContent(context, viewModel),
        SliverToBoxAdapter(
          child: SizedBox(
            height: bottomPadding == 0 ? 16 : bottomPadding,
          ),
        ),
      ],
    );
  }

  /// 构建页面标题
  Widget _buildTitle() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Text(
          '${widget.spaceName.isNotEmpty ? widget.spaceName : spaceName}环境信息设置',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 17,
            color: const Color(0xFF111111),
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        const Text(
          '选择提供环境信息的设备',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: Color(0xFF747474),
          ),
        ),
      ],
    );
  }

  /// 构建天气卡片
  Widget _buildWeatherCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: const BoxDecoration(
        border: CommonUIComponents.weatherCardGradientBorder,
        borderRadius: BorderRadius.all(
          Radius.circular(22),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(22)),
        child: Stack(
          children: <Widget>[
            _buildBackground(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  _buildLocationRow(),
                  const SizedBox(height: 12),
                  _buildWeatherInfoRow(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建背景组件
  Widget _buildBackground() {
    return StoreConnector<SmartHomeState, String?>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.wholeHouseState.environmentState.outdoorWeatherState
            ?.customBackgroundImage;
      },
      builder: (BuildContext context, String? customBackgroundImage) {
        return Positioned.fill(
          child:
              customBackgroundImage != null && customBackgroundImage.isNotEmpty
                  ? _buildCustomBackground(customBackgroundImage)
                  : Container(color: const Color(0xFF3C7BF9)),
        );
      },
    );
  }

  /// 构建自定义背景图片
  Widget _buildCustomBackground(String imageUrl) {
    return CommonNetworkRefreshImg(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      alignment: Alignment.center,
    );
  }

  /// 构建定位信息行
  Widget _buildLocationRow() {
    return GestureDetector(
      onTap: () => LocationWeatherUtils.handleLocationClicked(context),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Flexible(
            child: _buildLocationText(),
          ),
          const SizedBox(width: 2),
          SizedBox(
            width: 8,
            height: 8,
            child: Image.asset(
              'assets/images/env_device/down_arrow.webp',
              package: SmartHomeConstant.package,
              width: 8,
              height: 8,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationText() {
    return StoreConnector<SmartHomeState, String>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.wholeHouseState.areaState.areaName;
      },
      builder: (BuildContext context, String area) {
        return Text(
          area,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.white,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        );
      },
    );
  }

  /// 构建天气信息行
  Widget _buildWeatherInfoRow() {
    return Row(
      children: <Widget>[
        // 天气图标
        _buildWeatherIcon(),
        const SizedBox(width: 12),
        // 温度
        _buildTemperatureWidget(),
        const SizedBox(width: 12),
        Expanded(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // 湿度
              _buildHumidityWidget(),
              _buildDivideLine(),
              // PM2.5
              _buildPM25Widget(),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建天气图标
  Widget _buildWeatherIcon() {
    return StoreConnector<SmartHomeState, String?>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.wholeHouseState.environmentState.outdoorWeatherState
            ?.customIcon;
      },
      builder: (BuildContext context, String? iconUrl) {
        if (iconUrl == null) {
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
            ),
          );
        }
        return Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: iconUrl.isEmpty ? Colors.white.withOpacity(0.7) : null,
          ),
          child: iconUrl.isNotEmpty
              ? SizedBox(
                  width: 48,
                  height: 48,
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: CommonNetworkRefreshImg(
                      imageUrl: iconUrl,
                      alignment: Alignment.center,
                      errorWidget: Image.asset(
                        'assets/icons/default_device_img.webp',
                        package: SmartHomeConstant.package,
                        height: double.infinity,
                      ),
                    ),
                  ),
                )
              : null,
        );
      },
    );
  }

  Widget _buildTemperatureWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: <Widget>[
        StoreConnector<SmartHomeState, String?>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return store.state.wholeHouseState.environmentState
                .outdoorWeatherState?.temperature;
          },
          builder: (BuildContext context, String? value) {
            return Text(
              _formatEnvironmentDataDisplay(value),
              style: TextStyle(
                fontSize: 40,
                fontWeight: FontWeight.w600,
                color: Colors.white,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            );
          },
        ),
        Text(
          EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_TEMPERATURE),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
      ],
    );
  }

  Widget _buildHumidityWidget() {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          StoreConnector<SmartHomeState, String?>(
            distinct: true,
            converter: (Store<SmartHomeState> store) {
              return store.state.wholeHouseState.environmentState
                  .outdoorWeatherState?.humidity;
            },
            builder: (BuildContext context, String? value) {
              return Text(
                '${_formatEnvironmentDataDisplay(value)}${EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_HUMIDITY)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              );
            },
          ),
          Text(
            '湿度',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Colors.white,
              fontFamilyFallback: fontFamilyFallback(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPM25Widget() {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          StoreConnector<SmartHomeState, String?>(
            distinct: true,
            converter: (Store<SmartHomeState> store) {
              return store.state.wholeHouseState.environmentState
                  .outdoorWeatherState?.pm25;
            },
            builder: (BuildContext context, String? value) {
              return Text(
                _formatEnvironmentDataDisplay(value),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              );
            },
          ),
          Text(
            'PM2.5',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Colors.white,
              fontFamilyFallback: fontFamilyFallback(),
            ),
          ),
        ],
      ),
    );
  }

  String _formatEnvironmentDataDisplay(String? value) {
    return value == null || value.isEmpty ? '- -' : value;
  }

  Widget _buildDivideLine() {
    return SizedBox(
      width: 12,
      height: 24,
      child: Center(
        child: Container(
          width: 0.5,
          height: 12,
          color: Colors.white.withAlpha(20),
        ),
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Transform.translate(
        offset: const Offset(0, -44.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.asset(
              'assets/images/env_device/none_device.webp',
              package: 'smart_home',
              width: 80,
              height: 80,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无可提供环境空气信息的设备',
              style: TextStyle(
                fontSize: 14,
                color: const Color(0xFF8D8D8D),
                fontWeight: FontWeight.w400,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建Sliver列表内容
  Widget _buildSliverListContent(
      BuildContext context, EnvDeviceSettingListViewModel viewModel) {
    final List<_ListItem> flattenedItems = _flattenListItems(viewModel.list);

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          final _ListItem item = flattenedItems[index];

          if (item is _HeaderItem) {
            return _buildCategoryHeader(item.title, topShrink: index == 0);
          } else if (item is _DeviceItem) {
            return _buildDeviceItem(
                context, item.viewModel, index, flattenedItems);
          }

          return const SizedBox.shrink();
        },
        childCount: flattenedItems.length,
      ),
    );
  }

  /// 将分类列表转换为扁平列表项
  List<_ListItem> _flattenListItems(
      List<EnvDeviceSettingCategoryViewModel> categories) {
    final List<_ListItem> items = <_ListItem>[];

    for (final EnvDeviceSettingCategoryViewModel category in categories) {
      items.add(_HeaderItem(category.envName));

      for (final EnvDeviceSettingItemViewModel deviceViewModel
          in category.items) {
        items.add(_DeviceItem(deviceViewModel));
      }
    }

    return items;
  }

  /// 构建分类标题
  Widget _buildCategoryHeader(String title, {bool topShrink = false}) {
    return Padding(
      padding: EdgeInsets.only(left: 16, right: 16, top: topShrink ? 0 : 20),
      child: Container(
        height: 44,
        padding: const EdgeInsets.only(left: 4),
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF333333),
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
      ),
    );
  }

  /// 构建设备项
  Widget _buildDeviceItem(
      BuildContext context,
      EnvDeviceSettingItemViewModel viewModel,
      int index,
      List<_ListItem> items) {
    // 判断前一个和后一个项目是否也是设备项
    final bool hasPreviousDevice = index > 0 && items[index - 1] is _DeviceItem;
    final bool hasNextDevice =
        index < items.length - 1 && items[index + 1] is _DeviceItem;

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        children: <Widget>[
          // 设备卡片
          WholeHouseListCheckItemWidget.fromViewModel(
            context: context,
            viewModel: viewModel,
            spaceId: widget.spaceId,
            isWholeHouse: isWholeHouse,
            hideTopRadius: hasPreviousDevice,
            hideBottomRadius: hasNextDevice,
          ),
          // 如果有下一个设备，添加分割线
          if (hasNextDevice) _buildDivider(),
        ],
      ),
    );
  }

  /// 构建分隔线
  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Container(
        height: 0.5,
        color: const Color(0xFFEEEEEE),
      ),
    );
  }
}

/// 列表项基类
abstract class _ListItem {}

/// 类别标题项
class _HeaderItem extends _ListItem {
  final String title;

  _HeaderItem(this.title);
}

/// 设备项
class _DeviceItem extends _ListItem {
  final EnvDeviceSettingItemViewModel viewModel;

  _DeviceItem(this.viewModel);
}
