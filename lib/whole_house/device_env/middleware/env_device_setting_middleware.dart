import 'package:device_utils/log/log.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/actions/env_devices_actions.dart';
import 'package:smart_home/whole_house/device_env/services/env_devices_response_model.dart';
import 'package:smart_home/whole_house/device_env/services/preference_request_model.dart';

import '../env_device_constants.dart';

/// 环境设备设置Middleware
class EnvDeviceSettingMiddleware implements MiddlewareClass<SmartHomeState>{

  const EnvDeviceSettingMiddleware();

  @override
  void call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) {

    next(action);

    // 如果是设置环境设备偏好的Action，则调用API
    if (action is SetWholeHouseEnvDeviceSettingAction) {
      _handleEnvDeviceSetting(store, action);
    }
  }

  Future<void> _handleEnvDeviceSetting(
    Store<SmartHomeState> store,
    SetWholeHouseEnvDeviceSettingAction action,
  ) async {

    final String spaceId = action.spaceId;
    final String familyId = store.state.familyState.familyId;
    final String deviceType = action.deviceType;

    final SpaceEnvironmentModel? spaceModel = 
        store.state.wholeHouseState.envDeviceState.getSpace(spaceId);
    
    if (spaceModel == null) {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: 'EnvDeviceSettingMiddleware: Space not found. spaceId = $spaceId',
      );
      return;
    }
    
    final EnvDevicePreferenceRequestModel request = EnvDevicePreferenceRequestModel(
      familyId: familyId,
      spaceId: spaceId,
      temperature: deviceType == EnvDeviceConstants.TYPE_TEMPERATURE ? action.deviceId : null,
      humidity: deviceType == EnvDeviceConstants.TYPE_HUMIDITY ? action.deviceId : null,
      pm25: deviceType == EnvDeviceConstants.TYPE_PM25 ? action.deviceId : null,
    );
    
    DevLogger.info(
      tag: SmartHomeConstant.package,
      msg: 'EnvDeviceSettingMiddleware: Sending preference request: $request',
    );
    
    // 调用API设置环境设备偏好
    final bool success = await HttpService.setEnvDevicePreference(request);
    
    if (success) {
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'EnvDeviceSettingMiddleware: Set preference successfully.',
      );
    } else {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: 'EnvDeviceSettingMiddleware: Failed to set preference.',
      );
    }
  }
} 
