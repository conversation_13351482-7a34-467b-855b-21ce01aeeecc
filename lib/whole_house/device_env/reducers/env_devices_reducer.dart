import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/actions/env_devices_actions.dart';
import 'package:smart_home/whole_house/device_env/env_device_constants.dart';
import 'package:smart_home/whole_house/device_env/env_device_state.dart';
import 'package:smart_home/whole_house/device_env/services/env_devices_response_model.dart';

final Reducer<SmartHomeState> envDeviceReducer = combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateWholeHouseEnvDeviceStateAction>(_updateEnvDeviceState).call,
  TypedReducer<SmartHomeState, SetWholeHouseEnvDeviceSettingAction>(_setEnvDeviceSetting).call,
]);

SmartHomeState _updateEnvDeviceState(
  SmartHomeState state,
  UpdateWholeHouseEnvDeviceStateAction action,
) {
  final EnvDeviceState updatedEnvDeviceState = EnvDeviceState(
    spacesList: action.spaces,
  );
  return state.copyWith(
    wholeHouseState: state.wholeHouseState.copyWith(
      envDeviceState: updatedEnvDeviceState,
    ),
  );
}

/// 设置环境设备选中状态
/// 
/// 根据Action更新环境设备的选中状态，实现单选逻辑
SmartHomeState _setEnvDeviceSetting(
  SmartHomeState state,
  SetWholeHouseEnvDeviceSettingAction action,
) {
  final EnvDeviceState currentEnvDeviceState = state.wholeHouseState.envDeviceState;
  final SpaceEnvironmentModel? targetSpace = currentEnvDeviceState.getSpace(action.spaceId);
  
  if (targetSpace == null || !_isValidDeviceType(action.deviceType)) {
    return state;
  }
  
  // 获取对应类型的设备列表
  final List<EnvDeviceItemModel> deviceList = _getDeviceListByType(targetSpace, action.deviceType);
  
  // 更新设备列表，实现单选逻辑
  final List<EnvDeviceItemModel> updatedDeviceList = deviceList.map((EnvDeviceItemModel device) => 
    device.deviceId == action.deviceId
      ? _createUpdatedDevice(device, action.selected)
      : _createUpdatedDevice(device, false) // 其他设备设为不选中
  ).toList();
  
  final SpaceEnvironmentModel updatedSpace = _createUpdatedSpace(
    targetSpace,
    action.deviceType,
    updatedDeviceList,
  );
  
  return state.copyWith(
    wholeHouseState: state.wholeHouseState.copyWith(
      envDeviceState: EnvDeviceState(
        spacesList: currentEnvDeviceState.spacesList.map(
          (SpaceEnvironmentModel space) => 
            space.spaceId == action.spaceId ? updatedSpace : space
        ).toList(),
      ),
    ),
  );
}

bool _isValidDeviceType(String deviceType) {
  return deviceType == EnvDeviceConstants.TYPE_TEMPERATURE || 
         deviceType == EnvDeviceConstants.TYPE_HUMIDITY || 
         deviceType == EnvDeviceConstants.TYPE_PM25;
}

List<EnvDeviceItemModel> _getDeviceListByType(SpaceEnvironmentModel space, String deviceType) {
  switch (deviceType) {
    case EnvDeviceConstants.TYPE_TEMPERATURE:
      return space.temperature;
    case EnvDeviceConstants.TYPE_HUMIDITY:
      return space.humidity;
    case EnvDeviceConstants.TYPE_PM25:
      return space.pm25;
    default:
      return <EnvDeviceItemModel>[];
  }
}

EnvDeviceItemModel _createUpdatedDevice(EnvDeviceItemModel device, bool selected) {
  return EnvDeviceItemModel(
    deviceId: device.deviceId,
    deviceName: device.deviceName,
    roomName: device.roomName,
    floorName: device.floorName,
    cardPageImg: device.cardPageImg,
    reportedValue: device.reportedValue,
    isOnline: device.isOnline,
    selected: selected,
  );
}

SpaceEnvironmentModel _createUpdatedSpace(
  SpaceEnvironmentModel originalSpace,
  String deviceType,
  List<EnvDeviceItemModel> updatedDeviceList,
) {
  return SpaceEnvironmentModel(
    spaceId: originalSpace.spaceId,
    temperature: deviceType == EnvDeviceConstants.TYPE_TEMPERATURE ? updatedDeviceList : originalSpace.temperature,
    humidity: deviceType == EnvDeviceConstants.TYPE_HUMIDITY ? updatedDeviceList : originalSpace.humidity,
    pm25: deviceType == EnvDeviceConstants.TYPE_PM25 ? updatedDeviceList : originalSpace.pm25,
  );
} 
