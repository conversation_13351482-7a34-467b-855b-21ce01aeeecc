import 'package:flutter/foundation.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/env_device_constants.dart';

/// 全屋头部环境设备列表ViewModel
/// 
/// 用于显示特定空间内的环境设备列表
@immutable
class WholeHouseHeaderEnvDeviceListViewModel {

  final String spaceId;
  
  final List<WholeHouseHeaderEnvDeviceItemViewModel> list;

  
  const WholeHouseHeaderEnvDeviceListViewModel({
    required this.spaceId,
    required this.list,
  });
  
  bool get isEmpty => list.isEmpty;
  
  bool get isNotEmpty => list.isNotEmpty;
  
  int get length => list.length;
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is WholeHouseHeaderEnvDeviceListViewModel &&
        other.spaceId == spaceId &&
        listEquals(other.list, list);
  }
  
  @override
  int get hashCode => Object.hash(
    spaceId,
    Object.hashAll(list),
  );
  
  @override
  String toString() {
    return 'WholeHouseHeaderEnvDeviceListViewModel{spaceId: $spaceId, devices: ${list.length}}';
  }
}


/// 全屋头部环境设备项ViewModel
/// 
/// 用于显示单个环境设备数据项（温度、湿度、PM2.5等）
@immutable
class WholeHouseHeaderEnvDeviceItemViewModel {

  final String icon;
  
  /// 设备值
  final String value;
  
  final String unit;
  
  final String deviceType;

  const WholeHouseHeaderEnvDeviceItemViewModel({
    required this.icon,
    required this.value,
    required this.unit,
    required this.deviceType,
  });
  
  static WholeHouseHeaderEnvDeviceItemViewModel fromStore({
    required Store<SmartHomeState> store,
    required String deviceType,
    required String value,
    required String unit,
  }) {
    
    final String icon = EnvDeviceConstants.getIcon(deviceType);
    
    return WholeHouseHeaderEnvDeviceItemViewModel(
      icon: icon,
      value: value,
      unit: unit,
      deviceType: deviceType,
    );
  }
  
  String get formattedValue => '$value$unit';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is WholeHouseHeaderEnvDeviceItemViewModel &&
        other.icon == icon &&
        other.value == value &&
        other.unit == unit &&
        other.deviceType == deviceType;
  }
  
  @override
  int get hashCode => Object.hash(
    icon,
    value,
    unit,
    deviceType,
  );
  
  @override
  String toString() {
    return 'WholeHouseHeaderEnvDeviceItemViewModel{'
        'type: $deviceType, value: $value$unit}';
  }
} 
