import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/widgets/whole_house_header_icon_widget.dart';

import '../../common/constant.dart';
import '../../common/smart_home_util.dart';
import '../widgets/animated_visible_fade_widget.dart';
import 'env_device_constants.dart';
import 'env_device_selectors.dart';
import 'env_device_view_model.dart';
import 'pages/env_device_setting_page.dart';

/// 全屋头部环境设备组件
///
/// 用于显示温度、湿度、PM2.5等环境数据
class WholeHouseHeaderEnvDeviceWidget extends StatelessWidget {
  /// 空间ID，用于获取该空间的环境设备数据
  final String spaceId;

  final String spaceName;

  const WholeHouseHeaderEnvDeviceWidget({
    super.key,
    required this.spaceId,
    required this.spaceName,
  });

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState,
        WholeHouseHeaderEnvDeviceListViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) =>
          EnvDeviceSelectors.selectEnvDeviceListViewModel(spaceId)(store.state),
      builder:
          (BuildContext context, WholeHouseHeaderEnvDeviceListViewModel vm) {
        return AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: vm.isNotEmpty ? 1.0 : 0.0,
            child: Row(
              children: <Widget>[
                for (int i = 0; i < vm.list.length; i++)
                  _buildEnvDeviceItem(context, vm.list[i]),
              ],
            ));
      },
    );
  }

  /// 构建单个环境设备项
  Widget _buildEnvDeviceItem(
      BuildContext context, WholeHouseHeaderEnvDeviceItemViewModel item) {
    return _EnvDeviceIconWidget(
      item: item,
      spaceId: spaceId,
      spaceName: spaceName,
    );
  }
}

/// 环境设备图标组件，封装WholeHouseHeaderIconWidget并添加埋点
class _EnvDeviceIconWidget extends StatefulWidget {
  final WholeHouseHeaderEnvDeviceItemViewModel item;
  final String spaceId;
  final String spaceName;

  const _EnvDeviceIconWidget({
    required this.item,
    required this.spaceId,
    required this.spaceName,
  });

  @override
  State<_EnvDeviceIconWidget> createState() => _EnvDeviceIconWidgetState();
}

class _EnvDeviceIconWidgetState extends State<_EnvDeviceIconWidget> {
  @override
  void initState() {
    super.initState();
    gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
      'value': EnvDeviceConstants.getDisplayName(widget.item.deviceType),
    });
  }

  @override
  Widget build(BuildContext context) {
    return WholeHouseHeaderIconWidget(
      smallIcon: widget.item.icon,
      value: widget.item.value,
      unit: widget.item.unit,
      margin: const EdgeInsets.only(right: 8),
      clickCallback: (BuildContext context) {
        if (isFamilyMemberRole()) {
          ToastHelper.showToast(SmartHomeConstant.contactFamilyRoleWarning);
          return;
        }
        gioTrack(GioConst.wholeHouseIconClick, <String, String>{
          'value': EnvDeviceConstants.getDisplayName(widget.item.deviceType),
        });
        Navigator.of(context).push(
          MaterialPageRoute<void>(
            builder: (BuildContext context) => EnvDeviceSettingPage(
              spaceId: widget.spaceId,
            ),
          ),
        );
      },
    );
  }
}
