import 'dart:collection';

import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';

import 'env_device_constants.dart';
import 'env_device_state.dart';
import 'env_device_view_model.dart';
import 'services/env_devices_response_model.dart' as local_model;

class EnvDeviceSelectors {
  // 最大缓存选择器数量
  static const int _maxCachedSelectorsThreshold = 20;

  static final LinkedHashMap<String,
          Selector<SmartHomeState, WholeHouseHeaderEnvDeviceListViewModel>>
      _envDeviceSelectors = LinkedHashMap<String,
          Selector<SmartHomeState, WholeHouseHeaderEnvDeviceListViewModel>>();

  static Selector<SmartHomeState, WholeHouseHeaderEnvDeviceListViewModel>
      selectEnvDeviceListViewModel(String roomId) {
    // 如果存在，移除并重新添加以更新使用顺序
    if (_envDeviceSelectors.containsKey(roomId)) {
      final Selector<SmartHomeState, WholeHouseHeaderEnvDeviceListViewModel>?
          selector = _envDeviceSelectors[roomId];
      _envDeviceSelectors.remove(roomId);
      if (selector != null) {
        _envDeviceSelectors[roomId] = selector;
        return selector;
      }
    }

    // 缓存管理，当超过最大缓存数据，移除LRU (Least Recently Used) 的一项
    if (_envDeviceSelectors.length >= _maxCachedSelectorsThreshold) {
      final String keyToRemove = _envDeviceSelectors.keys.first;
      _envDeviceSelectors.remove(keyToRemove);
    }

    final Selector<SmartHomeState, WholeHouseHeaderEnvDeviceListViewModel>
        selector = createSelector3(
      (SmartHomeState state) => state.wholeHouseState.envDeviceState,
      (SmartHomeState state) => state.familyState.familyId,
      (SmartHomeState state) => roomId,
      _createViewModel,
    );

    _envDeviceSelectors[roomId] = selector;
    return selector;
  }

  /// 获取指定空间的环境模型
  static local_model.SpaceEnvironmentModel? getSpaceEnvironmentModel(
    SmartHomeState state,
    String spaceId,
  ) {
    return state.wholeHouseState.envDeviceState.getSpace(spaceId);
  }

  /// 添加特定类型的设备到列表中
  static void _addDevicesOfType({
    required List<WholeHouseHeaderEnvDeviceItemViewModel> deviceList,
    required List<local_model.EnvDeviceItemModel> devices,
    required String type,
    required String unit,
  }) {
    // 过滤出已选择且在线的设备
    final List<local_model.EnvDeviceItemModel> filteredDevices = devices
        .where((local_model.EnvDeviceItemModel device) =>
            device.selected && device.isOnline)
        .toList();

    if (filteredDevices.isNotEmpty) {
      final local_model.EnvDeviceItemModel device = filteredDevices.first;

      final String iconPath = EnvDeviceConstants.getIcon(type.toLowerCase());

      deviceList.add(WholeHouseHeaderEnvDeviceItemViewModel(
        icon: iconPath,
        value: device.reportedValue,
        unit: unit,
        deviceType: type,
      ));
    }
  }

  static WholeHouseHeaderEnvDeviceListViewModel _createViewModel(
      EnvDeviceState envDeviceState, String familyId, String roomId) {
    final local_model.SpaceEnvironmentModel? spaceModel =
        envDeviceState.getSpace(roomId);
    final List<WholeHouseHeaderEnvDeviceItemViewModel> deviceList =
        <WholeHouseHeaderEnvDeviceItemViewModel>[];
    if (spaceModel != null) {
      // 处理温度设备
      _addDevicesOfType(
          deviceList: deviceList,
          devices: spaceModel.temperature,
          type: EnvDeviceConstants.TYPE_TEMPERATURE,
          unit:
              EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_TEMPERATURE));

      // 处理湿度设备
      _addDevicesOfType(
          deviceList: deviceList,
          devices: spaceModel.humidity,
          type: EnvDeviceConstants.TYPE_HUMIDITY,
          unit: EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_HUMIDITY));

      // 处理PM2.5设备
      _addDevicesOfType(
          deviceList: deviceList,
          devices: spaceModel.pm25,
          type: EnvDeviceConstants.TYPE_PM25,
          unit: EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_PM25));
    }
    return WholeHouseHeaderEnvDeviceListViewModel(
      spaceId: roomId,
      list: deviceList,
    );
  }
}
