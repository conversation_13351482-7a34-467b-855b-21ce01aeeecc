import 'services/env_devices_response_model.dart';

bool anyDeviceOnlineAndSelected(List<EnvDeviceItemModel> devices) {
  return devices
      .any((EnvDeviceItemModel device) => device.isOnline && device.selected);
}

/// 判断是否有所有空间拥有在线且被选中的环境设备
bool hasAnyOnlineDeviceAllRoom(List<SpaceEnvironmentModel> spaces) {
  return spaces.any((SpaceEnvironmentModel space) =>
      anyDeviceOnlineAndSelected(space.temperature) ||
      anyDeviceOnlineAndSelected(space.humidity) ||
      anyDeviceOnlineAndSelected(space.pm25));
}
