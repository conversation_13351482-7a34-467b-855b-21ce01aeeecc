import 'dart:convert';

import 'package:device_utils/log/log.dart';
import 'package:smart_home/whole_house/device_consumables/device_consumables_presenter.dart';
import 'package:smart_home/whole_house/device_env/env_device_presenter.dart';
import 'package:smart_home/whole_house/device_fault_alarm/device_fault_alarm_presenter.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_action.dart';
import 'package:smart_home/whole_house/location_weather/actions/location_weather_actions.dart';
import 'package:smart_home/whole_house/whole_house_cache_model.dart';
import 'package:storage/storage.dart';
import 'package:user/user.dart';

import '../common/constant.dart';
import '../store/smart_home_store.dart';
import 'preference_setting/preference_setting_presenter.dart';
import 'store/whole_house_action.dart';
import 'whole_house_debounce.dart';

class WholeHousePresenter {
  // 单例实例
  static final WholeHousePresenter _instance = WholeHousePresenter._internal();

  // 工厂构造函数
  factory WholeHousePresenter() => _instance;

  // 私有构造函数
  WholeHousePresenter._internal();

  /// 查询仪表盘数据
  Future<void> getWholeHouseData(
      {required TriggerType triggerType, String? familyId}) async {
    familyId ??= smartHomeStore.state.familyState.familyId;
    DevLogger.debug(
        tag: 'WholeHousePresenter', msg: 'triggerType:$triggerType');
    // 当前家庭变化，清空运行中的设备数据
    if (triggerType == TriggerType.currentFamilyChange) {
      smartHomeStore.dispatch(const ClearRunningDeviceAction());
    }
    // 偏好设置, 除了设备列表变化，其他状态都查询
    if (triggerType != TriggerType.deviceListChange) {
      PreferenceSettingPresenter()
          .getWholeHousePreferenceSettingWithDebounce(familyId);
    }

    // 6个状态都查询故障接口
    DeviceFaultAlarmPresenter()
        .getWholeHouseDeviceFaultAlarmDataWithDebounce(familyId: familyId);

    WholeHouseDebounce().debounce.run(() {
      // 6个状态都查询耗材接口
      DeviceConsumablesPresenter().fetchDeviceConsumables(familyId!);

      EnvDevicePresenter().getWholeHouseEnvDeviceDataWithCoordinator(familyId);

      // 查询位置和天气数据（通过middleware）
      smartHomeStore.dispatch(
        const LocationAndWeatherDataRequestAction(),
      );
    });
  }

  void getWholeHousePreferenceSetting(String familyId) {
    DevLogger.debug(
        tag: 'WholeHousePresenter',
        msg: 'getWholeHousePreferenceSetting: $familyId');
    PreferenceSettingPresenter()
        .getWholeHousePreferenceSettingWithDebounce(familyId);
  }

  /// 清空仪表盘数据
  void clearWholeHouseData() {
    DevLogger.debug(tag: SmartHomeConstant.package, msg: 'clearWholeHouseData');
    smartHomeStore.dispatch(const ClearWholeHouseDataAction());
  }

  static WholeHouseCacheModel? getWholeHouseDataFromStorage(
      String wholeHouseString) {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'getWholeHouseDataFromStorage: $wholeHouseString');
    if (wholeHouseString.isEmpty) {
      return null;
    }
    try {
      final dynamic jsonData = jsonDecode(wholeHouseString);
      if (jsonData is Map) {
        return WholeHouseCacheModel.fromJson(jsonData);
      } else {
        return null;
      }
    } catch (e) {
      DevLogger.error(
          tag: 'getWholeHouseDataFromStorage', msg: 'jsonDecode error:$e');
      return null;
    }
  }

  static Future<void> putWholeHouseDataToStorage(
      String familyId, WholeHouseCacheModel model) async {
    final String userId = User.getOauthDataSync()?.uhome_user_id ?? '';
    final String wholeHouseKey = 'whole_house_${familyId}_$userId';

    try {
      Storage.putStringValue(wholeHouseKey, jsonEncode(model.toJson()));
    } catch (e) {
      DevLogger.error(
          tag: 'putSceneListListFromStorage', msg: 'jsonEncode error:$e');
    }
  }
}

/// 触发时机
enum TriggerType {
  familyPositionChange,
  pullToRefresh,
  userRefresh,
  currentFamilyChange,
  deviceListChange
}
