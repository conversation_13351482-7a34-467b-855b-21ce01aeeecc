import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_selector.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_view_model.dart';
import 'package:smart_home/whole_house/widgets/whole_house_list_item_widget.dart';
import 'package:smart_home/whole_house/widgets/whole_house_popup_empty.dart';

// 运行中设备半屏弹窗
class RunningDeviceBottomSheetWidget extends StatelessWidget {
  const RunningDeviceBottomSheetWidget({super.key, required this.category});

  final String category;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, RunningDeviceMapViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return RunningDeviceSelectors.selectRunningDeviceVM(store.state);
      },
      builder: (BuildContext context, RunningDeviceMapViewModel vm) {
        final RunningDeviceListViewModel? listViewModel = vm.map[category];
        if (listViewModel == null || listViewModel.list.isEmpty) {
          DevLogger.debug(
              tag: SmartHomeConstant.package,
              msg:
                  'RunningDeviceBottomSheetWidget build callBack value: ${vm.map[category]};');
          return const WholeHousePopupEmptyWidget(
            type: EmptyType.runningDevice,
            desc: '暂无运行中的设备',
          );
        }
        return ListView.separated(
          itemCount: listViewModel.list.length,
          physics: const ClampingScrollPhysics(),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            return _buildListCell(listViewModel, index);
          },
          separatorBuilder: (BuildContext context, int index) {
            if (index == listViewModel.list.length - 1) {
              return const SizedBox();
            }
            return const Padding(
              padding: EdgeInsets.only(top: 12),
            );
          },
        );
      },
    );
  }

  Widget _buildListCell(RunningDeviceListViewModel vm, int index) {
    return WholeHouseListItemWidget(
      deviceImage: vm.list[index].deviceImage,
      deviceName: vm.list[index].deviceName,
      roomName: vm.list[index].deviceRoom,
      desc: '运行中',
      clickCallback: (BuildContext context) {
        DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg:
                'RunningDeviceBottomSheetWidget click callBack value: ${vm.list[index]};');
        // TODO(liutong) 待明确此处处理逻辑
        final CardBaseViewModel? deviceCardVM = smartHomeStore
            .state.deviceState.allCardViewModelMap[vm.list[index].deviceId];
        if (deviceCardVM is DeviceCardViewModel) {
          // 跳转到设备详情页
          deviceCardVM.cardClick(context);
        }
      },
    );
  }
}
