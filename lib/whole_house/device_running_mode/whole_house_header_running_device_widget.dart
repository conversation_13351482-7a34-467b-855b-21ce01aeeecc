import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:lottie/lottie.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_running_mode/running_device_bottom_sheet_widget.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_selector.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_view_model.dart';
import 'package:smart_home/whole_house/widgets/show_whole_house_modal_bottom_sheet_widget.dart';
import 'package:smart_home/whole_house/widgets/whole_house_header_icon_widget.dart';

import '../widgets/animated_visible_fade_widget.dart';

/// 运行中设备widget
class HeaderRunningDeviceWidget extends StatelessWidget {
  const HeaderRunningDeviceWidget({super.key, required this.roomId});

  final String roomId;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, HeaderRunningDeviceListViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return RunningDeviceSelectors.selectHeaderRunningDeviceVMWithRoomId(store.state, roomId);
      },
      builder: (BuildContext context, HeaderRunningDeviceListViewModel vm) {
        return AnimatedVisibleFadeWidget(
            visible: vm.list.isNotEmpty,
            child: Row(
              children: <Widget>[
                for (int i = 0; i < vm.list.length; i++)
                  _HeaderIconWidget(
                        vm: vm.list[i],
                      )
              ],
            ));
      },
    );
  }
}

class _HeaderIconWidget extends StatefulWidget {
  const _HeaderIconWidget({
    super.key,
    required this.vm,
  });

  final HeaderRunningDeviceItemViewModel vm;

  @override
  State<_HeaderIconWidget> createState() => _HeaderIconWidgetState();
}

class _HeaderIconWidgetState extends State<_HeaderIconWidget> {
  @override
  void initState() {
    super.initState();
    gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
      'value': widget.vm.name,
    });
  }

  @override
  Widget build(BuildContext context) {
    return WholeHouseHeaderIconWidget(
      icon: Lottie.asset(
        widget.vm.icon,
        width: 32,
        height: 32,
        package: SmartHomeConstant.package,
      ),
      count: int.tryParse(widget.vm.count) ?? 0,
      margin: const EdgeInsets.only(right: 8),
      clickCallback: (BuildContext context) {
        // 增加点击事件
        DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg:
                'HeaderRunningDeviceWidget click callBack value: ${widget.vm};');
        gioTrack(GioConst.wholeHouseIconClick, <String, String>{
          'value': widget.vm.name,
        });
        if ((int.tryParse(widget.vm.count) ?? 0) == 1) {
          // TODO(liutong) 待明确此处处理逻辑
          final CardBaseViewModel? deviceCardVM = smartHomeStore
              .state.deviceState.allCardViewModelMap[widget.vm.deviceId];
          if (deviceCardVM is DeviceCardViewModel) {
            // 跳转到设备详情页
            deviceCardVM.cardClick(context);
          }
        } else {
          // 拉起半屏弹窗
          showWholeHouseModalBottomSheetWidget(
            context,
            title: '运行中的设备',
            child: RunningDeviceBottomSheetWidget(
              category: widget.vm.name,
            ),
          );
        }
      },
    );
  }
}
