import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_constant.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_state.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_view_model.dart';

class RunningDeviceSelectors {
  static final Selector<SmartHomeState, HeaderRunningDeviceListViewModel>
      selectHeaderRunningDeviceVM = createSelector2(
    // 输入选择器 - 提取关键依赖数据
    (SmartHomeState state) =>
        state.wholeHouseState.runningDeviceState.runningDeviceMap,
    (SmartHomeState state) =>
        state.wholeHouseState.preferenceSettingState.homeSwitch,
    // 结果选择器 - 转换为顶部区域视图模型
    _createHeaderVMList,
  );

  static HeaderRunningDeviceListViewModel selectHeaderRunningDeviceVMWithRoomId(
      SmartHomeState state, String roomId) {
    if (roomId == state.familyState.familyId) {
      return selectHeaderRunningDeviceVM(state);
    } else {
      return HeaderRunningDeviceListViewModel(
        list: <HeaderRunningDeviceItemViewModel>[],
      );
    }
  }

  static final Selector<SmartHomeState, RunningDeviceMapViewModel>
      selectRunningDeviceVM = createSelector2(
    // 输入选择器 - 提取关键依赖数据
    (SmartHomeState state) =>
        state.wholeHouseState.runningDeviceState.runningDeviceMap,
    (SmartHomeState state) => state.deviceState.cardShowFloor,
    // 结果选择器 - 转换为半屏列表视图模型
    _createRunningDeviceVMMap,
  );

  /// 创建顶部区域视图模型列表
  static HeaderRunningDeviceListViewModel _createHeaderVMList(
    Map<String, List<RunningDeviceItemModel>> runningDeviceMap,
    bool homeSwitch,
  ) {
    if (!homeSwitch) {
      return HeaderRunningDeviceListViewModel(
          list: <HeaderRunningDeviceItemViewModel>[]);
    }
    return _buildHeaderViewModel(runningDeviceMap);
  }

  static HeaderRunningDeviceListViewModel _buildHeaderViewModel(
    Map<String, List<RunningDeviceItemModel>> runningDeviceMap,
  ) {
    final List<HeaderRunningDeviceItemViewModel> headerList = runningDeviceMap
        .entries
        .where((MapEntry<String, List<RunningDeviceItemModel>> entry) =>
            entry.value.isNotEmpty)
        .map((MapEntry<String, List<RunningDeviceItemModel>> entry) {
      return _createHeaderItemViewModel(entry.key, entry.value);
    }).toList();
    return HeaderRunningDeviceListViewModel(list: headerList);
  }

  static HeaderRunningDeviceItemViewModel _createHeaderItemViewModel(
      String categoryName, List<RunningDeviceItemModel> devices) {
    final String icon = _queryDeviceIcon(devices.first.appTypeCode) ??
        'assets/icons/default_device_img.webp';

    return HeaderRunningDeviceItemViewModel(
      name: categoryName,
      icon: icon,
      count: devices.length.toString(),
      deviceId: devices.first.deviceId,
    );
  }

  static String? _queryDeviceIcon(
    String appTypeCode,
  ) {
    final Map<String, String>? categoryMap =
        RunningDeviceConstant.runningDeviceTypeCodeMap[appTypeCode];

    return categoryMap?[RunningDeviceConstant.iconKey];
  }

  /// 创建半屏弹窗视图模型列表
  static RunningDeviceMapViewModel _createRunningDeviceVMMap(
    Map<String, List<RunningDeviceItemModel>> runningDeviceMap,
    bool cardShowFloor,
  ) {
    final Map<String, RunningDeviceListViewModel> map = runningDeviceMap
        .map((String key, List<RunningDeviceItemModel> devices) {
      final List<RunningDeviceItemViewModel> deviceItemList =
          _mapDevicesToViewModel(devices, cardShowFloor);

      return MapEntry<String, RunningDeviceListViewModel>(
        key,
        RunningDeviceListViewModel(list: deviceItemList),
      );
    });

    return RunningDeviceMapViewModel(map: map);
  }

  static List<RunningDeviceItemViewModel> _mapDevicesToViewModel(
    List<RunningDeviceItemModel> devices,
    bool cardShowFloor,
  ) {
    return devices.map((RunningDeviceItemModel device) {
      return RunningDeviceItemViewModel(
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        deviceImage: device.deviceImage,
        deviceRoom: cardShowFloor
            ? device.floorName + device.roomName
            : device.roomName,
      );
    }).toList();
  }
}
