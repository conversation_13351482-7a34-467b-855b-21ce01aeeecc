import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

class HeaderRunningDeviceItemViewModel {
  HeaderRunningDeviceItemViewModel({
    required this.name,
    required this.icon,
    required this.count,
    required this.deviceId,
  });
  // 运行中设备名
  final String name;
  // 运行中设备icon
  final String icon;
  // 运行中设备数量
  final String count;
  // 运行中设备ID
  final String deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HeaderRunningDeviceItemViewModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          icon == other.icon &&
          count == other.count &&
          deviceId == other.deviceId;

  @override
  int get hashCode =>
      name.hashCode ^ icon.hashCode ^ count.hashCode ^ deviceId.hashCode;

  @override
  String toString() {
    return 'HeaderRunningDeviceItemViewModel{name: $name, icon: $icon, count: $count, deviceId: $deviceId}';
  }
}

// 运行中设备 - 顶部区域VM
class HeaderRunningDeviceListViewModel {
  HeaderRunningDeviceListViewModel({
    required this.list,
  });

  // 运行中设备列表
  final List<HeaderRunningDeviceItemViewModel> list;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HeaderRunningDeviceListViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(list, other.list);

  @override
  int get hashCode => listHashCode(list);

  @override
  String toString() {
    return 'HeaderRunningDeviceListViewModel{list: $list}';
  }
}

/// 运行中设备 - 弹窗区域VM
class RunningDeviceMapViewModel {
  RunningDeviceMapViewModel({
    required this.map,
  });

  final Map<String, RunningDeviceListViewModel> map;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RunningDeviceMapViewModel &&
          runtimeType == other.runtimeType &&
          mapEquals(map, other.map);

  @override
  int get hashCode => mapHashCode(map);

  @override
  String toString() {
    return 'RunningDeviceMapViewModel{map: $map}';
  }
}

class RunningDeviceListViewModel {
  RunningDeviceListViewModel({
    required this.list,
  });

  // 运行中设备列表
  final List<RunningDeviceItemViewModel> list;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RunningDeviceListViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(list, other.list);

  @override
  int get hashCode => listHashCode(list);

  @override
  String toString() {
    return 'RunningDeviceListViewModel{list: $list}';
  }
}

class RunningDeviceItemViewModel {
  RunningDeviceItemViewModel({
    required this.deviceId,
    required this.deviceName,
    required this.deviceImage,
    required this.deviceRoom,
  });
  // 运行中设备ID
  final String deviceId;
  // 运行中设备名
  final String deviceName;
  // 运行中设备数量
  final String deviceImage;
  // 运行中设备位置信息
  final String deviceRoom;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RunningDeviceItemViewModel &&
          runtimeType == other.runtimeType &&
          deviceId == other.deviceId &&
          deviceName == other.deviceName &&
          deviceImage == other.deviceImage &&
          deviceRoom == other.deviceRoom;

  @override
  int get hashCode =>
      deviceId.hashCode ^
      deviceName.hashCode ^
      deviceImage.hashCode ^
      deviceRoom.hashCode;

  @override
  String toString() {
    return 'RunningDeviceItemViewModel{deviceId: $deviceId, deviceName: $deviceName, deviceImage: $deviceImage, deviceRoom: $deviceRoom}';
  }
}
