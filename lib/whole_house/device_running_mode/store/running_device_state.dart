import 'dart:collection';

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class RunningDeviceState {
  /// 运行中设备数据模型
  ///
  /// [key] 设备品类
  ///
  /// [value] 运行中设备数据模型列表
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap =
      LinkedHashMap<String, List<RunningDeviceItemModel>>();

  /// 存储运行中设备id
  List<String> runningDeviceId = <String>[];

  /// clear清空状态方法
  void clear() {
    runningDeviceId = <String>[];
    runningDeviceMap = LinkedHashMap<String, List<RunningDeviceItemModel>>();
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RunningDeviceState &&
          runtimeType == other.runtimeType &&
          listEquals(runningDeviceId, other.runningDeviceId) &&
          mapEquals(runningDeviceMap, other.runningDeviceMap);

  @override
  int get hashCode =>
      listHashCode(runningDeviceId) ^ mapHashCode(runningDeviceMap);

  @override
  String toString() {
    return 'RunningDeviceState{runningDeviceId: $runningDeviceId, runningDeviceMap: $runningDeviceMap}';
  }
}

class RunningDeviceItemModel {
  RunningDeviceItemModel({
    required this.deviceId,
    required this.deviceName,
    required this.deviceImage,
    required this.floorName,
    required this.roomName,
    required this.appTypeCode,
  });

  // 设备id
  String deviceId = '';
  // 设备名称
  String deviceName = '';
  // 设备图片
  String deviceImage = '';
  // 楼层名称
  String floorName = '';
  // 房间名称
  String roomName = '';
  // 产品类型编码
  String appTypeCode = '';

  RunningDeviceItemModel.fromDeviceCardVM(DeviceCardViewModel viewModel) {
    deviceId = viewModel.device.basicInfo.deviceId;
    deviceName = viewModel.device.basicInfo.deviceName;
    deviceImage = viewModel.device.basicInfo.cardPageImg;
    floorName = viewModel.device.basicInfo.roomInfo.floorName;
    roomName = viewModel.device.basicInfo.roomInfo.roomName;
    appTypeCode = viewModel.device.basicInfo.appTypeCode;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RunningDeviceItemModel &&
          runtimeType == other.runtimeType &&
          deviceId == other.deviceId &&
          deviceName == other.deviceName &&
          deviceImage == other.deviceImage &&
          floorName == other.floorName &&
          roomName == other.roomName &&
          appTypeCode == other.appTypeCode;

  @override
  int get hashCode =>
      deviceId.hashCode ^
      deviceName.hashCode ^
      deviceImage.hashCode ^
      floorName.hashCode ^
      roomName.hashCode ^
      appTypeCode.hashCode;

  @override
  String toString() {
    return 'RunningDeviceItemModel{deviceId: $deviceId, deviceName: $deviceName, deviceImage: $deviceImage, floorName: $floorName, roomName: $roomName, appTypeCode: $appTypeCode}';
  }
}
