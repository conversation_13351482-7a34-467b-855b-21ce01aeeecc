class RunningDeviceConstant {
  static const String iconKey = 'icon';
  static const String categoryKey = 'category';
  static const Map<String, String> airConditioner = <String, String>{
    iconKey: 'assets/images/running_device/air_conditioner.json',
    categoryKey: '空调',
  };
  static const Map<String, String> speaker = <String, String>{
    iconKey: 'assets/images/running_device/speaker.json',
    categoryKey: '音乐',
  };
  static const Map<String, String> stove = <String, String>{
    iconKey: 'assets/images/running_device/stove.json',
    categoryKey: '灶具',
  };
  static const Map<String, String> oven = <String, String>{
    iconKey: 'assets/images/running_device/oven.json',
    categoryKey: '烤箱',
  };
  static const Map<String, String> tv = <String, String>{
    iconKey: 'assets/images/running_device/tv.json',
    categoryKey: '电视',
  };
  static const Map<String, String> airPurifier = <String, String>{
    iconKey: 'assets/images/running_device/air_purifier.json',
    categoryKey: '空气净化器',
  };
  static const Map<String, String> dehumidifier = <String, String>{
    iconKey: 'assets/images/running_device/dehumidifier.json',
    categoryKey: '除湿机',
  };
  static const Map<String, String> humidifier = <String, String>{
    iconKey: 'assets/images/running_device/humidifier.json',
    categoryKey: '加湿器',
  };
  static const Map<String, String> cleaner = <String, String>{
    iconKey: 'assets/images/running_device/cleaner.json',
    categoryKey: '扫地机',
  };
  static const Map<String, String> washer = <String, String>{
    iconKey: 'assets/images/running_device/washer.json',
    categoryKey: '洗衣机',
  };
  static const Map<String, String> dishWasher = <String, String>{
    iconKey: 'assets/images/running_device/dish_washer.json',
    categoryKey: '洗碗机',
  };
  static const Map<String, String> rangeHood = <String, String>{
    iconKey: 'assets/images/running_device/range_hood.json',
    categoryKey: '吸油烟机',
  };
  static const Map<String, String> freshAirFan = <String, String>{
    iconKey: 'assets/images/running_device/fresh_air_fan.json',
    categoryKey: '新风机',
  };

  /// [key] 产品类型编码（应用分类）
  ///
  /// [value] 运行中设备图标静态资源路径及分类名称（点位上报）
  static const Map<String, Map<String, String>> runningDeviceTypeCodeMap =
      <String, Map<String, String>>{
    // 空调
    'A177': airConditioner,
    'A178': airConditioner,
    'A120': airConditioner,
    // 洗衣机
    'A169': washer,
    'A168': washer,
    'A021': washer,
    // 洗碗机
    'A066': dishWasher,
    // 吸油烟机
    'A065': rangeHood,
    // 电视
    'A014': tv,
    // 音箱
    'A001': speaker,
    'A088': speaker,
    // 灶具
    'A047': stove,
    'A100': stove,
    // 烤箱
    'A030': oven,
    'A018': oven,
    'A101': oven,
    // 空气净化器
    'A033': airPurifier,
    // 除湿机
    'A006': dehumidifier,
    // 新风机
    'A069': freshAirFan,
    // 加湿器
    'A026': humidifier,
    // 扫地机
    'A050': cleaner,
  };
}
