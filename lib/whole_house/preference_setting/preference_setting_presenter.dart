import 'package:smart_home/whole_house/preference_setting/models/preference_setting_response_model.dart';
import 'package:upservice/model/uhome_response_model.dart';

import '../../service/http_service.dart';
import '../../store/smart_home_store.dart';
import '../whole_house_debounce.dart';
import 'store/preference_setting_action.dart';

class PreferenceSettingPresenter {
  // 单例实例
  static final PreferenceSettingPresenter _instance =
      PreferenceSettingPresenter._internal();

  // 工厂构造函数，返回单例实例
  factory PreferenceSettingPresenter() {
    return _instance;
  }

  // 私有构造函数，防止外部直接实例化
  PreferenceSettingPresenter._internal();

  /// 带防抖的设置查询
  void getWholeHousePreferenceSettingWithDebounce(String familyId) {
    WholeHouseDebounce().preferenceSettingDebounce.run(() {
      getWholeHousePreferenceSetting(familyId: familyId);
    });
  }

  /// 查询仪表盘设置数据
  Future<void> getWholeHousePreferenceSetting({String? familyId}) async {
    familyId ??= smartHomeStore.state.familyState.familyId;
    if (familyId.isEmpty) {
      return;
    }
    final PreferenceSettingResponseModel? responseModel =
        await HttpService.getWholeHousePreferenceSetting(familyId);
    if (responseModel is PreferenceSettingResponseModel) {
      smartHomeStore.dispatch(UpdateWholeHousePreferenceSettingStateAction(
          responseModel.dashBoardSwitch == PreferenceSwitchEnum.on));
    }
  }

  /// 设置仪表盘偏好
  Future<void> setWholeHousePreferenceSetting(bool value) async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    if (familyId.isEmpty) {
      return;
    }
    final UhomeResponseModel? responseModel =
        await HttpService.setWholeHousePreferenceSetting(
      familyId: familyId,
      preferType: 'dashBoardSwitch',
      switchStatus: value
          ? PreferenceSwitchEnum.on.value
          : PreferenceSwitchEnum.off.value,
    );
    if (responseModel is UhomeResponseModel &&
        responseModel.retCode == '00000') {
      smartHomeStore
          .dispatch(UpdateWholeHousePreferenceSettingStateAction(value));
    }
  }
}
