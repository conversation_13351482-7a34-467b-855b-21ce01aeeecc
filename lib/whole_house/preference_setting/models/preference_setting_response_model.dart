import 'package:upservice/model/uhome_response_model.dart';

/// 仪表盘-偏好设置查询接口返回model
class PreferenceSettingResponseModel extends UhomeResponseModel {
  PreferenceSettingResponseModel.fromJson(super.json) : super.fromJson() {
    if (super.retData['dashBoardSwitch'] is String) {
      dashBoardSwitch = PreferenceSwitchEnum.fromValue(
          super.retData['dashBoardSwitch'] as String);
    }
  }

  // 开关设置，默认开
  PreferenceSwitchEnum dashBoardSwitch = PreferenceSwitchEnum.on;

  // toJson函数
  Map<String, String> toJson() {
    return <String, String>{
      'dashBoardSwitch': dashBoardSwitch.value,
    };
  }
}

/// 仪表盘开关状态枚举
/// 表示仪表盘在首页是否显示
enum PreferenceSwitchEnum {
  off('0'),
  on('1');

  final String value;
  const PreferenceSwitchEnum(this.value);

  static PreferenceSwitchEnum fromValue(String? value) {
    return value == '0'
        ? PreferenceSwitchEnum.off
        : PreferenceSwitchEnum.on;
  }
}
