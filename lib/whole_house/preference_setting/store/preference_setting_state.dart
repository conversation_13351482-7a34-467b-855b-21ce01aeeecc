/// 仪表盘偏好设置状态
class PreferenceSettingState {
  /// [homeSwitch] 仪表盘是否在首页展示
  bool homeSwitch = false;

    /// clear清空状态方法
  void clear() {
    homeSwitch = false;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PreferenceSettingState &&
          runtimeType == other.runtimeType &&
          homeSwitch == other.homeSwitch;

  @override
  int get hashCode => homeSwitch.hashCode;

  @override
  String toString() {
    return 'PreferenceSettingState{homeSwitch: $homeSwitch}';
  }
}
