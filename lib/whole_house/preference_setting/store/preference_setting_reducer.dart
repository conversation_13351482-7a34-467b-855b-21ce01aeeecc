import 'package:redux/redux.dart';

import '../../../store/smart_home_state.dart';
import 'preference_setting_action.dart';

final Reducer<SmartHomeState> preferenceSettingReducer =
    TypedReducer<SmartHomeState, UpdateWholeHousePreferenceSettingStateAction>(
            _updateWholeHousePreferenceSettingReducer)
        .call;

/// 更新仪表盘是否在首页显示的reducer
SmartHomeState _updateWholeHousePreferenceSettingReducer(
    SmartHomeState state, UpdateWholeHousePreferenceSettingStateAction action) {
  state.wholeHouseState.preferenceSettingState.homeSwitch = action.homeSwitch;

  return state;
}
