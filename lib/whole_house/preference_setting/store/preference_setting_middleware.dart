import 'package:redux/redux.dart';

import '../../../store/smart_home_state.dart';
import '../preference_setting_presenter.dart';
import 'preference_setting_action.dart';

/// 仪表盘偏好设置中间件
/// 拦截偏好设置Action，调用设置接口
class PreferenceSettingMiddleware implements MiddlewareClass<SmartHomeState> {
  final PreferenceSettingPresenter presenter = PreferenceSettingPresenter();

  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    // 仪表盘偏好设置的Action
    if (action is SetWholeHousePreferenceSettingAction) {
      presenter.setWholeHousePreferenceSetting(action.homeSwitch);
    } else {
      next(action);
    }
  }
}
