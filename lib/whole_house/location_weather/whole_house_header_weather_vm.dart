import 'package:flutter/material.dart';

/// 全屋头部天气ViewModel，用于连接Redux与UI
@immutable
class WholeHouseHeaderWeatherViewModel {
  final String icon;
  final String value;
  final String unit;
  final Color backgroundColor;
  final String customBackgroundImage;
  final String area;
  final String humidity;
  final String humidityUnit;
  final bool visible;

  const WholeHouseHeaderWeatherViewModel({
    required this.icon,
    required this.value,
    required this.unit,
    required this.backgroundColor,
    required this.area,
    required this.humidity,
    required this.humidityUnit,
    required this.visible,
    this.customBackgroundImage = '',
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is WholeHouseHeaderWeatherViewModel &&
        other.icon == icon &&
        other.value == value &&
        other.unit == unit &&
        other.backgroundColor == backgroundColor &&
        other.customBackgroundImage == customBackgroundImage &&
        other.area == area &&
        other.humidity == humidity &&
        other.humidityUnit == humidityUnit &&
        other.visible == visible;
  }

  @override
  int get hashCode => Object.hash(
        icon,
        value,
        unit,
        backgroundColor,
        customBackgroundImage,
        area,
        humidity,
        humidityUnit,
        visible,
      );
}
