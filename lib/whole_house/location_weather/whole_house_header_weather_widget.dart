import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/location_weather/whole_house_header_weather_vm.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../../common/constant.dart';
import '../../common/smart_home_util.dart';
import '../device_env/pages/env_device_setting_page.dart';
import 'weather_selectors.dart';

/// 仪表盘顶部天气小图标
class WholeHouseHeaderWeatherWidget extends StatefulWidget {
  final String spaceId;
  final String spaceName;

  const WholeHouseHeaderWeatherWidget({
    super.key,
    required this.spaceId,
    required this.spaceName,
  });

  @override
  State<WholeHouseHeaderWeatherWidget> createState() =>
      _WholeHouseHeaderWeatherWidgetState();
}

class _WholeHouseHeaderWeatherWidgetState
    extends State<WholeHouseHeaderWeatherWidget> {
  // 地区名称最大显示长度
  static const int _maxAreaNameLength = 5;
  @override
  void initState() {
    super.initState();

    gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
      'value': '天气',
    });
  }

  @override
  void didUpdateWidget(WholeHouseHeaderWeatherWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.spaceId != widget.spaceId) {
      gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
        'value': '天气',
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用StoreConnector连接Redux与UI
    return StoreConnector<SmartHomeState, WholeHouseHeaderWeatherViewModel>(
      // 数据转换器，从Store提取ViewModel
      converter: (Store<SmartHomeState> store) =>
          WeatherSelectors.weatherViewModelWithRoomIdSelector(
              store.state, widget.spaceId),
      distinct: true,
      builder:
          (BuildContext context, WholeHouseHeaderWeatherViewModel viewModel) {
        if (!viewModel.visible) {
          return const SizedBox.shrink();
        }
        return _buildWeatherWidget(context, viewModel);
      },
    );
  }

  /// 构建天气组件
  Widget _buildWeatherWidget(
      BuildContext context, WholeHouseHeaderWeatherViewModel viewModel) {
    return GestureDetector(
      onTap: () => _handleTap(context),
      child: Container(
        constraints: const BoxConstraints(minWidth: 120),
        height: 44,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(1000),
          child: Stack(
            children: <Widget>[
              _buildBackground(viewModel),
              _buildContent(viewModel),
            ],
          ),
        ),
      ),
    );
  }

  void _handleTap(BuildContext context) {
    Network
        .isOnline()
        .then((NetworkStatus networkStatus) {
      if (!networkStatus.isOnline) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
      } else {
        _handleWeatherTap(context);
      }
    });
  }

  /// 处理天气组件点击事件
  void _handleWeatherTap(BuildContext context) {
    if (isFamilyMemberRole()) {
      ToastHelper.showToast(SmartHomeConstant.contactFamilyRoleWarning);
      return;
    }
    gioTrack(GioConst.wholeHouseIconClick, <String, String>{
      'value': '天气',
    });
    Navigator.of(context).push(
      MaterialPageRoute<void>(
        builder: (BuildContext context) => EnvDeviceSettingPage(
          spaceId: widget.spaceId,
          showWeatherCard: true,
        ),
      ),
    );
  }

  /// 构建背景组件
  Widget _buildBackground(WholeHouseHeaderWeatherViewModel viewModel) {
    return Positioned.fill(
      child: viewModel.customBackgroundImage.isNotEmpty
          ? _buildCustomBackground(viewModel.customBackgroundImage)
          : Container(color: viewModel.backgroundColor),
    );
  }

  /// 构建自定义背景图片
  Widget _buildCustomBackground(String imageUrl) {
    return CommonNetworkRefreshImg(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      alignment: Alignment.center,
    );
  }

  /// 构建内容组件
  Widget _buildContent(WholeHouseHeaderWeatherViewModel viewModel) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 20),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildWeatherIcon(viewModel.icon),
          const SizedBox(width: 8),
          _buildValueUnitWidget(viewModel.value, viewModel.unit),
          const SizedBox(width: 4),
          _buildValueUnitWidget(viewModel.humidity, viewModel.humidityUnit),
          const SizedBox(width: 4),
          _buildAreaName(viewModel.area),
        ],
      ),
    );
  }

  /// 构建天气图标
  Widget _buildWeatherIcon(String iconUrl) {
    return Center(
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: iconUrl.isEmpty ? Colors.white.withOpacity(0.7) : null,
        ),
        child: iconUrl.isNotEmpty
            ? SizedBox(
                width: 20,
                height: 20,
                child: AspectRatio(
                  aspectRatio: 1,
                  child: CommonNetworkRefreshImg(
                    imageUrl: iconUrl,
                    alignment: Alignment.center,
                    errorWidget: Image.asset(
                      'assets/icons/default_device_img.webp',
                      package: 'smart_home',
                      height: double.infinity,
                    ),
                  ),
                ),
              )
            : null,
      ),
    );
  }

  /// 构建地区名称
  Widget _buildAreaName(String area) {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.only(top: 1),
        child: Text(
          area.length > _maxAreaNameLength
              ? '${area.substring(0, _maxAreaNameLength)}...'
              : area,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            color: Colors.white,
            fontSize: 13,
            height: 1,
            fontWeight: FontWeight.w400,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
      ),
    );
  }

  Widget _buildValueUnitWidget(String value, String unit) {
    return Padding(
      padding: const EdgeInsets.only(top: 1),
      child: Text.rich(
        TextSpan(
          children: <InlineSpan>[
            TextSpan(
              text: value.isEmpty ? '- -' : value,
              style: TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
            const WidgetSpan(child: SizedBox(width: 2)),
            TextSpan(
              text: unit,
              style: TextStyle(
                fontSize: 6,
                fontWeight: FontWeight.w500,
                color: Colors.white,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
