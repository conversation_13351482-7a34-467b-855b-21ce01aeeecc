import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/whole_house/location_weather/widgets/accuracy_location_modal_tools.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

class AccuracyLocationModal {
  static void show(BuildContext context) {
    final Dialogs dialogs = Dialogs();

    dialogs.showDoubleBtnModal<dynamic>(
      context: context,
      title: '开启“精确定位”',
      child: (BuildContext context) {
        return _buildContent();
      },
      confirmText: '同意并设置',
      confirmCallback: () {
        goToPageWithDebounce(LocationWeatherUtils.appPermissionUrl);
        dialogs.closeSmartHomeModalBottomSheet();
      },
      cancelCallback: () {
        LocationWeatherUtils.gotoEditLocation();
      },
    );
  }

  static Widget _buildContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text.rich(
          TextSpan(
            children: <TextSpan>[
              TextSpan(
                text:
                    '为了在智能设备自动化功能中，精准判断日出日落时间、是否回家以及进入预设位置时，会收集设定家庭的精确位置信息。请放心，上述信息不会用于其他功能，更多信息参见',
                style: TextStyle(
                  color: const Color(0xFF666666),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              ),
              TextSpan(
                text: '《精确位置信息处理单独授权同意书》',
                style: TextStyle(
                  color: const Color(0xFF0081FF),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    goToPageWithDebounce(LocationWeatherUtils.privacyPolicyUrl);
                  },
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
