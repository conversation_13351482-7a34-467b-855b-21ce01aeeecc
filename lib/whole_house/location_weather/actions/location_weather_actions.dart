import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';
import 'package:smart_home/whole_house/location_weather/services/location/location_model.dart';

class LocationAndWeatherDataRequestAction {
  const LocationAndWeatherDataRequestAction();
}

class LocationAndWeatherChangedAction {
  final LocationModel? rawLocation;

  final OutdoorWeatherModel? outdoorWeatherModel;

  const LocationAndWeatherChangedAction({
    required this.rawLocation,
    required this.outdoorWeatherModel,
  });

  @override
  String toString() {
    return 'LocationAndWeatherChangedAction{rawLocation: $rawLocation, outdoorWeatherModel: $outdoorWeatherModel}';
  }
}
