import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/location_weather/actions/location_weather_actions.dart';
import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';
import 'package:smart_home/whole_house/location_weather/store/area_state.dart';
import 'package:smart_home/whole_house/location_weather/store/environment_state.dart';

/// 全屋状态的reducer，处理全屋相关的action
final Reducer<SmartHomeState> locationWeatherReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, LocationAndWeatherChangedAction>(
          _updateLocationAndWeather)
      .call,
]);

SmartHomeState _updateLocationAndWeather(
    SmartHomeState state, LocationAndWeatherChangedAction action) {
  String? areaName;
  final OutdoorWeatherModel? weatherModel = action.outdoorWeatherModel;
  if (weatherModel != null) {
    if (action.rawLocation == null) {
      areaName = _getAreaNameFromWeatherModel(weatherModel);
    } else {
      areaName = action.rawLocation!.areaName;
    }
  }

  final EnvironmentState newEnvironmentState =
      state.wholeHouseState.environmentState.copyWith(
    outdoorWeatherState: action.outdoorWeatherModel,
  );
  final AreaState newAreaState = state.wholeHouseState.areaState.copyWith(
    areaName: areaName,
  );
  return state.copyWith(
    wholeHouseState: state.wholeHouseState.copyWith(
      environmentState: newEnvironmentState,
      areaState: newAreaState,
    ),
  );
}

/// 优先级：区/县 > 城市 > 省份 > 国家
String _getAreaNameFromWeatherModel(OutdoorWeatherModel weatherModel) {
  if (weatherModel.area.district.isNotEmpty) {
    return weatherModel.area.district;
  }

  if (weatherModel.area.city.isNotEmpty) {
    return weatherModel.area.city;
  }

  if (weatherModel.area.province.isNotEmpty) {
    return weatherModel.area.province;
  }

  if (weatherModel.area.country.isNotEmpty) {
    return weatherModel.area.country;
  }

  return '';
}
