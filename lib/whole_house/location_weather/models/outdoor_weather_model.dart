import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/whole_house/location_weather/services/weather/weather_response_model.dart';

/// 室外天气模型
@immutable
class OutdoorWeatherModel {
  /// 温度，单位摄氏度
  final String temperature;

  /// 天气现象代码，如 CLEAR_DAY, CLOUDY 等
  final String skycon;

  /// 自定义图标路径
  final String customIcon;

  /// 自定义背景图片路径
  final String customBackgroundImage;

  final String windDirection;

  final String windPower;

  final String humidity;

  final String pm25;

  /// 空气质量
  final String aqi;

  /// 区域信息, 如果家庭和定位都获取不到，则使用服务端返回ip定位
  final AreaModel area;

  const OutdoorWeatherModel({
    required this.temperature,
    required this.skycon,
    required this.customIcon,
    this.customBackgroundImage = '',
    required this.windDirection,
    required this.windPower,
    required this.humidity,
    required this.area,
    required this.pm25,
    required this.aqi,
  });

  factory OutdoorWeatherModel.fromJson(Map<dynamic, dynamic> json) {
    return OutdoorWeatherModel(
      temperature: json.stringValueForKey('temperature', '0.0'),
      skycon: json.stringValueForKey('skycon', ''),
      customIcon: json.stringValueForKey('customIcon', ''),
      customBackgroundImage:
          json.stringValueForKey('customBackgroundImage', ''),
      windDirection: json.stringValueForKey('windDirection', ''),
      windPower: json.stringValueForKey('windPower', ''),
      humidity: json.stringValueForKey('humidity', '0'),
      area:
          AreaModel.fromJson(json.mapValueForKey('area', <dynamic, dynamic>{})),
      pm25: json.stringValueForKey('pm25', ''),
      aqi: json.stringValueForKey('aqi', ''),
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'temperature': temperature,
      'skycon': skycon,
      'customIcon': customIcon,
      'customBackgroundImage': customBackgroundImage,
      'windDirection': windDirection,
      'windPower': windPower,
      'humidity': humidity,
      'area': area.toJson(),
      'pm25': pm25,
      'aqi': aqi,
    };
  }

  /// 创建新实例，同时可更新部分属性
  OutdoorWeatherModel copyWith({
    String? temperature,
    String? skycon,
    String? customIcon,
    String? customBackgroundImage,
    String? windDirection,
    String? windPower,
    String? humidity,
    AreaModel? area,
    String? pm25,
    String? aqi,
  }) {
    return OutdoorWeatherModel(
      temperature: temperature ?? this.temperature,
      skycon: skycon ?? this.skycon,
      customIcon: customIcon ?? this.customIcon,
      customBackgroundImage:
          customBackgroundImage ?? this.customBackgroundImage,
      windDirection: windDirection ?? this.windDirection,
      windPower: windPower ?? this.windPower,
      humidity: humidity ?? this.humidity,
      area: area ?? this.area,
      pm25: pm25 ?? this.pm25,
      aqi: aqi ?? this.aqi,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is OutdoorWeatherModel &&
        other.temperature == temperature &&
        other.skycon == skycon &&
        other.customIcon == customIcon &&
        other.customBackgroundImage == customBackgroundImage &&
        other.windDirection == windDirection &&
        other.windPower == windPower &&
        other.humidity == humidity &&
        other.area == area &&
        other.pm25 == pm25 &&
        other.aqi == aqi;
  }

  @override
  int get hashCode => Object.hash(
        temperature,
        skycon,
        customIcon,
        customBackgroundImage,
        windDirection,
        windPower,
        humidity,
        area,
        pm25,
        aqi,
      );

  @override
  String toString() {
    return 'OutdoorWeatherModel{temperature: $temperature, skycon: $skycon, '
        'customIcon: $customIcon, customBackgroundImage: $customBackgroundImage, '
        'windDirection: $windDirection, windPower: $windPower, humidity: $humidity, '
        'area: $area, pm25: $pm25, aqi: $aqi}';
  }
}
