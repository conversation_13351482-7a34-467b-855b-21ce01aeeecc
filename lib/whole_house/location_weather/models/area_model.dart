import 'package:device_utils/typeId_parse/template_map.dart';

/// 区域模型
class AreaModel {
  /// 区域名称
  final String areaName;

  /// 区域代码
  final String areaCode;

  /// 构造函数
  const AreaModel({
    required this.areaName,
    required this.areaCode,
  });

  /// 从JSON创建
  factory AreaModel.fromJson(Map<String, dynamic> json) {
    return AreaModel(
      areaName: json.stringValueForKey('name', ''),
      areaCode: json.stringValueForKey('code', ''),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'name': areaName,
      'code': areaCode,
    };
  }
} 
