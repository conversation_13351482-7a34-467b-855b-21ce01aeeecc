import 'package:flutter/material.dart';
import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/env_device_state.dart';
import 'package:smart_home/whole_house/device_env/view_models/weather_card_view_model.dart';
import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';
import 'package:smart_home/whole_house/location_weather/whole_house_header_weather_vm.dart';

import '../device_env/env_device_constants.dart';

/// 全屋头部天气选择器，用于从Redux状态中提取数据
class WeatherSelectors {
  /// 创建天气视图模型选择器
  static final Selector<SmartHomeState, WholeHouseHeaderWeatherViewModel>
      weatherViewModelSelector = createSelector3(
    (SmartHomeState state) =>
        state.wholeHouseState.environmentState.outdoorWeatherState,
    (SmartHomeState state) => state.wholeHouseState.areaState.areaName,
    (SmartHomeState state) => state.wholeHouseState.envDeviceState,
    _createViewModel,
  );

  static final Selector<SmartHomeState, WeatherCardViewModel>
      weatherCardSelector = createSelector2(
    (SmartHomeState state) =>
        state.wholeHouseState.environmentState.outdoorWeatherState,
    (SmartHomeState state) => state.wholeHouseState.areaState.areaName,
    _createWeatherCardViewModel,
  );

  static WholeHouseHeaderWeatherViewModel weatherViewModelWithRoomIdSelector(
      SmartHomeState state, String roomId) {
    final WholeHouseHeaderWeatherViewModel defaultViewModel =
        weatherViewModelSelector(state);

    return WholeHouseHeaderWeatherViewModel(
      icon: defaultViewModel.icon,
      value: defaultViewModel.value,
      unit: defaultViewModel.unit,
      backgroundColor: defaultViewModel.backgroundColor,
      customBackgroundImage: defaultViewModel.customBackgroundImage,
      area: defaultViewModel.area,
      humidity: defaultViewModel.humidity,
      humidityUnit: defaultViewModel.humidityUnit,
      visible: state.familyState.familyId == roomId && defaultViewModel.visible,
    );
  }

  static _WeatherBaseData _extractBaseWeatherData(
    OutdoorWeatherModel? outdoorWeather,
    String areaName,
  ) {
    return _WeatherBaseData(
      icon: outdoorWeather?.customIcon ?? '',
      value: outdoorWeather?.temperature ?? '',
      unit: EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_TEMPERATURE),
      customBackgroundImage: outdoorWeather?.customBackgroundImage ?? '',
      backgroundColor: _getBackgroundColor(outdoorWeather?.skycon),
      humidity: outdoorWeather?.humidity ?? '',
      humidityUnit:
          EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_HUMIDITY),
      area: areaName,
    );
  }

  static WholeHouseHeaderWeatherViewModel _createViewModel(
    OutdoorWeatherModel? outdoorWeather,
    String areaName,
    EnvDeviceState envDeviceState,
  ) {
    final _WeatherBaseData baseData =
        _extractBaseWeatherData(outdoorWeather, areaName);
    final bool visible = outdoorWeather != null && areaName.isNotEmpty;

    return WholeHouseHeaderWeatherViewModel(
      icon: baseData.icon,
      value: baseData.value,
      unit: baseData.unit,
      backgroundColor: baseData.backgroundColor,
      customBackgroundImage: baseData.customBackgroundImage,
      area: baseData.area,
      humidity: baseData.humidity,
      humidityUnit: baseData.humidityUnit,
      visible: visible,
    );
  }

  static WeatherCardViewModel _createWeatherCardViewModel(
    OutdoorWeatherModel? outdoorWeather,
    String areaName,
  ) {
    final _WeatherBaseData baseData =
        _extractBaseWeatherData(outdoorWeather, areaName);

    return WeatherCardViewModel(
      icon: baseData.icon,
      value: baseData.value,
      unit: baseData.unit,
      backgroundColor: baseData.backgroundColor,
      customBackgroundImage: baseData.customBackgroundImage,
      area: baseData.area,
      humidity: baseData.humidity,
      humidityUnit: baseData.humidityUnit,
      pm25: outdoorWeather?.pm25 ?? '',
      aqi: outdoorWeather?.aqi ?? '',
    );
  }

  /// 根据天气现象获取背景颜色
  static Color _getBackgroundColor(String? skycon) {
    const Color defaultColor = Color(0xFF3C7BF9);
    return defaultColor;
  }
}

class _WeatherBaseData {
  final String icon;
  final String value;
  final String unit;
  final Color backgroundColor;
  final String customBackgroundImage;
  final String area;
  final String humidity;
  final String humidityUnit;

  const _WeatherBaseData({
    required this.icon,
    required this.value,
    required this.unit,
    required this.backgroundColor,
    required this.customBackgroundImage,
    required this.area,
    required this.humidity,
    required this.humidityUnit,
  });
}
