import 'package:device_utils/typeId_parse/template_map.dart';

/// 经纬度类
/// 
/// 用于存储和管理位置的经纬度信息
class LocationCoordinateInfo {
  /// 经度
  final double longitude;
  
  /// 纬度
  final double latitude;
  
  /// 构造函数
  LocationCoordinateInfo({
    this.longitude = 0.0,
    this.latitude = 0.0,
  });
  
  /// 创建空的位置坐标信息
  factory LocationCoordinateInfo.empty() {
    return LocationCoordinateInfo();
  }
  
  /// 从JSON创建位置坐标信息
  factory LocationCoordinateInfo.fromJson(Map<String, dynamic> json) {
    return LocationCoordinateInfo(
      longitude: json.doubleValueForKey('longitude', 0.0),
      latitude: json.doubleValueForKey('latitude', 0.0),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'longitude': longitude,
      'latitude': latitude,
    };
  }
  
  /// 是否有效的坐标信息
  bool get isValid => longitude != 0.0 && latitude != 0.0;
  
  /// 创建新实例，同时可更新部分属性
  LocationCoordinateInfo copyWith({
    double? longitude,
    double? latitude,
  }) {
    return LocationCoordinateInfo(
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationCoordinateInfo &&
           other.longitude == longitude &&
           other.latitude == latitude;
  }

  @override
  int get hashCode => longitude.hashCode ^ latitude.hashCode;

  @override
  String toString() {
    return 'LocationCoordinateInfo{longitude: $longitude, latitude: $latitude}';
  }
}


/// 位置信息模型
class LocationModel {
  /// 区域ID
  final String areaCode;
  
  /// 区域名称
  final String areaName;

  /// 经纬度
  final LocationCoordinateInfo coordinate;
  
  /// 位置来源
  final LocationSource source;

  /// 构造函数
  LocationModel({
    required this.areaCode,
    required this.areaName,
    required this.coordinate,
    required this.source,
  });

  /// 获取空白位置模型
  factory LocationModel.empty() {
    return LocationModel(
      areaCode: '',
      areaName: '',
      coordinate: LocationCoordinateInfo.empty(),
      source: LocationSource.none,
    );
  }
  
  /// 从JSON创建实例
  factory LocationModel.fromJson(Map<String, dynamic> json) {
    final Map<dynamic, dynamic> rawCoordinateJson = json.mapValueForKey('coordinate', <dynamic, dynamic>{});
    final Map<String, dynamic> coordinateJson = rawCoordinateJson.isEmpty
        ? <String, dynamic>{}
        : Map<String, dynamic>.from(rawCoordinateJson);
    
    return LocationModel(
      areaCode: json.stringValueForKey('areaCode', ''),
      areaName: json.stringValueForKey('areaName', ''),
      coordinate: coordinateJson.isNotEmpty
          ? LocationCoordinateInfo.fromJson(coordinateJson)
          : LocationCoordinateInfo.empty(),
      source: _sourceFromString(json.stringValueForKey('source', '')),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'areaCode': areaCode,
      'areaName': areaName,
      'coordinate': coordinate.toJson(),
      'source': source.toString().split('.').last,
    };
  }
  
  /// 从字符串解析LocationSource枚举
  static LocationSource _sourceFromString(String source) {
    switch (source) {
      case 'family':
        return LocationSource.family;
      case 'phone':
        return LocationSource.phone;
      default:
        return LocationSource.none;
    }
  }
  
  /// 创建新实例，同时可更新部分属性
  LocationModel copyWith({
    String? areaCode,
    String? areaName,
    LocationCoordinateInfo? coordinate,
    LocationSource? source,
  }) {
    return LocationModel(
      areaCode: areaCode ?? this.areaCode,
      areaName: areaName ?? this.areaName,
      coordinate: coordinate ?? this.coordinate,
      source: source ?? this.source,
    );
  }

  /// 是否有效位置
  bool get isValid => areaCode.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel &&
           other.areaCode == areaCode &&
           other.areaName == areaName &&
           other.coordinate == coordinate &&
           other.source == source;
  }

  @override
  int get hashCode => Object.hash(
    areaCode,
    areaName,
    coordinate,
    source,
  );

  @override
  String toString() {
    return 'LocationModel{areaId: $areaCode, areaName: $areaName, coordinate: $coordinate, source: $source}';
  }
}

/// 位置信息来源
enum LocationSource {
  /// 家庭位置（最高优先级）
  family,
  
  /// 手机位置（次优先级）
  phone,
  
  /// 未知来源
  none
} 