import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:location/location.dart';
import 'package:location/locationmodel.dart' as location_model;

import 'location_model.dart';
import 'location_service.dart';

/// 实际位置服务实现类，负责获取用户当前位置信息
class RealLocationService implements LocationService {
  static const String _logTag = 'RealLocationService';

  /// 获取当前家庭信息
  Future<FamilyInfo> getCurrentFamilyInfo() async {
    final FamilyModel familyModel = await Family.getCurrentFamily();
    return familyModel.info;
  }

  /// 从家庭位置创建LocationModel
  LocationModel? _createLocationFromFamilyInfo(FamilyInfo familyInfo) {
    if (familyInfo.familyLocation.cityCode.isEmpty) {
      return null;
    }
    
    return LocationModel(
      areaCode: familyInfo.familyLocation.cityCode,
      areaName: familyInfo.familyPosition.split(' ').last,
      coordinate: LocationCoordinateInfo(
        longitude: familyInfo.familyLocation.longitude,
        latitude: familyInfo.familyLocation.latitude,
      ),
      source: LocationSource.family,
    );
  }

  /// 获取手机位置信息
  Future<LocationModel?> _getPhoneLocation() async {
    try {
      final location_model.LocationModel locationCity =
          await Location.getLocation(isNeedRequestPermission: false);
          
      final bool hasValidLocation = locationCity.adcode.isNotEmpty && 
          (locationCity.district.isNotEmpty || locationCity.city.isNotEmpty);
      
      if (!hasValidLocation) {
        return null;
      }
      
      return LocationModel(
        areaCode: locationCity.adcode,
        areaName: locationCity.district.isNotEmpty
            ? locationCity.district
            : locationCity.city,
        coordinate: LocationCoordinateInfo(
          longitude: locationCity.longitude,
          latitude: locationCity.latitude,
        ),
        source: LocationSource.phone,
      );
    } catch (e) {
      DevLogger.error(tag: _logTag, msg: '_getPhoneLocation error: $e');
      return null;
    }
  }

  /// 获取当前地址
  ///
  /// 优先级：家庭位置 > 手机定位
  /// 如果高优先级的位置无效，则尝试使用次优先级的位置
  @override
  Future<LocationModel?> getLocation() async {
    try {
      // 首先获取当前家庭信息
      final FamilyInfo familyInfo = await getCurrentFamilyInfo();
      final LocationModel? familyLocation = _createLocationFromFamilyInfo(familyInfo);
      
      if (familyLocation != null) {
        return familyLocation;
      }

      return null;
      // 如果家庭位置无效，则尝试使用手机定位
      //return await _getPhoneLocation();
    } catch (e) {
      DevLogger.error(tag: _logTag, msg: 'getLocation() error: $e');
      return null;
    }
  }

  /// 获取区域代码
  Future<String?> getAreaCode() async {
    final LocationModel? location = await getLocation();
    return location?.areaCode;
  }
}
