import 'package:device_utils/log/log.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/service/http_service.dart';

import '../../models/outdoor_weather_model.dart';
import 'weather_response_model.dart';
import 'weather_service.dart';

/// 真实天气服务实现
class RealWeatherService implements WeatherService {
  /// 私有构造函数
  RealWeatherService._();

  /// 单例实例
  static final RealWeatherService _instance = RealWeatherService._();

  /// 获取实例
  factory RealWeatherService() => _instance;

  @override
  Future<OutdoorWeatherModel?> getWholeHouseWeather({
    String? areaId,
    String? longitude,
    String? latitude,
  }) async {
    try {
      // 调用HTTP服务获取天气数据
      final WeatherResponseModel? responseModel =
          await HttpService.getWholeHouseWeather(
        areaId: areaId,
        longitude: longitude,
        latitude: latitude,
      );

      // 检查响应是否为空
      if (responseModel == null || responseModel.outdoorWeather == null) {
        _logError('获取天气数据失败: responseModel 为空');
        return null;
      }

      // 获取天气条件模型
      final WeatherConditionModel weatherCondition =
          responseModel.outdoorWeather!.weatherCondition;
      final AreaModel area = responseModel.outdoorWeather!.area;
      final AirConditionModel airCondition =
          responseModel.outdoorWeather!.airCondition;
      // 解析并返回天气模型
      return _createOutdoorWeatherModel(weatherCondition, area, airCondition);
    } catch (e, stackTrace) {
      _logError('获取或转换天气数据失败: $e\n$stackTrace');
      return null;
    }
  }

  /// 创建天气模型
  OutdoorWeatherModel _createOutdoorWeatherModel(
    WeatherConditionModel condition,
    AreaModel area,
    AirConditionModel airCondition,
  ) {
    return OutdoorWeatherModel(
      temperature: condition.temp,
      skycon: condition.skycon,
      customIcon: condition.customIcon,
      windDirection: condition.windDir,
      windPower: condition.windLevel,
      humidity: condition.humidity,
      customBackgroundImage: condition.customBackgroundImage,
      area: area,
      pm25: airCondition.pm25,
      aqi: airCondition.aqi,
    );
  }

  /// 记录错误信息
  void _logError(String message) {
    DevLogger.error(
      tag: SmartHomeConstant.package,
      msg: message,
    );
  }
}
