import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:upservice/model/uhome_response_model.dart';

/// 天气API响应模型
class WeatherResponseModel extends UhomeResponseModel {
  /// 响应数据
  WeatherDataModel? outdoorWeather;

  WeatherResponseModel.fromJson(super.data) : super.fromJson() {
    outdoorWeather = WeatherDataModel.fromJson(
      super.retData.mapValueForKey('outdoorWeather', <dynamic, dynamic>{}),
    );
  }

  /// 构造函数
  WeatherResponseModel({
    this.outdoorWeather,
  }) : super.fromJson(null);


  /// 模型转JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'outdoorWeather': outdoorWeather?.toJson(),
    };
  }
}

/// 天气数据模型
@immutable
class WeatherDataModel {
  /// 区域信息
  final AreaModel area;
  
  /// 语言
  final String language;
  
  /// 天气状况
  final WeatherConditionModel weatherCondition;
  
  /// 空气质量
  final AirConditionModel airCondition;

  /// 构造函数
  const WeatherDataModel({
    required this.area,
    required this.language,
    required this.weatherCondition,
    required this.airCondition,
  });

  /// 从JSON创建模型
  factory WeatherDataModel.fromJson(Map<dynamic, dynamic> json) {
    return WeatherDataModel(
      area: AreaModel.fromJson(
        json.mapValueForKey('area', <dynamic, dynamic>{}),
      ),
      language: json.stringValueForKey('language', ''),
      weatherCondition: WeatherConditionModel.fromJson(
        json.mapValueForKey('weatherCondition', <dynamic, dynamic>{}),
      ),
      airCondition: AirConditionModel.fromJson(
        json.mapValueForKey('airCondition', <dynamic, dynamic>{}),
      ),
    );
  }

  /// 模型转JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'area': area.toJson(),
      'language': language,
      'weatherCondition': weatherCondition.toJson(),
      'airCondition': airCondition.toJson(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WeatherDataModel &&
        other.area == area &&
        other.language == language &&
        other.weatherCondition == weatherCondition &&
        other.airCondition == airCondition;
  }

  @override
  int get hashCode =>
      Object.hash(area, language, weatherCondition, airCondition);
}

/// 区域信息模型
@immutable
class AreaModel {
  /// 国家
  final String country;

  /// 区域ID
  final String areaId;

  /// 省份
  final String province;

  /// 城市
  final String city;

  /// 区县
  final String district;

  /// 构造函数
  const AreaModel({
    required this.country,
    required this.areaId,
    required this.province,
    required this.city,
    required this.district,
  });

  /// 从JSON创建模型
  factory AreaModel.fromJson(Map<dynamic, dynamic> json) {
    return AreaModel(
      country: json.stringValueForKey('country', ''),
      areaId: json.stringValueForKey('areaId', ''),
      province: json.stringValueForKey('province', ''),
      city: json.stringValueForKey('city', ''),
      district: json.stringValueForKey('district', ''),
    );
  }

  /// 模型转JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'country': country,
      'areaId': areaId,
      'province': province,
      'city': city,
      'district': district,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is AreaModel &&
        other.country == country &&
        other.areaId == areaId &&
        other.province == province &&
        other.city == city &&
        other.district == district;
  }

  @override
  int get hashCode => Object.hash(country, areaId, province, city, district);

  @override
  String toString() {
    return 'AreaModel{country: $country, areaId: $areaId, province: $province, city: $city, district: $district}';
  }
}

/// 天气状况模型
@immutable
class WeatherConditionModel {
  /// 降水量
  final String precipitation;

  /// 天气状况
  final String condition;

  /// 温度
  final String temp;

  /// 日落时间
  final String sunSet;

  final String windLevel;

  /// 天气图标
  final String icon;

  final String customIcon;

  //自定义天气背景图
  final String customBackgroundImage;

  final String skycon;

  final String humidity;

  final String updateTime;

  /// 气压
  final String pressure;

  final String windDir;

  /// 日出时间
  final String sunRise;

  const WeatherConditionModel({
    required this.precipitation,
    required this.condition,
    required this.temp,
    required this.sunSet,
    required this.windLevel,
    required this.icon,
    required this.customIcon,
    required this.customBackgroundImage,
    required this.skycon,
    required this.humidity,
    required this.updateTime,
    required this.pressure,
    required this.windDir,
    required this.sunRise,
  });

  factory WeatherConditionModel.fromJson(Map<dynamic, dynamic> json) {
    return WeatherConditionModel(
      precipitation: json.stringValueForKey('precipitation', ''),
      condition: json.stringValueForKey('condition', ''),
      temp: json.stringValueForKey('temp', ''),
      sunSet: json.stringValueForKey('sunSet', ''),
      windLevel: json.stringValueForKey('windLevel', ''),
      icon: json.stringValueForKey('icon', ''),
      customIcon: json.stringValueForKey('customIcon', ''),
      customBackgroundImage:
          json.stringValueForKey('customBackgroundImage', ''),
      skycon: json.stringValueForKey('skycon', ''),
      humidity: json.stringValueForKey('humidity', ''),
      updateTime: json.stringValueForKey('updateTime', ''),
      pressure: json.stringValueForKey('pressure', ''),
      windDir: json.stringValueForKey('windDir', ''),
      sunRise: json.stringValueForKey('sunRise', ''),
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'precipitation': precipitation,
      'condition': condition,
      'temp': temp,
      'sunSet': sunSet,
      'windLevel': windLevel,
      'icon': icon,
      'customIcon': customIcon,
      'customBackgroundImage': customBackgroundImage,
      'skycon': skycon,
      'humidity': humidity,
      'updateTime': updateTime,
      'pressure': pressure,
      'windDir': windDir,
      'sunRise': sunRise,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WeatherConditionModel &&
        other.precipitation == precipitation &&
        other.condition == condition &&
        other.temp == temp &&
        other.sunSet == sunSet &&
        other.windLevel == windLevel &&
        other.icon == icon &&
        other.customIcon == customIcon &&
        other.customBackgroundImage == customBackgroundImage &&
        other.skycon == skycon &&
        other.humidity == humidity &&
        other.updateTime == updateTime &&
        other.pressure == pressure &&
        other.windDir == windDir &&
        other.sunRise == sunRise;
  }

  @override
  int get hashCode => Object.hash(
        precipitation,
        condition,
        temp,
        sunSet,
        windLevel,
        Object.hash(
          icon,
          customIcon,
          customBackgroundImage,
          skycon,
          humidity,
          updateTime,
          pressure,
          windDir,
          sunRise,
        ),
      );
}

/// 空气质量模型
@immutable
class AirConditionModel {
  final String pm25;

  /// 空气质量指数
  final String aqi;

  final String updateTime;

  const AirConditionModel({
    required this.pm25,
    required this.aqi,
    required this.updateTime,
  });

  factory AirConditionModel.fromJson(Map<dynamic, dynamic> json) {
    return AirConditionModel(
      pm25: json.stringValueForKey('pm25', ''),
      aqi: json.stringValueForKey('aqi', ''),
      updateTime: json.stringValueForKey('updateTime', ''),
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'pm25': pm25,
      'aqi': aqi,
      'updateTime': updateTime,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AirConditionModel &&
        other.pm25 == pm25 &&
        other.aqi == aqi &&
        other.updateTime == updateTime;
  }

  @override
  int get hashCode => Object.hash(pm25, aqi, updateTime);
}
