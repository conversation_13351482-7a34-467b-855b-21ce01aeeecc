import 'package:device_utils/log/log.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/location_weather/actions/location_weather_actions.dart';
import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';
import 'package:smart_home/whole_house/location_weather/services/location/location_model.dart';
import 'package:smart_home/whole_house/location_weather/services/location/real_location_service.dart';
import 'package:smart_home/whole_house/location_weather/services/weather/real_weather_service.dart';

class LocationWeatherMiddleware implements MiddlewareClass<SmartHomeState> {
  const LocationWeatherMiddleware();

  @override
  void call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    next(action);

    if (action is LocationAndWeatherDataRequestAction) {
      _fetchLocationAndWeatherData();
    }
  }

  Future<void> _fetchLocationAndWeatherData() async {
    try {
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg:
            'LocationWeatherMiddleware: Starting to fetch location and weather data',
      );

      final LocationModel? location = await RealLocationService().getLocation();
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'LocationWeatherMiddleware: Got location : $location',
      );

      final OutdoorWeatherModel? outdoorWeatherModel =
          await RealWeatherService().getWholeHouseWeather(
        areaId: location?.areaCode,
        longitude: location?.coordinate.longitude.toString(),
        latitude: location?.coordinate.latitude.toString(),
      );

      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg:
            'LocationWeatherMiddleware: Got weather data : $outdoorWeatherModel',
      );

      smartHomeStore.dispatch(LocationAndWeatherChangedAction(
        rawLocation: location,
        outdoorWeatherModel: outdoorWeatherModel,
      ));
    } catch (e, stackTrace) {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg:
            'LocationWeatherMiddleware: Error fetching location and weather data : $e\n$stackTrace',
      );
    }
  }
}
