import 'package:flutter/foundation.dart';
import 'package:device_utils/typeId_parse/template_map.dart';

/// 区域状态模型，包含区域相关信息
class AreaState {
  /// 区域名称
  final String areaName;

  /// 构造函数
  const AreaState({
    this.areaName = '',
  });

  /// 从JSON创建实例
  factory AreaState.fromJson(Map<String, dynamic> json) {
    return AreaState(
      areaName: json.stringValueForKey('areaName', ''),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'areaName': areaName,
    };
  }

  /// 创建新实例，同时可更新部分属性
  AreaState copyWith({
    String? areaName,
  }) {
    return AreaState(
      areaName: areaName ?? this.areaName,
    );
  }

  /// 判断是否相等
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AreaState && other.areaName == areaName;
  }

  /// 获取哈希码
  @override
  int get hashCode => areaName.hashCode;

  /// 字符串表示
  @override
  String toString() {
    return 'AreaState{areaName: $areaName}';
  }
}
