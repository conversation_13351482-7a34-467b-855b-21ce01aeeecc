import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';

/// 环境状态模型，包含室内外环境相关信息
class EnvironmentState {
  /// 室外天气状态
  final OutdoorWeatherModel? outdoorWeatherState;
  
  const EnvironmentState({
    this.outdoorWeatherState,
  });
  
  factory EnvironmentState.fromJson(Map<String, dynamic> json) {
    final Map<dynamic, dynamic> weatherMap = json.mapValueForKey('outdoorWeatherState', <dynamic, dynamic>{});
    return EnvironmentState(
      outdoorWeatherState: weatherMap.isNotEmpty
          ? OutdoorWeatherModel.fromJson(Map<String, dynamic>.from(weatherMap))
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      if (outdoorWeatherState != null) 'outdoorWeatherState': outdoorWeatherState!.toJson(),
    };
  }
  
  EnvironmentState copyWith({
    OutdoorWeatherModel? outdoorWeatherState,
    bool clearOutdoorWeatherState = false,
  }) {
    return EnvironmentState(
      outdoorWeatherState: clearOutdoorWeatherState
          ? null
          : outdoorWeatherState ?? this.outdoorWeatherState,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnvironmentState && 
           other.outdoorWeatherState == outdoorWeatherState;
  }
  
  @override
  int get hashCode => outdoorWeatherState.hashCode;
  
  @override
  String toString() {
    return 'EnvironmentState{outdoorWeatherState: $outdoorWeatherState}';
  }
}
 
