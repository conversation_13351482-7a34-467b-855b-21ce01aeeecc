import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';

enum EmptyType { alarm, consumables, runningDevice }

class WholeHousePopupEmptyWidget extends StatelessWidget {
  const WholeHousePopupEmptyWidget({
    super.key,
    required this.desc,
    required this.type,
  });

  final String desc;
  final EmptyType type;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 12,
      ),
      height: 108,
      child: Center(
        child: Column(
          children: <Widget>[
            Image.asset(
              _convertImage(),
              width: 80,
              height: 80,
              package: SmartHomeConstant.package,
            ),
            const SizedBox(
              height: 8,
            ),
            Text(
              desc,
              style: const TextStyle(
                fontSize: 14,
                color: Color.fromRGBO(141, 141, 141, 1),
              ),
            )
          ],
        ),
      ),
    );
  }

  String _convertImage() {
    switch (type) {
      case EmptyType.alarm:
        return 'assets/images/alarm_default.webp';
      case EmptyType.consumables:
        return 'assets/images/consumables_default.webp';
      case EmptyType.runningDevice:
        return 'assets/images/running_device_default.webp';
    }
  }
}
