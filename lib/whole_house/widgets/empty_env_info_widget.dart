import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/whole_house/device_env/pages/env_device_setting_page.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../../common/smart_home_util.dart';

/// 全屋环境信息为空时显示的组件
class EmptyEnvInfoWidget extends StatelessWidget {
  /// 空间ID，用于跳转到环境设备设置页面
  final String spaceId;

  const EmptyEnvInfoWidget({
    super.key,
    required this.spaceId,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
         if (isFamilyMemberRole()) {
          ToastHelper.showToast(SmartHomeConstant.contactFamilyRoleWarning);
          return;
        }
        gioTrack(GioConst.wholeHouseIconClick, <String, String>{
          'value': '暂无环境信息',
        });
        Navigator.of(context).push(
          MaterialPageRoute<void>(
            builder: (BuildContext context) => EnvDeviceSettingPage(
              spaceId: spaceId,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.only(left: 20, right: 20, bottom: 7),
        margin: const EdgeInsets.only(bottom: 5),//增加可点击区域
        height: 25,
        child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                '暂无环境信息',
                style: TextStyle(
                    color: const Color(0xFF999999),
                    fontSize: 13,
                    height: 1.0,
                    leadingDistribution: TextLeadingDistribution.even,
                    fontWeight: FontWeight.w400,
                    fontFamilyFallback: fontFamilyFallback()),
              ),
              const SizedBox(width: 4),
              Padding(
                padding: const EdgeInsets.only(top: 1.8),
                child: Image.asset(
                  'assets/images/env_device/arrow.webp',
                  width: 12,
                  height: 12,
                  package: SmartHomeConstant.package,
                ),
              ),
            ],
          ),
      ),
    );
  }
} 
