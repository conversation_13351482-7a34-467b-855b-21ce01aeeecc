import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../common/common_network_image_widget.dart';
import '../../common/constant.dart';

// 半弹窗列表组件WholeHouseListItemWidget(故障、运行中设备)
class WholeHouseListItemWidget extends StatelessWidget {
  const WholeHouseListItemWidget(
      {super.key,
      this.deviceImage = '',
      this.deviceName = '',
      this.roomName = '',
      this.desc = '',
      this.descColor = const Color(0xFF666666),
      this.clickCallback});

  /// 图标
  final String deviceImage;

  /// 设备名称
  final String deviceName;

  /// 房间名称(包含楼层)
  final String roomName;

  /// 描述(故障、运行中)
  final String desc;

  /// 描述的颜色
  final Color descColor;

  /// 点击的回调函数
  final void Function(BuildContext context)? clickCallback;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: PressableOverlayWidget(
        child: GestureDetector(
          onTap: clickCallback != null
              ? () {
                  // 添加统一关闭弹窗逻辑
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  }
                  clickCallback!(context);
                }
              : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(children: <Widget>[
              // 设备图片
              DeviceImageWidget(deviceImage: deviceImage),
              // 设备信息
              DeviceInfoWidget(
                  deviceName: deviceName,
                  roomName: roomName,
                  desc: desc,
                  descColor: descColor),
              // 右箭头
              Image.asset(
                'assets/icons/whole_house_right_arrow.webp',
                width: 16,
                height: 16,
                package: SmartHomeConstant.package,
              )
            ]),
          ),
        ),
      ),
    );
  }
}

/// 设备图片组件
class DeviceImageWidget extends StatelessWidget {
  const DeviceImageWidget({super.key, required this.deviceImage});

  /// 设备图片url
  final String deviceImage;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: SizedBox(
        width: 40,
        height: 40,
        child: AspectRatio(
            aspectRatio: 1,
            child: CommonNetworkRefreshImg(
              imageUrl: deviceImage,
              alignment: Alignment.center,
              errorWidget: Image.asset(
                'assets/icons/default_device_img.webp',
                package: 'smart_home',
                height: double.infinity,
              ),
            )),
      ),
    );
  }
}

/// 设备信息组件，包含 设备名称、房间名称 | 描述(故障、运行中)
class DeviceInfoWidget extends StatelessWidget {
  const DeviceInfoWidget(
      {super.key,
      this.deviceName = '',
      this.roomName = '',
      this.desc = '',
      this.descColor = const Color(0xFF666666)});

  /// 设备名称
  final String deviceName;

  /// 房间名称(包含楼层)
  final String roomName;

  /// 描述(故障、运行中)
  final String desc;

  /// 描述的颜色
  final Color descColor;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 设备名称
          if (deviceName.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(deviceName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      color: Color(0xff111111),
                      fontSize: 16,
                      fontWeight: FontWeight.w500)),
            ),

          // 房间 + 描述(故障/运行中)
          if (roomName.isNotEmpty || desc.isNotEmpty)
            RoomInfoWidget(
              roomName: roomName,
              desc: desc,
              descColor: descColor,
            ),
        ],
      ),
    );
  }
}

/// 房间信息组件，显示房间名称和描述，中间有分隔符
class RoomInfoWidget extends StatelessWidget {
  const RoomInfoWidget({
    super.key,
    required this.roomName,
    required this.desc,
    required this.descColor,
  });

  /// 房间名称(包含楼层)
  final String roomName;

  /// 描述(故障、运行中)
  final String desc;

  /// 描述的颜色
  final Color descColor;

  static const TextStyle _baseTextStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: Color(0xFF666666),
  );

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Row(
        children: <Widget>[
          Text(roomName, style: _baseTextStyle),
          if (desc.isNotEmpty) const Text('｜', style: _baseTextStyle),
          if (desc.isNotEmpty)
            Flexible(
              child: Text(
                desc,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: descColor),
              ),
            )
        ],
      ),
    );
  }
}
