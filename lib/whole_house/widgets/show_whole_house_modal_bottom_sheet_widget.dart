// 定一个showWholeHouseModalBottomSheetWidget方法，入参为context和child, 返回一个showModalBottomSheet方法
// 该方法用于显示底部弹窗
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';

void showWholeHouseModalBottomSheetWidget(BuildContext context,
    {required String title, required Widget child}) {
  final Dialogs dialogs = Dialogs();
  dialogs.showSingleBtnModal<SmartHomeState>(
      store: smartHomeStore,
      title: title,
      context: context,
      child: (BuildContext context) => child,
      enableDrag: true,
      btnText: '取消',
      btnType: ButtonType.secondary,
      callback: () {
        dialogs.closeSmartHomeModalBottomSheet();
      });
}
