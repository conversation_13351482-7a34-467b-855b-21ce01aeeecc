import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../device/store/device_state.dart';
import 'device_env/env_device_state.dart';
import 'whole_house_header_view_model.dart';

/// 设备故障警报相关的选择器
class WholeHouseHeaderSelectors {
  /// 获取设备故障警报列表视图模型
  static final Selector<SmartHomeState, WholeHouseHeaderViewModel>
      selectWholeHouseHeaderViewModel = createSelector4(
    (SmartHomeState state) => state.isLogin,
    (SmartHomeState state) => state.deviceState.deviceStatus,
    (SmartHomeState state) => state.isEditState,
    (SmartHomeState state) => state.wholeHouseState.envDeviceState,
    _createWholeHouseHeaderViewModel,
  );

  static WholeHouseHeaderViewModel selectWholeHouseHeaderWithRoomIdViewModel(
      SmartHomeState state, String roomId) {
    final WholeHouseHeaderViewModel defaultViewModel =
        selectWholeHouseHeaderViewModel(state);

    final bool emptyEnvInfoShow = state.familyState.familyId != roomId &&
        state.wholeHouseState.envDeviceState
            .hasAnyDeviceNotSelectedOrOffline(roomId);

    return WholeHouseHeaderViewModel(
      visible: defaultViewModel.visible &&
          (state.familyState.familyId == roomId ||
              (roomId.isNotEmpty &&
                  state.wholeHouseState.envDeviceState
                      .hasAnyOnlineDevice(roomId))),
      isEdit: defaultViewModel.isEdit,
      emptyEnvInfoShow: emptyEnvInfoShow,
    );
  }

  static WholeHouseHeaderViewModel _createWholeHouseHeaderViewModel(
    bool isLogin,
    DeviceStatus deviceStatus,
    bool isEditState,
    EnvDeviceState envDeviceState,
  ) {
    return WholeHouseHeaderViewModel(
      visible: isLogin && deviceStatus == DeviceStatus.hasDevice,
      isEdit: isEditState,
      emptyEnvInfoShow: true,
    );
  }
}
