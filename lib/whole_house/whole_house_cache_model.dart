/*
 * 描述：仪表盘缓存model
 * 作者：fancunshuo
 * 建立时间: 2025/4/25
 */
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumable_item_model.dart';
import 'package:smart_home/whole_house/device_env/services/env_devices_response_model.dart';
import 'package:smart_home/whole_house/device_fault_alarm/models/device_fault_alarm_item_model.dart';

import 'location_weather/models/outdoor_weather_model.dart';

class WholeHouseCacheModel {
  List<DeviceFaultAlarmItemModel> list = <DeviceFaultAlarmItemModel>[];

  Map<String, DeviceConsumableItemModel> consumableMap =
      <String, DeviceConsumableItemModel>{};

  Map<String, SpaceEnvironmentModel> spaces = <String, SpaceEnvironmentModel>{};

  String areaName = '';

  OutdoorWeatherModel? outdoorWeatherState;

  WholeHouseCacheModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> list = json.listValueForKey('list', <dynamic>[]);
    for (final dynamic element in list) {
      if (element is Map<dynamic, dynamic>) {
        this.list.add(DeviceFaultAlarmItemModel.fromJson(element));
      }
    }

    final Map<dynamic, dynamic> consumableMap =
        json.mapValueForKey('consumableMap', <dynamic, dynamic>{});

    for (final dynamic element in consumableMap.keys) {
      if (consumableMap[element] is Map<dynamic, dynamic> &&
          element is String) {
        this.consumableMap[element] = DeviceConsumableItemModel.fromJson(
            consumableMap[element] as Map<dynamic, dynamic>);
      }
    }

    final Map<dynamic, dynamic> spaces =
        json.mapValueForKey('spaces', <dynamic, dynamic>{});

    for (final dynamic element in spaces.keys) {
      if (spaces[element] is Map<dynamic, dynamic> && element is String) {
        this.spaces[element] = SpaceEnvironmentModel.fromJson(
            spaces[element] as Map<dynamic, dynamic>);
      }
    }

    areaName = json.stringValueForKey('areaName', '');

    final Map<dynamic, dynamic>? outdoorWeatherJson =
        json.nullableMapValueForKey('outdoorWeatherState');
    outdoorWeatherState = outdoorWeatherJson != null
        ? OutdoorWeatherModel.fromJson(outdoorWeatherJson)
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['list'] = list;
    data['consumableMap'] = consumableMap;
    data['spaces'] = spaces;
    data['areaName'] = areaName;
    if (outdoorWeatherState != null) {
      data['outdoorWeatherState'] = outdoorWeatherState?.toJson();
    }
    return data;
  }

  WholeHouseCacheModel(this.list, this.consumableMap, this.spaces,
      this.areaName, this.outdoorWeatherState);
}
