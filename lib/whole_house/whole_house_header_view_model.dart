import 'package:flutter/foundation.dart';

@immutable
class WholeHouseHeaderViewModel {
  
  const WholeHouseHeaderViewModel({
    required this.visible,
    required this.isEdit,
    required this.emptyEnvInfoShow,
  });
  
  final bool visible;
  final bool isEdit;
  
  /// 控制是否显示"暂无环境信息"组件
  final bool emptyEnvInfoShow;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WholeHouseHeaderViewModel &&
          runtimeType == other.runtimeType &&
          visible == other.visible &&
          isEdit == other.isEdit &&
          emptyEnvInfoShow == other.emptyEnvInfoShow;

  @override
  int get hashCode => visible.hashCode ^ isEdit.hashCode ^ emptyEnvInfoShow.hashCode;

  @override
  String toString() {
    return 'WholeHouseHeaderViewModel{visible: $visible, isEdit: $isEdit, emptyEnvInfoShow: $emptyEnvInfoShow}';
  }
}
