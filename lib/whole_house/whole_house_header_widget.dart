import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/whole_house/device_consumables/whole_house_header_device_consumables_widget.dart';
import 'package:smart_home/whole_house/device_fault_alarm/whole_house_header_fault_alarm_widget.dart';
import 'package:smart_home/whole_house/device_running_mode/whole_house_header_running_device_widget.dart';
import 'package:smart_home/whole_house/whole_house_header_selectors.dart';

import '../store/smart_home_state.dart';
import '../widget_common/card_text_style.dart';
import 'device_env/whole_house_header_env_device_widget.dart';
import 'location_weather/whole_house_header_weather_widget.dart';
import 'whole_house_header_view_model.dart';
import 'widgets/empty_env_info_widget.dart';

// 仪表盘顶部区域
class WholeHouseHeaderWidget extends StatelessWidget {
  const WholeHouseHeaderWidget(
      {super.key, required this.roomId, required this.roomName});

  final String roomId;
  final String roomName;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, WholeHouseHeaderViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return WholeHouseHeaderSelectors.selectWholeHouseHeaderWithRoomIdViewModel(
              store.state, roomId);
        },
        builder: (BuildContext context, WholeHouseHeaderViewModel vm) {
          return Visibility(
              visible: vm.visible || vm.emptyEnvInfoShow,
              child: !vm.visible && vm.emptyEnvInfoShow ?
              EmptyEnvInfoWidget(spaceId: roomId) :
              _HeaderContent(
                  isEdit: vm.isEdit, roomId: roomId, roomName: roomName));
        });
  }
}

class _HeaderContent extends StatelessWidget {
  const _HeaderContent(
      {super.key,
      required this.isEdit,
      required this.roomId,
      required this.roomName});

  final bool isEdit;
  final String roomId;
  final String roomName;

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
        style: TextStyle(
          color: const Color(0xFF111111),
          fontFamilyFallback: fontFamilyFallback(),
        ),
        child: AbsorbPointer(
            absorbing: isEdit,
            child: Opacity(
                opacity: isEdit ? 0.39 : 1,
                child: _HeaderListView(roomId: roomId, roomName: roomName))));
  }
}

class _HeaderListView extends StatelessWidget {
  const _HeaderListView(
      {super.key, required this.roomId, required this.roomName});
  final String roomId;
  final String roomName;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
      child: ListView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        shrinkWrap: true,
        children: <Widget>[
          WholeHouseHeaderFaultAlarmWidget(roomId: roomId),
          WholeHouseHeaderDeviceConsumablesWidget(roomId: roomId),

          /// 运行中设备
          HeaderRunningDeviceWidget(roomId: roomId),

          /// 环境设备
          WholeHouseHeaderEnvDeviceWidget(spaceId: roomId, spaceName: roomName),

          /// 室外天气
          WholeHouseHeaderWeatherWidget(spaceId: roomId, spaceName: roomName),
        ],
      ),
    );
  }
}
