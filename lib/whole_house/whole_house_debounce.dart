import '../common/debounce.dart';

class WholeHouseDebounce {
  static final WholeHouseDebounce _instance = WholeHouseDebounce._();

  factory WholeHouseDebounce() => _instance;
  WholeHouseDebounce._();

  // 整体数据刷新防抖
  final Debouncer _debounce = Debouncer(milliseconds: 300);

  // 故障数据防抖
  final Debouncer _faultDebounce = Debouncer(milliseconds: 300);

  // 偏好设置防抖
  final Debouncer _preferenceSettingDebounce = Debouncer(milliseconds: 300);

  Debouncer get debounce => _debounce;
  Debouncer get faultDebounce => _faultDebounce;
  Debouncer get preferenceSettingDebounce => _preferenceSettingDebounce;

  void dispose() {
    _debounce.dispose();
    _faultDebounce.dispose();
    _preferenceSettingDebounce.dispose();
  }
}
