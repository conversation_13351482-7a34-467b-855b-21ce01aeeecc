import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_consumables/consumables_server_model.dart';

import '../models/device_consumable_item_model.dart';
import 'device_consumables_action.dart';

final Reducer<SmartHomeState> deviceConsumablesReducer =
    TypedReducer<SmartHomeState, UpdateWholeHouseDeviceConsumablesStateAction>(
            _updateWholeHouseDeviceConsumablesReducer)
        .call;

SmartHomeState _updateWholeHouseDeviceConsumablesReducer(
    SmartHomeState state, UpdateWholeHouseDeviceConsumablesStateAction action) {
  state.wholeHouseState.deviceConsumablesState.consumableMap =
      <String, DeviceConsumableItemModel>{
    for (final ConsumableDeviceModel v in action.responseModel.devices)
      v.deviceId: DeviceConsumableItemModel.fromServerModel(v)
  };
  return state;
}
