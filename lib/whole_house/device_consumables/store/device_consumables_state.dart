import 'package:flutter/foundation.dart';
import 'package:plugin_device/utils/util_diff.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumable_item_model.dart';

class DeviceConsumablesState {
  Map<String, DeviceConsumableItemModel> consumableMap =
      <String, DeviceConsumableItemModel>{};

  /// clear清空状态方法
  void clear() {
    consumableMap = <String, DeviceConsumableItemModel>{};
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceConsumablesState &&
          runtimeType == other.runtimeType &&
          mapEquals(consumableMap, other.consumableMap);

  @override
  int get hashCode => mapHashCode(consumableMap);

  @override
  String toString() {
    return 'DeviceConsumablesState{list: $consumableMap}';
  }
}
