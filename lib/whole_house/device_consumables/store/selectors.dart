import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumable_item_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumables_list_view_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/whole_house_header_device_consumables_view_model.dart';

/// 设备耗材相关的选择器
class DeviceConsumablesSelectors {
  static final Selector<SmartHomeState,
          WholeHouseHeaderDeviceConsumablesViewModel>
      selectHeaderConsumablesViewModel = createSelector2(
    (SmartHomeState state) =>
        state.wholeHouseState.deviceConsumablesState.consumableMap,
    (SmartHomeState state) =>
        state.wholeHouseState.preferenceSettingState.homeSwitch,
    _createHeaderConsumablesViewModel,
  );

  static WholeHouseHeaderDeviceConsumablesViewModel
      _createHeaderConsumablesViewModel(
    Map<String, DeviceConsumableItemModel> consumableMap,
    bool homeSwitch,
  ) {
    final List<DeviceConsumableItemModel> deviceConsumables = consumableMap
        .values
        .takeWhile((DeviceConsumableItemModel value) => value.consumables.values
            .any(
                (ConsumableModel element) => element.isWarn || element.isAlarm))
        .map((DeviceConsumableItemModel e) =>
            DeviceConsumableItemModel.fromItemModel(e))
        .toList();
    return WholeHouseHeaderDeviceConsumablesViewModel(
      icon: 'assets/icons/consumables.webp',
      count: deviceConsumables.length,
      // 偏好设置打开 && 耗材有数据
      visible: homeSwitch && deviceConsumables.isNotEmpty,
    );
  }

  static WholeHouseHeaderDeviceConsumablesViewModel
      selectHeaderFaultViewModelWithRoomId(
          SmartHomeState state, String roomId) {
    final WholeHouseHeaderDeviceConsumablesViewModel defaultViewModel =
        selectHeaderConsumablesViewModel(state);

    return WholeHouseHeaderDeviceConsumablesViewModel(
      icon: defaultViewModel.icon,
      count: defaultViewModel.count,
      visible: roomId == state.familyState.familyId && defaultViewModel.visible,
    );
  }

  static final Selector<SmartHomeState, DeviceConsumablesListViewModel>
      selectListConsumablesViewModel = createSelector1(
    (SmartHomeState state) =>
        state.wholeHouseState.deviceConsumablesState.consumableMap,
    _createListConsumablesViewModel,
  );

  static DeviceConsumablesListViewModel _createListConsumablesViewModel(
    Map<String, DeviceConsumableItemModel> consumableMap,
  ) {
    final List<DeviceConsumableItemModel> deviceConsumables = consumableMap
        .values
        .takeWhile((DeviceConsumableItemModel value) => value.consumables.values
            .any(
                (ConsumableModel element) => element.isWarn || element.isAlarm))
        .map((DeviceConsumableItemModel e) =>
            DeviceConsumableItemModel.fromItemModel(e))
        .toList();

    return DeviceConsumablesListViewModel(deviceConsumables);
  }
}
