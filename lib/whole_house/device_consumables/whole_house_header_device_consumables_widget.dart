import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:library_widgets/components/listView/hide_water_ripple_list_view.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_consumables/store/selectors.dart';
import 'package:smart_home/whole_house/device_consumables/widgets/consumables_list.dart';
import 'package:smart_home/whole_house/widgets/show_whole_house_modal_bottom_sheet_widget.dart';
import 'package:smart_home/whole_house/widgets/whole_house_header_icon_widget.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../../store/smart_home_state.dart';
import '../widgets/animated_visible_fade_widget.dart';
import 'models/whole_house_header_device_consumables_view_model.dart';

class WholeHouseHeaderDeviceConsumablesWidget extends StatelessWidget {
  const WholeHouseHeaderDeviceConsumablesWidget(
      {super.key, required this.roomId});

  final String roomId;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState,
            WholeHouseHeaderDeviceConsumablesViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) =>
            DeviceConsumablesSelectors.selectHeaderFaultViewModelWithRoomId(
                store.state, roomId),
        builder: (BuildContext context,
            WholeHouseHeaderDeviceConsumablesViewModel vm) {
          return AnimatedVisibleFadeWidget(
              visible: vm.visible,
              child: _ConsumableIconWidget(icon: vm.icon, count: vm.count));
        });
  }
}

class _ConsumableIconWidget extends StatefulWidget {
  const _ConsumableIconWidget(
      {super.key, required this.icon, required this.count});

  final String icon;
  final int count;

  @override
  State<_ConsumableIconWidget> createState() => _ConsumableIconWidgetState();
}

class _ConsumableIconWidgetState extends State<_ConsumableIconWidget> {
  @override
  void initState() {
    super.initState();
    gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
      'value': '耗材',
    });
  }

  @override
  Widget build(BuildContext context) {
    return WholeHouseHeaderIconWidget(
      margin: const EdgeInsets.only(right: 8),
      icon: Image.asset(
        widget.icon,
        width: 24,
        height: 24,
        package: SmartHomeConstant.package,
      ),
      count: widget.count,
      clickCallback: (BuildContext context) {
        gioTrack(GioConst.wholeHouseIconClick, <String, String>{
          'value': '耗材',
        });
        _handleDeviceConsumablesClick(context);
      },
    );
  }

  void _handleDeviceConsumablesClick(BuildContext context) {
    showWholeHouseModalBottomSheetWidget(
      context,
      title: '耗材不足',
      child: StoreProvider<SmartHomeState>(
        store: smartHomeStore,
        child: ScrollConfiguration(
          behavior: OverScrollBehavior(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Flexible(
                child: SingleChildScrollView(
                  child: _buildConsumableListWithFloorStatus(),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: _buildToWholeHousePage(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConsumableListWithFloorStatus() {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return smartHomeStore.state.deviceState.cardShowFloor;
        },
        builder: (BuildContext context, bool showFloor) {
          return ConsumablesList(
            showFloor: showFloor,
          );
        });
  }

  Widget _buildToWholeHousePage() {
    return Builder(builder: (BuildContext context) {
      return GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
          gioTrack(GioConst.wholeHouseClickToConsumableList);
          goToPageWithDebounce(
              '${SmartHomeConstant.wholeHouseUrl}${smartHomeStore.state.familyState.familyId}');
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              '查看全部耗材',
              style: TextStyle(
                color: const Color(0xFF666666),
                fontSize: 16,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
            Image.asset(
              'assets/icons/view_consumable_more.webp',
              package: SmartHomeConstant.package,
              height: 16,
              width: 16,
            ),
          ],
        ),
      );
    });
  }
}
