/*
 * 描述：设备耗材presenter
 * 作者：fancunshuo
 * 建立时间: 2025/3/21
 */
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_consumables/consumables_server_model.dart';
import 'package:smart_home/whole_house/device_consumables/store/device_consumables_action.dart';

class DeviceConsumablesPresenter {
  // 单例实例
  static final DeviceConsumablesPresenter _instance =
      DeviceConsumablesPresenter._internal();

  // 工厂构造函数，返回单例实例
  factory DeviceConsumablesPresenter() {
    return _instance;
  }

  // 私有构造函数，防止外部直接实例化
  DeviceConsumablesPresenter._internal();

  Future<void> fetchDeviceConsumables(String familyId) async {
    if (familyId.isEmpty) {
      return;
    }
    final ConsumableServerResponseModel? responseModel =
        await HttpService.getDeviceConsumables(familyId);
    if (responseModel is ConsumableServerResponseModel) {
      smartHomeStore.dispatch(
          UpdateWholeHouseDeviceConsumablesStateAction(responseModel));
    }
  }
}
