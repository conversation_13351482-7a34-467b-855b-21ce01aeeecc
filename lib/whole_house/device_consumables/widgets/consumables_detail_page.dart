/*
 * 描述：单设备耗材详情页
 * 作者：fancunshuo
 * 建立时间: 2025/3/24
 */
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/whole_house/device_consumables/models/clear_reset_consumables_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/single_consumable_model.dart';
import 'package:smart_home/whole_house/device_consumables/utils.dart';
import 'package:smart_home/whole_house/device_consumables/viewmodels/consumable_detail_viewmodel.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vdn/vdn.dart';

import 'consumables_list.dart';

class ConsumablesDetailPage extends StatefulWidget {
  const ConsumablesDetailPage({
    super.key,
    required this.deviceId,
    required this.consumableCode,
  });

  final String deviceId;
  final String consumableCode;

  @override
  State<ConsumablesDetailPage> createState() => _ConsumablesDetailPageState();
}

class _ConsumablesDetailPageState extends State<ConsumablesDetailPage> {
  final Dialogs _dialogs = Dialogs();

  final ConsumableDetailViewModel _consumableDetailViewModel =
      ConsumableDetailViewModel.init();

  static const String _tag = 'ConsumablesDetailPage';

  static const String _onlineText = '在线';
  static const String _offlineText = '离线';

  static const String _errorTipContent = '耗材获取失败，请稍后再试';

  DeviceInfoModel? _deviceInfo;

  @override
  void initState() {
    super.initState();
    DevLogger.debug(
        tag: _tag,
        msg:
            'consumable___ initState() getDeviceInfoById start, deviceId:${widget.deviceId}, consumableCode:${widget.consumableCode}');
    // 注册物理返回拦截监听
    InterceptSystemBackUtil.interceptSystemBack(
        pageName: pageName,
        callback: () {
          Vdn.close();
          InterceptSystemBackUtil.cancelInterceptSystemBack(pageName);
        });

    UpDevicePlugin.getDeviceInfoById(widget.deviceId)
        .then((DeviceInfoModel deviceInfo) {
      _deviceInfo = deviceInfo;
      _updateConsumableDeviceInfo(deviceInfo);
      _queryConsumableInfo(deviceInfo);
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: _tag, msg: 'consumable___ getDeviceInfoById err:$err');
    });

    ToastHelper.init(context);
  }

  void _updateConsumableDeviceInfo(DeviceInfoModel model) {
    if (mounted) {
      setState(() {
        _consumableDetailViewModel.deviceName = model.deviceName;
        _consumableDetailViewModel.deviceOnlineInfo =
            model.online ? _onlineText : _offlineText;

        _consumableDetailViewModel.typeId = model.wifiType;
        _consumableDetailViewModel.appTypeName = model.apptypeName;
        _consumableDetailViewModel.prodNo = model.prodNo;
      });
    }
  }

  void _queryConsumableInfo(DeviceInfoModel model) {
    final ConsumableInfoRequestModel reqModel =
        ConsumableInfoRequestModel.fromDeviceInfo(
            deviceId: widget.deviceId,
            consumableCode: widget.consumableCode,
            deviceInfo: model);

    HttpService.queryConsumableInfo(reqModel)
        .then(_handleQueryConsumableSuccess)
        .catchError(_handleQueryConsumableError);
  }

  void _handleQueryConsumableSuccess(
      ConsumableInfoResponseModel? responseModel) {
    if (responseModel == null) {
      DevLogger.error(
          tag: _tag,
          msg: 'consumable___ queryConsumableInfo, response is null, return');
      ToastHelper.showToast(_errorTipContent);
      return;
    }
    if (responseModel.retCode != SmartHomeConstant.zjServerRetSuccessCode) {
      DevLogger.error(
          tag: _tag,
          msg:
              'consumable___ queryConsumableInfo failed, errCode:${responseModel.retCode}, return');
      ToastHelper.showToast(_errorTipContent);
      return;
    }
    _updateConsumableInfo(responseModel.consumable);
  }

  void _handleQueryConsumableError(Object err) {
    DevLogger.error(
        tag: _tag, msg: 'consumable___ queryConsumableInfo err: $err');
    ToastHelper.showToast(_errorTipContent);
  }

  void _updateConsumableInfo(ConsumableInfoModel consumableModel) {
    if (mounted) {
      setState(() {
        _consumableDetailViewModel.resetType = consumableModel.resetType;
        _consumableDetailViewModel.resetAttrName =
            consumableModel.attributeValue;

        _consumableDetailViewModel.consumableName =
            consumableModel.consumableName;
        _consumableDetailViewModel.consumableIcon =
            consumableModel.consumableIcon;
        _consumableDetailViewModel.surplusRatio = consumableModel.surplusRatio;
        _consumableDetailViewModel.actionList = consumableModel.actionList;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppSemanticColors.background.secondary,
      appBar: AppBar(
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        leading: GestureDetector(
          onTap: () {
            Vdn.close();
            InterceptSystemBackUtil.cancelInterceptSystemBack(pageName);
          },
          child: Center(
            child: Image.asset(
              'assets/icons/navi_back.webp',
              package: SmartHomeConstant.package,
              height: 24,
              width: 24,
            ),
          ),
        ),
        title: _buildTitle(),
      ),
      body: _buildPageContent(context),
    );
  }

  Widget _buildTitle() {
    return Text(
      _consumableDetailViewModel.deviceName,
      style: TextStyle(
        fontSize: 17,
        color: AppSemanticColors.item.primary,
        fontWeight: FontWeight.w500,
        fontFamilyFallback: fontFamilyFallback(),
      ),
    );
  }

  Widget _buildDeviceImage(String consumableIcon) {
    return Padding(
      padding: const EdgeInsets.only(top: 21),
      child: AspectRatio(
        aspectRatio: 358 / 200,
        child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          child: Container(
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(16)),
              color: Colors.white,
            ),
            child: Image.network(
              consumableIcon,
              errorBuilder:
                  (BuildContext context, Object error, StackTrace? stackTrace) {
                return Image.asset(
                  'assets/images/consumables_icon_default.webp',
                  package: 'smart_home',
                  height: double.infinity,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCommonItemLayout(
      {required double height,
      required Widget title,
      Widget? subTitle,
      required List<Widget> actions}) {
    return Container(
      alignment: Alignment.center,
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                title,
                if (subTitle != null)
                  const SizedBox(
                    height: 4,
                  ),
                subTitle ?? const SizedBox(),
              ],
            ),
          ),
          const SizedBox(
            width: 12,
          ),
          Row(
            children: <Widget>[
              ...actions,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConsumableAlarm({required String desc}) {
    return _buildCommonItemLayout(
        height: 75,
        title: Text(
          _consumableDetailViewModel.consumableName,
          style: TextStyle(
            color: const Color(0xFF262626),
            fontSize: 16,
            fontWeight: FontWeight.w500,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        subTitle: Text(
          desc,
          style: TextStyle(
            color: isHighPriorityWarn(_consumableDetailViewModel.appTypeName)
                ? AppSemanticColors.item.warn.primary
                : AppSemanticColors.item.remind.primary,
            fontSize: 11,
            fontWeight: FontWeight.w400,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        actions: <Widget>[]);
  }

  Widget _buildDeviceStatus() {
    return GestureDetector(
      onTap: () {
        goToPageWithDebounce(
            SmartHomeConstant.vdnDeviceDetail + widget.deviceId);
      },
      child: ColoredBox(
        color: Colors.transparent,
        child: _buildCommonItemLayout(
            height: 54,
            title: Text(
              '设备状态',
              style: TextStyle(
                color: AppSemanticColors.item.primary,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
            actions: <Widget>[
              Row(
                children: <Widget>[
                  _buildDeviceOnlineState(),
                  const SizedBox(
                    width: 4,
                  ),
                  Image.asset(
                    'assets/icons/whole_house_right_arrow.webp',
                    width: 16,
                    height: 16,
                    package: SmartHomeConstant.package,
                  ),
                ],
              )
            ]),
      ),
    );
  }

  Widget _buildDeviceOnlineState() {
    return Text(
      _consumableDetailViewModel.deviceOnlineInfo,
      style: TextStyle(
        fontSize: 14,
        color: AppSemanticColors.item.secWeaken,
        fontWeight: FontWeight.w400,
        fontFamilyFallback: fontFamilyFallback(),
      ),
    );
  }

  String _formatSurplusRatio(String ratioString) {
    final int percentage =
        double.tryParse(ratioString.replaceAll('%', ''))?.round() ?? 0;
    return '剩余$percentage%';
  }

  Widget _buildConsumableArea() {
    final bool showPercentageCircle =
        _consumableDetailViewModel.surplusRatio.isNotEmpty;
    final String desc = showPercentageCircle
        ? _formatSurplusRatio(_consumableDetailViewModel.surplusRatio)
        : '不足';

    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      child: ColoredBox(
        color: Colors.white,
        child: Column(
          children: <Widget>[
            _buildConsumableAlarm(desc: desc),
            _buildDeviceStatus()
          ],
        ),
      ),
    );
  }

  Widget _buildBottomButton(
      {required String text, bool primary = false, VoidCallback? action}) {
    return GestureDetector(
      onTap: () {
        action?.call();
      },
      child: Container(
        height: 44,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            color: primary
                ? AppSemanticColors.component.primary.fill
                : AppSemanticColors.component.secondary.invert),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16,
            color: primary
                ? AppSemanticColors.component.primary.on
                : AppSemanticColors.component.secondary.on,
            fontWeight: FontWeight.w500,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
      ),
    );
  }

  void _cleanResetActionDialog(ActionItem actionItem, BuildContext context) {
    _dialogs.showDoubleBtnDialog(
      title: '',
      context: context,
      content: actionItem.actionContent,
      confirmCallback: _execClearResetConsumable,
      confirmText: actionItem.actionName,
    );
  }

  void _execClearResetConsumable() {
    final ClearResetConsumablesRequestModel requestModel =
        ClearResetConsumablesRequestModel(
      deviceId: widget.deviceId,
      resetType: _consumableDetailViewModel.resetType,
      typeId: _consumableDetailViewModel.typeId,
      prodNo: _consumableDetailViewModel.prodNo,
      resetAttrName: _consumableDetailViewModel.resetAttrName,
    );
    HttpService.cleanResetConsumables(requestModel).then((bool result) {
      DevLogger.debug(
          tag: _tag,
          msg: 'consumable___ cleanResetConsumables__ end, result:$result');
      if (_deviceInfo == null) {
        return;
      }
      _queryConsumableInfo(_deviceInfo!);
    }).catchError((Object err) {
      DevLogger.debug(
          tag: _tag, msg: 'consumable___ cleanResetConsumables__ err:$err');
    });
  }

  void _callCustomerService(BuildContext context) {
    _dialogs.showDoubleBtnDialog(
      title: '',
      context: context,
      content: '现在拨打售后电话：************',
      confirmText: '呼叫',
      confirmCallback: () {
        const String url = 'tel:************';
        launch(url);
      },
    );
  }

  void _handleAction(ActionItem actionItem, BuildContext context) {
    gioTrack(GioConst.wholeHouseClickConsumableDetailButton, <String, String>{
      GioConst.gioParamsValueKey: actionItem.actionName,
      GioConst.gioParamsSourceKey: _consumableDetailViewModel.consumableName,
    });

    switch (actionItem.actionTypeEnum) {
      case ActionType.t400:
        _callCustomerService(context);
      case ActionType.dialog:
        _cleanResetActionDialog(actionItem, context);
      case ActionType.jump:
        goToPageWithDebounce(actionItem.actionUrl);
      default:
        _handleUnknownActionType(actionItem, context);
    }
  }

  void _handleUnknownActionType(ActionItem actionItem, BuildContext context) {
    // 处理未知交互类型的逻辑
  }

  Widget _buildBottomBottomArea(BuildContext context) {
    final List<ActionItem> actionList = _consumableDetailViewModel.actionList;

    if (actionList.isEmpty) {
      return Container();
    }
    final List<Widget> btnList = <Widget>[];
    for (final ActionItem actionItem in actionList) {
      btnList.add(const SizedBox(height: 12));
      btnList.add(
        _buildBottomButton(
            text: actionItem.actionName,
            primary: actionItem.actionTypeEnum == ActionType.jump,
            action: () => _handleAction(actionItem, context)),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        children: <Widget>[...btnList],
      ),
    );
  }

  Widget _buildPageContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          bottom: MediaQuery.of(context).padding.bottom),
      child: Column(
        children: <Widget>[
          Expanded(
            child: Column(
              children: <Widget>[
                _buildDeviceImage(_consumableDetailViewModel.consumableIcon),
                const SizedBox(
                  height: 12,
                ),
                _buildConsumableArea(),
              ],
            ),
          ),
          _buildBottomBottomArea(context),
        ],
      ),
    );
  }
}
