import 'package:smart_home/whole_house/device_consumables/models/single_consumable_model.dart';

class ConsumableDetailViewModel {
  String deviceName = '';
  String appTypeName = '';
  String consumableIcon = '';
  String consumableName = '';
  String surplusRatio = '';
  String deviceOnlineInfo = '';
  List<ActionItem> actionList = <ActionItem>[];

  String typeId = '';
  String prodNo = '';
  String resetType = '';
  String resetAttrName = '';

  ConsumableDetailViewModel({
    required this.deviceName,
    required this.appTypeName,
    required this.consumableIcon,
    required this.consumableName,
    required this.surplusRatio,
    required this.deviceOnlineInfo,
    required this.actionList,
    this.typeId = '',
    this.prodNo = '',
    this.resetType = '',
    this.resetAttrName = '',
  });

  factory ConsumableDetailViewModel.init() {
    return ConsumableDetailViewModel(
      deviceName: '',
      appTypeName: '',
      consumableIcon: '',
      consumableName: '',
      surplusRatio: '',
      deviceOnlineInfo: '',
      actionList: <ActionItem>[],
    );
  }
}
