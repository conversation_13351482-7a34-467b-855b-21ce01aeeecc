/*
 * 描述：详情家庭耗材列表V6模型 ref: https://stp.haier.net/project/79/interface/api/233452
 * 作者：fancunshuo
 * 建立时间: 2025/3/21
 */
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class ConsumableDeviceModel {
  String deviceId = '';
  String productCode = '';
  String typeId = '';
  String deviceName = '';
  String devName = '';
  String imageAddr1 = '';
  String imageAddr2 = '';
  String appTypeName = '';
  String roomName = '';
  String roomId = '';
  List<ConsumableItemModel> consumables = <ConsumableItemModel>[];
  String deviceType = '';
  String devFloorId = '';
  String devFloorOrderId = '';
  String devFloorName = '';
  bool online = false;

  ConsumableDeviceModel(
      this.deviceId,
      this.productCode,
      this.typeId,
      this.deviceName,
      this.devName,
      this.imageAddr1,
      this.imageAddr2,
      this.appTypeName,
      this.roomName,
      this.roomId,
      this.consumables,
      this.deviceType,
      this.devFloorId,
      this.devFloorOrderId,
      this.devFloorName,
      this.online);

  ConsumableDeviceModel.fromJson(Map<dynamic, dynamic> json) {
    deviceId = json.stringValueForKey('deviceId', '');
    productCode = json.stringValueForKey('productCode', '');
    typeId = json.stringValueForKey('typeId', '');
    deviceName = json.stringValueForKey('deviceName', '');
    devName = json.stringValueForKey('devName', '');
    imageAddr1 = json.stringValueForKey('imageAddr1', '');
    imageAddr2 = json.stringValueForKey('imageAddr2', '');
    appTypeName = json.stringValueForKey('appTypeName', '');
    roomName = json.stringValueForKey('roomName', '');
    roomId = json.stringValueForKey('roomId', '');
    deviceType = json.stringValueForKey('deviceType', '');
    devFloorId = json.stringValueForKey('devFloorId', '');
    devFloorOrderId = json.stringValueForKey('devFloorOrderId', '');
    devFloorName = json.stringValueForKey('devFloorName', '');
    online = json.boolValueForKey('online', false);
    if (json['consumables'] is List<dynamic>) {
      for (final dynamic v in json['consumables'] as List<dynamic>) {
        if (v is Map<dynamic, dynamic>) {
          consumables.add(ConsumableItemModel.fromJson(v));
        }
      }
    }
  }
}

class ConsumableItemModel {
  String name = '';
  String surplusRatio = '';
  bool isWarn = false;
  bool isAlarm = false;
  String action = '';
  bool buttonFlag = false;
  String saleUrl = '';
  String saleButtonName = '';
  String consumableCode = '';
  String consumableIcon = '';
  bool isOrderUser = false;
  String orderUrl = '';
  String goodsUrl = '';
  String consumableOrderStatus = '';
  String installBootUrl = '';
  String resetType = '';
  String attributeValue = '';
  String detailsUrl = '';

  ConsumableItemModel(
      this.name,
      this.surplusRatio,
      this.isWarn,
      this.isAlarm,
      this.action,
      this.buttonFlag,
      this.saleUrl,
      this.saleButtonName,
      this.consumableCode,
      this.consumableIcon,
      this.isOrderUser,
      this.orderUrl,
      this.goodsUrl,
      this.consumableOrderStatus,
      this.installBootUrl,
      this.resetType,
      this.detailsUrl,
      this.attributeValue);

  ConsumableItemModel.fromJson(Map<dynamic, dynamic> json) {
    name = json.stringValueForKey('name', '');
    surplusRatio = json.stringValueForKey('surplusRatio', '');
    isWarn = json.boolValueForKey('isWarn', false);
    isAlarm = json.boolValueForKey('isAlarm', false);
    action = json.stringValueForKey('action', '');
    buttonFlag = json.boolValueForKey('buttonFlag', false);
    saleUrl = json.stringValueForKey('saleUrl', '');
    saleButtonName = json.stringValueForKey('saleButtonName', '');
    consumableCode = json.stringValueForKey('consumableCode', '');
    consumableIcon = json.stringValueForKey('consumableIcon', '');
    isOrderUser = json.boolValueForKey('isOrderUser', false);
    orderUrl = json.stringValueForKey('orderUrl', '');
    goodsUrl = json.stringValueForKey('goodsUrl', '');
    consumableOrderStatus = json.stringValueForKey('consumableOrderStatus', '');
    installBootUrl = json.stringValueForKey('installBootUrl', '');
    resetType = json.stringValueForKey('resetType', '');
    attributeValue = json.stringValueForKey('attributeValue', '');
    detailsUrl = json.stringValueForKey('detailsUrl', '');
  }
}

class ConsumableServerResponseModel extends UhomeResponseModel {
  ConsumableServerResponseModel.fromJson(super.data) : super.fromJson() {
    if (super.retData['devices'] is List<dynamic>) {
      for (final dynamic v in super.retData['devices'] as List<dynamic>) {
        if (v is Map<dynamic, dynamic>) {
          devices.add(ConsumableDeviceModel.fromJson(v));
        }
      }
    }
  }

  List<ConsumableDeviceModel> devices = <ConsumableDeviceModel>[];
}
