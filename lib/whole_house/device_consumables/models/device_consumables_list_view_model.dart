/*
 * 描述：耗材列表vm
 * 作者：fancunshuo
 * 建立时间: 2025/4/22
 */
import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumable_item_model.dart';

class DeviceConsumablesListViewModel {
  List<DeviceConsumableItemModel> list;

  DeviceConsumablesListViewModel(this.list);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceConsumablesListViewModel &&
          runtimeType == other.runtimeType &&
          listEquals(list, other.list);

  @override
  int get hashCode => listHashCode(list);
}
