/*
 * 描述：设备耗材的viewModel
 * 作者：fancunshuo
 * 建立时间: 2025/3/25
 */
import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_home/whole_house/device_consumables/consumables_server_model.dart';

class DeviceConsumableItemModel {
  String deviceId = '';
  String deviceName = '';
  String deviceImage = '';
  String productCode = '';
  String typeId = '';
  String floorName = '';
  String roomName = '';
  bool online = false;
  String appTypeName = '';
  Map<String, ConsumableModel> consumables = <String, ConsumableModel>{};

  DeviceConsumableItemModel(
      this.deviceId,
      this.deviceName,
      this.deviceImage,
      this.productCode,
      this.typeId,
      this.floorName,
      this.roomName,
      this.online,
      this.consumables,
      this.appTypeName);

  String get offlineInfo => online ? '在线' : '离线';

  DeviceConsumableItemModel.fromServerModel(ConsumableDeviceModel v) {
    deviceId = v.deviceId;
    deviceName = v.deviceName;
    deviceImage = v.imageAddr1;
    productCode = v.productCode;
    typeId = v.typeId;
    floorName = v.devFloorName;
    roomName = v.roomName;
    online = v.online;
    appTypeName = v.appTypeName;
    consumables = <String, ConsumableModel>{
      for (final ConsumableItemModel c in v.consumables)
        c.consumableCode: ConsumableModel.fromServerModel(c)
    };
  }

  DeviceConsumableItemModel.fromItemModel(DeviceConsumableItemModel v) {
    deviceId = v.deviceId;
    deviceName = v.deviceName;
    deviceImage = v.deviceImage;
    productCode = v.productCode;
    typeId = v.typeId;
    floorName = v.floorName;
    roomName = v.roomName;
    online = v.online;
    appTypeName = v.appTypeName;
    consumables = <String, ConsumableModel>{};

    for (final ConsumableModel c in v.consumables.values) {
      if (c.isAlarm || c.isWarn) {
        consumables[c.consumableCode] = c;
      }
    }
  }

  DeviceConsumableItemModel.fromJson(Map<dynamic, dynamic> json) {
    deviceId = json.stringValueForKey('deviceId', '');
    deviceName = json.stringValueForKey('deviceName', '');
    deviceImage = json.stringValueForKey('deviceImage', '');
    productCode = json.stringValueForKey('productCode', '');
    typeId = json.stringValueForKey('typeId', '');
    floorName = json.stringValueForKey('floorName', '');
    roomName = json.stringValueForKey('roomName', '');
    online = json.boolValueForKey('online', false);
    appTypeName = json.stringValueForKey('appTypeName', '');
    if (json['consumables'] is Map<dynamic, dynamic>) {
      for (final dynamic v
          in (json['consumables'] as Map<dynamic, dynamic>).values) {
        if (v is Map<dynamic, dynamic>) {
          consumables[v.stringValueForKey('consumableCode', '')] =
              ConsumableModel.fromJson(v);
        }
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['deviceId'] = deviceId;
    data['deviceName'] = deviceName;
    data['deviceImage'] = deviceImage;
    data['productCode'] = productCode;
    data['typeId'] = typeId;
    data['floorName'] = floorName;
    data['roomName'] = roomName;
    data['online'] = online;
    data['consumables'] = consumables;
    data['appTypeName'] = appTypeName;
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceConsumableItemModel &&
          runtimeType == other.runtimeType &&
          deviceId == other.deviceId &&
          deviceName == other.deviceName &&
          deviceImage == other.deviceImage &&
          productCode == other.productCode &&
          typeId == other.typeId &&
          floorName == other.floorName &&
          roomName == other.roomName &&
          offlineInfo == other.offlineInfo &&
          appTypeName == other.appTypeName &&
          mapEquals(consumables, other.consumables);

  @override
  int get hashCode =>
      deviceId.hashCode ^
      deviceName.hashCode ^
      deviceImage.hashCode ^
      productCode.hashCode ^
      typeId.hashCode ^
      floorName.hashCode ^
      roomName.hashCode ^
      offlineInfo.hashCode ^
      appTypeName.hashCode ^
      mapHashCode(consumables);
}

class ConsumableModel {
  String name = '';
  String consumableCode = '';
  String surplusRatio = ''; // 剩余比例
  bool isWarn = false;
  bool isAlarm = false;
  String resetType = '';
  String action = ''; // 按钮名称 重置/自清洁/预约添加/预约更换/查看详情
  bool buttonFlag = false; // 是否按钮
  String saleUrl = '';
  String saleButtonName = '';
  String attributeValue = '';
  String consumableIcon = '';
  String detailsUrl = '';

  ConsumableModel(
      this.name,
      this.consumableCode,
      this.surplusRatio,
      this.isWarn,
      this.isAlarm,
      this.resetType,
      this.action,
      this.buttonFlag,
      this.saleUrl,
      this.saleButtonName,
      this.attributeValue,
      this.detailsUrl,
      this.consumableIcon);

  ConsumableModel.fromServerModel(ConsumableItemModel c) {
    name = c.name;
    consumableCode = c.consumableCode;
    surplusRatio = c.surplusRatio;
    isWarn = c.isWarn;
    isAlarm = c.isAlarm;
    resetType = c.resetType;
    action = c.action;
    buttonFlag = c.buttonFlag;
    saleUrl = c.saleUrl;
    saleButtonName = c.saleButtonName;
    attributeValue = c.attributeValue;
    consumableIcon = c.consumableIcon;
    detailsUrl = c.detailsUrl;
  }

  ConsumableModel.fromJson(Map<dynamic, dynamic> json) {
    name = json.stringValueForKey('name', '');
    consumableCode = json.stringValueForKey('consumableCode', '');
    surplusRatio = json.stringValueForKey('surplusRatio', '');
    isWarn = json.boolValueForKey('isWarn', false);
    isAlarm = json.boolValueForKey('isAlarm', false);
    resetType = json.stringValueForKey('resetType', '');
    action = json.stringValueForKey('action', '');
    buttonFlag = json.boolValueForKey('buttonFlag', false);
    saleUrl = json.stringValueForKey('saleUrl', '');
    saleButtonName = json.stringValueForKey('saleButtonName', '');
    attributeValue = json.stringValueForKey('attributeValue', '');
    consumableIcon = json.stringValueForKey('consumableIcon', '');
    detailsUrl = json.stringValueForKey('detailsUrl', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['consumableCode'] = consumableCode;
    data['surplusRatio'] = surplusRatio;
    data['isWarn'] = isWarn;
    data['isAlarm'] = isAlarm;
    data['resetType'] = resetType;
    data['action'] = action;
    data['buttonFlag'] = buttonFlag;
    data['saleUrl'] = saleUrl;
    data['saleButtonName'] = saleButtonName;
    data['attributeValue'] = attributeValue;
    data['consumableIcon'] = consumableIcon;
    data['detailsUrl'] = detailsUrl;
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConsumableModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          consumableCode == other.consumableCode &&
          surplusRatio == other.surplusRatio &&
          isWarn == other.isWarn &&
          isAlarm == other.isAlarm &&
          resetType == other.resetType &&
          action == other.action &&
          buttonFlag == other.buttonFlag &&
          saleUrl == other.saleUrl &&
          saleButtonName == other.saleButtonName &&
          detailsUrl == other.detailsUrl &&
          attributeValue == other.attributeValue &&
          consumableIcon == other.consumableIcon;

  @override
  int get hashCode =>
      name.hashCode ^
      consumableCode.hashCode ^
      surplusRatio.hashCode ^
      isWarn.hashCode ^
      isAlarm.hashCode ^
      resetType.hashCode ^
      action.hashCode ^
      buttonFlag.hashCode ^
      saleUrl.hashCode ^
      saleButtonName.hashCode ^
      detailsUrl.hashCode ^
      attributeValue.hashCode ^
      consumableIcon.hashCode;
}
