class ClearResetConsumablesRequestModel {
  final String deviceId;
  final String resetType;
  final String typeId;
  final String prodNo;
  final String resetAttrName;

  ClearResetConsumablesRequestModel(
      {required this.deviceId,
      required this.resetType,
      required this.typeId,
      required this.prodNo,
      required this.resetAttrName});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['deviceId'] = deviceId;
    data['resetType'] = resetType;
    data['typeId'] = typeId;
    data['prodNo'] = prodNo;
    data['resetAttrName'] = resetAttrName;
    return data;
  }

  @override
  String toString() {
    return 'ClearResetConsumablesRequestModel{deviceId: $deviceId, resetType: $resetType, typeId: $typeId, prodNo: $prodNo, resetAttrName: $resetAttrName}';
  }
}
