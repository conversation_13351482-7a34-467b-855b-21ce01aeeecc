import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:upservice/model/uhome_response_model.dart';

class ConsumableInfoRequestModel {
  String deviceId = '';
  String typeId = '';
  String accessType = '';
  String apptypeCode = '';
  String deviceType = '';
  String prodNo = '';
  String consumableCode = '';

  ConsumableInfoRequestModel({
    required this.deviceId,
    required this.typeId,
    required this.accessType,
    required this.apptypeCode,
    required this.deviceType,
    required this.prodNo,
    required this.consumableCode,
  });

  factory ConsumableInfoRequestModel.fromDeviceInfo({
    required String deviceId,
    required String consumableCode,
    required DeviceInfoModel deviceInfo,
  }) {
    return ConsumableInfoRequestModel(
      deviceId: deviceId,
      consumableCode: consumableCode,
      typeId: deviceInfo.wifiType,
      accessType: deviceInfo.accessType,
      apptypeCode: deviceInfo.apptypeCode,
      deviceType: deviceInfo.deviceType,
      prodNo: deviceInfo.prodNo,
    );
  }

  ConsumableInfoRequestModel.fromJson(Map<dynamic, dynamic> json) {
    deviceId = json.stringValueForKey('deviceId', '');
    typeId = json.stringValueForKey('typeId', '');
    accessType = json.stringValueForKey('accessType', '');
    apptypeCode = json.stringValueForKey('apptypeCode', '');
    deviceType = json.stringValueForKey('deviceType', '');
    prodNo = json.stringValueForKey('prodNo', '');
    consumableCode = json.stringValueForKey('consumableCode', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['deviceId'] = deviceId;
    data['typeId'] = typeId;
    data['accessType'] = accessType;
    data['apptypeCode'] = apptypeCode;
    data['deviceType'] = deviceType;
    data['prodNo'] = prodNo;
    data['consumableCode'] = consumableCode;

    return data;
  }

  @override
  String toString() {
    return 'ConsumableInfoRequestModel{deviceId: $deviceId, typeId: $typeId, accessType: $accessType, apptypeCode: $apptypeCode, deviceType: $deviceType, prodNo: $prodNo, consumableCode: $consumableCode}';
  }
}

class ConsumableInfoResponseModel extends UhomeResponseModel {
  ConsumableInfoResponseModel.fromJson(super.data) : super.fromJson() {
    consumable = ConsumableInfoModel.fromJson(super.retData);
  }

  ConsumableInfoModel consumable =
      ConsumableInfoModel.fromJson(<dynamic, dynamic>{});
}

class ConsumableInfoModel {
  String consumableIcon = '';
  String consumableName = '';
  String surplusRatio = '';
  String resetType = '';
  String attributeValue = '';

  List<ActionItem> actionList = <ActionItem>[];

  ConsumableInfoModel.fromJson(Map<dynamic, dynamic> json) {
    consumableIcon = json.stringValueForKey('consumableIcon', '');
    consumableName = json.stringValueForKey('consumableName', '');
    surplusRatio = json.stringValueForKey('surplusRatio', '');
    resetType = json.stringValueForKey('resetType', '');
    attributeValue = json.stringValueForKey('attributeValue', '');

    final List<dynamic> tmpActionList =
        json.listValueForKey('actionList', <dynamic>[]);
    tmpActionList.forEach((dynamic element) {
      if (element is Map) {
        actionList.add(ActionItem.fromJson(element));
      }
    });
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'consumableIcon': consumableIcon,
      'consumableName': consumableName,
      'surplusRatio': surplusRatio,
      'resetType': resetType,
      'attributeValue': attributeValue,
      'actionList': actionList.map((ActionItem e) => e.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ConsumableInfoModel{consumableIcon: $consumableIcon, consumableName: $consumableName, surplusRatio: $surplusRatio, resetType: $resetType, attributeValue: $attributeValue, actionList: $actionList}';
  }
}

class ActionItem {
  String actionType = '';
  String actionName = '';
  String actionContent = '';
  String actionUrl = '';

  ActionType get actionTypeEnum => ActionType.fromString(actionType);

  ActionItem.fromJson(Map<dynamic, dynamic> json) {
    actionType = json.stringValueForKey('actionType', '');
    actionName = json.stringValueForKey('actionName', '');
    actionContent = json.stringValueForKey('actionContent', '');
    actionUrl = json.stringValueForKey('actionUrl', '');
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'actionType': actionType,
      'actionName': actionName,
      'actionContent': actionContent,
      'actionUrl': actionUrl,
    };
  }

  @override
  String toString() {
    return 'ActionItem{actionType: $actionType, actionName: $actionName, actionContent: $actionContent, actionUrl: $actionUrl}';
  }
}

enum ActionType {
  t400('t400'),
  dialog('dialog'),
  jump('jump');

  final String value;
  const ActionType(this.value);

  static ActionType fromString(String value) {
    return ActionType.values.firstWhere(
      (ActionType type) => type.value == value,
      orElse: () => ActionType.dialog,
    );
  }

  @override
  String toString() => value;
}
