class WholeHouseHeaderDeviceConsumablesViewModel {
  WholeHouseHeaderDeviceConsumablesViewModel(
      {required this.icon, required this.count, required this.visible});

  /// [icon] 图标
  final String icon;

  /// [count] 数量
  final int count;

  /// [visible] 是否显示
  final bool visible;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WholeHouseHeaderDeviceConsumablesViewModel &&
          runtimeType == other.runtimeType &&
          icon == other.icon &&
          count == other.count &&
          visible == other.visible;

  @override
  int get hashCode => icon.hashCode ^ count.hashCode ^ visible.hashCode;

  @override
  String toString() {
    return 'WholeHouseHeaderDeviceConsumablesViewModel{icon: $icon, count: $count, visible: $visible}';
  }
}
