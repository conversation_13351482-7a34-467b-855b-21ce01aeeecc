import 'package:smart_home/whole_house/device_consumables/store/device_consumables_state.dart';
import 'package:smart_home/whole_house/preference_setting/store/preference_setting_state.dart';

import '../device_env/env_device_state.dart';
import '../device_fault_alarm/store/device_fault_alarm_state.dart';
import '../device_running_mode/store/running_device_state.dart';
import '../location_weather/store/area_state.dart';
import '../location_weather/store/environment_state.dart';

/// 全屋状态管理类
class WholeHouseState {

    // 故障state
  DeviceFaultAlarmState deviceFaultAlarmState = DeviceFaultAlarmState();
  // 运行中State
  RunningDeviceState runningDeviceState = RunningDeviceState();
  // 耗材State
  DeviceConsumablesState deviceConsumablesState = DeviceConsumablesState();

  /// 设置state
  PreferenceSettingState preferenceSettingState = PreferenceSettingState();

  /// 环境信息状态
  final EnvironmentState environmentState;
  
  /// 区域信息状态
  final AreaState areaState;
  
  /// 环境设备状态
  final EnvDeviceState envDeviceState;

  /// 构造函数
  WholeHouseState({
    DeviceFaultAlarmState? deviceFaultAlarmState,
    RunningDeviceState? runningDeviceState,
    DeviceConsumablesState? deviceConsumablesState,
    PreferenceSettingState? preferenceSettingState,
    EnvironmentState? environmentState,
    AreaState? areaState,
    EnvDeviceState? envDeviceState,
  }) : 
    this.deviceFaultAlarmState = deviceFaultAlarmState ?? DeviceFaultAlarmState(),
    this.runningDeviceState = runningDeviceState ?? RunningDeviceState(),
    this.deviceConsumablesState = deviceConsumablesState ?? DeviceConsumablesState(),
    this.preferenceSettingState = preferenceSettingState ?? PreferenceSettingState(),
    this.environmentState = environmentState ?? const EnvironmentState(),
    this.areaState = areaState ?? const AreaState(),
    this.envDeviceState = envDeviceState ?? EnvDeviceState();

  /// clear清空状态方法
  void clear() {
    deviceFaultAlarmState.clear();
    runningDeviceState.clear();
    preferenceSettingState.clear();
    deviceConsumablesState.clear();
  }
  
  /// 判断是否相等
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WholeHouseState &&
        other.deviceFaultAlarmState == deviceFaultAlarmState &&
        other.runningDeviceState == runningDeviceState &&
        other.deviceConsumablesState == deviceConsumablesState &&
        other.preferenceSettingState == preferenceSettingState &&
        other.environmentState == environmentState &&
        other.areaState == areaState &&
        other.envDeviceState == envDeviceState;
  }
  
  /// 获取哈希码
  @override
  int get hashCode => Object.hash(
        deviceFaultAlarmState,
        runningDeviceState,
        deviceConsumablesState,
        preferenceSettingState,
        environmentState,
        areaState,
        envDeviceState,
      );
      
  /// 字符串表示
  @override
  String toString() {
    return 'WholeHouseState{deviceFaultAlarmState: $deviceFaultAlarmState, '
        'runningDeviceState: $runningDeviceState, '
        'deviceConsumablesState: $deviceConsumablesState, '
        'preferenceSettingState: $preferenceSettingState, '
        'environmentState: $environmentState, '
        'areaState: $areaState, '
        'envDeviceState: $envDeviceState}';
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'deviceFaultAlarmState': deviceFaultAlarmState.toString(),
      'runningDeviceState': runningDeviceState.toString(),
      'deviceConsumablesState': deviceConsumablesState.toString(),
      'preferenceSettingState': preferenceSettingState.toString(),
      'environmentState': environmentState.toJson(),
      'areaState': areaState.toString(),
      'envDeviceState': envDeviceState.toString(),
    };
  }

    /// 创建新实例，同时可更新部分属性
  WholeHouseState copyWith({
    DeviceFaultAlarmState? deviceFaultAlarmState,
    RunningDeviceState? runningDeviceState,
    DeviceConsumablesState? deviceConsumablesState,
    PreferenceSettingState? preferenceSettingState,
    EnvironmentState? environmentState,
    AreaState? areaState,
    EnvDeviceState? envDeviceState,
  }) {
    return WholeHouseState(
      deviceFaultAlarmState: deviceFaultAlarmState ?? this.deviceFaultAlarmState,
      runningDeviceState: runningDeviceState ?? this.runningDeviceState,
      deviceConsumablesState: deviceConsumablesState ?? this.deviceConsumablesState,
      preferenceSettingState: preferenceSettingState ?? this.preferenceSettingState,
      environmentState: environmentState ?? this.environmentState,
      areaState: areaState ?? this.areaState,
      envDeviceState: envDeviceState ?? this.envDeviceState,
    );
  }
}
