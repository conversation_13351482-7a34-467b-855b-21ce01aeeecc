import 'package:redux/redux.dart';
import 'package:smart_home/cache/store/cache_middleware.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_middleware.dart';

import '../device_env/middleware/env_device_setting_middleware.dart';
import '../device_fault_alarm/store/device_fault_alarm_middleware.dart';
import '../location_weather/middleware/location_weather_middleware.dart';
import '../preference_setting/store/preference_setting_middleware.dart';

final List<Middleware<SmartHomeState>> wholeHouseMiddleware =
    <Middleware<SmartHomeState>>[
  DeviceFaultAlarmMiddleware().call,
  PreferenceSettingMiddleware().call,
  const EnvDeviceSettingMiddleware().call,
  const LocationWeatherMiddleware().call,
  RunningDeviceMiddleware().call,
  CacheMiddleware().call,
];
