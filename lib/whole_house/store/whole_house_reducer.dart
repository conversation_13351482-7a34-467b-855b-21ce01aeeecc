import 'package:redux/redux.dart';
import 'package:smart_home/whole_house/device_consumables/store/device_consumables_reducer.dart';
import 'package:smart_home/whole_house/device_env/env_device_state.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_reducer.dart';
import 'package:smart_home/whole_house/whole_house_cache_model.dart';

import '../../store/smart_home_state.dart';
import '../device_env/reducers/env_devices_reducer.dart';
import '../device_fault_alarm/store/device_fault_alarm_reducer.dart';
import '../location_weather/reducers/location_weather_reducer.dart';
import '../location_weather/store/area_state.dart';
import '../location_weather/store/environment_state.dart';
import '../preference_setting/store/preference_setting_reducer.dart';
import 'whole_house_action.dart';

final Reducer<SmartHomeState> wholeHouseCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  /// 位置天气reducer
  locationWeatherReducer,

  /// 故障reducer
  deviceFaultAlarmReducer,

  /// 运行模式reducer
  deviceRunningReducer,

  /// 耗材reducer
  deviceConsumablesReducer,

  /// 环境设备状态更新reducer
  envDeviceReducer,

  // 偏好设置reducer
  preferenceSettingReducer,

  TypedReducer<SmartHomeState, ClearWholeHouseDataAction>(
          _clearWholeHouseDataReducer)
      .call,

  TypedReducer<SmartHomeState, UpdateWholeHouseDataFromCache>(
          _updateWholeHouseDataFromCacheReducer)
      .call,
]);

SmartHomeState _clearWholeHouseDataReducer(
    SmartHomeState state, ClearWholeHouseDataAction action) {
  state.wholeHouseState.clear();
  return state;
}

SmartHomeState _updateWholeHouseDataFromCacheReducer(
    SmartHomeState state, UpdateWholeHouseDataFromCache action) {
  return updateWholeHouseDataFromCache(state, action.model);
}

SmartHomeState updateWholeHouseDataFromCache(
    SmartHomeState state, WholeHouseCacheModel? model) {
  if (model != null) {
    state.wholeHouseState.deviceFaultAlarmState.list = model.list;
    state.wholeHouseState.deviceConsumablesState.consumableMap =
        model.consumableMap;
    return state.copyWith(
      wholeHouseState: state.wholeHouseState.copyWith(
        areaState: AreaState(
          areaName: model.areaName,
        ),
        environmentState: EnvironmentState(
          outdoorWeatherState: model.outdoorWeatherState,
        ),
        envDeviceState: EnvDeviceState(
          spacesList: model.spaces.values.toList(),
        ),
      ),
    );
  }
  return state;
}
