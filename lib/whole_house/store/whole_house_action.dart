import 'package:smart_home/whole_house/whole_house_cache_model.dart';

/// 清空全屋仪表盘数据的Action
///
/// 用于清除全屋相关的所有状态数据，通常在退出仪表盘页面或者切换家庭时使用
class ClearWholeHouseDataAction {
  /// 构造函数
  const ClearWholeHouseDataAction();

  @override
  String toString() {
    return 'ClearWholeHouseDataAction{}';
  }
}

class UpdateWholeHouseDataFromCache {
  const UpdateWholeHouseDataFromCache(this.model);

  final WholeHouseCacheModel model;
}
