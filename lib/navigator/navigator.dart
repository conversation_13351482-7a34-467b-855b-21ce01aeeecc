import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/src/store.dart';
import 'package:smart_home/navigator/family/family.dart';
import 'package:smart_home/store/smart_home_state.dart';

import 'custom_service/custom_service.dart';
import 'menu/menu.dart';

class NavigatorWidget extends StatelessWidget {
  const NavigatorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 20),
        height: 44,
        color: Colors.transparent,
        child: StoreConnector<SmartHomeState, bool>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return store.state.familyState.netAvailable;
          },
          builder: (BuildContext context, bool isNetAvailable) {
            if (!isNetAvailable) {
              return Container(
                height: 36,
                alignment: Alignment.center,
                child: NetworkUnavailable(
                  netAvailable: isNetAvailable,
                ),
              );
            }
            return const Row(
              children: <Widget>[
                Expanded(child: FamilyWidget()),
                CustomServiceWidget(),
                SizedBox(
                  width: 20,
                ),
                MenuButtonWidget(),
              ],
            );
          },
        ),
      ),
    );
  }
}
