import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import 'util_customer_service.dart';

class CustomServiceWidget extends StatelessWidget {
  const CustomServiceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        gioTrack(GioConst.smartHomeClickCustomer);
        CustomerServiceUtil.jumpIntoCustomerServicePage();
      },
      child: Image.asset(
        'assets/icons/staff_black.webp',
        fit: BoxFit.fill,
        width: 24,
        height: 24,
        package: SmartHomeConstant.package,
      ),
    );
  }
}
