import 'package:device_utils/log/log.dart';

import '../../common/constant.dart';
import '../../common/smart_home_util.dart';

class CustomerServiceUtil {
  static String _generateCustomerServicePageUri() {
    const String pId = 'zjapp';
    const String sceneId = 'cd0134';
    const String entranceId = 'jt';
    const String fromPageTitle = '智家app首页顶部入口';

    final String fromPageTitleEncode = Uri.encodeComponent(fromPageTitle);

    final String customerService = 'mpaas://CustomerService/?needAuthLogin=1'
        '&pId=$pId'
        '&sceneId=$sceneId'
        '&entranceId=$entranceId'
        '&fromPageTitle=$fromPageTitleEncode'
        '#/zhijiaHome';
    return customerService;
  }

  static void jumpIntoCustomerServicePage() {
    final String customerServiceUri = _generateCustomerServicePageUri();
    DevLogger.info(
        tag: SmartHomeConstant.package,
        msg: 'jumpIntoCustomerServicePage, uri:$customerServiceUri');
    goToPageWithDebounce(customerServiceUri);
  }
}
