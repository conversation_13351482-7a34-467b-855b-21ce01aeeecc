import 'package:device_utils/compare/compare.dart';

import 'family_const.dart';

class FamilyHeaderViewModel {
  FamilyHeaderViewModel(this.isLogin, String familyName, this.isExpand) {
    this.familyName = isLogin ? familyName : FamilyConst.familyNameNoLogin;
  }

  bool isLogin = false;
  String familyName = '';
  bool isExpand = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FamilyHeaderViewModel &&
          runtimeType == other.runtimeType &&
          isLogin == other.isLogin &&
          familyName == other.familyName &&
          isExpand == other.isExpand;

  @override
  int get hashCode =>
      isLogin.hashCode ^ familyName.hashCode ^ isExpand.hashCode;

  @override
  String toString() {
    return 'FamilyHeaderViewModel{isLogin: $isLogin, familyName: $familyName}, isExpand: $isExpand}';
  }
}

class FamilyItemModel {
  String familyId = '';
  String familyName = '';
  bool isOwner = false;
  int createTime = 0;
  int memberType = 2;

  FamilyItemModel(
      {required this.familyId,
      required this.familyName,
      required this.isOwner,
      required this.createTime,
      required this.memberType});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FamilyItemModel &&
          runtimeType == other.runtimeType &&
          familyId == other.familyId &&
          familyName == other.familyName &&
          isOwner == other.isOwner &&
          createTime == other.createTime &&
          memberType == other.memberType;

  @override
  int get hashCode =>
      familyId.hashCode ^
      familyName.hashCode ^
      isOwner.hashCode ^
      createTime.hashCode ^
      memberType.hashCode;
}

class FamilyPopViewModel {
  String familyId = '';
  double height = 0;
  List<FamilyItemModel> familyModelList = <FamilyItemModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FamilyPopViewModel &&
          runtimeType == other.runtimeType &&
          familyId == other.familyId &&
          height == other.height &&
          isListEqual(familyModelList, other.familyModelList);

  @override
  int get hashCode =>
      familyId.hashCode ^ listHashCode(familyModelList) ^ height.hashCode;

  FamilyPopViewModel(this.familyId, this.familyModelList, this.height);
}
