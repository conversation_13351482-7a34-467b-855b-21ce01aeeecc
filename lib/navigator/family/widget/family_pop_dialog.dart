import 'dart:ui';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:reselect/reselect.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/navigator/family/family_viewmodel.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/widget_common/ui_components.dart';

import '../../../common/constant_gio.dart';
import '../../../widget_common/card_text_style.dart';
import '../store/family_action.dart';
import '../store/family_selectors.dart';

typedef FamilyItemClick = void Function(String familyId);

BuildContext? dialogContext;

void hideFamilyPopDialog() {
  if (dialogContext is BuildContext) {
    Navigator.of(dialogContext!).maybePop();
    dialogContext = null;
  }
}

void showFamilyPopDialog(BuildContext context) {
  if (!context.mounted) {
    return;
  }
  smartHomeStore.dispatch(UpdateExpandStatusAction(true));
  showGeneralDialog<void>(
    barrierColor: Colors.transparent,
    barrierDismissible: true,
    barrierLabel: 'family_popup',
    context: context,
    pageBuilder: (BuildContext popContext, Animation<double> animation1,
        Animation<double> animation2) {
      dialogContext = popContext;
      return Stack(
        children: <Widget>[
          Positioned(
            top: 44,
            left: 16,
            child: StoreProvider<SmartHomeState>(
              store: smartHomeStore,
              child: SafeArea(
                child: Material(
                  color: Colors.transparent,
                  child: CommonUIComponents.buildPopupContentWithBlur(
                      const FamilyPopContentWidget()),
                ),
              ),
            ),
          ),
        ],
      );
    },
    transitionDuration: const Duration(milliseconds: 200),
    transitionBuilder: (BuildContext ctx, Animation<double> a1,
        Animation<double> a2, Widget child) {
      final double curvedValue = Curves.easeOutCubic.transform(a1.value);
      return Transform.scale(
        origin: Offset(-MediaQuery.of(context).size.width / 2,
                -MediaQuery.of(context).size.height / 2) +
            Offset(16, 44 + MediaQuery.of(context).padding.top),
        scale: curvedValue,
        child: Opacity(
          opacity: a1.value,
          child: child,
        ),
      );
    },
  ).then((_) {
    dialogContext = null;
    smartHomeStore.dispatch(UpdateExpandStatusAction(false));
  });
}

class FamilyPopContentWidget extends StatefulWidget {
  const FamilyPopContentWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return FamilyPopContentState();
  }
}

class FamilyPopContentState extends State<FamilyPopContentWidget> {
  final ItemScrollController _scrollViewController = ItemScrollController();

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, FamilyPopViewModel>(
        distinct: true,
        builder: (BuildContext context, FamilyPopViewModel viewModel) {
          _scrollToCurrentFamily(viewModel);
          return Container(
            decoration: CommonUIComponents.dialogGradientBorderDecoration,
            child: ClipRRect(
              borderRadius: const BorderRadius.all(
                Radius.circular(16),
              ),
              child: Container(
                width: 198,
                constraints: const BoxConstraints(maxHeight: 416),
                child: IntrinsicHeight(
                  child: Column(
                    children: <Widget>[
                      Expanded(child: _buildFamilyContentWidget(viewModel)),
                      Visibility(
                          visible: viewModel.familyModelList.isNotEmpty,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Container(
                              height: 0.75,
                              color: const Color.fromRGBO(0, 0, 0, 0.13),
                            ),
                          )),
                      GestureDetector(
                        onTap: _handleClickSetting,
                        child: Container(
                          color: Colors.transparent,
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, bottom: 8),
                          height: 62,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              const Text(
                                '家庭管理',
                                style: TextStyle(
                                  color: Color.fromRGBO(0, 0, 0, 0.93),
                                  fontSize: 16,
                                ),
                              ),
                              Image.asset('assets/images/family_setting.webp',
                                  package: SmartHomeConstant.package,
                                  fit: BoxFit.cover,
                                  width: 24,
                                  height: 24)
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        converter: (Store<SmartHomeState> store) {
          return FamilySelectors.familyPopViewModel(store.state);
        });
  }

  Widget _buildFamilyContentWidget(FamilyPopViewModel viewModel) {
    return SizedBox(
      height: viewModel.height,
      child: ScrollablePositionedList.builder(
        itemScrollController: _scrollViewController,
        padding: EdgeInsets.zero,
        physics: const ClampingScrollPhysics(),
        itemCount: viewModel.familyModelList.length,
        itemBuilder: (BuildContext context, int index) {
          return _buildFamilyItemWidget(
              viewModel.familyModelList[index], viewModel.familyId, index);
        },
      ),
    );
  }

  Widget _buildFamilyItemWidget(
      FamilyItemModel model, String currentFamilyId, int index) {
    final bool selected = currentFamilyId == model.familyId;
    final double itemPadding = index == 0 ? 8 : 0;
    return PressableOverlayWidget(
      opacity: selected ? 0 : null,
      child: GestureDetector(
        onTap: () {
          _handleClickFamilyItem(model.familyId, currentFamilyId);
        },
        child: Container(
          color: Colors.transparent,
          padding: EdgeInsets.only(left: 16, right: 16, top: itemPadding),
          height:
              (model.isOwner ? familyOwnerItemHeight : familyShareItemHeight) +
                  itemPadding,
          child: Row(
            children: <Widget>[
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      model.familyName,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: selected
                            ? AppSemanticColors.component.information.on
                            : AppSemanticColors.item.primary,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamilyFallback: fontFamilyFallback(),
                      ),
                    ),
                    if (!model.isOwner) const SizedBox(height: 4),
                    if (!model.isOwner)
                      Text('共享',
                          style: TextStyle(
                              color: selected
                                  ? AppSemanticColors.component.information.on
                                  : AppSemanticColors.item.secondary,
                              fontSize: 12)),
                  ],
                ),
              ),
              if (selected)
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: Image.asset('assets/images/family_selected.webp',
                      package: SmartHomeConstant.package,
                      fit: BoxFit.cover,
                      width: 24,
                      height: 24),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleClickFamilyItem(
      String familyId, String currentFamilyId) async {
    try {
      final NetworkStatus networkStatus =
          await Network.isOnline();
      if (networkStatus.isOnline && mounted) {
        Navigator.maybePop(context);
        if (familyId == currentFamilyId) {
          return;
        }
        gioTrack(GioConst.smartHomeSwitchFamily);
        Family.setCurrentFamily(familyId).then((dynamic value) {
          DevLogger.info(
              tag: 'showFamilyPopDialog',
              msg: '_handleClickFamilyItem success:$familyId');
        });
      } else {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
      }
    } catch (err) {
      DevLogger.error(
          tag: 'showFamilyPopDialog', msg: '_handleClickFamilyItem error:$err');
    }
  }

  void _handleClickSetting() {
    Navigator.maybePop(context);
    gioTrack(GioConst.smartHomeClickFamilyManager);
    goToPage('mpaas://familymanage?needAuthLogin=1');
  }

  void _scrollToCurrentFamily(FamilyPopViewModel popViewModel) {
    if (mounted) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        final int index = popViewModel.familyModelList.indexWhere(
            (FamilyItemModel element) =>
                element.familyId == popViewModel.familyId);
        _scrollViewController.jumpTo(index: index > 0 ? index : 0);
      });
    }
  }
}
