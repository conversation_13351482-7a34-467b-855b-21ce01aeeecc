import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:library_widgets/common/util.dart';
import 'package:location/location.dart';
import 'package:location/locationmodel.dart';
import 'package:smart_home/navigator/family/store/family_action.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';

import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../scene/switch/switch_status_query_model.dart';
import '../../service/http_service.dart';
import '../../store/smart_home_store.dart';
import 'model/geofencing_response_model.dart';

class FamilyChangePresenter {
  static const int familyChangeSwitchType = 15;
  static const String switchClose = '0';
  static const String switchOpen = '1';
  static bool isInitFamilyChange = false;

  static Future<void> queryFamilyChangeStatus(String familyId) async {
    if (familyId.isEmpty) {
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryFamilyChangeStatus familyId is empty');
      return;
    }
    if (isInitFamilyChange) {
      return;
    }
    isInitFamilyChange = true;
    try {
      final Map<String, FamilyModel> familyMap = await Family.getFamilyMap();
      if (familyMap.isEmpty || familyMap.length == 1) {
        return;
      }

      final bool switchStatus = await queryFamilyChangeSwitch();
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryFamilyChangeStatus switchStatus: $switchStatus');
      if (!switchStatus) {
        return;
      }
      final LocationModel location =
          await Location.getLocation(isNeedRequestPermission: false);

      await queryGeofencingByLocation(location, familyId);
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryFamilyChangeStatus err: $err');
    }
  }

  static Future<bool> queryFamilyChangeSwitch() async {
    try {
      final OauthData? oauthData = User.getOauthDataSync();
      if (oauthData == null) {
        return false;
      }
      final SwitchStatusRequestModel reqModel = SwitchStatusRequestModel(
          switchType: familyChangeSwitchType, switchKey: oauthData.uc_user_id);
      final SceneSwitchStatusResponseModel? response =
          await HttpService.switchQuery(reqModel);
      if (response?.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        final String switchStatus = response?.data.status ?? switchClose;
        return switchStatus == switchOpen;
      }

      return false;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryFamilyChangeSwitch err: $err');
      return false;
    }
  }

  static const int kFamilyBubbleAutoHideSeconds = 5;

  static Future<void> queryGeofencingByLocation(
      LocationModel locationModel, String familyId) async {
    try {
      final GeofencingRequestModel reqModel = GeofencingRequestModel(
          longitude: locationModel.longitude.toString(),
          latitude: locationModel.latitude.toString());
      final GeofencingResponseModel? response =
          await HttpService.queryFamilyGeofencing(reqModel);
      if (response != null &&
          response.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        final List<FamilyChangeModel> data = response.data;
        if (data.isEmpty) {
          return;
        }

        bool isChange = true;
        for (final FamilyChangeModel familyChangeModel in data) {
          if (familyChangeModel.familyId == familyId) {
            isChange = false;
            break;
          }
        }

        if (!isChange) {
          DevLogger.debug(
              tag: SmartHomeConstant.package,
              msg: 'queryGeofencingByLocation isChange: $isChange familyId: $familyId');
          return;
        }

        final Map<String, dynamic> params = <String, dynamic>{
          'trigger_type': '位置变更',
          'current_location': locationModel.POIName,
        };

        gioTrack(GioConst.smartHomeTabChangeFamilyTip, params);
        smartHomeStore.dispatch(UpdateFamilyChangeListAction(data));
        Future<dynamic>.delayed(
            const Duration(seconds: kFamilyBubbleAutoHideSeconds), () {
          smartHomeStore.dispatch(UpdateFamilyBubbleInvisibleAction());
        });
      }
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryGeofencingByLocation err: $err');
    }
  }
}
