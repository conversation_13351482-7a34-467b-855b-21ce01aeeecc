const int _defaultFamilyNameLength = 15; //格式123****4567的家

String fetchValidFamilyName(String familyName) {
  final int length = familyName.length;
  int charLength = 0;
  int index = 0;
  for (; index < length; index++) {
    final String subString = familyName.substring(index, index + 1);
    if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(subString)) {
      charLength += 2;
    } else {
      charLength += 1;
    }
    if (charLength > _defaultFamilyNameLength) {
      break;
    }
  }

  return length >= index + 1
      ? ('${familyName.substring(0, index)}...')
      : familyName;
}
