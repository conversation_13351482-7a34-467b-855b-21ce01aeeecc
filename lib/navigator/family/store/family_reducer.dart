import 'package:family/family_model.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/store/device_reducer.dart';
import 'package:smart_home/navigator/family/family_const.dart';
import 'package:user/user.dart';

import '../../../common/smart_home_util.dart';
import '../../../edit/store/edit_action.dart';
import '../../../edit/util/edit_manager.dart';
import '../../../store/smart_home_action.dart';
import '../../../store/smart_home_state.dart';
import '../../../store/smart_home_store.dart';
import '../family_viewmodel.dart';
import '../model/geofencing_response_model.dart';
import 'family_action.dart';
import 'family_reducer_util.dart';
import 'family_state.dart';

final Reducer<SmartHomeState> familyCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateAppCurrentFamilyInfoAction>(
          _updateCurFamilyInfo)
      .call,
  TypedReducer<SmartHomeState, UpdateAppFamilyMapAction>(_updateFamilyMap).call,
  TypedReducer<SmartHomeState, UpdateNetworkStateAction>(_updateNetStatus).call,
  TypedReducer<SmartHomeState, UpdateExpandStatusAction>(_updateExpandStatus)
      .call,
  TypedReducer<SmartHomeState, UpdateFamilyChangeListAction>(
          _updateFamilyChangeAction)
      .call,
  TypedReducer<SmartHomeState, UpdateFamilyBubbleInvisibleAction>(
          _updateFamilyBubbleInvisibleAction)
      .call,
]);

SmartHomeState _updateFamilyBubbleInvisibleAction(
    SmartHomeState state, UpdateFamilyBubbleInvisibleAction action) {
  state.familyState.bubbleType = BubbleType.invisible;
  return state;
}

SmartHomeState _updateFamilyChangeAction(
    SmartHomeState state, UpdateFamilyChangeListAction action) {
  final List<FamilyChangeModel> familyChangeList = action.familyChangeList;
  state.familyState.familyChangeList = familyChangeList;
  if (familyChangeList.isEmpty) {
    state.familyState.bubbleType = BubbleType.invisible;
  } else if (familyChangeList.length == 1) {
    state.familyState.bubbleType = BubbleType.single;
  } else {
    state.familyState.bubbleType = BubbleType.multiple;
  }
  return state;
}

SmartHomeState _updateCurFamilyInfo(
    SmartHomeState state, UpdateAppCurrentFamilyInfoAction action) {
  final FamilyActionModel model = action.familyModel;
  final String familyName = model.familyName;
  if (model.familyId != state.familyState.familyId) {
    updateDeviceFilterToInit(state);
  }

  state.familyState.familyId = model.familyId;
  state.familyState.familyName = fetchValidFamilyName(familyName);
  if (model.memberType != null) {
    state.familyState.familyRole = FamilyRole.fromValue(model.memberType!);
  }
  _checkRoleExitEditState();
  return state;
}

SmartHomeState _updateFamilyMap(
    SmartHomeState state, UpdateAppFamilyMapAction action) {
  final FamilyModel? curFamily = action.familyMap[state.familyState.familyId];
  if (curFamily != null) {
    _updateCurrentFamily(state, curFamily);
  }

  final List<FamilyModel> familyModelList = action.familyMap.values.toList();
  final List<FamilyItemModel> familyList =
      _createFamilyItemList(familyModelList);

  state.familyState.familyModelList = familyList;

  return state;
}

void _updateCurrentFamily(SmartHomeState state, FamilyModel curFamily) {
  state.familyState.familyId = curFamily.familyId;
  state.familyState.familyName =
      fetchValidFamilyName(curFamily.info.familyName);
  state.familyState.familyRole = FamilyRole.fromValue(curFamily.memberType);
  _checkRoleExitEditState();
}

List<FamilyItemModel> _createFamilyItemList(List<FamilyModel> familyModelList) {
  final String userId = User.getOauthDataSync()?.uhome_user_id ?? '';
  final List<FamilyItemModel> familyList = <FamilyItemModel>[];

  if (userId.isEmpty || familyModelList.isEmpty) {
    return familyList;
  }

  for (final FamilyModel model in familyModelList) {
    final FamilyItemModel familyItem = _createFamilyItem(userId, model);
    familyList.add(familyItem);
  }

  _sortFamilyList(familyList);
  return familyList;
}

FamilyItemModel _createFamilyItem(String userId, FamilyModel model) {
  final bool isOwner = model.ownerId == userId;
  String time = model.createTime;

  if (!isOwner && model.joinTime.isNotEmpty) {
    time = model.joinTime;
  }

  final int timestamp = parseTimeToTimestamp(time);

  return FamilyItemModel(
    familyId: model.familyId,
    familyName: fetchValidFamilyName(model.info.familyName),
    isOwner: isOwner,
    createTime: timestamp,
    memberType: model.memberType,
  );
}

void _sortFamilyList(List<FamilyItemModel> familyList) {
  familyList.sort((FamilyItemModel a, FamilyItemModel b) {
    if (a.isOwner && !b.isOwner) {
      return -1;
    } else if (!a.isOwner && b.isOwner) {
      return 1;
    } else {
      return a.createTime.compareTo(b.createTime);
    }
  });
}

SmartHomeState _updateNetStatus(
    SmartHomeState state, UpdateNetworkStateAction action) {
  state.familyState.netAvailable = action.isNetAvailable;
  return state;
}

SmartHomeState _updateExpandStatus(
    SmartHomeState state, UpdateExpandStatusAction action) {
  state.familyState.isExpand = action.expand;
  return state;
}

void _checkRoleExitEditState() {
  if (isFamilyMemberRole()) {
    if (smartHomeStore.state.isEditState) {
      smartHomeStore.dispatch(ExitEditStateAction());
    }

    if (SmartHomeEditManager.editDialogContext != null) {
      SmartHomeEditManager.closeEditDialog();
    }
  }
}
