import 'package:device_utils/compare/compare.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/navigator/family/family_viewmodel.dart';
import 'package:smart_home/navigator/family/model/geofencing_response_model.dart';

enum BubbleType {
  invisible,
  single,
  multiple;
}

class FamilyState {
  bool netAvailable = true;
  String familyId = '';
  String familyName = '';
  bool isExpand = false;
  FamilyRole familyRole = FamilyRole.member;
  List<FamilyItemModel> familyModelList = <FamilyItemModel>[];
  List<FamilyChangeModel> familyChangeList = <FamilyChangeModel>[];
  BubbleType bubbleType = BubbleType.invisible;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FamilyState &&
          runtimeType == other.runtimeType &&
          netAvailable == other.netAvailable &&
          familyId == other.familyId &&
          familyName == other.familyName &&
          isExpand == other.isExpand &&
          isListEqual(familyModelList, other.familyModelList) &&
          isListEqual(familyChangeList, other.familyChangeList);

  @override
  int get hashCode =>
      netAvailable.hashCode ^
      familyId.hashCode ^
      familyName.hashCode ^
      isExpand.hashCode ^
      listHashCode(familyModelList) ^
      listHashCode(familyChangeList);
}
