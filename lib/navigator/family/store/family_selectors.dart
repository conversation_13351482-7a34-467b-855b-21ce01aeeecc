import 'package:reselect/reselect.dart';
import 'package:smart_home/navigator/family/model/geofencing_response_model.dart';
import 'package:smart_home/navigator/family/store/family_state.dart';
import 'package:smart_home/smart_home.dart';

import '../../../store/smart_home_state.dart';
import '../family_viewmodel.dart';

const double familyOwnerItemHeight = 54;
const double familyShareItemHeight = 75;
const double kMaxFamilyListHeight = 363;

class FamilySelectors {
  static final Selector<SmartHomeState, FamilyPopViewModel> familyPopViewModel =
      createSelector2(
    (SmartHomeState state) => state.familyState.familyId,
    (SmartHomeState state) => state.familyState.familyModelList,
    _createFamilyPopViewModel,
  );

  static FamilyPopViewModel _createFamilyPopViewModel(
    String familyId,
    List<FamilyItemModel> familyModelList,
  ) {
    double totalHeight = 0;
    for (final FamilyItemModel model in familyModelList) {
      totalHeight = totalHeight +
          (model.isOwner ? familyOwnerItemHeight : familyShareItemHeight);
      if (totalHeight > kMaxFamilyListHeight) {
        totalHeight = kMaxFamilyListHeight;
        break;
      }
    }
    return FamilyPopViewModel(familyId, familyModelList, totalHeight);
  }

  static final Selector<SmartHomeState, SwitchFamilyViewModel>
      switchFamilySelector = createSelector2(
          (SmartHomeState state) => state.familyState.bubbleType,
          (SmartHomeState state) => state.familyState.familyChangeList,
          (BubbleType bubbleType, List<FamilyChangeModel> familyChangeList) {
    String bubbleText = '';
    String familyId = '';
    String familyName = '';
    if (bubbleType == BubbleType.single) {
      final String? name = familyChangeList.firstOrNull?.familyName;
      bubbleText = name != null ? '您可能在“$name”' : '';
      familyId = familyChangeList.firstOrNull?.familyId ?? '';
      familyName = name ?? '';
    } else if (bubbleType == BubbleType.multiple) {
      bubbleText = '您可能在其他家庭';
    }
    return SwitchFamilyViewModel(bubbleType, bubbleText, familyId, familyName);
  });
}
