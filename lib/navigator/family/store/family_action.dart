import 'package:family/family_model.dart';

import '../model/geofencing_response_model.dart';

class FamilyBaseAction {}

class UpdateAppCurrentFamilyInfoAction extends FamilyBaseAction {
  UpdateAppCurrentFamilyInfoAction(this.familyModel);

  FamilyActionModel familyModel;
}

class UpdateAppFamilyMapAction extends FamilyBaseAction {
  UpdateAppFamilyMapAction(this.familyMap);

  Map<String, FamilyModel> familyMap;
}

class UpdateExpandStatusAction extends FamilyBaseAction {
  UpdateExpandStatusAction(this.expand);

  bool expand;
}

class FamilyActionModel {
  String familyId = '';
  String familyName = '';
  int? memberType;

  FamilyActionModel({
    required this.familyId,
    required this.familyName,
    required this.memberType,
  });
}

class UpdateFamilyChangeListAction extends FamilyBaseAction {
  UpdateFamilyChangeListAction(this.familyChangeList);

  List<FamilyChangeModel> familyChangeList;
}

class UpdateFamilyBubbleInvisibleAction extends FamilyBaseAction {
  UpdateFamilyBubbleInvisibleAction();
}
