import 'package:device_utils/typeId_parse/template_map.dart';

class GeofencingRequestModel {
  GeofencingRequestModel({
    required this.longitude,
    required this.latitude,
  });

  String longitude;
  String latitude;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    return data;
  }

  @override
  String toString() {
    return 'GeofencingRequestModel{longitude: $longitude, latitude: $latitude}';
  }
}

class GeofencingResponseModel {
  String retCode = '';
  String retInfo = '';
  List<FamilyChangeModel> data = <FamilyChangeModel>[];

  GeofencingResponseModel.fromJson(Map<dynamic, dynamic> json) {
    retCode = json.stringValueForKey('retCode', '');
    retInfo = json.stringValueForKey('retInfo', '');
    final List<dynamic> list = json.listValueForKey('data', <dynamic>[]);
    for (final dynamic v in list) {
      if (v is Map<dynamic, dynamic>) {
        data.add(FamilyChangeModel.fromJson(v));
      }
    }
  }

  @override
  String toString() {
    return 'GeofencingResponseModel{retCode: $retCode, retInfo: $retInfo, data: $data}';
  }
}

class FamilyChangeModel {
  String familyId = '';
  String familyName = '';

  FamilyChangeModel.fromJson(Map<dynamic, dynamic> json) {
    familyId = json.stringValueForKey('familyId', '');
    familyName = json.stringValueForKey('familyName', '');
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FamilyChangeModel &&
          runtimeType == other.runtimeType &&
          familyId == other.familyId &&
          familyName == other.familyName;

  @override
  int get hashCode => familyId.hashCode ^ familyName.hashCode;

  @override
  String toString() {
    return 'FamilyChangeModel{familyId: $familyId, familyName: $familyName}';
  }
}
