import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/navigator/family/widget/family_pop_dialog.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import 'package:user/user.dart';

import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../common/smart_home_util.dart';
import '../../store/smart_home_state.dart';
import 'family_viewmodel.dart';

class FamilyWidget extends StatelessWidget {
  const FamilyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildFamilyName();
  }

  Widget _buildFamilyName() {
    return StoreConnector<SmartHomeState, FamilyHeaderViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return FamilyHeaderViewModel(
            store.state.isLogin,
            store.state.familyState.familyName,
            store.state.familyState.isExpand);
      },
      builder: (BuildContext context, FamilyHeaderViewModel viewModel) {
        if (viewModel.familyName.isEmpty) {
          return Container();
        }
        return GestureDetector(
          onTap: () {
            if (!(User.getLoginStatusSync()?.isLogin ?? false)) {
              goToPageWithDebounce(SmartHomeConstant.loginUrl);
              return;
            }

            gioTrack(GioConst.tabChangeFamilyClick);
            showFamilyPopDialog(context);
          },
          child: PressableChangeOpacityWidget(
            opacity: 0.39,
            child: Row(
              children: <Widget>[
                Flexible(child: _buildFamilyNameWidget(viewModel.familyName)),
                _buildArrowWidget(viewModel.isExpand, viewModel.isLogin),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFamilyNameWidget(String familyName) {
    return Text(
      familyName,
      style: TextStyle(
          fontWeight: FontWeight.w500,
          fontFamilyFallback: fontFamilyFallback(),
          fontSize: 20,
          color: const Color(0xff111111)),
      softWrap: true,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  Widget _buildArrowWidget(bool isExpand, bool isLogin) {
    double angle = -pi / 2;
    if (isLogin) {
      angle = 0;
    }
    if (isExpand) {
      angle = -pi;
    }
    return Container(
      margin: const EdgeInsets.only(left: 4),
      child: Transform.rotate(
        angle: angle,
        child: Image.asset(
          'assets/icons/more_arrow.webp',
          package: 'smart_home',
          width: 12,
          height: 12,
        ),
      ),
    );
  }
}
