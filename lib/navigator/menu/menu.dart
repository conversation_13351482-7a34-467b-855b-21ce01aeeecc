import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/navigator/menu/menu_pop_widget.dart';
import 'package:smart_home/widget_common/ui_components.dart';
import 'package:user/user.dart';
import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../common/smart_home_util.dart';
import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';

class MenuButtonWidget extends StatelessWidget {
  const MenuButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (!(User.getLoginStatusSync()?.isLogin ?? false)) {
            goToPageWithDebounce(SmartHomeConstant.loginUrl);
            return;
          }

          _addButtonClickGio();
          if (isFamilyMemberRole()) {
            ToastHelper.showToast(SmartHomeConstant.familyRoleWarning);
            return;
          }

          showPopUpMenu(context);
        },
        child: _buildAddWidget());
  }

  Widget _buildAddWidget() {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        builder: (BuildContext context, bool isGray) {
          return Opacity(
            opacity: isGray ? 0.1 : 1,
            child: Image.asset(
              'assets/icons/add_black.webp',
              width: 24,
              height: 24,
              package: SmartHomeConstant.package,
            ),
          );
        },
        converter: (Store<SmartHomeState> store) {
          return store.state.isLogin && isFamilyMemberRole();
        });
  }

  void showPopUpMenu(BuildContext context) {
    showGeneralDialog<void>(
      barrierColor: Colors.transparent,
      barrierDismissible: true,
      barrierLabel: 'family_popup',
      context: context,
      pageBuilder: (BuildContext dialogContext, Animation<double> animation1,
          Animation<double> animation2) {
        return Stack(
          children: <Widget>[
            Positioned(
              top: 44 + MediaQuery.of(context).padding.top,
              right: 16,
              child: Material(
                  color: Colors.transparent,
                  child: CommonUIComponents.buildPopupContentWithBlur(
                      const MenuPopWidget())),
            ),
          ],
        );
      },
      transitionBuilder: (BuildContext ctx, Animation<double> a1,
          Animation<double> a2, Widget child) {
        final double curvedValue = Curves.easeOutCubic.transform(a1.value);
        return Transform.scale(
          origin: Offset(MediaQuery.of(context).size.width / 2,
                  -MediaQuery.of(context).size.height / 2) +
              Offset(-16, 44 + MediaQuery.of(context).padding.top),
          scale: curvedValue,
          child: Opacity(
            opacity: a1.value,
            child: child,
          ),
        );
      },
    );
  }

  void _addButtonClickGio() {
    final FamilyRole familyRole = smartHomeStore.state.familyState.familyRole;
    String role = '家庭创建者';
    if (familyRole == FamilyRole.admin) {
      role = '管理员';
    } else if (familyRole == FamilyRole.member) {
      role = '成员';
    }
    gioTrack(GioConst.smartHomeAddClick, <String, String>{
      'role_type': role,
    });
  }
}
