import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:family/member_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import 'package:smart_home/widget_common/ui_components.dart';

import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../common/constant_gio_scene.dart';
import '../../common/smart_home_util.dart';
import '../../scene/switch/switch_presenter.dart';
import '../../store/smart_home_store.dart';

class MenuPopWidget extends StatefulWidget {
  const MenuPopWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MenuPopWidgetState();
  }
}

class _MenuPopWidgetState extends State<MenuPopWidget> {
  bool sceneSwitch = true;

  @override
  void initState() {
    super.initState();

    getSceneSwitch();
  }

  Future<void> getSceneSwitch() async {
    sceneSwitch = await SwitchPresenter.querySwitchStatusFromStorage();
    DevLogger.info(
        tag: SmartHomeConstant.package,
        msg: 'getSceneSwitch sceneSwitch: $sceneSwitch');
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: CommonUIComponents.dialogGradientBorderDecoration,
      child: ClipRRect(
        borderRadius: const BorderRadius.all(
          Radius.circular(16),
        ),
        child: SizedBox(
          width: 198,
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: _buildMenuList()),
        ),
      ),
    );
  }

  List<Widget> _buildMenuList() {
    final List<Widget> menuList = <Widget>[];
    final List<MenuItem> menuItems =
        MenuViewModel.fromState(sceneSwitch).menuItems;
    for (int i = 0; i < menuItems.length; i++) {
      final MenuItem item = menuItems[i];
      menuList.add(
          _buildItem(item.title, item.icon, i, menuItems.length, onTap: () {
        _menuItemClickGio(itemName: item.title);
        Navigator.pop(context);
        if (item.title == createSceneTitle) {
          gioTrack(GioScene.gioMenuSceneCreate,
              <String, String>{'channel_name': '智家页'});
          _jumpIfNotMemberRole(item.jumpUrl);
        } else if (item.title == inviteFamilyTitle) {
          _showFamilyRoleSelectDialog(context);
        } else {
          _jumpIfNotMemberRole(item.jumpUrl);
        }
      }));
    }
    return menuList;
  }

  void _jumpIfNotMemberRole(String jumpUrl) {
    if (isFamilyMemberRole()) {
      ToastHelper.showToast(SmartHomeConstant.contactFamilyRole);
      return;
    }

    goToPageWithDebounce(jumpUrl);
  }

  static const String sourceName = '智家tab';

  Future<void> _showFamilyRoleSelectDialog(BuildContext context) async {
    if (isFamilyMemberRole()) {
      ToastHelper.showToast(SmartHomeConstant.contactFamilyRole);
      return;
    }
    List<MemberModel> memberList = <MemberModel>[];
    try {
      final FamilyModel familyModel = await Family.getCurrentFamily();
      memberList = _getMemberList(familyModel);
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: '_showFamilyRoleSelectDialog err:$err');
    }

    if (memberList.length >= maxMemberCount) {
      ToastHelper.showToast(SmartHomeConstant.memberCountLimit);
      return;
    }

    if (!context.mounted) {
      return;
    }

    showInviteFamilyBottomSheetWidget(
      package: SmartHomeConstant.package,
      context,
      sourceName: sourceName,
      onNextTap: (BuildContext _context, FamilyRole familyRole) {
        if (_isRoleLimitExceeded(familyRole, memberList)) {
          return;
        }

        Navigator.pop(_context);
        final String jumpUrl = getInviteFamilyUrl(familyRole);
        goToPageWithDebounce(jumpUrl);
      },
    );
  }

  List<MemberModel> _getMemberList(FamilyModel? familyModel) {
    final List<MemberModel> memberList = <MemberModel>[];
    if (familyModel == null) {
      return memberList;
    }
    memberList.addAll(familyModel.members);
    memberList.removeWhere((MemberModel element) =>
        element.memberInfo.virtualUserFlag ||
        element.memberType == FamilyRole.creator.value);

    return memberList;
  }

  static const int maxMemberCount = 50;
  static const int maxAdminCount = 10;

  bool _isRoleLimitExceeded(
      FamilyRole familyRole, List<MemberModel> memberList) {
    int count = 0;
    for (final MemberModel memberModel in memberList) {
      if (memberModel.memberType == familyRole.value) {
        count++;
      }
    }
    if (familyRole == FamilyRole.admin && count >= maxAdminCount) {
      ToastHelper.showToast(SmartHomeConstant.adminCountLimit);
      return true;
    }
    return false;
  }

  void _menuItemClickGio({required String itemName}) {
    gioTrack(GioConst.smartHomeClickMenu, <String, String>{
      'value': itemName,
    });
  }

  Widget _buildItem(String title, String icon, int index, int total,
      {void Function()? onTap}) {
    final double height = index == 0 || index == total - 1 ? 62 : 54;
    return PressableOverlayWidget(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: Container(
          height: height,
          padding: EdgeInsets.only(
              top: index == 0 ? 8 : 0,
              bottom: index == total - 1 ? 8 : 0,
              left: 16,
              right: 16),
          color: Colors.transparent,
          child: Row(
            children: <Widget>[
              Image.asset(
                icon,
                width: 24,
                height: 24,
                fit: BoxFit.cover,
                package: SmartHomeConstant.package,
              ),
              const SizedBox(
                width: 8,
              ),
              Text(
                title,
                style: TextStyle(
                  color: Colors.black.withOpacity(0.93),
                  fontSize: 16,
                  height: 22 / 16,
                  fontWeight: FontWeight.w500,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 添加设备
const String addDevice =
    'http://uplus.haier.com/uplusapp/main/qrcodescan.html?needAuthLogin=1';

// 创建场景url
const String createScene = 'mpaas://scene?needAuthLogin=1&from=2#scenemade';

// 扫一扫
const String scanQrCode =
    'https://uplus.haier.com/uplusapp/scan/homescanpage.html?needAuthLogin=1';

// 邀请家人
String getInviteFamilyUrl(FamilyRole familyRole) {
  return 'mpaas://familymanage?familyID=${smartHomeStore.state.familyState.familyId}&memberRole=${familyRole.value}#/invitationPage';
}

// 手动执行
String getManuallySceneUrl() {
  return 'mpaas://scene_mi?needAuthLogin=1&familyId=${smartHomeStore.state.familyState.familyId}&channelName=智家页#scenemade/manually';
}

// 自动执行
String getPlatformSceneUrl() {
  return 'mpaas://scene_mi?needAuthLogin=1&familyId=${smartHomeStore.state.familyState.familyId}&channelName=智家页#scenemade/platform';
}

const String addDeviceTitle = '添加设备';
const String manualControlTitle = '手动控制';
const String autoExecuteTitle = '自动执行';
const String createSceneTitle = '创建场景';
const String scanQRCodeTitle = '扫一扫';
const String inviteFamilyTitle = '邀请家人';

class MenuViewModel {
  final List<MenuItem> menuItems;

  MenuViewModel({required this.menuItems});

  static MenuViewModel fromState(bool sceneSwitch) {
    final List<MenuItem> items = <MenuItem>[
      MenuItem(addDeviceTitle, 'assets/icons/menu_add_device.webp', addDevice),
    ];

    if (sceneSwitch) {
      items.add(MenuItem(manualControlTitle,
          'assets/icons/menu_add_manually.webp', getManuallySceneUrl()));
      items.add(MenuItem(autoExecuteTitle,
          'assets/icons/menu_add_platform.webp', getPlatformSceneUrl()));
    } else {
      items.add(MenuItem(
          createSceneTitle, 'assets/icons/menu_add_scene.webp', createScene));
    }
    items.add(MenuItem(
        scanQRCodeTitle, 'assets/icons/menu_scan_qr_code.webp', scanQrCode));
    items.add(MenuItem(
        inviteFamilyTitle, 'assets/icons/menu_invite_family.webp', ''));
    return MenuViewModel(menuItems: items);
  }
}

class MenuItem {
  String title;
  String icon;
  String jumpUrl;

  MenuItem(this.title, this.icon, this.jumpUrl);
}
