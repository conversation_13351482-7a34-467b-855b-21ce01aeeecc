import 'package:flutter_common_ui/flutter_common_ui.dart';

import 'analysis_const.dart';

enum TraceLocation {
  t1_1,
  t1_2,
  t2,
  t3,
  t4,
}

enum TraceType {
  devListChange,
  curFamilyChange,
}

class TimeConsumeStatisticTracker {
  static bool _initLogin = false;
  static int _t0 = -1;

  static bool _gioEnable = true;

  static final Map<int, Map<TraceLocation, int>> _gioParamsWithTraceId =
      <int, Map<TraceLocation, int>>{};

  static void init(bool isLogin) {
    _t0 = DateTime.now().millisecondsSinceEpoch;
    _initLogin = isLogin;
    if (!isLogin) {
      _gioEnable = false;
    }
  }

  static void trace(
      {required int traceId,
      required TraceLocation loc,
      required TraceType traceType,
      bool? gioEnable,
      int? deviceCount}) {
    if (gioEnable != null) {
      _gioEnable = gioEnable;
    }
    if (!_gioEnable) {
      return;
    }
    final Map<TraceLocation, int> tmpMap =
        _gioParamsWithTraceId[traceId] ?? <TraceLocation, int>{};
    tmpMap[loc] = DateTime.now().millisecondsSinceEpoch;
    _gioParamsWithTraceId[traceId] = tmpMap;

    if (loc == TraceLocation.t4) {
      _submit(traceId, traceType, deviceCount ?? 99999);
    }
  }

  static void _submit(int traceId, TraceType traceType, int deviceCount) {
    if (_gioEnable) {
      _gioEnable = false;
      if (!_gioParamsWithTraceId.containsKey(traceId)) {
        return;
      }

      Future<void>.delayed(const Duration(seconds: 5), () {
        final Map<String, dynamic> gioParams = _buildGioParamsWith(traceId);
        gioParams[TimeStatisticConst.sizeAllDevList] = deviceCount;

        final String eventId = traceType == TraceType.devListChange
            ? TimeStatisticConst.timeStatisticEventId
            : TimeStatisticConst.timeStatisticCurFamilyChangeEventId;
        gioTrack(eventId, gioParams);
      });
    }
  }

  static Map<String, dynamic> _buildGioParamsWith(int traceId) {
    final Map<String, dynamic> finalGioParams = <String, dynamic>{};

    final Map<TraceLocation, int> locationMap = _gioParamsWithTraceId[traceId]!;
    // D0
    if (locationMap.containsKey(TraceLocation.t1_1) && _t0 != -1) {
      finalGioParams[TimeStatisticConst.d0Init2DevListChange] =
          locationMap[TraceLocation.t1_1]! - _t0;
    } else if (locationMap.containsKey(TraceLocation.t1_2) && _t0 != -1) {
      finalGioParams[TimeStatisticConst.d0Init2CurFamilyChange] =
          locationMap[TraceLocation.t1_2]! - _t0;
    }
    // D1
    if (locationMap.containsKey(TraceLocation.t1_1) &&
        locationMap.containsKey(TraceLocation.t2)) {
      finalGioParams[TimeStatisticConst.d1GetDeviceInfoInDevListChange] =
          locationMap[TraceLocation.t2]! - locationMap[TraceLocation.t1_1]!;
    } else if (locationMap.containsKey(TraceLocation.t1_2) &&
        locationMap.containsKey(TraceLocation.t2)) {
      finalGioParams[TimeStatisticConst.d1GetDeviceInfoInCurFamilyChange] =
          locationMap[TraceLocation.t2]! - locationMap[TraceLocation.t1_2]!;
    }
    // D2
    if (locationMap.containsKey(TraceLocation.t2) &&
        locationMap.containsKey(TraceLocation.t3)) {
      finalGioParams[TimeStatisticConst.d2ConvertDeviceModel] =
          locationMap[TraceLocation.t3]! - locationMap[TraceLocation.t2]!;
    }
    // D3
    if (locationMap.containsKey(TraceLocation.t3) &&
        locationMap.containsKey(TraceLocation.t4)) {
      finalGioParams[TimeStatisticConst.d3SortDeviceList] =
          locationMap[TraceLocation.t4]! - locationMap[TraceLocation.t3]!;
    }
    // D4
    if (_initLogin) {
      // 初始化已登录 页面初始化-设备VM组装完成 耗时统计
      if (locationMap.containsKey(TraceLocation.t4) && _t0 != -1) {
        finalGioParams[TimeStatisticConst.d4TotalInitLogin] =
            locationMap[TraceLocation.t4]! - _t0;
      }
    } else {
      // 初始化未登录->登录 过程耗时不定，不统计
    }
    return finalGioParams;
  }
}
