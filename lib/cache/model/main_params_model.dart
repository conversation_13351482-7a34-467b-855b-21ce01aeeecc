/*
 * 描述：启动参数模型
 * 作者：fancunshuo
 * 建立时间: 2025/4/25
 */

import 'package:smart_home/scene/scene_presenter.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/whole_house/whole_house_cache_model.dart';
import 'package:smart_home/whole_house/whole_house_presenter.dart';
import 'package:upservice/map_analysis_extension/map_analysis_extension.dart';

/// 接口文档：https://ihaier.feishu.cn/wiki/DPjwwXJFyil1JIkz1NqcKZWAnMe
class MainParamsKey {
  static const String keyIsLogin = 'is_logined';
  static const String keyUserId = 'userId';
  static const String keyFamilyId = 'familyId';
  static const String keyFamilyName = 'familyName';

  static const String valueTrue = 'true';
  static const String valueFalse = 'false';
}

class MainParamsModel {
  String familyId = '';
  String familyName = '';
  bool isLogin = false;
  SceneViewModel? sceneViewModel;
  WholeHouseCacheModel? wholeHouseCache;

  MainParamsModel.fromJson(Map<dynamic, dynamic> json) {
    familyId = json.stringValueForKey(MainParamsKey.keyFamilyId, '');
    familyName = json.stringValueForKey(MainParamsKey.keyFamilyName, '');
    final String isLoginString =
        json.stringValueForKey(MainParamsKey.keyIsLogin, 'false');
    isLogin = isLoginString == MainParamsKey.valueTrue;

    final String sceneListString = json.stringValueForKey('scene_list', '');

    sceneViewModel =
        ScenePresenter.getSceneListListFromStorage(sceneListString);

    final String wholeHouseString = json.stringValueForKey('whole_house', '');

    wholeHouseCache =
        WholeHousePresenter.getWholeHouseDataFromStorage(wholeHouseString);
  }
}
