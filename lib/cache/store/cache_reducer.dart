import 'package:redux/redux.dart';
import 'package:smart_home/cache/model/main_params_model.dart';
import 'package:smart_home/cache/store/cache_action.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/store/whole_house_reducer.dart';

import '../../navigator/family/family_const.dart';
import '../../navigator/family/store/family_reducer_util.dart';

final Reducer<SmartHomeState> cacheCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateAppCacheAction>(_updateAppCache).call,
]);

SmartHomeState _updateAppCache(
    SmartHomeState state, UpdateAppCacheAction action) {
  state.isLogin = action.mainParamsModel.isLogin;
  state.familyState.familyId = action.mainParamsModel.familyId;
  state.familyState.familyName =
      fetchValidFamilyName(action.mainParamsModel.familyName);

  if (state.isLogin) {
    _initSceneDataFromCache(state, action.mainParamsModel);
    return updateWholeHouseDataFromCache(
        state, action.mainParamsModel.wholeHouseCache);
  }
  return state;
}

void _initSceneDataFromCache(
    SmartHomeState state, MainParamsModel mainParamsModel) {
  if (mainParamsModel.sceneViewModel is SceneViewModel) {
    // 初始化读场景缓存 无需发dispatch 直接更State
    state.sceneState.sceneMap[mainParamsModel.familyId] = <SceneItemViewModel>[
      ...mainParamsModel.sceneViewModel!.sceneList
    ];
    state.sceneState.visibility =
        mainParamsModel.sceneViewModel!.sceneList.isNotEmpty;
  }
}
