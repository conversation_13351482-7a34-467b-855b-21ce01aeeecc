import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/src/store.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/features/device_filter/selectors/filter_selectors.dart';
import 'package:smart_home/features/device_filter/viewmodels/filter_entrance_viewmodel.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

class FilterEntranceWidget extends StatelessWidget {
  const FilterEntranceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, FilteredEntranceViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return DeviceFilteredSelectors.selectFilteredEntranceViewModel(
            store.state);
      },
      builder: (BuildContext context, FilteredEntranceViewModel vm) {
        return Container(
          height: 32,
          margin: const EdgeInsets.only(left: 3, right: 16),
          padding:
              EdgeInsets.symmetric(horizontal: vm.isCategorySelectAll ? 12 : 8),
          decoration: BoxDecoration(
              color: vm.isCategorySelectAll
                  ? const Color(0xff000000).withOpacity(0.05)
                  : const Color(0xff2283E2).withOpacity(0.1),
              borderRadius: BorderRadius.circular(22)),
          child: ColorFiltered(
            colorFilter: ColorFilter.mode(
              vm.isCategorySelectAll
                  ? const Color(0xff666666)
                  : const Color(0xff2283E2),
              BlendMode.srcIn,
            ),
            child: Row(
              children: <Widget>[
                Image.asset(
                  'assets/icons/icon_filter.webp',
                  package: SmartHomeConstant.package,
                  height: vm.isCategorySelectAll ? 16 : 12,
                  width: vm.isCategorySelectAll ? 16 : 12,
                ),
                if (vm.isCategorySelectAll) const SizedBox(width: 2),
                Text(
                  vm.selectedDeviceCategory,
                  style: TextStyle(
                    fontSize: 11,
                    height: 14 / 11,
                    fontWeight: FontWeight.w400,
                    color: AppSemanticColors.item.information.primary,
                    fontFamilyFallback: fontFamilyFallback(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
