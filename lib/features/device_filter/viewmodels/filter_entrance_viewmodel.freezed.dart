// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_entrance_viewmodel.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FilteredEntranceViewModel {
  bool get isCategorySelectAll => throw _privateConstructorUsedError;
  String get selectedDeviceCategory => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FilteredEntranceViewModelCopyWith<FilteredEntranceViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilteredEntranceViewModelCopyWith<$Res> {
  factory $FilteredEntranceViewModelCopyWith(FilteredEntranceViewModel value,
          $Res Function(FilteredEntranceViewModel) then) =
      _$FilteredEntranceViewModelCopyWithImpl<$Res, FilteredEntranceViewModel>;
  @useResult
  $Res call({bool isCategorySelectAll, String selectedDeviceCategory});
}

/// @nodoc
class _$FilteredEntranceViewModelCopyWithImpl<$Res,
        $Val extends FilteredEntranceViewModel>
    implements $FilteredEntranceViewModelCopyWith<$Res> {
  _$FilteredEntranceViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCategorySelectAll = null,
    Object? selectedDeviceCategory = null,
  }) {
    return _then(_value.copyWith(
      isCategorySelectAll: null == isCategorySelectAll
          ? _value.isCategorySelectAll
          : isCategorySelectAll // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedDeviceCategory: null == selectedDeviceCategory
          ? _value.selectedDeviceCategory
          : selectedDeviceCategory // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FilteredEntranceViewModelImplCopyWith<$Res>
    implements $FilteredEntranceViewModelCopyWith<$Res> {
  factory _$$FilteredEntranceViewModelImplCopyWith(
          _$FilteredEntranceViewModelImpl value,
          $Res Function(_$FilteredEntranceViewModelImpl) then) =
      __$$FilteredEntranceViewModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isCategorySelectAll, String selectedDeviceCategory});
}

/// @nodoc
class __$$FilteredEntranceViewModelImplCopyWithImpl<$Res>
    extends _$FilteredEntranceViewModelCopyWithImpl<$Res,
        _$FilteredEntranceViewModelImpl>
    implements _$$FilteredEntranceViewModelImplCopyWith<$Res> {
  __$$FilteredEntranceViewModelImplCopyWithImpl(
      _$FilteredEntranceViewModelImpl _value,
      $Res Function(_$FilteredEntranceViewModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCategorySelectAll = null,
    Object? selectedDeviceCategory = null,
  }) {
    return _then(_$FilteredEntranceViewModelImpl(
      isCategorySelectAll: null == isCategorySelectAll
          ? _value.isCategorySelectAll
          : isCategorySelectAll // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedDeviceCategory: null == selectedDeviceCategory
          ? _value.selectedDeviceCategory
          : selectedDeviceCategory // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FilteredEntranceViewModelImpl implements _FilteredEntranceViewModel {
  const _$FilteredEntranceViewModelImpl(
      {required this.isCategorySelectAll,
      required this.selectedDeviceCategory});

  @override
  final bool isCategorySelectAll;
  @override
  final String selectedDeviceCategory;

  @override
  String toString() {
    return 'FilteredEntranceViewModel(isCategorySelectAll: $isCategorySelectAll, selectedDeviceCategory: $selectedDeviceCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilteredEntranceViewModelImpl &&
            (identical(other.isCategorySelectAll, isCategorySelectAll) ||
                other.isCategorySelectAll == isCategorySelectAll) &&
            (identical(other.selectedDeviceCategory, selectedDeviceCategory) ||
                other.selectedDeviceCategory == selectedDeviceCategory));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isCategorySelectAll, selectedDeviceCategory);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FilteredEntranceViewModelImplCopyWith<_$FilteredEntranceViewModelImpl>
      get copyWith => __$$FilteredEntranceViewModelImplCopyWithImpl<
          _$FilteredEntranceViewModelImpl>(this, _$identity);
}

abstract class _FilteredEntranceViewModel implements FilteredEntranceViewModel {
  const factory _FilteredEntranceViewModel(
          {required final bool isCategorySelectAll,
          required final String selectedDeviceCategory}) =
      _$FilteredEntranceViewModelImpl;

  @override
  bool get isCategorySelectAll;
  @override
  String get selectedDeviceCategory;
  @override
  @JsonKey(ignore: true)
  _$$FilteredEntranceViewModelImplCopyWith<_$FilteredEntranceViewModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
