import 'package:reselect/reselect.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/features/device_filter/viewmodels/filter_entrance_viewmodel.dart';
import 'package:smart_home/store/smart_home_state.dart';

class DeviceFilteredSelectors {
  static final Selector<SmartHomeState, FilteredEntranceViewModel>
      selectFilteredEntranceViewModel = createSelector1(
    (SmartHomeState state) => state.deviceState.selectedDeviceCategory,
    _createFilteredEntranceViewModel,
  );

  static FilteredEntranceViewModel _createFilteredEntranceViewModel(
      String selectedDeviceCategory) {
    return FilteredEntranceViewModel(
      isCategorySelectAll:
          selectedDeviceCategory == SmartHomeConstant.deviceFilterSelectAll,
      selectedDeviceCategory:
          selectedDeviceCategory == SmartHomeConstant.deviceFilterSelectAll
              ? ''
              : selectedDeviceCategory,
    );
  }
}
