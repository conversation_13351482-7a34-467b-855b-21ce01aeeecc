import 'package:redux/redux.dart';

import '../../../store/smart_home_state.dart';
import 'activity_action.dart';

final Reducer<SmartHomeState> operatingActivityCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, SummerActivityUpdateAction>(
          _updateSummerActivity)
      .call,
]);

SmartHomeState _updateSummerActivity(
    SmartHomeState state, SummerActivityUpdateAction action) {
  state.operatingActivityState.summerActivity = action.summerActivity;
  return state;
}
