import 'package:device_utils/log/log.dart';
import 'package:upservice/dio/uhome_dio/uhome_dio.dart';

import '../../../common/constant.dart';
import '../models/summer_activity_model.dart';
import 'activity_client.dart';

class ActivityService {
  static Future<bool> checkSummerActivityNormal() async {
    try {
      final SummerActivityModel model = await ActivityRestClient(UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .checkSummerActivityNormal();
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'checkSummerActivityNormal:${model.isNormal}');
      return model.isNormal;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'checkSummerActivityNormal err:$err');
      return false;
    }
  }
}
