import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

import '../models/summer_activity_model.dart';

part 'activity_client.g.dart';

@RestApi()
abstract class ActivityRestClient {
  factory ActivityRestClient(Dio dio, {String baseUrl}) = _ActivityRestClient;

  // 夏日活动-判断活动当前是否正在举行中 https://stp.haier.net/project/191/interface/api/243735
  @GET('/api-gw/shpm/task/checkSummerActivityNormal')
  Future<SummerActivityModel> checkSummerActivityNormal();
}
