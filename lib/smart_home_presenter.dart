import 'dart:async';

import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/scene/scene_presenter.dart';
import 'package:smart_home/whole_house/device_consumables/device_consumables_presenter.dart';
import 'package:smart_home/whole_house/whole_house_presenter.dart';
import 'package:storage/storage.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import 'common/smart_home_util.dart';
import 'device/device_guide/device_guide_widget.dart';
import 'store/smart_home_store.dart';

class SmartHomePresenter {
  WholeHousePresenter wholeHousePresenter = WholeHousePresenter();
  ScenePresenter scenePresenter = ScenePresenter();
  DeviceConsumablesPresenter consumablesPresenter =
      DeviceConsumablesPresenter();
  bool _showBackTopBtn = false;
  static const int _showRocketHeight = 250;
  static const int _smartHomeIndex = 0;
  static const int _scrollAnimationDurationMs = 200;
  static const String _smartHomePresenter = 'SmartHomePresenter';


  static void changeTabbarToInit() {
    smartHomeStore.dispatch(UpdateDeviceTabIndexAction(0));
  }

  void fetchWholeHouseAndSceneDataInCurFamily(String familyId) {
    scenePresenter.fetchSceneData(familyId);
  }

  /// 查询仪表盘数据
  Future<void> fetchWholeHouseDataInCurFamily(
      {required TriggerType triggerType}) async {
    wholeHousePresenter.getWholeHouseData(triggerType: triggerType);
  }

  void handleJumpToUrl(Map<dynamic, dynamic> params) {
    switch (params['tabType']) {
      case 'water':
        goToPageWithDebounce(SmartHomeConstant.waterPage);
      case 'heating':
        goToPageWithDebounce(SmartHomeConstant.heatingPage);
      case 'air':
        goToPageWithDebounce(SmartHomeConstant.airPage);
      case 'whole_house':
        goToPageWithDebounce(SmartHomeConstant.wholeHousePage);
      case 'mine':
        goToPageWithDebounce(SmartHomeConstant.sceneMinePage);
      case 'recommend':
        goToPageWithDebounce(SmartHomeConstant.sceneRecommendPage);
      default:
    }
  }

  // 跳转至聚合引导
  Future<void> checkGuideStatus(BuildContext context) async {
    try {
      final LoginStatus loginStatus = await User.getLoginStatus();
      if (!loginStatus.isLogin) {
        return;
      }

      final UserInfo userInfo = await User.getUserInfo();
      final String guideAgeKey =
          '${SmartHomeConstant.userGuideStatus}${userInfo.userId}';
      final String guideFamilyKey =
          '${SmartHomeConstant.userGuideFamilyStatus}${userInfo.userId}';
      final bool guideAgeStatus = await Storage.getBooleanValue(guideAgeKey);
      if (guideAgeStatus) {
        await jumpFamilyGuide(guideAgeKey, guideFamilyKey);
      } else {
        jumpFullGuide(guideAgeKey, guideFamilyKey);
      }
    } catch (err) {
      DevLogger.error(
          tag: _smartHomePresenter, msg: 'checkGuideStatus error $err');
    }
  }

  void jumpFullGuide(String guideAgeKey, String guideFamilyKey) {
    gioTrack('MB35999');
    Storage.putBooleanValue(guideAgeKey, true);
    Storage.putBooleanValue(guideFamilyKey, true);
    goToPage(SmartHomeConstant.deviceGuideUrl,
        params: <String, dynamic>{ONLY_FAMILY_GUIDE: false});
  }

  Future<void> jumpFamilyGuide(
      String guideAgeKey, String guideFamilyKey) async {
    final bool familyGuideStatus =
        await Storage.getBooleanValue(guideFamilyKey);
    if (!familyGuideStatus) {
      gioTrack('MB35999');
      Storage.putBooleanValue(guideFamilyKey, true);
      goToPage(SmartHomeConstant.deviceGuideUrl);
    }
  }

  // 滚动监听并处理UIMessage发送
  void handleBackTopButtonVisibility(double offset) {
    final bool shouldShowButton = offset > _showRocketHeight;
    if (shouldShowButton != _showBackTopBtn) {
      _showBackTopBtn = shouldShowButton;
      UIMessage.fireEvent(ShowBackTopMessage(
          tabIndex: _smartHomeIndex, showBackTop: _showBackTopBtn));
    }
  }

  void sendHideBackTopMessage() {
    if (_showBackTopBtn) {
      _showBackTopBtn = false;
      UIMessage.fireEvent(
          ShowBackTopMessage(tabIndex: _smartHomeIndex, showBackTop: false));
    }
  }
}
