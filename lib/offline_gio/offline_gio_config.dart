import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/material.dart';
import 'package:function_toggle/function_toggle.dart';

//远端对 offline的配置
class OfflineGIOConfig {
  factory OfflineGIOConfig() {
    return _instance;
  }

  OfflineGIOConfig._internal();

  static final OfflineGIOConfig _instance = OfflineGIOConfig._internal();

  OfflineGIOConfigModel model =
      OfflineGIOConfigModel.fromJson(<dynamic, dynamic>{});

  Future<void> init() async {
    model = await FunctionToggle.instance
        .getFunctiontoggleModel<OfflineGIOConfigModel>(
            'SmartDevice_OfflineGIO',
            (Map<dynamic, dynamic> json) =>
                OfflineGIOConfigModel.fromJson(json));
  }

  bool isInRange(String apptypeCode) {
    return model.appTypeCodes.contains(apptypeCode);
  }
}

class OfflineGIOConfigModel extends FunctionToggleModel {
  OfflineGIOConfigModel.fromJson(Map<dynamic, dynamic> json)
      : super.fromJson(json) {
    deviceCount = json.intValueForKey('deviceCount', 0);

    final List<dynamic> list = json.listValueForKey('appTypeCodes', <String>[]);
    appTypeCodes.clear();
    list.forEach((dynamic element) {
      if (element is String) {
        appTypeCodes.add(element);
      }
    });
  }

  int deviceCount = 0;
  List<String> appTypeCodes = <String>[];

  @override
  String toString() {
    return 'OfflineGIOConfigModel{deviceCount: $deviceCount, appTypeCodes: $appTypeCodes}';
  }
}
