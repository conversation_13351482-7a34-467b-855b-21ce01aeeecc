import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

class OfflineStatusModel {
  bool isLoading = false;
  bool isSpecialOffline = false;
  bool isWaitDeployNetwork = false;
  bool isLongTime = false;
  String onlineState = ''; //在线1 离线0
  bool isReported = false;
  String appTypeCode = '';
  String deviceId = '';

  OfflineStatusModel(DeviceCardViewModel viewModel) {
    final SmartHomeDevice device = viewModel.device;
    final SmartHomeDeviceBasicInfo basicInfo = device.basicInfo;
    isLoading = basicInfo.configState == SmartHomeDeviceConfigState.loading;
    isSpecialOffline = viewModel.lowerPowerOrWifiClose;
    isWaitDeployNetwork = basicInfo.faultInformationStateCode == '1004';
    isLongTime = device.offlineDays == -1 || device.offlineDays > 30;
    onlineState = viewModel.deviceOffline ? '0' : '1';
    appTypeCode = basicInfo.appTypeCode;
    deviceId = basicInfo.deviceId;
  }

  @override
  String toString() {
    return '{isLoading: $isLoading, isSpecialOffline: $isSpecialOffline, isWaitDeployNetwork: $isWaitDeployNetwork, isLongTime: $isLongTime, onlineState: $onlineState, isReported: $isReported, appTypeCode: $appTypeCode}';
  }
}

class OfflineCountModel {
  int totalCount = 0;
  int offlineCount = 0;
  int specialOfflineCount = 0;
  int loadingCount = 0;
  int longTimeCount = 0;

  OfflineCountModel.defaultModel();

  @override
  String toString() {
    return '{totalCount: $totalCount, offlineCount: $offlineCount, specialOfflineCount: $specialOfflineCount, loadingCount: $loadingCount, longTimeCount: $longTimeCount}';
  }
}
