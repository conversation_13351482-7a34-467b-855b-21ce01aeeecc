/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2025/4/25
 */

import 'package:reselect/reselect.dart';
import 'package:smart_home/edit/edit_card_base_info_model.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';

import '../../device/device_view_model/card_base_view_model.dart';
import '../../device/device_view_model/device_card_view_model.dart';
import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';
import '../edit_bottom_viewmodel.dart';
import '../util/edit_constant.dart';

class EditSelectors {
  static Selector<SmartHomeState, bool> showCancelSelector = createSelector5(
      (SmartHomeState state) => state.editState.cacheCardList,
      (SmartHomeState state) => state.deviceState.allSmallCardSortIdList,
      (SmartHomeState state) => state.editState.cacheSceneCardList,
      (SmartHomeState state) =>
          state.sceneState.sceneMap[state.deviceState.selectedRoomId] ??
          <SceneItemViewModel>[],
      (SmartHomeState state) => state.editState.resizeMark, (
    List<CacheCardModel> cacheCardList,
    List<String> allSmallCardSortIdList,
    List<SceneItemViewModel> cacheSceneCardList,
    List<SceneItemViewModel> sceneList,
    int resizeMark,
  ) {
    return _isDeviceCardReorderOrResize(
            cacheCardList, allSmallCardSortIdList) ||
        _isSceneCardReorder(cacheSceneCardList, sceneList);
  });

  /// 设备卡片排序判断
  static bool _isDeviceCardReorderOrResize(
    List<CacheCardModel> cacheCardList,
    List<String> allSmallCardSortIdList,
  ) {
    for (int i = 0; i < cacheCardList.length; i++) {
      final String vmId = allSmallCardSortIdList[i];
      final CardBaseViewModel? viewModel =
          smartHomeStore.state.deviceState.allCardViewModelMap[vmId];

      final CacheCardModel cacheVm = cacheCardList[i];
      if (viewModel is DeviceCardViewModel) {
        if (vmId != cacheVm.id || cacheVm.type != viewModel.size) {
          return true;
        }
      }
    }
    return false;
  }

  /// 场景卡片排序判断
  static bool _isSceneCardReorder(List<SceneItemViewModel> cacheSceneCardList,
      List<SceneItemViewModel> sceneList) {
    for (int i = 0; i < cacheSceneCardList.length; i++) {
      final SceneItemViewModel sceneItemViewModel = cacheSceneCardList[i];
      if (sceneList.length > i &&
          sceneItemViewModel.sceneId != sceneList[i].sceneId) {
        return true;
      }
    }
    return false;
  }

  static Selector<SmartHomeState, EditBottomViewModel> editBottomSelector =
      createSelector4(
          (SmartHomeState state) => state.editState.editBtnList,
          (SmartHomeState state) => state.editState.cardSizeList,
          (SmartHomeState state) => state.editState.selectedCardsList,
          (SmartHomeState state) => state.editState.selectedSceneCardList,
          (List<EditBtnInfoViewModel> editBtnList,
              List<CardSizeOptionViewModel> cardSizeList,
              List<EditDeviceCardInfo> selectedCardsList,
              List<EditDeviceCardInfo> selectedSceneCardList) {
    return _createEditBottomSelector(
        editBtnList, cardSizeList, selectedCardsList, selectedSceneCardList);
  });

  static EditBottomViewModel _createEditBottomSelector(
      List<EditBtnInfoViewModel> editBtnList,
      List<CardSizeOptionViewModel> cardSizeList,
      List<EditDeviceCardInfo> selectedCardsList,
      List<EditDeviceCardInfo> selectedSceneCardList) {
    final bool hasSelectedItems =
        selectedCardsList.isNotEmpty ||
            selectedSceneCardList.isNotEmpty;

    final String placeHolderText = (hasSelectedItems && editBtnList.isEmpty)
        ? EditConstant.sceneEditNotSupport
        : EditConstant.editNotSupport;
    return EditBottomViewModel(editBtnList, cardSizeList, placeHolderText);
  }
}
