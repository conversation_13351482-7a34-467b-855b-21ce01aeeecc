import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_state.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/resize_device_card/resize_overlay.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:user/user.dart';

import '../../device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import '../../device/aggregation/aggregation_card/util/aggregation_device_util.dart';
import '../../device/aggregation/aggregation_detail/view_model/aggregation_room_select_view_model.dart';
import '../../device/device_view_model/card_base_view_model.dart';
import '../../device/device_view_model/device_card_view_model.dart';
import '../../device/resize_device_card/resize_base_model.dart';
import '../../store/smart_home_state.dart';
import '../edit_bottom_viewmodel.dart';
import '../edit_card_base_info_model.dart';
import '../edit_presenter/edit_presenter_manager.dart';
import '../util/edit_constant.dart';
import 'edit_action.dart';

final Reducer<SmartHomeState> editCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, EnterEditStateByCardAction>(
          _enterEditStateByCard)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceCardSelectedStateAction>(
          _updateDeviceCardSelectedState)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceCardSizeAction>(
          _updateDeviceCardSize)
      .call,
  TypedReducer<SmartHomeState, ExitEditStateAction>(_exitEditState).call,
  TypedReducer<SmartHomeState, ResetIsNeedSaveEditedCardListDataAction>(
          _resetIsNeedSaveEditedCardListData)
      .call,
  TypedReducer<SmartHomeState, DragResizeAction>(_updateDragStatus).call,
  TypedReducer<SmartHomeState, EnterEditStateBySceneCardAction>(
          _enterEditStateBySceneCard)
      .call,
  TypedReducer<SmartHomeState, UpdateSceneCardSelectedStatusAction>(
          _updateSceneCardSelectedStatus)
      .call,
  TypedReducer<SmartHomeState, UpdateEditBtnListAction>(
          _updateEditBtnListAction)
      .call,
  TypedReducer<SmartHomeState, UpdateRoomSelectAllStatusAction>(
          _updateSelectAllStatus)
      .call,
  TypedReducer<SmartHomeState, DisplayInHomePageUpdateRequestedAction>(
          _displayInHomePageStatusChanged)
      .call,
]);

SmartHomeState _enterEditStateByCard(
    SmartHomeState state, EnterEditStateByCardAction action) {
  state.isEditState = true;
  state.isScrollableScrollPhysics = false;
  state.editState.pageType = EditPresenterManager.currentPageForEdit;

  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[action.id];

  _cacheList(state);
  if (viewModel is DeviceCardViewModel) {
    viewModel.isSelected = true;
    final EditDeviceCardInfo deviceInfo = _getEditDeviceInfo(viewModel);
    state.editState.selectedCardsList.add(deviceInfo);

    _updateSelectAllButtonStatus(state, viewModel);
  }

  _updateSceneEditState(state, true, false);

  _checkEditAuthority(state);
  _setCardSizeList(state);
  return state;
}

SmartHomeState _updateDeviceCardSize(
    SmartHomeState state, UpdateDeviceCardSizeAction action) {
  state.editState.resizeMark++;
  return state;
}

List<SceneItemViewModel> _sceneList(SmartHomeState state) {
  return state.sceneState.sceneMap[state.deviceState.selectedRoomId] ??
      <SceneItemViewModel>[];
}

void _updateSceneEditState(
    SmartHomeState state, bool isEditState, bool isSelected) {
  final List<SceneItemViewModel> sceneList = _sceneList(state);
  for (final SceneItemViewModel element in sceneList) {
    element.isEditState = isEditState;
    element.isSelected = isSelected;
  }
}

SmartHomeState _enterEditStateBySceneCard(
    SmartHomeState state, EnterEditStateBySceneCardAction action) {
  state.isEditState = true;
  state.isScrollableScrollPhysics = false;

  _cacheList(state);
  // 场景卡片长按
  SceneItemViewModel? _sceneShowVm;
  final List<SceneItemViewModel> sceneList = _sceneList(state);
  for (final SceneItemViewModel element in sceneList) {
    if (element.sceneId == action.id) {
      _sceneShowVm = element;
    }
    element.isEditState = true;
    element.isSelected = element.sceneId == action.id;
  }

  if (_sceneShowVm == null) {
    return state;
  }

  final EditDeviceCardInfo deviceInfo = _getEditDeviceInfoByScene(_sceneShowVm);
  state.editState.selectedSceneCardList.add(deviceInfo);

  _checkEditAuthority(state);
  return state;
}

// 缓存数据
void _cacheList(SmartHomeState state) {
  state.editState.cacheCardList =
      state.deviceState.allSmallCardSortIdList.map((String id) {
    final CardBaseViewModel? viewModel =
        state.deviceState.allCardViewModelMap[id];
    return CacheCardModel(
      id: id,
      type: viewModel?.deviceCardType ?? DeviceCardType.middleCard,
    );
  }).toList();

  // 增加场景缓存列表--取消编辑回滚原场景
  final List<SceneItemViewModel> tmpCacheSceneCardList = <SceneItemViewModel>[];

  final List<SceneItemViewModel> sceneList = _sceneList(state);
  for (int i = 0; i < sceneList.length; i++) {
    tmpCacheSceneCardList.add(SceneItemViewModel.fromObject(sceneList[i]));
  }

  state.editState.cacheSceneCardList = tmpCacheSceneCardList;
}

SmartHomeState _exitEditState(
    SmartHomeState state, ExitEditStateAction action) {
  dragResizing = false;

  state.isEditState = false;
  state.isScrollableScrollPhysics = true;
  state.deviceState.allCardViewModelMap
      .forEach((String key, CardBaseViewModel value) {
    value.isSelected = false;
  });
  final List<SceneItemViewModel> sceneList = _sceneList(state);
  sceneList.forEach((SceneItemViewModel element) {
    element.isSelected = false;
    element.isEditState = false;
  });
  if (!state.familyState.netAvailable &&
      EditPresenterManager.isChangeSizeOrSort()) {
    state.editState.isNeedSaveEditedCardListData = true;
  }
  if (action.allCardSortList != null &&
      action.allCardSortList is List<String>) {
    state.deviceState.allSmallCardSortIdList = action.allCardSortList!;
  }
  _clearEditState(state);
  return state;
}

SmartHomeState _resetIsNeedSaveEditedCardListData(
    SmartHomeState state, ResetIsNeedSaveEditedCardListDataAction action) {
  state.editState.isNeedSaveEditedCardListData = false;
  return state;
}

SmartHomeState _updateDragStatus(
    SmartHomeState state, DragResizeAction action) {
  state.editState.dragResizing = action.dragging;
  return state;
}

SmartHomeState _updateDeviceCardSelectedState(
    SmartHomeState state, UpdateDeviceCardSelectedStateAction action) {
  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[action.id];
  if (viewModel is DeviceCardViewModel) {
    viewModel.isSelected = !viewModel.isSelected;
    final EditDeviceCardInfo deviceInfo = _getEditDeviceInfo(viewModel);
    if (viewModel.isSelected) {
      state.editState.selectedCardsList.add(deviceInfo);
    } else {
      state.editState.selectedCardsList
          .removeWhere((EditDeviceCardInfo element) => element.id == action.id);
    }
    _updateSelectAllButtonStatus(state, viewModel);
  }
  _checkEditAuthority(state);
  _setCardSizeList(state);
  return state;
}

/// 更新聚合页面中的全选按钮状态
SmartHomeState _updateSelectAllButtonStatus(
    SmartHomeState state, DeviceCardViewModel viewModel) {
  if (state.editState.pageType == EditPageType.home) {
    return state;
  }

  final SmartHomeDeviceBasicInfo basicInfo = viewModel.device.basicInfo;
  final String parentId = basicInfo.aggregationParentId;

  final List<String>? deviceList =
      _getAggregationDeviceList(state, parentId, basicInfo.roomInfo);
  if (deviceList == null) {
    return state;
  }

  final int selectedCount = state.editState.selectedCardsList
      .where((EditDeviceCardInfo item) => deviceList.contains(item.id))
      .length;

  _updateRoomSelectStatus(
      state: state,
      aggregationRoomSelectVM: AggregationRoomSelectViewModel(
          aggregationId: parentId,
          roomInfo: basicInfo.roomInfo,
          isSelectAll: selectedCount == deviceList.length));
  return state;
}

SmartHomeState _updateSceneCardSelectedStatus(
    SmartHomeState state, UpdateSceneCardSelectedStatusAction action) {
  final List<SceneItemViewModel> sceneList = _sceneList(state);
  for (int i = 0; i < sceneList.length; i++) {
    final SceneItemViewModel _tmpModel = sceneList[i];
    if (_tmpModel.sceneId == action.id) {
      final SceneItemViewModel element =
          SceneItemViewModel.fromObject(_tmpModel);
      element.isSelected = !element.isSelected;
      sceneList[i] = element;

      if (element.isSelected) {
        final EditDeviceCardInfo _sceneInfo =
            _getEditDeviceInfoByScene(element);
        state.editState.selectedSceneCardList.add(_sceneInfo);
      } else {
        state.editState.selectedSceneCardList.removeWhere(
            (EditDeviceCardInfo element) => element.id == action.id);
      }
      break;
    }
  }

  _checkEditAuthority(state);
  return state;
}

// 组装卡片尺寸list
void _setCardSizeList(SmartHomeState state) {
  List<CardSizeOptionViewModel> _cardSizeList = <CardSizeOptionViewModel>[
    CardSizeOptionViewModel(
        name: '中卡片', type: DeviceCardType.middleCard, actived: false),
    CardSizeOptionViewModel(
        name: '小卡片', type: DeviceCardType.smallCard, actived: false),
  ];
  if (state.editState.selectedCardsList.length == 1) {
    final CardBaseViewModel? viewModel = state.deviceState
        .allCardViewModelMap[state.editState.selectedCardsList[0].id];
    if (viewModel is DeviceCardViewModel) {
      if (viewModel.supportLargeCard) {
        _cardSizeList.insert(
            0,
            CardSizeOptionViewModel(
                name: '大卡片', type: DeviceCardType.largeCard, actived: false));
      }

      // 当前卡片状态高亮
      _cardSizeList.forEach((CardSizeOptionViewModel element) {
        if (element.type == viewModel.deviceCardType) {
          element.actived = true;
        }
      });
    }
  } else if (state.editState.selectedCardsList.length > 1) {
    // 是否所有已选卡片都支持大卡
    final bool _isAllSupportLarge = state.editState.selectedCardsList
        .every((EditDeviceCardInfo element) => element.supportLargeCard);
    if (_isAllSupportLarge) {
      _cardSizeList.insert(
          0,
          CardSizeOptionViewModel(
              name: '大卡片', type: DeviceCardType.largeCard, actived: false));
    }
  } else {
    _cardSizeList = <CardSizeOptionViewModel>[];
  }
  state.editState.cardSizeList = _cardSizeList;
}

SmartHomeState _updateEditBtnListAction(
    SmartHomeState state, UpdateEditBtnListAction action) {
  state.editState.pageType = action.pageType;
  _checkEditAuthority(state);
  return state;
}

// 功能鉴权
SmartHomeState _checkEditAuthority(SmartHomeState state) {
  state.editState.editBtnList = <EditBtnInfoViewModel>[];

  /// 说明：编辑按钮顺序为切换大小、移到顶部、共享、更改房间、重命名、转移家庭、删除设备、关闭聚合、编辑，下述代码按照此顺序鉴权
  if (_isSelectedScene(state)) {
    // 场景移出
    _checkSceneRemoveAuthority(state);
    // 场景编辑
    _checkSceneEditAuthority(state);
  } else {
    // 切换大小
    _checkChangeCardSizeAuthority(state);
    // 移到顶部
    _checkMoveToTopAuthority(state);
    // 共享
    _editBtnListAddShare(state);
    // 更改房间
    _checkChangeDevicePositionAuthority(state);
    // 重命名
    _checkRenameAuthority(state);
    // 转移家庭
    _checkChangeDeviceFamilyAuthority(state);
    // 编辑模式下的首页展示按钮
    _editBtnListHomeShow(state);
    // 编辑模式下的首页隐藏按钮
    _editBtnListHomeHide(state);
    // 删除设备
    _checkDeleteDeviceAuthority(state);
    // 关闭聚合
    _checkAggCloseAuthority(state);
    // 编辑聚合
    _checkAggEditAuthority(state);
  }

  if (state.editState.editBtnList.length >= 6) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.more, EditConstant.more, EditConstant.moreIcon);
    state.editState.editBtnList.insert(4, vm);
  }
  return state;
}

bool _isInUnNormalAggPage(EditPageType pageType) {
  final List<EditPageType> unNormalAggPageList = <EditPageType>[
    EditPageType.camera,
    EditPageType.offline,
    EditPageType.nonNet,
  ];
  return unNormalAggPageList.contains(pageType);
}

EditDeviceCardInfo _getEditDeviceInfo(DeviceCardViewModel vm) {
  return EditDeviceCardInfo(
    id: vm.device.basicInfo.deviceId,
    deviceName: vm.device.basicInfo.deviceName,
    ownUserId: vm.device.basicInfo.ownerId,
    deviceRole: vm.device.basicInfo.deviceRole,
    configType: vm.device.basicInfo.configType,
    groupType: vm.device.basicInfo.deviceGroupType,
    type: vm.deviceCardType,
    supportLargeCard: vm.supportLargeCard,
    isSupportShare: vm.isSupportShare,
    isShareDevice: vm.isSharedDevice,
    deviceIcon: vm.deviceIcon,
    productCode: vm.prodNo,
    noNetDevice: vm.nonNetDevice,
    isRebind: vm.device.basicInfo.isRebind,
    ucUserId: vm.device.basicInfo.ucUserId,
  );
}

EditDeviceCardInfo _getEditDeviceInfoByScene(SceneItemViewModel vm) {
  return EditDeviceCardInfo(
    id: vm.sceneId,
    deviceName: vm.sceneName,
    ownUserId: '',
    deviceRole: '',
    configType: '',
    groupType: '',
    type: DeviceCardType.middleCard,
    supportLargeCard: true,
    isSceneInvalid: vm.isInvalid,
  );
}

void _clearEditState(SmartHomeState state) {
  state.editState.selectedCardsList.clear();
  state.editState.editBtnList.clear();
  state.editState.cardSizeList.clear();
  state.editState.cacheCardList.clear();
  state.editState.selectedSceneCardList.clear();
  state.editState.cacheSceneCardList.clear();
  state.aggregationState.aggregationRoomSelectStatus.clear();
}

bool _isSelectedScene(SmartHomeState state) {
  return state.editState.selectedSceneCardList.isNotEmpty;
}

// 切换大小鉴权
void _checkChangeCardSizeAuthority(SmartHomeState state) {
  if (state.editState.selectedCardsList.isNotEmpty &&
      !_isInUnNormalAggPage(state.editState.pageType)) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.changeSize,
        EditConstant.changeSize,
        EditConstant.changeSizeIcon);
    state.editState.editBtnList.add(vm);
  }
}

// 移到顶部鉴权
void _checkMoveToTopAuthority(SmartHomeState state) {
  if (state.editState.selectedCardsList.isNotEmpty &&
      _isNotFilteredCardList(state) &&
      state.editState.pageType == EditPageType.home) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.moveToTop,
        EditConstant.MoveToTop,
        EditConstant.moveToTopIcon);
    state.editState.editBtnList.add(vm);
  }
}

// 重命名鉴权
void _checkRenameAuthority(SmartHomeState state) {
  if (isSelectAggregationCard(state)) {
    return;
  }

  if (state.editState.selectedCardsList.length != 1) {
    return;
  }
  final EditDeviceCardInfo editDeviceCardInfo =
      state.editState.selectedCardsList[0];
  if (editDeviceCardInfo.isShareDevice) {
    return;
  }

  final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
      ButtonActionType.rename, EditConstant.rename, EditConstant.renameIcon);
  state.editState.editBtnList.add(vm);
}

// 转移房间鉴权
void _checkChangeDevicePositionAuthority(SmartHomeState state) {
  bool isShow = false;
  if (state.editState.selectedCardsList.isNotEmpty) {
    isShow = _checkChangeDevicePositionAuthorityBySelectedDeviceList(
        state.editState.selectedCardsList);
    if (!isShow) {
      return;
    }
  }
  if (isSelectAggregationCard(state)) {
    return;
  }
  if (isShow) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.changeDevicePosition,
        EditConstant.changeDevicePosition,
        EditConstant.changeRoomIcon);
    state.editState.editBtnList.add(vm);
  }
}

// 是否选择了聚合卡片
bool isSelectAggregationCard(SmartHomeState state) {
  return state.editState.selectedCardsList
      .any((EditDeviceCardInfo element) => isDeviceAggregation(element.id));
}

// 是否选择的全部是聚合卡片
bool isAllSelectAreAggregation(SmartHomeState state) {
  return state.editState.selectedCardsList
      .every((EditDeviceCardInfo element) => isDeviceAggregation(element.id));
}

// 关闭聚合鉴权
void _checkAggCloseAuthority(SmartHomeState state) {
  final bool isSingleSelected = state.editState.selectedCardsList.length == 1;
  if (isSingleSelected &&
      isAllSelectAreAggregation(state) &&
      state.editState.pageType == EditPageType.home) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.closeAggregation,
        EditConstant.closeAggregation,
        EditConstant.closeAggregationIcon);
    state.editState.editBtnList.add(vm);
  }
}

// 编辑聚合鉴权
void _checkAggEditAuthority(SmartHomeState state) {
  final bool isSingleSelected = state.editState.selectedCardsList.length == 1;
  if (!isSingleSelected) {
    return;
  }
  if (isSingleSelected &&
      isNormalAggregation(state.editState.selectedCardsList[0].id) &&
      state.editState.pageType == EditPageType.home) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.aggregationSetting,
        EditConstant.aggregationSetting,
        EditConstant.aggregationSettingIcon);
    state.editState.editBtnList.add(vm);
  }
}

// 场景移出鉴权
void _checkSceneRemoveAuthority(SmartHomeState state) {
  if (_isSelectedScene(state) &&
      state.editState.selectedCardsList.isEmpty &&
      state.editState.pageType == EditPageType.home) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.sceneRemove,
        EditConstant.sceneRemove,
        EditConstant.sceneAuthRemoveIcon);
    state.editState.editBtnList.add(vm);
  }
}

// 场景编辑鉴权
void _checkSceneEditAuthority(SmartHomeState state) {
  if (state.editState.selectedSceneCardList.length == 1 &&
      state.editState.selectedSceneCardList[0].isSceneInvalid &&
      state.editState.pageType == EditPageType.home) {
    return;
  }
  if (state.editState.selectedSceneCardList.length == 1 &&
      state.editState.selectedCardsList.isEmpty) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.sceneEdit,
        EditConstant.sceneEdit,
        EditConstant.sceneAuthEditIcon);
    state.editState.editBtnList.add(vm);
  }
}

bool _checkChangeDevicePositionAuthorityBySelectedDeviceList(
    List<EditDeviceCardInfo> list) {
  bool isShow = false;

  for (int i = 0; i < list.length; i++) {
    final EditDeviceCardInfo info = list[i];
    if (!info.isShareDevice) {
      isShow = true;
    } else {
      isShow = false;
      break;
    }
  }
  return isShow;
}

// 判断列表是否是筛选过的
bool _isNotFilteredCardList(SmartHomeState state) {
  final List<String> _filtered = <String>[
    state.deviceState.selectedRoom,
    state.deviceState.selectedFloor,
    state.deviceState.selectedDeviceCategory
  ];
  return _filtered.every((String e) => e == '全部');
}

// editBtnList添加分享vm
// 合并为isSupportShare字段（1.选中的是单设备卡片；2. 用户是绑定用户 3. 不是型号缺失状态 4. 不是子设备（组设备） 5. 不是非网器 6. 不是三方设备）
// 7. 选中设备<=10（>10隐藏）
void _editBtnListAddShare(SmartHomeState state) {
  final List<EditDeviceCardInfo> list = state.editState.selectedCardsList;
  if (list.length > 10 ||
      state.editState.pageType == EditPageType.offline ||
      state.editState.pageType == EditPageType.nonNet) {
    return;
  }

  bool isShow = false;

  for (int i = 0; i < list.length; i++) {
    final EditDeviceCardInfo info = list[i];
    if (info.isSupportShare) {
      isShow = true;
    } else {
      isShow = false;
      break;
    }
  }

  if (!isShow) {
    return;
  }
  final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
      ButtonActionType.share, EditConstant.share, EditConstant.shareIcon);
  state.editState.editBtnList.add(vm);
}

// 转移家庭鉴权
void _checkChangeDeviceFamilyAuthority(SmartHomeState state) {
  bool isShow = false;
  if (isSelectAggregationCard(state)) {
    return;
  }
  if (_isInUnNormalAggPage(state.editState.pageType)) {
    return;
  }
  final Map<String, FamilyModel>? familyMap = Family.getFamilyMapSync();
  if (familyMap == null || (familyMap.length <= 1)) {
    return;
  }
  if (state.editState.selectedCardsList.isNotEmpty) {
    isShow = _checkChangeDeviceFamilyAuthorityBySelectedDeviceList(
        state.editState.selectedCardsList);
    if (!isShow) {
      return;
    }
  }
  if (isShow) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.changeDeviceFamily,
        EditConstant.changeDeviceFamily,
        EditConstant.changeDeviceFamilyIcon);
    state.editState.editBtnList.add(vm);
  }
}

bool _checkChangeDeviceFamilyAuthorityBySelectedDeviceList(
    List<EditDeviceCardInfo> list) {
  final String oauthData = User.getOauthDataSync()?.uhome_user_id ?? '';
  for (int i = 0; i < list.length; i++) {
    final EditDeviceCardInfo info = list[i];
    if (info.isShareDevice) {
      return false;
    }
    if (info.deviceRole == deviceRoleSub) {
      return false;
    }
    if (info.noNetDevice && info.ownUserId != oauthData) {
      return false;
    }
  }
  return true;
}

// 删除鉴权
void _checkDeleteDeviceAuthority(SmartHomeState state) {
  bool isShow = false;
  if (isSelectAggregationCard(state)) {
    return;
  }
  if (state.editState.selectedCardsList.isNotEmpty) {
    isShow = _checkDeleteDeviceAuthorityBySelectedDeviceList(
        state.editState.selectedCardsList);
    if (!isShow) {
      return;
    }
  }
  if (isShow) {
    final EditBtnInfoViewModel vm = EditBtnInfoViewModel(
        ButtonActionType.deleteDevice,
        EditConstant.deleteDevice,
        EditConstant.deleteDeviceIcon);
    state.editState.editBtnList.add(vm);
  }
}

bool _checkDeleteDeviceAuthorityBySelectedDeviceList(
    List<EditDeviceCardInfo> list) {
  bool canDelete = false;
  final String uHomeUserId = User.getOauthDataSync()?.uhome_user_id ?? '';
  for (int i = 0; i < list.length; i++) {
    final EditDeviceCardInfo info = list[i];

    if (info.isShareDevice &&
        info.groupType != groupDevice &&
        info.deviceRole != deviceRoleSub) {
      /// 1. 共享设备（非组设备且非子设备）可以删除
      canDelete = true;
    } else if (!info.isRebind && info.ownUserId == uHomeUserId) {
      /// 2. 高安全 && 当前为绑定用户
      canDelete = true;
    } else if (info.noNetDevice && info.ownUserId == uHomeUserId) {
      /// 3. 非网器 && 当前为绑定用户当前为绑定用户
      canDelete = true;
    } else if (info.isRebind &&
        !info.noNetDevice &&
        info.configType != thirdPartyDevice) {
      /// 4. 非高安全 && 网器 && 非第三方设备可以删除
      canDelete = true;
    } else {
      canDelete = false;
      break;
    }
  }
  return canDelete;
}

SmartHomeState _updateSelectAllStatus(
    SmartHomeState state, UpdateRoomSelectAllStatusAction action) {
  final AggregationRoomSelectViewModel selectVM =
      action.aggregationRoomSelectVM;

  final List<String>? deviceList = _getAggregationDeviceList(
      state, selectVM.aggregationId, selectVM.roomInfo);
  if (deviceList == null) {
    return state;
  }
  _updateRoomSelectStatus(state: state, aggregationRoomSelectVM: selectVM);
  _handleDeviceSelection(state, deviceList, selectVM.isSelectAll);
  _checkEditAuthority(state);
  return state;
}

SmartHomeState _displayInHomePageStatusChanged(
  SmartHomeState state,
  DisplayInHomePageUpdateRequestedAction action,
) {
  final List<String> deviceIds = action.deviceIds;
  final int displayStatus = action.displayStatus;

  for (final String id in deviceIds) {
    final DeviceCardViewModel? cardVm = _getDeviceCardVM(state, id);
    if (cardVm != null) {
      cardVm.device.basicInfo.displayedInHomePage = displayStatus;
    }
  }

  return state;
}

DeviceCardViewModel? _getDeviceCardVM(
  SmartHomeState state,
  String id,
) {
  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[id];
  if (viewModel is DeviceCardViewModel) {
    return viewModel;
  }
  return null;
}

void _handleDeviceSelection(
  SmartHomeState state,
  List<String> deviceList,
  bool isSelectAll,
) {
  if (isSelectAll) {
    _selectDevicesAndAddToSelectedList(state, deviceList);
  } else {
    _unselectDevicesAndRemoveFromSelectedList(state, deviceList);
  }
}

void _unselectDevicesAndRemoveFromSelectedList(
  SmartHomeState state,
  List<String> removeDeviceList,
) {
  final Set<String> deviceIds = removeDeviceList.toSet();
  for (final String deviceId in deviceIds) {
    final CardBaseViewModel? viewModel =
        state.deviceState.allCardViewModelMap[deviceId];
    if (viewModel is DeviceCardViewModel) {
      viewModel.isSelected = false;
    }
  }

  state.editState.selectedCardsList
      .removeWhere((EditDeviceCardInfo info) => deviceIds.contains(info.id));
}

void _selectDevicesAndAddToSelectedList(
  SmartHomeState state,
  List<String> addDeviceList,
) {
  final Set<String> deviceSet = addDeviceList.toSet();
  final List<EditDeviceCardInfo> selectedCards = <EditDeviceCardInfo>[];
  for (final String deviceId in deviceSet) {
    final CardBaseViewModel? viewModel =
        state.deviceState.allCardViewModelMap[deviceId];
    if (viewModel is DeviceCardViewModel && !viewModel.isSelected) {
      viewModel.isSelected = true;
      selectedCards.add(_getEditDeviceInfo(viewModel));
    }
  }
  state.editState.selectedCardsList.addAll(selectedCards);
}

void _updateRoomSelectStatus({
  required SmartHomeState state,
  required AggregationRoomSelectViewModel aggregationRoomSelectVM,
}) {
  // 获取当前聚合卡片的 RoomSelectStatus，若不存在则创建一个新的
  final RoomSelectStatus currentStatus = state.aggregationState
          .aggregationRoomSelectStatus[aggregationRoomSelectVM.aggregationId] ??
      RoomSelectStatus();

  // 更新指定房间的状态
  final Map<String, bool> updatedRoomStatus = <String, bool>{
    ...currentStatus.roomSelectStatus,
    aggregationRoomSelectVM.roomInfo.roomId:
        aggregationRoomSelectVM.isSelectAll,
  };

  // 构建新的 aggregationRoomSelectStatus 映射
  final Map<String, RoomSelectStatus> newRoomSelectStatus =
      <String, RoomSelectStatus>{
    ...state.aggregationState.aggregationRoomSelectStatus,
    aggregationRoomSelectVM.aggregationId:
        RoomSelectStatus(roomSelectStatus: updatedRoomStatus),
  };

  state.aggregationState.aggregationRoomSelectStatus = newRoomSelectStatus;
}

List<String>? _getAggregationDeviceList(
    SmartHomeState state, String aggregationId, SmartHomeRoomInfo roomInfo) {
  if (aggregationId.isEmpty || roomInfo.roomId.isEmpty) {
    return null;
  }
  final CardBaseViewModel? cardBaseViewModel =
      state.deviceState.allCardViewModelMap[aggregationId];

  if (cardBaseViewModel is! AggregationBaseViewModel) {
    return null;
  }

  final AggregationDevicesByRoomViewModel? aggregationModel =
      cardBaseViewModel.devicesByRoom[roomInfo];

  if (aggregationModel == null || aggregationModel.deviceList.isEmpty) {
    return null;
  }

  return aggregationModel.deviceList;
}

// 将编辑模式对象转换为卡片VM
List<DeviceCardViewModel?> _convertToViewModelList(
    List<EditDeviceCardInfo> list, SmartHomeState state) {
  return list.map((EditDeviceCardInfo e) {
    final CardBaseViewModel? viewModel =
        state.deviceState.allCardViewModelMap[e.id];
    if (viewModel is DeviceCardViewModel) {
      return viewModel;
    }
    return null;
  }).toList();
}

// 编辑模式下的在首页展示按钮
void _editBtnListHomeShow(SmartHomeState state) {
  final List<EditDeviceCardInfo> list = state.editState.selectedCardsList;
  if (list.isEmpty) {
    return;
  }

  final List<DeviceCardViewModel?> viewModels =
      _convertToViewModelList(list, state);
  // 所有选择的设备是否都支持首页展示
  final bool support = viewModels.every((DeviceCardViewModel? element) =>
      (element?.supportDisplayedInHomePage ?? false));
  // 有首页不展示的则显示在“首页展示”
  final bool anyHide = viewModels.any((DeviceCardViewModel? element) =>
      (element?.supportDisplayedInHomePage ?? false) &&
      !(element?.displayedInHomePage ?? false));

  if (!(support && anyHide)) {
    return;
  }

  _addEditButton(state, ButtonActionType.displayedInHomePage);
}

// 编辑模式下的首页隐藏按钮
void _editBtnListHomeHide(SmartHomeState state) {
  final List<EditDeviceCardInfo> list = state.editState.selectedCardsList;
  if (list.isEmpty) {
    return;
  }

  // 所有的都支持聚合并且都在首页展示则显示“首页隐藏”
  final bool isShow = _convertToViewModelList(list, state).every(
      (DeviceCardViewModel? element) =>
          (element?.supportDisplayedInHomePage ?? false) &&
          (element?.displayedInHomePage ?? false));

  if (!isShow) {
    return;
  }

  _addEditButton(state, ButtonActionType.hideInHomePage);
}

// 封装统一的添加编辑按钮VM方法
void _addEditButton(SmartHomeState state, ButtonActionType type) {
  EditBtnInfoViewModel vm;
  switch (type) {
    case ButtonActionType.displayedInHomePage:
      vm = EditBtnInfoViewModel(
          ButtonActionType.displayedInHomePage,
          EditConstant.displayedInHomePage,
          EditConstant.displayedInHomePageIcon);
    case ButtonActionType.hideInHomePage:
      vm = EditBtnInfoViewModel(
        ButtonActionType.hideInHomePage,
        EditConstant.hideInHomePage,
        EditConstant.hideInHomePageIcon,
      );
    default:
      return;
  }

  state.editState.editBtnList.add(vm);
}
