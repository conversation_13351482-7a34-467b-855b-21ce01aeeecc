import 'package:redux/redux.dart';
import '../../device/device_view_model/camera_view_model.dart';
import '../../store/smart_home_state.dart';
import 'edit_action.dart';

class EditMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    if (action is EnterEditStateByCardAction ||
        action is EnterEditStateBySceneCardAction) {
      ///在这里关闭正在播放的摄像头卡片.
      CameraLiveCoordinator.instance.stopAllPlayer();
    }
    next(action);
  }
}
