import 'package:smart_home/edit/edit_card_base_info_model.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';

import '../edit_bottom_viewmodel.dart';
import '../edit_presenter/edit_presenter_manager.dart';

class EditState {
  EditPageType pageType = EditPageType.home;
  // 已选择的卡片列表
  List<EditDeviceCardInfo> selectedCardsList = <EditDeviceCardInfo>[];

  // 已选择的场景卡片列表
  List<EditDeviceCardInfo> selectedSceneCardList = <EditDeviceCardInfo>[];

  // 功能按钮列表
  List<EditBtnInfoViewModel> editBtnList = <EditBtnInfoViewModel>[];

  // 缓存卡片排序（deviceId）和尺寸信息
  List<CacheCardModel> cacheCardList = <CacheCardModel>[];

  /// 卡片更新大中小标识
  int resizeMark = 1;

  // 缓存场景卡片排序
  List<SceneItemViewModel> cacheSceneCardList = <SceneItemViewModel>[];

  // 切换大小卡片尺寸列表
  List<CardSizeOptionViewModel> cardSizeList = <CardSizeOptionViewModel>[];

  // 恢复网络时判断是否需要重新保存排序和尺寸数据
  // 用于实现断网时退出编辑，排序或尺寸改变未上传云端，网络恢复后主动保存一次编辑数据的功能
  bool isNeedSaveEditedCardListData = false;

  // 用于标识是否正在拖动调整大小
  // 以实现 拖拽调整卡片大小时，其他设备卡片透明度降低，松手后其他卡片透明度恢复原状
  bool dragResizing = false;


  @override
  String toString() {
    return 'EditState{selectedCardsList: $selectedCardsList,'
        ' cardSizeList: $cardSizeList}'
        ' editBtnList: $editBtnList}';
  }
}
