import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../common/smart_home_util.dart';
import '../../widget_common/card_text_style.dart';
import 'edit_manager.dart';

class EditCommonDialog {

  static void show({
    required BuildContext context,
    ActionConfig actionConfig = const ActionConfig.none(),
    void Function(BuildContext _context)? cancelCallback,
    bool barrierDismissible = false, // 是否点击蒙层退出
    String dialogKeyForSystemBack = '',
    String title = '', // 标题
    String contentText = '', // 内容
    Widget? contentWidget, // 自定义内容Widget
    bool isNeedCloseAfterDeviceListUpdate = true, // 是否需要设备列表变化时自动关闭弹框
  }) {
    showDialog<void>(
      barrierDismissible: barrierDismissible,
      context: context,
      barrierColor: AppSemanticColors.container.cover,
      builder: (BuildContext _context) {
        if (isNeedCloseAfterDeviceListUpdate) {
          SmartHomeEditManager.setEditDialogContext(_context);
        }
        return Dialog(
          elevation: 0,
          insetPadding: const EdgeInsets.symmetric(horizontal: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
          backgroundColor: AppSemanticColors.container.window,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(32),
              color: AppSemanticColors.container.window,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(top: 20, left: 16, right: 16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      if (title.isNotEmpty) ...<Widget>[
                        Text(
                          title,
                          style: TextStyle(
                              fontSize: 17,
                              color: AppSemanticColors.item.primary,
                              fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(
                          height: 12,
                        )
                      ],
                      contentWidget ?? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: Text(
                          contentText,
                          style: TextStyle(
                              fontSize: 14,
                              fontFamilyFallback: fontFamilyFallback(),
                              color: AppSemanticColors.item.secondary),
                        ),
                      ),
                    ],
                  ),
                ),
                if (actionConfig.actions.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: DialogComponents.buildBottomActions(
                        actionConfig.actions,
                        actionDirection: actionConfig.actionDirection),
                  ),
              ],
            ),
          ),
        );
      }
    ).then((Object? value) {
      SmartHomeEditManager.closeEditDialog();
      if (dialogKeyForSystemBack.isNotEmpty) {
        InterceptSystemBackUtil.cancelInterceptSystemBack(dialogKeyForSystemBack);
      }
    });
  }
}
