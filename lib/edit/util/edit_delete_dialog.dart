import 'package:flutter/material.dart';
import 'package:smart_home/widget_common/card_text_style.dart';
import '../../common/smart_home_util.dart';
import '../../device/component_widget/button_control_animation.dart';
import 'edit_manager.dart';

class EditDeleteDialog {
  static void show({
    required BuildContext context,
    required void Function(BuildContext _context) confirmCallback,
    void Function(BuildContext _context)? cancelCallback,
    bool barrierDismissible = false, // 是否点击蒙层退出
    bool confirmClose = true, // 点击确认后是否需要自动关闭弹框
    String dialogKeyForSystemBack = '',
    String title = '', // 标题
    String contentText = '', // 内容
    TextAlign contentTextAlign = TextAlign.center,
    bool hideContent = false,
    String cancelBtnText = '取消', // 取消按钮文案
    String confirmBtnText = '删除', // 确认按钮文案
    Color cancelBtnTextColor = const Color(0xff111111), // 取消按钮文案颜色
    Color confirmBtnTextColor = const Color(0xffED2856), // 确认按钮文案颜色
    Color cancelBtnBgColor = const Color(0xFFF5F5F5), // 取消按钮背景颜色
    Color confirmBtnBgColor = const Color(0xFFFEE4EA), // 确认按钮背景颜色
    Widget? contentWidget, // 自定义内容Widget
    bool isNeedCloseAfterDeviceListUpdate = true, // 是否需要设备列表变化时自动关闭弹框
  }) {
    showDialog<void>(
        barrierDismissible: barrierDismissible,
        context: context,
        barrierColor: const Color(0xff000000).withOpacity(0.50),
        builder: (BuildContext _context) {
          if (isNeedCloseAfterDeviceListUpdate) {
            SmartHomeEditManager.setEditDialogContext(_context);
          }
          return SimpleDialog(
            elevation: 0,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20))),
            contentPadding: EdgeInsets.zero,
            title: Text(
              title,
              style: TextStyle(
                fontSize: 17,
                color: const Color(0xff111111),
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
              ),
              textAlign: TextAlign.center,
            ),
            titlePadding: EdgeInsets.only(
                top: title.isEmpty ? 0 : 20, left: 16, right: 16),
            children: <Widget>[
              Container(
                padding: EdgeInsets.only(
                    left: 20, right: 20, top: 12, bottom: hideContent ? 0 : 20),
                alignment: Alignment.topCenter,
                child: contentWidget ??
                    Text(
                      contentText,
                      textAlign: contentTextAlign,
                      style: TextStyle(
                        fontSize: 14,
                        color: title.isEmpty
                            ? const Color(0xFF111111)
                            : const Color(0xFF666666),
                      ),
                    ),
              ),
              Container(
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  child: Row(children: <Widget>[
                    Expanded(
                      child: _buildDialogBottomBtnWidget(
                          cancelBtnText, cancelBtnTextColor, cancelBtnBgColor,
                          () {
                        _closeDialog(
                            _context, isNeedCloseAfterDeviceListUpdate);
                        if (cancelCallback != null) {
                          cancelCallback(_context);
                        }
                      }),
                    ),
                    const SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: _buildDialogBottomBtnWidget(confirmBtnText,
                          confirmBtnTextColor, confirmBtnBgColor, () {
                        if (confirmClose) {
                          _closeDialog(
                              _context, isNeedCloseAfterDeviceListUpdate);
                        }
                        confirmCallback(_context);
                      }),
                    ),
                  ]))
            ],
          );
        }).then((Object? value) {
      SmartHomeEditManager.closeEditDialog();
      if (dialogKeyForSystemBack.isNotEmpty) {
        InterceptSystemBackUtil.cancelInterceptSystemBack(
            dialogKeyForSystemBack);
      }
    });
  }

  static GestureDetector _buildDialogBottomBtnWidget(String btnText,
      Color textColor, Color backgroundColor, void Function() clickCallBack) {
    return GestureDetector(
      child: Container(
        width: double.infinity,
        height: 44,
        alignment: Alignment.center,
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Text(
          btnText,
          style: TextStyle(
            height: 1,
            fontSize: 16,
            color: textColor,
            fontWeight: FontWeight.w500,
            fontFamilyFallback: fontFamilyFallback(),
          ),
          textAlign: TextAlign.center,
        ),
      ),
      onTap: debounce(() {
        clickCallBack();
      }, const Duration(milliseconds: 1000)) as void Function()?,
    );
  }

  static void _closeDialog(
      BuildContext context, bool isNeedCloseAfterDeviceListUpdate) {
    if (isNeedCloseAfterDeviceListUpdate) {
      SmartHomeEditManager.closeEditDialog();
    } else {
      final NavigatorState navigator = Navigator.of(context);
      if (navigator.canPop()) {
        navigator.pop();
      }
    }
  }
}
