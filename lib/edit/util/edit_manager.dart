import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/resize_device_card/resize_overlay.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:upsystem/upsystem.dart';

import '../../common/constant_gio.dart';
import '../../common/smart_home_util.dart';
import '../edit_presenter/edit_presenter_manager.dart';
import '../store/edit_reducer.dart';
import 'edit_constant.dart';

class SmartHomeEditInterceptor {
  SmartHomeEditInterceptor._internal();

  static final SmartHomeEditInterceptor _instance =
  SmartHomeEditInterceptor._internal();

  static SmartHomeEditInterceptor get instance => _instance;

  final List<VoidCallback> _list = <VoidCallback>[];

  void addEditInterceptor(VoidCallback vcb) {
    if (!_list.contains(vcb)) {
      _list.add(vcb);
    }
  }

  void removeEditInterceptor(VoidCallback vcb) {
    if (_list.contains(vcb)) {
      _list.remove(vcb);
    }
  }

  bool haveEditInterceptor() {
    if (_list.isEmpty) {
      return false;
    }
    final VoidCallback vcb = _list.removeLast();
    vcb();
    return true;
  }

  bool get show => _list.isEmpty;
}

/// 页面编辑-工具类
class SmartHomeEditManager {
  static const String tag = 'SmartHomeEditManager';
  static const String pageName = '${SmartHomeConstant.package}/editPage';
  static BuildContext? editDialogContext;

  static const String deviceCard = '单设备卡片';
  static const String aggregationCard = '聚合卡片';
  static int editStartTimestampMs = 0;

  /// 退出编辑
  static void dismissEditStatus() {
    closeEditDialog();
    // 移除物理返回拦截监听
    InterceptSystemBackUtil.cancelInterceptSystemBack(pageName);

    final int timeLength =
        DateTime.now().millisecondsSinceEpoch - editStartTimestampMs;
    gioTrack(GioConst.editStayTime, <String, String>{
      EditConstant.timeLength: timeLength.toString(),
    });
  }

  /// 进入编辑模式初始化
  static void enterEditInit(BuildContext context) {
    editStartTimestampMs = DateTime.now().millisecondsSinceEpoch;
    gioTrack(GioConst.enterEditStatue, <String, String>{
      'product_type': getProDuctTypeForGio(),
      'source': getSourceForGio(),
    });

    // 注册物理返回拦截监听
    InterceptSystemBackUtil.interceptSystemBack(
        pageName: pageName,
        callback: () {
          final bool flag = SmartHomeEditManager.closeEditDialog();

          if (flag) {
            return;
          }

          if (SmartHomeEditInterceptor.instance.haveEditInterceptor()) {
            return;
          }

          // 移除拖动调整大小涂层
          removeOverlay();
          // 在没有弹框打开的情况下才执行取消逻辑
          if (editDialogContext == null) {
            EditPresenterManager.onClickDone();
          }
        });
  }

  static void setEditDialogContext(BuildContext context) {
    editDialogContext = context;
  }

  static bool closeEditDialog() {
    if (editDialogContext != null && editDialogContext!.mounted) {
      final NavigatorState navigator = Navigator.of(editDialogContext!);
      if (navigator.canPop()) {
        navigator.pop();
      }
      editDialogContext = null;
      return true;
    }
    return false;
  }

  static String getProDuctTypeForGio() {
    String _productType = '';
    if (isSelectAggregationCard(smartHomeStore.state)) {
      if (isAllSelectAreAggregation(smartHomeStore.state)) {
        _productType = SmartHomeEditManager.aggregationCard;
      } else {
        _productType =
        '${SmartHomeEditManager.deviceCard}、${SmartHomeEditManager
            .aggregationCard}';
      }
    } else {
      _productType = SmartHomeEditManager.deviceCard;
    }
    return _productType;
  }

  static String getSourceForGio() {
    return EditPresenterManager.currentPageForEdit == EditPageType.home
        ? '首页'
        : '聚合二级页';
  }
}
