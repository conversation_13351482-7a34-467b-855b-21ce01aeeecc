class EditConstant {
  static const String done = '完成';
  static const String save = '保存';
  static const String editNotSupport = '请选择卡片后进行编辑';
  static const String rename = '重命名';
  static const String changeSize = '切换大小';
  static const String MoveToTop = '移到顶部';
  static const String changeDevicePosition = '更改房间';
  static const String share = '共享';
  static const String changeDeviceFamily = '转移家庭';
  static const String deleteDevice = '删除';
  static const String more = '更多';
  static const String closeAggregation = '关闭聚合';
  static const String aggregationSetting = '编辑';
  static const String sceneRemove = '移出';
  static const String sceneEdit = '编辑';
  static const String sceneEditNotSupport = '选中项无法批量编辑';
  static const String saveContentText = '未保存的变化将会丢失，是否放弃？';
  static const String sceneAuthRemoveIcon =
      'assets/images/edit/scene_auth_remove.webp';
  static const String sceneAuthEditIcon =
      'assets/images/edit/scene_auth_edit.webp';

  static const String renameIcon = 'assets/images/edit/rename.webp';
  static const String changeSizeIcon = 'assets/images/edit/change_size.webp';
  static const String moveToTopIcon = 'assets/images/edit/move_to_top.webp';
  static const String shrinkCardIcon =
      'assets/images/edit/shrink_card_icon.webp';
  static const String changeDevicePositionIcon =
      'assets/images/edit/change_position.webp';
  static const String changeRoomIcon =
      'assets/images/edit/change_room_icon.webp';
  static const String shareIcon = 'assets/images/edit/share.webp';
  static const String changeDeviceFamilyIcon =
      'assets/images/edit/change_family.webp';
  static const String deleteDeviceIcon =
      'assets/images/edit/delete_device.webp';
  static const String closeAggregationIcon =
      'assets/images/edit/close_aggregation.webp';
  static const String aggregationSettingIcon =
      'assets/images/edit/aggregation_setting.webp';
  static const String moreIcon = 'assets/images/edit/more.webp';
  static const String closeIcon = 'assets/images/edit/dialog_close.png';
  static const String quickListWithoutCardLightBg =
      'assets/images/edit/quick_list_without_card_light.webp';
  static const String quickListWithoutCardDarkBg =
      'assets/images/edit/quick_list_without_card_dark.webp';
  static const String addLightIcon = 'assets/images/edit/add_light.webp';
  static const String addDarkIcon = 'assets/images/edit/add_dark.webp';
  static const String editLightIcon = 'assets/images/edit/edit_light.webp';
  static const String editDarkIcon = 'assets/images/edit/edit_dark.webp';
  static const String checkMarkIcon = 'assets/images/edit/check_mark.webp';
  static const String editStatusArrowPath =
      'assets/images/edit/edit_status_arrow.webp';
  static const String cancel = '取消';
  static const String back = '返回';

  // 聚合优化-首页展示/隐藏
  static const String displayedInHomePage = '首页展示';
  static const String hideInHomePage = '首页隐藏';
  static const String _iconBasePath = 'assets/images/edit/';
  static const String displayedInHomePageIcon =
      '${_iconBasePath}display_in_home_page.webp';
  static const String hideInHomePageIcon =
      '${_iconBasePath}hide_in_home_page.webp';

  // 保存卡片
  static const String saveFail = '保存失败，请稍后再试';
  static const String netError = '网络异常，请稍候再试';
  static const String reTry = '请稍候再试';

  // 放大卡片
  static const String enlargeCardSuccess = '添加卡片成功';
  static const String enlargeCardFail = '添加卡片失败';

  // 缩小卡片
  static const String shrinkCardSuccess = '移出卡片成功';
  static const String shrinkCardFail = '移出卡片失败';

  // 删除设备点击弹框
  static const String deleteTitle = '确认删除所选设备？';
  static const String deleteConfirmContent = '删除后，无法在APP查看和使用设备';
  static const String deletePrimaryContent = '删除后，无法在APP查看和使用该设备及其附属的子设备';
  static const String goOn = '继续';

  static const String next = '下一步';
  static const String deleteDeviceSuccess = '删除成功';
  static const String deleteDeviceFail = '删除失败，请稍后重试';

  // 转移家庭
  static const String changeFamilySuccess = '设备转移家庭成功';
  static const String changeFamilyFail = '设备转移失败，请稍后再试';
  static const String changeFamilyPayText =
      '在使用以下设备过程中，开通过自动扣费服务或签署支付协议，请联系该用户对设备进行操作后再试：';
  static const String changeFamilySafeText = '以下设备涉及隐私安全，因此需要联系绑定用户进行操作：';
  static const String changeFamilySelfDeviceText =
      '以下设备的部分APP功能涉及身份验证，因此需要联系绑定用户进行操作：';

  // 转移房间
  static const String changePositionSuccess = '设备转移房间成功';
  static const String changePositionFail = '转移房间失败，请稍后再试';

  // 重命名
  static const String renameTitle = '修改设备名称';
  static const String renameTips = '仅限于20字以内的中英文、数字';
  static const String renameError70014 = '70014';
  static const String renameError70014Text = '名称不能为空';
  static const String renameErrorB00007 = 'B00007';
  static const String renameErrorB00007Text = '仅支持中文、英文和数字';
  static const String renameErrorB00009 = 'B00009';
  static const String renameErrorB00009Text = '名称包含敏感词，请重新命名';
  static const String renameErrorB00011 = 'B00011';
  static const String renameErrorB00011Text = '不能使用其他家电名称命名';
  static const String renameErrorB00010 = 'B00010';
  static const String renameErrorB00010Text = '不能使用房间名称命名';
  static const String renameErrorB00014 = 'B00014';
  static const String renameErrorB00014Text = '不能使用楼层名称命名';
  static const String renameSuccessCode = '00000';
  static const String renameSuccessText = '设备名称修改成功';
  static const String renameOverLimit = '设备名称不能超过20个字';
  static const String renameFail = '名称修改失败，请稍后再试';
  static const String renameContinue = '继续修改';
  static const String renamePlaceHolder = '请输入设备名称';

  // gio打点中使用
  static const String resultSuccess = '成功';
  static const String resultFullFail = '失败';
  static const String resultPartialFail = '部分失败';
  static const String confirm = '确定';
  static const String buttonName = 'button_name';
  static const String name = 'name';
  static const String result = 'result';
  static const String timeLength = 'time_length';
}
