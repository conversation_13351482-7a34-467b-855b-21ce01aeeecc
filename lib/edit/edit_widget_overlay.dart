import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/edit/edit_controller.dart';
import 'package:smart_home/edit/edit_widget.dart';
import 'package:smart_home/edit/util/edit_manager.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

class EditWidgetOverlay extends StatefulWidget {
  final bool showEditWidget;
  const EditWidgetOverlay({super.key, this.enableAnimation = true,this.showEditWidget = false});
  final bool enableAnimation;

  @override
  State<EditWidgetOverlay> createState() => _EditWidgetOverlayState();
}

class _EditWidgetOverlayState extends State<EditWidgetOverlay> {
  late final EditController _editController = EditController();

  @override
  Widget build(BuildContext context) {
    return StoreProvider<SmartHomeState>(
        store: smartHomeStore,
        child: StoreConnector<SmartHomeState, bool>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return store.state.isEditState;
          },
          builder: (BuildContext context, bool showEditWidget) {
            if (showEditWidget && !_editController.isShowing) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _editController.show();
                SmartHomeEditManager.enterEditInit(context);
              });
            } else if (!showEditWidget && _editController.isShowing) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _editController.hide();
                SmartHomeEditManager.dismissEditStatus();
              });
            }
            return OverlayPortal(
              controller: _editController.controller,
              overlayChildBuilder: (BuildContext context) {
                return EditWidget(
                  _editController,
                  widget.enableAnimation,
                  widget.showEditWidget,
                );
              },
            );
          },
        ));
  }
}

double getEditWidgetBottomHeight(BuildContext context) {
  return MediaQueryData.fromView(View.of(context)).viewPadding.bottom +
      bottomWidgetOriginHeight;
}

mixin EditOverlayBackObserver<T extends StatefulWidget> on State<T> {
  VoidCallback? _editInterceptor;

  @override
  void initState() {
    _editInterceptor ??= () {
      if (mounted) {
        onEditBackPress();
      }
    };
    if (_editInterceptor != null) {
      SmartHomeEditInterceptor.instance.addEditInterceptor(_editInterceptor!);
    }
    super.initState();
  }

  @override
  void dispose() {
    if (_editInterceptor != null) {
      SmartHomeEditInterceptor.instance.removeEditInterceptor(_editInterceptor!);
    }
    super.dispose();
  }

  void onEditBackPress();
}
