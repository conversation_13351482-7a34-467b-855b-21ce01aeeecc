import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:smart_home/edit/store/edit_selectors.dart';
import 'package:smart_home/edit/util/edit_constant.dart';
import 'package:smart_home/edit/util/edit_manager.dart';

import '../common/constant.dart';
import '../common/constant_gio.dart';
import '../device/component_widget/button_control_animation.dart';
import '../store/smart_home_state.dart';
import '../store/smart_home_store.dart';
import '../widget_common/card_text_style.dart';
import 'edit_bottom_viewmodel.dart';
import 'edit_controller.dart';
import 'edit_header_viewmodel.dart';
import 'edit_presenter/edit_presenter_manager.dart';

const double bottomWidgetOriginHeight = 69;

class EditWidget extends StatefulWidget {
  const EditWidget(
      this.editController, this.enableAnimation, this.isShowEditWidget,
      {super.key});

  final EditController editController;

  final bool enableAnimation;
  final bool isShowEditWidget;

  @override
  State<EditWidget> createState() => _EditWidgetState();
}

class _EditWidgetState extends State<EditWidget>
    with SingleTickerProviderStateMixin {
  final GlobalKey containerKey = GlobalKey();
  AnimationController? _animationController;
  Animation<double>? _animation;

  @override
  void initState() {
    _dealAnimation();
    super.initState();
  }

  void _dealAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
        parent: _animationController!, curve: Curves.easeInOut));

    widget.editController.animationController = _animationController;

    if (!SmartHomeEditInterceptor.instance.show && widget.enableAnimation) {
      _animationController?.value = 1.0;
    }
    if (!widget.enableAnimation && widget.isShowEditWidget) {
      _animationController?.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController?.dispose();
    widget.editController.animationController = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double _topPadding =
        MediaQueryData.fromView(View.of(context)).viewPadding.top;
    final double _bottomPadding =
        MediaQueryData.fromView(View.of(context)).viewPadding.bottom;
    return StoreProvider<SmartHomeState>(
      store: smartHomeStore,
      child: Stack(
        children: <Widget>[
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            child: AnimatedBuilder(
                animation: _animationController!,
                builder: (BuildContext context, Widget? child) {
                  return SizeTransition(
                      sizeFactor: _animation!,
                      child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, -1), // 从顶部开始
                            end: Offset.zero,
                          ).animate(_animation!),
                          child: _buildEditHeaderWidget(_topPadding)));
                }),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: AnimatedBuilder(
                animation: _animationController!,
                builder: (BuildContext context, Widget? child) {
                  return SizeTransition(
                      sizeFactor: _animation!,
                      child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, 1), // 从底部开始
                            end: Offset.zero,
                          ).animate(_animation!),
                          child: _buildEditBottomWidget(_bottomPadding)));
                }),
          ),
        ],
      ),
    );
  }

  Widget _buildEditHeaderWidget(double tapPadding) {
    return Material(
      color: Colors.transparent,
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(left: 12, right: 12, top: tapPadding),
        height: 44 + tapPadding,
        child: StoreConnector<SmartHomeState, EditHeaderViewModel>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            return EditHeaderViewModel(
              store.state.editState.selectedCardsList.length +
                  store.state.editState.selectedSceneCardList.length,
              smartHomeStore.state.editState.pageType != EditPageType.home,
            );
          },
          builder: (BuildContext context, EditHeaderViewModel vm) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                _leftBtnWidget(vm),
                Text(
                  vm.selectedCount == 0 ? '请选择' : '已选${vm.selectedCount}个',
                  style: TextStyle(
                    fontSize: 17,
                    color: AppSemanticColors.item.primary,
                    fontFamilyFallback: fontFamilyFallback(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    gioTrack(GioConst.doneClick, <String, String>{
                      'source': SmartHomeEditManager.getSourceForGio(),
                    });
                    EditPresenterManager.onClickDone();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    width: 64,
                    child: Text(
                      EditConstant.save,
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 17,
                        color: AppSemanticColors.item.information.primary,
                        fontFamilyFallback: fontFamilyFallback(),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _leftBtnWidget(EditHeaderViewModel vm) {
    if (vm.backVisible) {
      return _buildBackButton();
    } else {
      return _cancelWidget(context);
    }
  }

  Widget _buildBackButton() {
    return Container(
      width: 64,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: GestureDetector(
        onTap: _onBackButtonPressed,
        child: Text(
          EditConstant.back,
          style: TextStyle(
              fontSize: 17,
              fontFamilyFallback: fontFamilyFallback(),
              color: AppSemanticColors.item.primary),
        ),
      ),
    );
  }

  void _onBackButtonPressed() {
    Navigator.pop(context);
  }

  Widget _cancelWidget(BuildContext context) {
    return StoreConnector<SmartHomeState, bool>(
      distinct: true,
      converter: (Store<SmartHomeState> store) =>
          EditSelectors.showCancelSelector(store.state),
      builder: (BuildContext context, bool isChange) {
        return Container(
          width: 64,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: GestureDetector(
            onTap: () {
              if (isChange) {
                EditPresenterManager.onCancel(context);
              }
            },
            child: Text(
              EditConstant.cancel,
              style: TextStyle(
                  fontSize: 17,
                  fontFamilyFallback: fontFamilyFallback(),
                  color: isChange
                      ? AppSemanticColors.item.primary
                      : Colors.transparent),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEditBottomWidget(double bottomPadding) {
    return StoreConnector<SmartHomeState, EditBottomViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) =>
            EditSelectors.editBottomSelector(store.state),
        builder: (BuildContext context, EditBottomViewModel vm) {
          return Material(
            key: containerKey,
            child: Container(
              color: AppSemanticColors.background.primary,
              height: bottomWidgetOriginHeight + bottomPadding,
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: _buildBottomWidget(context, vm, bottomPadding),
            ),
          );
        });
  }

  Widget _buildBottomWidget(
      BuildContext context, EditBottomViewModel vm, double bottomPadding) {
    if (vm.editBtnList.isEmpty) {
      return Padding(
          padding: EdgeInsets.only(bottom: bottomPadding),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Image.asset(
                  'assets/images/edit/unselect_card.webp',
                  package: 'smart_home',
                  width: 16,
                  height: 16,
                ),
                const SizedBox(
                  width: 8,
                ),
                SmartHomeText(
                  height: 1,
                  text: vm.placeHolderText,
                  fontSize: 14,
                  color: AppSemanticColors.item.primary,
                ),
              ],
            ),
          ));
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: vm.editBtnList
            .sublist(0, vm.editBtnList.length <= 5 ? vm.editBtnList.length : 5)
            .map((EditBtnInfoViewModel item) => Expanded(
                    child: GestureDetector(
                  onTap: debounce(() {
                    _onEditBtnClick(
                      item.actionType,
                      context,
                      moreBtnList: vm.editBtnList,
                      cardSizeList: vm.cardSizeList,
                    );
                  }, const Duration(milliseconds: 1000)) as void Function()?,
                  child: Column(
                    children: <Widget>[
                      Image.asset(
                        item.icon,
                        package: SmartHomeConstant.package,
                        width: 24,
                        height: 24,
                      ),
                      Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            item.name,
                            style: TextStyle(
                              fontSize: 12,
                              color: AppSemanticColors.item.primary,
                              fontFamilyFallback: fontFamilyFallback(),
                            ),
                          ))
                    ],
                  ),
                )))
            .toList(),
      );
    }
  }

  void _onEditBtnClick(ButtonActionType type, BuildContext context,
      {List<EditBtnInfoViewModel>? moreBtnList,
      List<CardSizeOptionViewModel>? cardSizeList}) {
    switch (type) {
      case ButtonActionType.changeSize:
        _showChangeSizeDialog(
            context, cardSizeList ?? <CardSizeOptionViewModel>[]);
      case ButtonActionType.moveToTop:
        gioTrack(GioConst.gioMoveToTop, <String, String>{
          'product_type': SmartHomeEditManager.getProDuctTypeForGio(),
          'source': SmartHomeEditManager.getSourceForGio(),
        });
        EditPresenterManager.onClickMoveToTop();
      case ButtonActionType.rename:
        gioTrack(GioConst.renameClick);
        EditPresenterManager.onClickRename(context);
      case ButtonActionType.changeDeviceFamily:
        gioTrack(GioConst.changeFamilyClick);
        EditPresenterManager.onClickChangeDeviceFamilyPosition(context);
      case ButtonActionType.changeDevicePosition:
        gioTrack(GioConst.changePositionClick);
        EditPresenterManager.onClickChangeDevicePosition(context);
      case ButtonActionType.deleteDevice:
        gioTrack(GioConst.deleteDeviceClick);
        EditPresenterManager.onClickDeleteDevice(context);
      case ButtonActionType.share:
        EditPresenterManager.onClickShare();
      case ButtonActionType.closeAggregation:
        gioTrack(GioConst.gioCloseAggregation, <String, String>{
          'source': SmartHomeEditManager.getSourceForGio(),
        });
        EditPresenterManager.onCLickCloseAggregation(context);
      case ButtonActionType.aggregationSetting:
        gioTrack(GioConst.gioAggregationSetting, <String, String>{
          'source': SmartHomeEditManager.getSourceForGio(),
        });
        EditPresenterManager.onCLickAggregationSetting(context);
      case ButtonActionType.more:
        if (moreBtnList != null && moreBtnList.length >= 6) {
          _showMoreDialog(context, moreBtnList.sublist(5, moreBtnList.length));
        }
      case ButtonActionType.sceneEdit:
        EditPresenterManager.onClickSceneEdit();
      case ButtonActionType.sceneRemove:
        EditPresenterManager.onClickSceneRemove(context);
      case ButtonActionType.displayedInHomePage:
        EditPresenterManager.onClickDisplayInHomePage();
      case ButtonActionType.hideInHomePage:
        EditPresenterManager.onClickHideInHomePage();
      default:
        break;
    }
  }

  // 显示更多弹框
  void _showMoreDialog(
      BuildContext context, List<EditBtnInfoViewModel> moreBtnList) {
    final RenderBox? box =
        containerKey.currentContext!.findRenderObject() as RenderBox?;
    final double dialogTop = box?.size.height ?? 0;
    showGeneralDialog(
      barrierLabel: 'moreDialog',
      barrierDismissible: true,
      barrierColor: AppSemanticColors.container.musk,
      transitionDuration: const Duration(milliseconds: 200),
      context: context,
      useRootNavigator: false,
      pageBuilder: (BuildContext pageBuilderContext, Animation<double> anim1,
          Animation<double> anim2) {
        SmartHomeEditManager.setEditDialogContext(pageBuilderContext);
        return Align(
            alignment: Alignment.bottomRight,
            child: Container(
              decoration: BoxDecoration(
                color: AppSemanticColors.background.primary,
                borderRadius: const BorderRadius.all(Radius.circular(16)),
              ),
              margin: EdgeInsets.only(bottom: dialogTop, right: 16),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              width: 200,
              height: 54 * moreBtnList.length + 16,
              child: Material(
                color: AppSemanticColors.background.primary,
                child: Column(
                  children: moreBtnList
                      .map((EditBtnInfoViewModel item) => GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: debounce(() {
                              SmartHomeEditManager.closeEditDialog();
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _onEditBtnClick(item.actionType, context);
                              });
                            }, const Duration(milliseconds: 1000)) as void
                                Function()?,
                            child: SizedBox(
                              height: 54,
                              child: Row(
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: Image.asset(
                                      item.icon,
                                      package: SmartHomeConstant.package,
                                      width: 24,
                                      height: 24,
                                    ),
                                  ),
                                  Text(
                                    item.name,
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontFamilyFallback:
                                            fontFamilyFallback(),
                                        fontWeight: FontWeight.w500,
                                        color: AppSemanticColors.item.primary),
                                  )
                                ],
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ),
            ));
      },
    ).whenComplete(() {
      SmartHomeEditManager.closeEditDialog();
    });
  }

  // 显示切换大小弹框
  void _showChangeSizeDialog(
      BuildContext context, List<CardSizeOptionViewModel> cardSizeList) {
    final RenderBox? box =
        containerKey.currentContext!.findRenderObject() as RenderBox?;
    final double _dialogTop = box?.size.height ?? 0;
    showGeneralDialog(
      barrierLabel: 'changeSizeDialog',
      barrierDismissible: true,
      barrierColor: AppSemanticColors.container.musk,
      transitionDuration: const Duration(milliseconds: 200),
      context: context,
      useRootNavigator: false,
      pageBuilder: (BuildContext context, Animation<double> anim1,
          Animation<double> anim2) {
        SmartHomeEditManager.setEditDialogContext(context);
        return Align(
            alignment: Alignment.bottomLeft,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              margin: EdgeInsets.only(bottom: _dialogTop, left: 16),
              width: 200,
              height: (54 * cardSizeList.length + 16).toDouble(),
              child: Material(
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(16)),
                child: Column(
                  children: cardSizeList
                      .asMap()
                      .entries
                      .map((MapEntry<int, CardSizeOptionViewModel> item) {
                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: debounce(() {
                        SmartHomeEditManager.closeEditDialog();
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          gioTrack(GioConst.gioChangeSize, <String, String>{
                            'product_type':
                                SmartHomeEditManager.getProDuctTypeForGio(),
                            'value': item.value.name,
                            'source': SmartHomeEditManager.getSourceForGio(),
                          });
                          EditPresenterManager.onClickChangeSize(
                              item.value.type);
                        });
                      }, const Duration(milliseconds: 1000)) as void
                          Function()?,
                      child: Container(
                        height:
                            item.key == 0 || item.key == cardSizeList.length - 1
                                ? 62
                                : 54,
                        padding: EdgeInsets.only(
                            left: 16,
                            right: 16,
                            top: item.key == 0 ? 8 : 0,
                            bottom:
                                item.key == cardSizeList.length - 1 ? 8 : 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              topLeft: item.key == 0
                                  ? const Radius.circular(16)
                                  : Radius.zero,
                              topRight: item.key == 0
                                  ? const Radius.circular(16)
                                  : Radius.zero,
                              bottomLeft: item.key == cardSizeList.length - 1
                                  ? const Radius.circular(16)
                                  : Radius.zero,
                              bottomRight: item.key == cardSizeList.length - 1
                                  ? const Radius.circular(16)
                                  : Radius.zero),
                          color: item.value.actived
                              ? AppSemanticColors.component.information.fill
                              : Colors.white,
                        ),
                        // borderRadius: const BorderRadius.all(Radius.circular(16)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Text(
                              item.value.name,
                              style: TextStyle(
                                  fontSize: 16,
                                  fontFamilyFallback: fontFamilyFallback(),
                                  fontWeight: FontWeight.w500,
                                  color: item.value.actived
                                      ? AppSemanticColors
                                          .item.information.primary
                                      : AppSemanticColors.item.primary),
                            ),
                            if (item.value.actived)
                              Image.asset(
                                EditConstant.checkMarkIcon,
                                package: SmartHomeConstant.package,
                                width: 12.5,
                                height: 7.5,
                              ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ));
      },
    ).whenComplete(() {
      SmartHomeEditManager.closeEditDialog();
    });
  }
}
