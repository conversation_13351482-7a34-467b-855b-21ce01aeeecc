import 'package:family/device_brief_model.dart';
import 'package:family/device_manage_model.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:family/floor_model.dart';
import 'package:family/room_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:library_widgets/common/log.dart';

import '../../common/constant_gio.dart';
import '../../common/device_filter_util.dart';
import '../../device/device_view_model/card_base_view_model.dart';
import '../../device/device_view_model/device_card_view_model.dart';
import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';
import '../../widget_common/card_text_style.dart';
import '../edit_card_base_info_model.dart';
import '../store/edit_action.dart';
import '../util/edit_constant.dart';
import '../util/edit_manager.dart';
import 'edit_presenter_manager.dart';

class ChangeDevicePositionPresenter {
  static List<FloorInfo> floorList = <FloorInfo>[];
  static List<RoomInfo> roomList = <RoomInfo>[];
  static int selectedFloorIndex = 0;
  static int selectRoomIndex = 0;
  static FixedExtentScrollController customFloorController =
      FixedExtentScrollController();
  static FixedExtentScrollController customRoomController =
      FixedExtentScrollController();

  static RoomInfo? _selectRoom;

  static final TextStyle _positionTextStyle = TextStyle(
      fontSize: 16,
      fontFamilyFallback: fontFamilyFallback(),
      fontWeight: FontWeight.w500,
      color: AppSemanticColors.item.primary);

  static void showDialog(BuildContext context) {
    _setFloorList();
    _selectRoom = RoomInfo('', '');
    if (floorList.isEmpty) {
      ToastHelper.showToast(EditConstant.netError);
      return;
    }
    _setEditDeviceList();

    EditPresenterManager.dialogs.showDoubleBtnModal<SmartHomeState>(
      context: context,
      title: EditConstant.changeDevicePosition,
      enableDrag: true,
      child: (BuildContext dialogContext) {
        return _getDialogWidget();
      },
      confirmCallback: () {
        final List<Map<String, String>> deviceIdList = <Map<String, String>>[];
        smartHomeStore.state.editState.selectedCardsList
            .forEach((EditDeviceCardInfo element) {
          final Map<String, String> jsonObject = <String, String>{
            'deviceId': element.id
          };
          deviceIdList.add(jsonObject);
        });
        EditPresenterManager.onClickDone(
            saveHomeCardCallback: ({bool? isSuccess}) {
          _finish(_selectRoom, deviceIdList);
        });

        _changeRoomDialogGio(EditConstant.confirm, _selectRoom?.roomName);
      },
      cancelCallback: () {
        _changeRoomDialogGio(EditConstant.cancel, _selectRoom?.roomName);
      },
    );
  }

  static void _setFloorList() {
    floorList = <FloorInfo>[];
    final FamilyModel? family = Family.getCurrentFamilySync();
    family?.floorInfos.forEach((FloorModel floorModel) {
      if (floorModel.rooms.isNotEmpty) {
        final List<RoomInfo> roomList = <RoomInfo>[];
        floorModel.rooms.forEach((RoomModel roomModel) {
          final RoomInfo room = RoomInfo(roomModel.roomId, roomModel.roomName);
          roomList.add(room);
        });
        final FloorInfo floor = FloorInfo(
            floorModel.floorId, getFloorName(floorModel.floorName), roomList);
        floorList.add(floor);
      }
    });
    if (floorList.length == 1) {
      roomList = floorList[0].roomList;
    }
  }

  static void _setEditDeviceList() {
    if (smartHomeStore.state.editState.selectedCardsList.length == 1) {
      final CardBaseViewModel? deviceVm =
          smartHomeStore.state.deviceState.allCardViewModelMap[
              smartHomeStore.state.editState.selectedCardsList[0].id];
      if (deviceVm is DeviceCardViewModel) {
        final int _floorIndex = floorList.indexWhere((FloorInfo element) =>
            element.floorId == deviceVm.device.basicInfo.roomInfo.floorId);
        selectedFloorIndex = _floorIndex == -1 ? 0 : _floorIndex;

        final int _roomIndex = floorList[selectedFloorIndex]
            .roomList
            .indexWhere((RoomInfo element) =>
                element.roomId == deviceVm.device.basicInfo.roomInfo.roomId);
        selectRoomIndex = _roomIndex == -1 ? 0 : _roomIndex;
      }
    } else {
      selectedFloorIndex = 0;
      selectRoomIndex = 0;
    }
    customFloorController =
        FixedExtentScrollController(initialItem: selectedFloorIndex);
    customRoomController =
        FixedExtentScrollController(initialItem: selectRoomIndex);
  }

  static Widget _getDialogWidget() {
    List<RoomInfo> roomList = floorList[selectedFloorIndex].roomList;
    if (roomList.isNotEmpty) {
      _selectRoom = roomList[selectRoomIndex];
    }
    return StatefulBuilder(builder:
        (BuildContext dialogContext, void Function(void Function()) state) {
      return Container(
          width: double.infinity,
          height: 220,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: AppSemanticColors.component.secondary.fill,
          ),
          child: Column(
            children: <Widget>[
              SizedBox(
                height: 220,
                child: Row(
                  children: <Widget>[
                    if (floorList.length > 1)
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 44,
                          diameterRatio: 2.0,
                          useMagnifier: true,
                          scrollController: customFloorController,
                          selectionOverlay: _selectionOverlayWidget(),
                          onSelectedItemChanged: (int index) {
                            state(() {
                              selectedFloorIndex = index;
                              roomList = floorList[selectedFloorIndex].roomList;
                              customRoomController
                                  .jumpToItem(roomList.length - 1);
                              customRoomController.jumpToItem(0);
                              if (roomList.isNotEmpty) {
                                _selectRoom = roomList[0];
                              }
                            });
                          },
                          children: floorList
                              .map((FloorInfo item) => Align(
                                    child: Text(item.floorName,
                                        textAlign: TextAlign.center,
                                        style: _positionTextStyle),
                                  ))
                              .toList(),
                        ),
                      ),
                    Expanded(
                      child: CupertinoPicker(
                        itemExtent: 44,
                        diameterRatio: 2.0,
                        useMagnifier: true,
                        scrollController: customRoomController,
                        selectionOverlay: _selectionOverlayWidget(),
                        onSelectedItemChanged: (int index) {
                          _selectRoom = roomList[index];
                        },
                        children: roomList
                            .map((RoomInfo item) => Align(
                                  child: Text(item.roomName,
                                      textAlign: TextAlign.center,
                                      style: _positionTextStyle),
                                ))
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ));
    });
  }

  static Widget _selectionOverlayWidget() {
    return Column(
      children: <Widget>[
        // TODO quzhenhao UI问题暂时使用分割线
        const Divider(height: 1, color: Colors.grey),
        Expanded(child: Container()),
        const Divider(height: 1, color: Colors.grey),
      ],
    );
  }

  static Future<void> _finish(
      RoomInfo? selectRoom, List<Map<String, String>> deviceIdList) async {
    final FamilyModel? family = Family.getCurrentFamilySync();
    final String familyId = family?.familyId ?? '';
    if (selectRoom != null) {
      Family.moveDevicesToOtherRoom(familyId, selectRoom.roomId, deviceIdList)
          .then((DeviceManageModel value) {
        DevLogger.info(
            tag: SmartHomeEditManager.tag,
            msg: 'ChangeDevicePositionPresenter _finish '
                'response = $value');
        if (value.successDevices.isNotEmpty && value.failureDevices.isEmpty) {
          ToastHelper.showToast(EditConstant.changePositionSuccess);
          _changeRoomResultGio(EditConstant.resultSuccess);
        } else if (value.successDevices.isNotEmpty &&
            value.failureDevices.isNotEmpty) {
          final List<String> successDevice = <String>[];
          final List<String> failDevice = <String>[];
          value.successDevices.forEach((DeviceBriefModel element) {
            if (element.deviceName.isNotEmpty) {
              successDevice.add(element.deviceName);
            }
          });
          value.failureDevices.forEach((DeviceBriefModel element) {
            if (element.deviceName.isNotEmpty) {
              failDevice.add(element.deviceName);
            }
          });
          ToastHelper.showToast(
              '${successDevice.join() + EditConstant.changePositionSuccess}，'
              '${failDevice.join() + EditConstant.changePositionFail}');
          _changeRoomResultGio(EditConstant.resultPartialFail);
        } else {
          ToastHelper.showToast(EditConstant.changePositionFail);
          _changeRoomResultGio(EditConstant.resultFullFail);
        }
      }).catchError((dynamic error) {
        DevLogger.error(
            tag: SmartHomeEditManager.tag,
            msg: 'ChangeDevicePositionPresenter _finish '
                'error = $error');
        ToastHelper.showToast(EditConstant.changePositionFail);
        _changeRoomResultGio(EditConstant.resultFullFail);
      });
    }
  }

  static void _changeRoomDialogGio(String buttonName, String? name) {
    gioTrack(GioConst.gioChangeRoomDialog, <String, String>{
      EditConstant.buttonName: buttonName,
      EditConstant.name: name ?? ''
    });
  }

  static void _changeRoomResultGio(String result) {
    gioTrack(GioConst.gioChangeRoomResult,
        <String, String>{EditConstant.result: result});
  }
}

class FloorInfo {
  String floorId;
  String floorName;
  List<RoomInfo> roomList;

  FloorInfo(this.floorId, this.floorName, this.roomList);
}

class RoomInfo {
  String roomId;
  String roomName;

  RoomInfo(this.roomId, this.roomName);
}
