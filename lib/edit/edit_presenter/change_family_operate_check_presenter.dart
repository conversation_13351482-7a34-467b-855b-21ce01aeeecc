/*
 * 描述：设备转移家庭权限校验
 * 作者：songFJ
 * 创建时间：2025/4/28
 */

import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/edit/edit_presenter/edit_presenter_manager.dart';

import '../../common/smart_home_text_widget.dart';
import '../../widget_common/card_text_style.dart';
import '../util/edit_manager.dart';

class ChangeFamilyOperateCheckPresenter {
  static void showDialog(
    BuildContext context,
    ChangeFamilyOperateCheckDialogModel viewModel,
  ) {
    EditPresenterManager.dialogs.showSingleBtnModal<void>(
        context: context,
        title: '以下设备无法转移家庭',
        enableDrag: true,
        callback: () {},
        child: (BuildContext context) {
          final List<Widget> componentList = <Widget>[];
          viewModel.messageList
              .forEach((OperateCheckDialogMessageModel element) {
            componentList.add(_messageWidget(element));
          });

          return SizedBox(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                /// 描述
                RichText(
                  text: SmartHomeSpan.textSpan(
                    text: viewModel.desc,
                    fontSize: 14,
                    color: AppSemanticColors.item.secondary,
                  ),
                ),

                /// 内容
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: componentList,
                  ),
                )
              ],
            ),
          );
        });
  }

  static Widget _messageWidget(OperateCheckDialogMessageModel viewModel) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        color: AppSemanticColors.background.primary,
      ),
      margin: const EdgeInsets.only(top: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: RichText(
          text: SmartHomeSpan.textSpan(
              text: viewModel.userName,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppSemanticColors.item.primary,
              children: <InlineSpan>[
                SmartHomeSpan.textSpan(
                  text: viewModel.desc,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.primary,
                ),
                SmartHomeSpan.textSpan(
                  text: viewModel.deviceNameList.join('、'),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.primary,
                ),
              ]),
        ),
      ),
    );
  }
}

class OperateCheckDialogMessageModel {
  final String userName;
  final String desc;
  final List<String> deviceNameList;

  OperateCheckDialogMessageModel(
      {this.userName = '',
      this.desc = '',
      this.deviceNameList = const <String>[]});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OperateCheckDialogMessageModel &&
          runtimeType == other.runtimeType &&
          userName == other.userName &&
          desc == other.desc &&
          listEquals(deviceNameList, other.deviceNameList);

  @override
  int get hashCode =>
      userName.hashCode ^ desc.hashCode ^ listHashCode(deviceNameList);
}

class ChangeFamilyOperateCheckDialogModel {
  final String title;
  final String desc;
  final List<OperateCheckDialogMessageModel> messageList;

  ChangeFamilyOperateCheckDialogModel(
      {this.title = '以下设备无法转移家庭',
      this.desc = '您可以联系有操作权限的用户进行转移，或取消勾选下方说明中提及的设备后，继续转移其他设备',
      this.messageList = const <OperateCheckDialogMessageModel>[]});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChangeFamilyOperateCheckDialogModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          desc == other.desc &&
          listEquals(messageList, other.messageList);

  @override
  int get hashCode =>
      title.hashCode ^ desc.hashCode ^ listHashCode(messageList);
}
