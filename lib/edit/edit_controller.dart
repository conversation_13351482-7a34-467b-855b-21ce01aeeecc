/*
 * 描述：编辑模式控制器
 * 作者：fancunshuo
 * 建立时间: 2025/5/24
 */
import 'package:flutter/material.dart';

class EditController {
  final OverlayPortalController overlayPortalController =
      OverlayPortalController();
  AnimationController? animationController;

  void show() {
    overlayPortalController.show();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      animationController?.forward();
    });
  }

  void hide() {
    Future<void>.delayed(const Duration(milliseconds: 350)).then((_) {
      overlayPortalController.hide();
    });
    animationController?.reverse();
  }

  bool get isShowing => overlayPortalController.isShowing;

  OverlayPortalController get controller => overlayPortalController;
}
