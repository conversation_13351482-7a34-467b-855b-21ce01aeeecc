/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2025/4/25
 */

import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:upservice/model/uhome_response_model.dart';

class OperateCheckResponseModel extends UhomeResponseModel {
  OperateCheckResponseModel.fromJson(super.data) : super.fromJson() {
    final Map<dynamic, dynamic> map = super.retData;
    if (map is Map<String, dynamic>) {
      data = OperateCheckResponseData.fromJson(map);
    }
  }

  OperateCheckResponseData data =
      OperateCheckResponseData.fromJson(<String, dynamic>{});

  @override
  String toString() {
    return 'OperateCheckResponseModel{data: $data}';
  }
}

class OperateCheckResponseData {
  OperateCheckFailReason deviceReasons =
      OperateCheckFailReason.fromJson(<String, dynamic>{});

  OperateCheckResponseData.fromJson(Map<String, dynamic> json) {
    final Map<dynamic, dynamic> map =
        json.mapValueForKey('deviceReasons', <dynamic, dynamic>{});
    if (map is Map<String, dynamic>) {
      deviceReasons = OperateCheckFailReason.fromJson(map);
    }
  }

  @override
  String toString() {
    return 'OperateCheckResponseData{deviceReasons: $deviceReasons}';
  }
}

class OperateCheckFailReason {
  List<OperateCheckFailReasonModel> reason1801001 =
      <OperateCheckFailReasonModel>[];
  List<OperateCheckFailReasonModel> reason1801004 =
      <OperateCheckFailReasonModel>[];

  OperateCheckFailReason.fromJson(Map<String, dynamic> json) {
    final List<dynamic> reason18010001List =
        json.listValueForKey('1801001', <dynamic>[]);
    final List<dynamic> reason18010004List =
        json.listValueForKey('1801004', <dynamic>[]);
    for (final dynamic item in reason18010001List) {
      if (item is Map<String, dynamic>) {
        reason1801001.add(OperateCheckFailReasonModel.fromJson(item));
      }
    }
    for (final dynamic item in reason18010004List) {
      if (item is Map<String, dynamic>) {
        reason1801004.add(OperateCheckFailReasonModel.fromJson(item));
      }
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OperateCheckFailReason &&
          runtimeType == other.runtimeType &&
          listEquals(reason1801001, other.reason1801001) &&
          listEquals(reason1801004, other.reason1801004);

  @override
  int get hashCode => listHashCode(reason1801001) ^ listHashCode(reason1801004);

  @override
  String toString() {
    return 'OperateCheckFailReason{reason1801001: $reason1801001, reason1801004: $reason1801004}';
  }
}

class OperateCheckFailReasonModel {
  String deviceId = '';
  String psiUserId = '';
  String nickname = '';

  OperateCheckFailReasonModel.fromJson(Map<String, dynamic> map) {
    deviceId = map.stringValueForKey('deviceId', '');
    psiUserId = map.stringValueForKey('psiUserId', '');
    nickname = map.stringValueForKey('nickname', '');
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OperateCheckFailReasonModel &&
          runtimeType == other.runtimeType &&
          deviceId == other.deviceId &&
          psiUserId == other.psiUserId &&
          nickname == other.nickname;

  @override
  int get hashCode =>
      deviceId.hashCode ^ psiUserId.hashCode ^ nickname.hashCode;

  @override
  String toString() {
    return 'OperateCheckFailReasonModel{deviceId: $deviceId, psiUserId: $psiUserId, nickname: $nickname}';
  }
}
