import '../device/resize_device_card/resize_base_model.dart';

class EditCardBaseInfo {}

class EditDeviceCardInfo extends EditCardBaseInfo {
  String id;

  // 设备归属人ID
  String ownUserId;

  // 设备类型：1 普通设备 2 网器设备 3 附件设备 4 子设备
  String deviceRole = '';

  // 配置类型
  String? configType;

  // 组设备类型
  String? groupType;

  // 非网器
  bool noNetDevice = false;

  // 高安全设备false，低安全设备true
  bool isRebind = false;

  // 设备名称
  String deviceName = '';

  // 卡片尺寸
  DeviceCardType type;

  // 是否支持大卡
  bool supportLargeCard;

  //判断是否是共享设备
  bool isShareDevice = false;

  //是否支持共享
  bool isSupportShare = false;

  // 设备图片
  String deviceIcon = '';

  // 成品编码
  String productCode = '';

  bool isSceneInvalid = false;

  String ucUserId = '';

  EditDeviceCardInfo(
      {required this.id,
      required this.ownUserId,
      required this.type,
      required this.supportLargeCard,
      this.noNetDevice = false,
      this.isRebind = false,
      this.deviceRole = '',
      this.configType,
      this.groupType,
      this.deviceName = '',
      this.isSupportShare = false,
      this.isShareDevice = false,
      this.deviceIcon = '',
      this.productCode = '',
      this.isSceneInvalid = false,
      this.ucUserId = ''});

  @override
  String toString() {
    return 'EditDeviceCardInfo{id: $id, ownUserId: $ownUserId, '
        'deviceRole: $deviceRole, configType: $configType, '
        'groupType: $groupType, supportLargeCard: $supportLargeCard, '
        'noNetDevice: $noNetDevice, isRebind: $isRebind, '
        'deviceName: $deviceName, type: $type， '
        'isSupportShare: $isSupportShare, isShareDevice: $isShareDevice, '
        'deviceIcon: $deviceIcon, productCode: $productCode ucUserId: $ucUserId}';
  }
}

class EditSizedViewModel {
  String name = ''; // 尺寸名称
  DeviceCardType type = DeviceCardType.middleCard;
  bool actived = false; // 选中状态

  EditSizedViewModel({
    required this.name,
    required this.type,
    required this.actived,
  });

  @override
  String toString() {
    return 'EditSizedViewModel{name: $name, type: $type, actived: $actived}';
  }
}

class CacheCardModel {
  String id;
  DeviceCardType type;

  CacheCardModel({
    required this.id,
    required this.type,
  });

  @override
  String toString() {
    return 'CacheCardModel{id: $id, type: $type}';
  }
}

const String cardTypeSmall = '0';
const String cardTypeBig = '1';

const String deviceRolePrimary = '2';
const String deviceRoleSub= '4';

const String groupDevice = 'group';
const String thirdPartyDevice = '1';

const int quickTab = 0;
const int deviceTab = 1;
