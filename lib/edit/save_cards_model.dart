import 'package:device_utils/typeId_parse/template_map.dart';

class SaveCardRequestModel {
  String familyId = '';
  List<String>? bigCardList = <String>[]; // 大卡设备deviceId集合
  List<String>? middleCardList = <String>[]; // 中卡设备deviceId集合
  List<String>? smallCardList = <String>[]; // 小卡设备deviceId集合
  List<String>? cardOrderList = <String>[]; // 卡片排序deviceId集合
  List<String>? lampGroupList = <String>[]; // 灯光聚合卡片
  List<String>? curtainList = <String>[]; // 窗帘聚合卡片

  SaveCardRequestModel({
    required this.familyId,
    this.bigCardList,
    this.middleCardList,
    this.smallCardList,
    this.cardOrderList,
    this.lampGroupList,
    this.curtainList,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['familyId'] = familyId;
    if (bigCardList != null && bigCardList is List<String>) {
      data['bigCardList'] =
          bigCardList?.map((String v) => v).toList();
    }
    if (smallCardList != null && smallCardList is List<String>) {
      data['smallCardList'] =
          smallCardList?.map((String v) => v).toList();
    }
    if (middleCardList != null && middleCardList is List<String>) {
      data['middleCardList'] =
          middleCardList?.map((String v) => v).toList();
    }
    if (cardOrderList != null && cardOrderList is List<String>) {
      data['cardOrderList'] =
          cardOrderList?.map((String v) => v).toList();
    }
    if (lampGroupList != null && lampGroupList is List<String>) {
      data['lampGroupList'] =
          lampGroupList?.map((String v) => v).toList();
    }
    if (curtainList != null && curtainList is List<String>) {
      data['curtainList'] =
          curtainList?.map((String v) => v).toList();
    }
    return data;
  }

  @override
  String toString() {
    return 'SaveCardRequestModel{familyId: $familyId, '
        'bigCardList: $bigCardList, '
        'middleCardList: $middleCardList, '
        'smallCardList: $smallCardList, '
        'cardOrderList: $cardOrderList, '
        'lampGroupList: $lampGroupList, '
        'curtainList: $curtainList}';
  }
}

class ShortcutSortModel {
  ShortcutSortModel({
    required this.cameraSort,
    required this.wholeHouseSort,
    required this.deviceBigCardSort,
  });
  List<String> cameraSort = <String>[];
  List<String> wholeHouseSort = <String>[];
  List<String> deviceBigCardSort = <String>[];

  ShortcutSortModel.fromJson(Map<dynamic, dynamic> json) {
    final List<dynamic> cameraList =
    json.listValueForKey('cameraSort', <dynamic>[]);
    for (final dynamic element in cameraList) {
      if (element is String) {
        cameraSort.add(element);
      }
    }

    final List<dynamic> wholeHouseList =
    json.listValueForKey('wholeHouseSort', <dynamic>[]);
    for (final dynamic element in wholeHouseList) {
      if (element is String) {
        wholeHouseSort.add(element);
      }
    }

    final List<dynamic> deviceBigCardList =
    json.listValueForKey('deviceBigCardSort', <dynamic>[]);
    for (final dynamic element in deviceBigCardList) {
      if (element is String) {
        deviceBigCardSort.add(element);
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cameraSort'] = cameraSort.map((String v) => v).toList();
    data['wholeHouseSort'] = wholeHouseSort.map((String v) => v).toList();
    data['deviceBigCardSort'] = deviceBigCardSort.map((String v) => v).toList();
    return data;
  }

  @override
  String toString() {
    return 'cameraSort: $cameraSort, wholeHouseSort: $wholeHouseSort, '
        'deviceBigCardSort: $deviceBigCardSort';
  }
}
