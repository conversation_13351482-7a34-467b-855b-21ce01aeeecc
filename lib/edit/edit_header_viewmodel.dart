import 'package:smart_home/store/smart_home_store.dart';

import 'edit_presenter/edit_presenter_manager.dart';

class EditHeaderViewModel {
  EditHeaderViewModel(this.selectedCount, this.backVisible);

  int selectedCount = 0;
  bool backVisible = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EditHeaderViewModel &&
          runtimeType == other.runtimeType &&
          selectedCount == other.selectedCount &&
          backVisible == other.backVisible;

  @override
  int get hashCode => selectedCount.hashCode ^ backVisible.hashCode;

  @override
  String toString() {
    return 'EditHeaderViewModel{selectedCount: $selectedCount, backVisible: $backVisible}';
  }
}
