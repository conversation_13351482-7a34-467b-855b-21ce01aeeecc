# 环境设备设置数据流程设计文档

本文档基于 `环境设置数据流.png` 设计图，描述了环境设备设置页面的数据模型、组件和流程。

## 1. 页面 (Page)

### `EnvDeviceSettingPage`

*   **职责**: 显示环境设备设置界面，允许用户选择要在全屋概览中显示的环境设备。
*   **主要组件**: `WholeHouseEnvDeviceListWidget` (或类似列表组件)。
*   **数据绑定**:
    *   页面标题: 绑定 `SmartHomeState.wholeHouseState.spaceName` (假设显示特定空间的设置)。
    *   列表数据: 绑定 `EnvDeviceSettingListViewModel`。
    *   列表组件: `WholeHouseListCheckItemWidge`
*   **布局**: 参考 `ConsumablesDetailPage` 页面布局。
*   **列表结构**: 页面 Body 使用列表展示，列表项根据 `EnvDeviceSettingCategoryViewModel` 分组。
*   **分组标题**: 列表的间隔处显示 `EnvDeviceSettingCategoryViewModel.envName` 作为分组标题。

## 2. 视图模型 (ViewModel)

### `EnvDeviceSettingListViewModel`

*   **职责**: 为 `EnvDeviceSettingPage` 提供列表数据。
*   **属性**:
    *   `List<EnvDeviceSettingCategoryViewModel> list`: 环境设备分类列表 (如: 温度、湿度、PM2.5)。

### `EnvDeviceSettingCategoryViewModel`

*   **职责**: 表示一个环境设备分类。
*   **属性**:
    *   `String envName`: 环境名称 (例如: "温度", "湿度", "空气质量")。
    *   `List<EnvDeviceSettingItemViewModel> items`: 该分类下的设备项列表。

### `EnvDeviceSettingItemViewModel`

*   **职责**: 表示一个具体的环境设备项，用于 `WholeHouseListCheckItemWidge` 的展示和交互。
*   **属性**:
    *   `String deviceName`: 设备名称。
    *   `String deviceImage`: 设备图片。
    *   `String roomName`: 房间名称。
    *   `String reportedValue`: 上报值 (设备当前读数)。
    *   `String unit`: 单位 (例如: "°C", "%", "μg/m³")。
    *   `bool isOnline`: 是否在线。
    *   `bool selected`: 是否选中 (用户是否希望在概览中显示此设备)。

## 3. 组件 (Widget)

### `WholeHouseListCheckItemWidge`

*   **职责**: 列表中的单个设备项，带有复选框。
*   **数据绑定**: 绑定 `EnvDeviceSettingItemViewModel`。
*   **显示内容**:
    *   设备图片 (`deviceImage`)
    *   设备名称 (`deviceName`)
    *   房间名称 (`roomName`)
    *   当前值 (`value` + `unit`) - *注意: 图片中为 `value`，对应 ViewModel 中为 `reportedValue`*
    *   选中状态 (`checked`) - *注意: 图片中为 `checked`，对应 ViewModel 中为 `selected`*
    *   离线状态 (`offline`) - *注意: 图片中为 `offline`，对应 ViewModel 中为 `!isOnline`*
*   **交互**:
    *   `ValueChanged<bool> onChanged`: 当用户点击复选框改变选中状态时触发。
        *   回调中发送 `SetWholeHouseEnvDeviceSettingAction`，传递设备ID和新的选中状态，以更新 `envDeviceState`。

## 4. 状态 (State)

### `wholeHouseState.envDeviceState`

*   **位置**: `SmartHomeState -> WholeHouseState -> EnvDeviceState`
*   **结构**: (根据图片描述) 环境属性维度存储。
    *   `temperature: List<EnvDeviceItemModel>`
    *   `humidity: List<EnvDeviceItemModel>`
    *   `pm25: List<EnvDeviceItemModel>`
    *   每个 `EnvDeviceItemModel` 包含 `selected` 字段，表示用户是否勾选。
*   **数据来源**: 通过 `EnvDevicesResponseModel` 从服务器获取原始数据，并存储处理。
*   **更新**: 通过 Reducer 响应 `SetWholeHouseEnvDeviceSettingAction` 来更新具体设备的 `selected` 状态。

## 5. 动作 (Action)

### `SetWholeHouseEnvDeviceSettingAction`

*   **职责**: 当用户在设置页面更改设备选中状态时，触发此 Action。
*   **参数**:
    *   `deviceId`: 被修改设备的 ID。
    *   `selected`: 新的选中状态 (true 或 false)。
*   **后续流程**:
    *   Middleware : 请求“环境信息偏好设置”服务接口，发送选中的设备数据。
    *   Reducer: 更新 `wholeHouseState.envDeviceState` 中对应设备的 `selected` 状态。

## 6. 数据流总结

1.  **进入页面**: 用户导航到 `EnvDeviceSettingPage`。
2.  **数据加载/选择**:\
    *   页面连接到 Redux Store。
    *   通过 Selector 函数从 `wholeHouseState.envDeviceState` 提取数据。
    *   Selector 将 `EnvDeviceItemModel` 列表按类型 (温度、湿度、PM2.5) 分组，并转换为 `EnvDeviceSettingListViewModel` (包含 `EnvDeviceSettingCategoryViewModel` 和 `EnvDeviceSettingItemViewModel` 列表)。
3.  **UI 渲染**:\
    *   `EnvDeviceSettingPage` 使用 `EnvDeviceSettingListViewModel` 渲染列表。
    *   列表按 `EnvDeviceSettingCategoryViewModel` 分组显示标题 (`envName`)。
    *   每个分组下使用 `WholeHouseListCheckItemWidge` 渲染设备项，绑定 `EnvDeviceSettingItemViewModel`。\
4.  **用户交互**:\
    *   用户点击 `WholeHouseListCheckItemWidge` 上的复选框。\
    *   `onChanged` 回调被触发。\
    *   回调函数 `dispatch` 一个 `SetWholeHouseEnvDeviceSettingAction`，包含设备 ID 和新的 `selected` 状态。
5.  **状态更新**:\
    *   Action 被 Reducer 捕获。\
    *   Reducer 找到 `wholeHouseState.envDeviceState` 中对应的 `EnvDeviceItemModel`，并更新其 `selected` 字段。
6.  **UI 反馈**:\
    *   Store 状态更新，通知连接到 Store 的组件 (`EnvDeviceSettingPage`)。\
    *   Selector 重新计算 ViewModel。\
    *   如果 ViewModel 发生变化 (`distinct: true` 通常会配置)，UI 会重新渲染，复选框状态更新。