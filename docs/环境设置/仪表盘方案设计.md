**仪表盘方案设计**

  ----------------------------------- -----------------------------------
  **要求上线时间**                    2025-4-30

  **Story**                           

  **开发人员**                        

  **评审状态**                        DEV DRAFT
  ----------------------------------- -----------------------------------

**必要性和目标**

**必要性**

目前只有环境信息(室内+室外)，缺少设备故障、耗材告警、设备运行这几个板块，而且没有展示的设置，所以需要重新设计方案。

**目标**

1
在智家tab展示设备故障(实时)、耗材异常、设备运行、环境信息，是否展示可以进行独立配置；

2 server 接口调用限制非必要不调用；

3 页面性能和生产一致；

**风险评估**

**评估等级**

中------只影响特定业务，影响范围可控，且出问题也不会有太大影响

**评估依据**

  -------------- ---------------------------- ----------------------------
  **序号**       **评估项**                   **评估结果**

  1              是否可以热更新修复           

  2              影响范围                     

  3              是否影响其他业务模块         

  4              开发成本（单位天）           

  5              开发类型（新增/修改/删除）   

  6              是否有单元测试覆盖           

  7              是否改动三方库               

  8              是否涉及用户隐私             
  -------------- ---------------------------- ----------------------------

**技术方案**

**功能成熟度**

成熟

**对外API**

无

**内部逻辑**

详细描述技术演进的内部改动逻辑

**功能点**

故障列表：

server接口model定义，返回了故障的设备及故障内容

刷新时机：设备属性上报时对比设备故障状态，降级接口调用频次→筛选(和state里的故障不一致：设备deviceId+故障个数+故障内容)→调接口

耗材告警(空调、净水机、智能马桶盖、软水机、门锁)：

server接口model定义，返回设备信息及耗材详情

刷新时机：消息通知回调里调用接口：smart_home-initState→添加监听Message.listen\<PushMessage\>((PushMessage
event)的回调里→调接口

耗材半弹窗的展示逻辑较复杂，空调：滤网清洁+自清洁，净水机：重置，需要调用接口

环境信息：

室外天气：家庭位置\>手机定位\>IP定位，model修改，增加天气现象字段

设备环境信息数据：server接口model定义，返回空间、环境、信息对应的设备的信息及环境信息

运行设备：模型定义\--设备上报

明确应用分类对应的大中类(需求补充)

DeviceCardViewModel扩展字段 runningMode，中大类中添加get runningMode方法

运行中设备刷新时机：设备上报，组装数据更新state

通用设置：模型定义 \-- server接口

通用的列表组件开发

> [仪表盘公共组件](https://ihaier.feishu.cn/wiki/J36Vw8tdLiTfbhknoCUcMkzSnzd)

运行中设备的动效方案： \@范存硕

**外部依赖**

**server的梳理文档：[仪表盘需求梳理](https://ihaier.feishu.cn/docx/K42Kd8TAMoBIVbxz2hXcYsiAnpl),
7个server新接口， 1个已有接口**

+:----+:-------+:--------------+:----------------+:---------+:--------+
|     | 接     | 地址          | path            | 入参     | 返      |
|     | 口功能 |               |                 |          | 回数据  |
+-----+--------+---------------+-----------------+----------+---------+
| 1   | 故     | h             | /               | familyId | \[{devi |
|     | 障查询 | ttps://stp.ha | api-gw/wisdomho |          | ceInfo: |
|     |        | ier.net/proje | use/house/v1/qu |          | {},     |
|     |        | ct/79/interfa | eryDeviceFaults |          | device  |
|     |        | ce/api/232903 |                 |          | Faults: |
|     |        |               |                 |          | \[\]    |
|     |        |               |                 |          | }\]     |
+-----+--------+---------------+-----------------+----------+---------+
| 2   | 耗     | h             | /api-gw         |          |         |
|     | 材详情 | ttps://stp.ha | /wisdomhouse/ho |          |         |
|     |        | ier.net/proje | use/v6/consumab |          |         |
|     |        | ct/79/interfa | les/family/list |          |         |
|     |        | ce/api/233452 |                 |          |         |
+-----+--------+---------------+-----------------+----------+---------+
| 3   | 查询当 | h             | /api-           | a        | 室      |
|     | 天天气 | ttps://stp.ha | gw/wisdomhouse/ | reaId、\ | 外温度  |
|     |        | ier.net/proje | house/v2/multif | servic   | ：temp  |
|     |        | ct/79/interfa | unctional/query | e=outdoo |         |
|     |        | ce/api/233047 |                 | rWeather |         |
+-----+--------+---------------+-----------------+----------+---------+
| 4   | 温     | h             | /a              | familyId | s       |
|     | 度、湿 | ttps://stp.ha | pi-gw/wisdomhou |          | paceId: |
|     | 度、空 | ier.net/proje | se/house/v1/que |          | {\      |
|     | 气质量 | ct/79/interfa | ryEnvPreference |          | tempe   |
|     | 设备偏 | ce/api/232957 |                 |          | rature: |
|     | 好查询 |               |                 |          | {\      |
|     |        |               |                 |          | se      |
|     |        |               |                 |          | lected: |
|     |        |               |                 |          | true，\ |
|     |        |               |                 |          | de      |
|     |        |               |                 |          | viceId: |
|     |        |               |                 |          | xxx,\   |
|     |        |               |                 |          | reporte |
|     |        |               |                 |          | dValue: |
|     |        |               |                 |          | xxx     |
|     |        |               |                 |          |         |
|     |        |               |                 |          | }\      |
|     |        |               |                 |          | }       |
+-----+--------+---------------+-----------------+----------+---------+
| 5   | 温     | h             | /api-gw/wisdomh | fam      |         |
|     | 度、湿 | ttps://stp.ha | ouse/house/v1/s | ilyId、s |         |
|     | 度、空 | ier.net/proje | etEnvPreference | paceId、 |         |
|     | 气质量 | ct/79/interfa |                 |          |         |
|     | 设备偏 | ce/api/232948 |                 | tempera  |         |
|     | 好设置 |               |                 | ture\[de |         |
|     |        |               |                 | viceId\] |         |
|     |        |               |                 | 、humidi |         |
|     |        |               |                 | ty、pm25 |         |
+-----+--------+---------------+-----------------+----------+---------+
| 6   | 全屋   | h             | /api-gw/wisdom  | familyId | d       |
|     | 偏好设 | ttps://stp.ha | house/house/v1/ |          | ashBoar |
|     | 置查询 | ier.net/proje | queryPreference |          | dSwitch |
|     |        | ct/79/interfa |                 |          |         |
|     |        | ce/api/232939 |                 |          |         |
+-----+--------+---------------+-----------------+----------+---------+
| 7   | 全屋偏 | h             | /api-gw/wisd    | fam      |         |
|     | 好设置 | ttps://stp.ha | omhouse/house/v | ilyId、\ |         |
|     |        | ier.net/proje | 1/setPreference | preferTy |         |
|     |        | ct/79/interfa |                 | pe=DASH_ |         |
|     |        | ce/api/232930 |                 | BOARD、\ |         |
|     |        |               |                 | switch   |         |
|     |        |               |                 | Status、 |         |
+-----+--------+---------------+-----------------+----------+---------+
| 8   | 设备   | https://stp.h | /api-           | de       |         |
|     | 保养耗 | aier.net/proj | gw/wisdomhouse/ | viceId、 |         |
|     | 材重置 | ect/79/interf | house/v1/device | resetTyp |         |
|     | (线上  | ace/api/11226 | /tob/cleanreset | e、typeI |         |
|     | 接口)  |               |                 | d、prodN |         |
|     |        |               |                 | o、reset |         |
|     |        |               |                 | AttrName |         |
+-----+--------+---------------+-----------------+----------+---------+

**数据流图**

运行中的设备范围及定义：[智家APP
AWE改版卡片类型及功能汇总](https://ihaier.feishu.cn/wiki/QD8ywT3cMiRV3HkF0UvczcwInKb?sheet=9Glu5O)

![](./images/media/image1.jpeg){width="5.75in"
height="5.583333333333333in"}

**测试方法**

**测试用例**

按照需求的测试用例进行测试。
