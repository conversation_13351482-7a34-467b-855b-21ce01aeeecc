# 环境信息偏好设置接口客户端开发计划

本文档基于 `docs/环境设置/服务接口` 目录下的接口说明，规划客户端实现环境信息偏好设置 API 对接的开发步骤。

## 1. 目标

实现客户端功能，允许用户将在环境设备设置页面所做的选择（即希望在全屋概览中显示的温度、湿度、PM2.5 设备）持久化保存到服务器。

## 2. 数据模型

### 2.1. 请求模型 (`EnvDevicePreferenceRequestModel.dart`)

根据 `环境设置接口body.png` 定义请求体的数据结构。

*   **路径**: `lib/whole_house/device_env/services/preference_request_model.dart`
*   **类名**: `EnvDevicePreferenceRequestModel`
*   **字段**:
    *   `String familyId`: 当前家庭 ID。
    *   `String spaceId`: 当前空间 ID。
    *   `String? temperature`: 用户选择的温度设备 ID (如果未选则为 null 或空字符串)。
    *   `String? humidity`: 用户选择的湿度设备 ID。
    *   `String? pm25`: 用户选择的 PM2.5 设备 ID。
*   **方法**:
    *   `Map<String, dynamic> toJson()`: 将模型转换为 JSON Map 以便发送。

### 2.2. 响应模型

根据 `环境设备设置返回值.png`，此接口的响应似乎是标准的成功/失败结构。

*   **复用**: 直接复用 `package:upservice/model/uhome_response_model.dart` 中的 `UhomeResponseModel`。通过判断 `retCode == '00000'` 来确定操作是否成功。

## 3. 服务层 (`HttpService` & `RestClient`)

### 3.1. 更新 `rest_client.dart`

在 `SmartHomeRestClient` 抽象类中添加新的 API 定义。

*   **路径**: `lib/service/rest_client.dart`
*   **方法**:
    ```dart
    /// 设置环境信息偏好
    @POST('/api-gw/wisdomdevice/device/v1/setEnvPreference')
    Future<UhomeResponseModel> setEnvDevicePreference(@Body() Map<String, String> params);
    ```
    *   确认实际的 `@POST` 路径。
    *   参数使用 `@Body() Map<String, String>` 接收 `toJson()` 后的请求模型。

### 3.2. 更新 `http_service.dart`

添加调用 `RestClient` 中新方法的静态方法。

*   **路径**: `lib/service/http_service.dart`
*   **方法**:
    ```dart
    /// 设置环境信息偏好
    static Future<UhomeResponseModel?> setEnvDevicePreference(EnvDevicePreferenceRequestModel request) async {
      try {
        // TODO: 确认 RestClient 实例获取方式和 baseUrl
        final UhomeResponseModel response = await SmartHomeRestClient(UhomeDio().dio, baseUrl: SmartHomeConstant.baseUrl)
            .setEnvDevicePreference(request.toJson());
        DevLogger.info(tag: SmartHomeConstant.package, msg: 'setEnvDevicePreference success: $response');
        return response;
      } catch (err, stackTrace) {
        DevLogger.error(tag: SmartHomeConstant.package, msg: 'setEnvDevicePreference error: $err\\n$stackTrace');
        return null;
      }
    }
    ```
    *   包含完整的 `try-catch` 异常处理和日志记录。

### 3.3. 代码生成

*   运行 `flutter pub run build_runner build --delete-conflicting-outputs` 重新生成 `rest_client.g.dart`。