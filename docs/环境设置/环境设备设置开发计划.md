**开发计划总览**

本计划旨在指导 `EnvDeviceSettingPage` 及其相关功能的开发，确保客户端能够让用户选择偏好的环境设备并在全屋总览中展示，同时将用户的选择持久化到后端。开发过程将严格遵循提供的设计文档和接口计划。

**开发任务分解**

---

**任务 1: 数据模型定义与创建**

*   **目标**: 定义和创建所需的数据模型文件，并确认与现有模型的整合。
*   **依据**:
    *   `环境设备设置数据流程设计文档.md` (第 2, 4 节)
    *   `环境信息偏好设置接口客户端开发计划.md` (第 2 节)
    *   代码分析结果
*   **步骤**:
    1.  **State Model**: 
        *   **确认复用**: 确认复用现有的 `EnvDeviceItemModel` (定义于 `lib/whole_house/device_env/services/env_devices_response_model.dart`)。该模型包含计划所需的大部分字段 (`id`, `name`, `roomId`/`roomName`, `value`/`reportedValue`, `unit`, `online`/`isOnline`, `selected`)。
        *   **类型确定方式**: 注意到现有模型**没有** `type` 字段。其类型（温度、湿度、PM2.5）将通过它在 `EnvDeviceState` 中所属的列表（`temperature`, `humidity`, `pm25`）来确定。
    2.  **API Request Model**: 在 `lib/whole_house/device_env/services/` 目录下创建 `preference_request_model.dart` 文件，实现 `EnvDevicePreferenceRequestModel` 类及其 `toJson` 方法，包含 `familyId`, `spaceId`, `temperature` (设备ID或null), `humidity` (设备ID或null), `pm25` (设备ID或null) 字段。
    3.  **View Models**: 根据数据流文档定义，确认或创建 `EnvDeviceSettingListViewModel`, `EnvDeviceSettingCategoryViewModel`, `EnvDeviceSettingItemViewModel` 的 Dart 类定义。确保 `EnvDeviceSettingItemViewModel` 包含 `type` 字段，该字段将由 Selector (任务 5) 根据其来源列表填充。

---

**任务 2: API 客户端接口实现**

*   **目标**: 在应用的服务层添加调用"设置环境信息偏好"接口的方法。
*   **依据**: `环境信息偏好设置接口客户端开发计划.md` (第 3 节)
*   **步骤**:
    1.  **RestClient 更新**: 打开 `lib/service/rest_client.dart`，在 `SmartHomeRestClient` 抽象类中添加 `@POST` 方法定义 `setEnvDevicePreference`，注意确认 API 路径 (`/api-gw/wisdomdevice/device/v1/setEnvPreference`) 和参数类型 (`@Body() Map<String, String> params`)。
    2.  **HttpService 更新**: 打开 `lib/service/http_service.dart`，添加静态方法 `setEnvDevicePreference`，接收 `EnvDevicePreferenceRequestModel` 对象，内部调用 RestClient 方法，包含 `try-catch` 异常处理和日志记录。
    3.  **代码生成**: 在终端运行 `flutter pub run build_runner build --delete-conflicting-outputs` 命令，生成更新后的 `rest_client.g.dart` 文件。

---

**任务 3: Redux State 结构与基础 Action 定义**

*   **目标**: 定义 Redux 中与环境设备设置相关的 State 结构和用户交互触发的 Action。
*   **依据**: `环境设备设置数据流程设计文档.md` (第 4, 5 节)
*   **步骤**:
    1.  **State 定义**: 
        *   **确认复用**: 确认复用现有的 `EnvDeviceState` (定义于 `lib/whole_house/device_env/env_device_state.dart`)。其内部结构 `Map<String, SpaceEnvironmentModel>`，其中 `SpaceEnvironmentModel` 包含 `temperature`, `humidity`, `pm25` 三个 `List<EnvDeviceItemModel>`，符合按环境属性存储设备列表的需求。
    2.  **Action 定义**: 创建 `SetWholeHouseEnvDeviceSettingAction` 类，包含 `String deviceId`, `bool selected`, `String deviceType` 参数。

---

**任务 4: Redux Reducer 实现 (状态更新与单选逻辑)**

*   **目标**: 实现 Reducer 逻辑，根据 Action 更新 State，并强制执行每个环境类别下的单选规则。
*   **依据**: `环境设备设置数据流程设计文档.md` (第 5 节后续流程 - Reducer 部分)
*   **步骤**:
    1.  定位并修改处理 `EnvDeviceState` 的 Reducer 函数。
    2.  在该 Reducer 中，监听 `SetWholeHouseEnvDeviceSettingAction`。
    3.  当收到 Action 时：
        *   根据 `action.deviceType` 确定要操作的状态列表 (e.g., `state.spaces[spaceId].temperature`)。
        *   遍历该列表，将**所有**设备的 `selected` 状态设置为 `false`。
        *   如果 `action.selected` 为 `true`，找到列表中 `deviceId` 与 `action.deviceId` 匹配的设备，将其 `selected` 状态设置为 `true`。
        *   返回包含已修改列表的新 `EnvDeviceState` 实例。

---

**任务 5: Redux Selector 实现 (State 到 ViewModel 转换)**

*   **目标**: 创建 Selector 函数，将 Redux State 中的原始设备列表转换为 UI 页面所需的 ViewModel 结构。
*   **依据**: `环境设备设置数据流程设计文档.md` (第 6 节第 2 点)
*   **步骤**:
    1.  创建新的 Selector 函数，输入为 `WholeHouseState` (或 `EnvDeviceState`)。
    2.  在 Selector 中：
        *   从 `state.envDeviceState` 中获取（某个空间的） `temperature`, `humidity`, `pm25` 列表。
        *   将每个列表 (`List<EnvDeviceItemModel>`) 映射为 `List<EnvDeviceSettingItemViewModel>`，在映射过程中，为每个 `EnvDeviceSettingItemViewModel` **设置其 `type` 字段** (根据来源列表是 temperature/humidity/pm25)。
        *   将这三个 `EnvDeviceSettingItemViewModel` 列表分别包装进 `EnvDeviceSettingCategoryViewModel` (设置 `envName` 为 "温度", "湿度", "空气质量")。
        *   将这三个 `EnvDeviceSettingCategoryViewModel` 放入一个 List 中。
        *   创建并返回 `EnvDeviceSettingListViewModel`，其 `list` 属性包含上述 Category 列表。

---

**任务 6: Redux Middleware 实现 (API 调用)**

*   **目标**: 实现 Middleware，在用户选择发生变化（状态更新后）时，自动调用 API 将偏好设置同步到服务器。
*   **依据**: `环境设备设置数据流程设计文档.md` (第 5 节后续流程 - Middleware 部分)
*   **步骤**:
    1.  创建或修改 Middleware 函数，监听 `SetWholeHouseEnvDeviceSettingAction` 或相应的 State 更新。
    2.  确保在 Reducer 处理 **之后** 执行 API 调用逻辑。
    3.  在 Middleware 中：
        *   从最新的 Store State (`store.state.wholeHouseState.envDeviceState`) 中获取相关的 `SpaceEnvironmentModel`。
        *   **查找选中设备**: 遍历 `temperature`, `humidity`, `pm25` 列表，分别查找 `selected` 为 `true` 的设备，获取其 `id`。如果某类别没有选中项，则 ID 为 `null` 或空字符串（根据接口要求）。
        *   获取当前的 `familyId` 和 `spaceId` (可能需要从 State 或 Action 中获取)。
        *   创建 `EnvDevicePreferenceRequestModel` 实例，填充 `familyId`, `spaceId`, 以及找到的三个设备 ID。
        *   调用 `HttpService.setEnvDevicePreference(request)`。
        *   根据 API 返回的 `UhomeResponseModel` 处理结果（例如，打印日志；如果需要用户反馈，可以 dispatch 一个新的 Action）。

---

**任务 7: UI 组件实现 (`WholeHouseListCheckItemWidge`)**

*   **目标**: **验证并适配**现有的 `WholeHouseListCheckItemWidge` 组件以满足环境设置页面的需求。
*   **依据**: 
    *   `环境设备设置数据流程设计文档.md` (第 3 节)
    *   UI 截图 (`env_dev_settings_list.png`, `env_dev_settings_offline.png`)
    *   代码分析结果 (组件存在于 `lib/whole_house/widgets/whole_house_list_check_item_widget.dart`)
*   **步骤**:
    1.  **检查现有组件**: 打开 `lib/whole_house/widgets/whole_house_list_check_item_widget.dart`。
    2.  **验证与修改**: 
        *   **单选支持**: 确认组件是否使用或可以方便地修改为使用**单选**控件（如 `Radio`, `RadioListTile`），而不是复选框。
        *   **离线状态**: 确认组件是否根据传入的 `isOnline` 状态，正确显示 " | 离线" 标识并调整样式。
        *   **回调参数**: 确认或修改其 `onChanged` 回调，使其能够提供 `deviceId`, 新的 `selected` 状态 (`true`), 以及 `deviceType`。即回调签名为 `Function(String deviceId, bool selected, String deviceType) onChanged`。
        *   **数据绑定**: 确认组件能接收并正确显示 `EnvDeviceSettingItemViewModel` 中的所有必要信息（图标、名称、房间、值、单位）。
            *   **图标来源**: 优先使用 `viewModel.deviceImage` (`cardPageImg`)。如果为空，根据 `viewModel.type` 提供默认图标 (e.g., 温度计, 湿度计, 空气质量图标)。
        *   **视觉样式**: 列表项需呈现为带圆角的白色卡片样式，项间使用细分隔线，整体视觉风格参照截图。
    3.  根据验证结果进行必要的修改。

---

**任务 8: UI 页面实现 (`EnvDeviceSettingPage`)**

*   **目标**: 开发环境设备设置的主页面，组织和显示设备列表。
*   **依据**: 
    *   `环境设备设置数据流程设计文档.md` (第 1, 6 节)
    *   UI 截图 (`环境设备整体页面.png`, `env_dev_settings_none.png`)
    *   参考 `ConsumablesDetailPage` 页面布局。
*   **步骤**:
    1.  **页面框架**: 
        *   参考 `ConsumablesDetailPage`, 创建 `EnvDeviceSettingPage` (StatefulWidget 或 HookWidget)。
        *   实现页面 AppBar，包含返回按钮和页面标题（例如 “环境设备设置” 或当前空间名称）。
        *   设置页面背景色为浅灰色（参照截图）。
    2.  **连接 Store**: 使用 `connect` 或 `context.watch` 等方式连接 Redux Store。
    3.  **获取数据**: 调用之前创建的 Selector (任务 5) 从 Store 获取 `EnvDeviceSettingListViewModel`。
    4.  **处理加载与空状态**: 
        *   根据需要显示加载指示器。
        *   如果获取到的 ViewModel 为空，或其 `list` 为空，则显示 "暂无环境设备" 的居中提示信息 (参考 `env_dev_settings_none.png`)。
    5.  **渲染列表**: 
        *   如果数据有效，使用 `ListView` 或 `CustomScrollView` + `SliverList` 构建页面主体，并设置适当的内/外边距以匹配截图布局。
        *   遍历 `viewModel.list` (即 `List<EnvDeviceSettingCategoryViewModel>`)。
        *   对于每个 `categoryViewModel`:
            *   渲染一个分组标题 (例如，使用 `SliverToBoxAdapter` 或列表项)，显示 `categoryViewModel.envName`。**样式需参照截图**（字体、颜色、间距）。
            *   遍历 `categoryViewModel.items` (`List<EnvDeviceSettingItemViewModel>`)。
            *   为每个 `itemViewModel` 渲染一个 (已适配的) `WholeHouseListCheckItemWidge` (任务 7)，传入 `itemViewModel` 和用于 dispatch Action 的回调。

