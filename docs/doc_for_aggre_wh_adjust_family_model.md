# 聚合、仪表盘适配家庭模型

## 1.聚合适配家庭模型

### 1.1 设备列表聚合编辑入口的展示前提增加不是家庭非管理员的判断条件（管理员或者创建者才展示入口），方法使用双虎封装的方法isFamilyMemberRole
```dart
// device_list_widget.dart
StoreConnector<SmartHomeState, _SettingViewModel>(
  distinct: true,
  converter: (Store<SmartHomeState> store) {
    final bool familyMemberRole = isFamilyMemberRole();
    final _SettingViewModel vm = _SettingViewModel(
        store.state.isEditState, store.state.isLogin, familyMemberRole);
    return vm;
  },
  builder: (BuildContext context, _SettingViewModel vm) {
    return vm.isLogin && !vm.isEdit && !familyMemberRole
    ? Padding(
    ...,
    )
: Container();
```

### 1.2 摄像头聚合详情页右上角菜单入口如果是家庭非管理员，则不展示（管理员或者创建者才展示入口），方法使用双虎封装的方法isFamilyMemberRole
```dart
// base_aggregation_widget_state.dart

AggCameraHeader(
  leadingRightButton: wrapper.isValid() || isFamilyMemberRole()
      ? Container(
          height: 0,
        )
      : leadingRightWidget(wrapper),
  title: title(),
  showExpand: showExpandedTitle(),
  isRecordPage: isRecordPage(),
)
```

### 1.3 聚合灯光/窗帘/环境详情页底部进入设备管理页面的入口（“编辑”按钮）管理员或者创建者才展示，非管理员不展示，isFamilyMemberRole作为判断依据
```dart
// aggregation_detail_list.dart

if (!isFamilyMemberRole()) EditBtnWidget(vm: viewModel)

if (!isFamilyMemberRole()) Center(
                  child: GestureDetector(
                onTap: () {
                  // 跳转编辑页面
```

### 1.4 聚合详情页和聚合管理页面房间排序
```dart
// 1-2d 和梦月对接排序方案。--通过调用梦月的方法改造排序逻辑
```

## 2.仪表盘
### 2.1 天气：只有家庭管理员(即非成员角色)才响应点击
```dart
// whole_house/location_weather/whole_house_header_weather_widget.dart
// 天气组件点击onTap方法里添加管理员的判断
if (isFamilyMemeberRole()) {
  return;
}
// 原有代码
```

### 2.2 环境(湿度、温度、空气质量)-只有家庭管理员(即非成员角色)才响应点击
```dart
// whole_house/device_env/whole_house_header_env_device_widget.dart
// _EnvDeviceIconWidget 点击回调 clickCallback 方法里添加管理员的判断
if (isFamilyMemeberRole()) {
  return;
}
// 原有代码
```
