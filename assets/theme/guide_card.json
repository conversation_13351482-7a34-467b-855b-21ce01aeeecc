{"nm": "å¡çåæ¢-f 2", "ddd": 0, "h": 1062, "w": 1170, "meta": {"g": "@lottiefiles/toolkit-js 0.59.0", "tc": "#e3e3e3"}, "layers": [{"ty": 2, "nm": "å°å¡æ¹ä¾¿æ¥æ¾.png", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6196", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [124.5, 20]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 124}, {"s": [100, 100, 100], "t": 153}]}, "p": {"a": 0, "k": [585, 1007, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 124}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 142.5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 239}, {"s": [0], "t": 245}]}}, "refId": "1", "ind": 1}, {"ty": 2, "nm": "ä¸­å¡ææ§å¼é¡¾.png", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6195", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [123.5, 20]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 6}, {"s": [100, 100, 90.909], "t": 35}]}, "p": {"a": 0, "k": [585, 1006.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 24.5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 118}, {"s": [0], "t": 124}]}}, "refId": "2", "ind": 2}, {"ty": 2, "nm": "å¤§å¡æ¹ä¾¿å¿«æ·.png", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6194", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [125, 20]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 245}, {"s": [100, 100, 100], "t": 274}]}, "p": {"a": 0, "k": [585, 1006.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 245}, {"s": [100], "t": 263.5}]}}, "refId": "3", "ind": 3}, {"ty": 2, "nm": "info_g.png", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6193", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [435, 57]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [595, 1006.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "4", "ind": 4}, {"ty": 2, "nm": "è®¾å¤.png", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6192", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [60, 60]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [222, 110, 0], "t": 118, "ti": [0, 2.333, 0], "to": [0, -4, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [222, 86, 0], "t": 147, "ti": [0, -1.667, 0], "to": [0, -2.333, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [222, 96, 0], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [222, 96, 0], "t": 245, "ti": [0, -2.333, 0], "to": [0, 2.333, 0]}, {"s": [222, 110, 0], "t": 264, "ti": [0, 4, 0], "to": [0, 2.333, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "5", "ind": 5}, {"ty": 3, "nm": "1s", "sr": 1, "st": 119, "op": 180, "ip": 119, "ln": "6191", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [585, 531]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ind": 6}, {"ty": 3, "nm": "500", "sr": 1, "st": 3, "op": 33, "ip": 3, "ln": "6190", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [585, 531]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ind": 7}, {"ty": 3, "nm": "300", "sr": 1, "st": 6, "op": 24, "ip": 6, "ln": "6189", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [585, 531]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ind": 8}, {"ty": 3, "nm": "200", "sr": 1, "st": 6, "op": 18, "ip": 6, "ln": "6188", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [585, 531]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ind": 9}, {"ty": 3, "nm": "100", "sr": 1, "st": 6, "op": 12, "ip": 6, "ln": "6187", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [585, 531]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "ind": 10}, {"ty": 4, "nm": "gradient-a-7", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6186", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-180, 0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [80, 100, 100], "t": 38}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 48}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [65, 100, 100], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 250}, {"s": [100, 100, 100], "t": 265}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [300, 84, 0], "t": 28, "ti": [23.333, -35.896, 0], "to": [-23.333, 35.896, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 305.375, 0], "t": 38, "ti": [0, 1, 0], "to": [-23.333, 35.896, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 299.375, 0], "t": 48}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 299.375, 0], "t": 118, "ti": [-23.333, 35.896, 0], "to": [23.333, -35.896, 0]}, {"s": [300, 84, 0], "t": 147}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [120, 14]}}, {"ty": "gf", "nm": "gradient001", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 11}, {"ty": 4, "nm": "gradient-a-6", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6185", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-180, 0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [80, 100, 100], "t": 38}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 48}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [65, 100, 100], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 250}, {"s": [100, 100, 100], "t": 265}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [300, 141, 0], "t": 28, "ti": [23.333, -35.896, 0], "to": [-23.333, 35.896, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 362.375, 0], "t": 38, "ti": [0, 1, 0], "to": [-23.333, 35.896, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 356.375, 0], "t": 48}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 356.375, 0], "t": 118, "ti": [-23.333, 35.896, 0], "to": [23.333, -35.896, 0]}, {"s": [300, 141, 0], "t": 147}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [120, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 12}, {"ty": 4, "nm": "gradient-a-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6184", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [381, 297, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 20}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 38}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 48}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 257}, {"s": [100], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3891", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 3, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [146, 52]}}, {"ty": "gf", "nm": "gradient03", "e": {"a": 0, "k": [-72.763, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.97647059, 0.97647059, 0.97647059, 0.5, 0.9627451, 0.9627451, 0.9627451, 1, 0.94901961, 0.94901961, 0.94901961]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [74.435, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 13}, {"ty": 4, "nm": "gradient-a-4", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6183", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [726, 297, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 266}, {"s": [100], "t": 277}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3892", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 3, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [52, 52]}}, {"ty": "gf", "nm": "gradient04", "e": {"a": 0, "k": [-26, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.97647059, 0.97647059, 0.97647059, 0.5, 0.9627451, 0.9627451, 0.9627451, 1, 0.94901961, 0.94901961, 0.94901961]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [26, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 14}, {"ty": 4, "nm": "gradient-a-5", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6182", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [930, 297, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 18}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 276}, {"s": [100], "t": 287}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3893", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 3, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [52, 52]}}, {"ty": "gf", "nm": "gradient05", "e": {"a": 0, "k": [-25.953, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.97647059, 0.97647059, 0.97647059, 0.5, 0.9627451, 0.9627451, 0.9627451, 1, 0.94901961, 0.94901961, 0.94901961]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [26.032, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 15}, {"ty": 4, "nm": "a-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6180", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [585, 212.875, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[157, -70], [-157, -70], [-157, 70], [157, 70]]}], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[163.667, -70], [-157, -70], [-157, 70], [163.667, 70]]}], "t": 9.5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-14.333, -69.653], [-157, -70], [-157, 70], [-14.333, 70.347]]}], "t": 38}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.333, -69.653], [-157, -70], [-157, 70], [-6.333, 70.347]]}], "t": 48}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.333, -69.653], [-157, -70], [-157, 70], [-6.333, 70.347]]}], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.333, -69.653], [-157, -70], [-156.539, -11.344], [-5.872, -10.996]]}], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.333, -69.653], [-157, -70], [-156.614, -4.335], [-5.947, -3.987]]}], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.333, -69.653], [-157, -70], [-156.614, -4.335], [-5.947, -3.987]]}], "t": 239}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[163.667, -70], [-157, -70], [-157, 70], [163.667, 70]]}], "t": 268}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[157, -70], [-157, -70], [-157, 70], [157, 70]]}], "t": 278}]}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, -0.625]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 16}, {"ty": 4, "nm": "å¡b", "sr": 1, "st": 0, "op": 4380, "ip": 0, "hd": true, "ln": "6179", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [226.5, 210, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [340.5, 669, 0], "t": 28, "ti": [-80.833, 76.333, 0], "to": [80.833, -76.333, 0]}, {"o": {"x": 0.35, "y": 0.35}, "i": {"x": 0.35, "y": 0.35}, "s": [825.5, 211, 0], "t": 57}, {"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [825.5, 211, 0], "t": 239, "ti": [80.833, -76.333, 0], "to": [-80.833, 76.333, 0]}, {"s": [340.5, 669, 0], "t": 268}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [], "ind": 17}, {"ty": 4, "nm": "gradient-b-1", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6178", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [108, 108, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3886", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [40, 40]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [20, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-20, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 18, "parent": 17}, {"ty": 4, "nm": "gradient-b-2", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6177", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [226.5, 297, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 14]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 19, "parent": 17}, {"ty": 4, "nm": "gradient-b-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6176", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [226.5, 354, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 20, "parent": 17}, {"ty": 4, "nm": "b-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6175", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [226.5, 210, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [50], "t": 43}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 239}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [50], "t": 254}, {"s": [100], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 21, "parent": 17}, {"ty": 4, "nm": "å¡c", "sr": 1, "st": 0, "op": 4380, "ip": 0, "hd": true, "ln": "6174", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [226.5, 210, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 669, 0], "t": 28, "ti": [81.5, 0, 0], "to": [-81.5, 0, 0]}, {"o": {"x": 0.447, "y": 0.447}, "i": {"x": 0.702, "y": 0.702}, "s": [340.5, 669, 0], "t": 57}, {"o": {"x": 0.296, "y": 0}, "i": {"x": 0.612, "y": 1}, "s": [340.5, 669, 0], "t": 118}, {"o": {"x": 0.248, "y": 0.248}, "i": {"x": 0.612, "y": 0.612}, "s": [340.5, 436, 0], "t": 147}, {"o": {"x": 0.248, "y": 0}, "i": {"x": 0.35, "y": 1}, "s": [340.5, 436, 0], "t": 239, "ti": [-81.5, -38.833, 0], "to": [0, 0, 0]}, {"s": [829.5, 669, 0], "t": 269, "ti": [81.5, 0, 0], "to": [81.5, 38.833, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [], "ind": 22}, {"ty": 4, "nm": "gradient-c-1", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6173", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [108, 108, 0], "t": 119, "ti": [0, 2, 0], "to": [0, -2.833, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [108, 91, 0], "t": 147, "ti": [0, -0.833, 0], "to": [0, -2, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [108, 96, 0], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [108, 96, 0], "t": 239, "ti": [0, -2, 0], "to": [0, 2, 0]}, {"s": [108, 108, 0], "t": 254, "ti": [0, 2.833, 0], "to": [0, 2, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3886", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [40, 40]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [20, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-20, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 23, "parent": 22}, {"ty": 4, "nm": "gradient-c-2", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6172", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [65, 100, 100], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 239}, {"s": [100, 100, 100], "t": 268}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [48, 297, 0], "t": 118, "ti": [-23, 37.583, 0], "to": [23, -37.583, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [186, 71.5, 0], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [186, 71.5, 0], "t": 239, "ti": [23, -37.583, 0], "to": [-23, 37.583, 0]}, {"s": [48, 297, 0], "t": 268}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 14]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 24, "parent": 22}, {"ty": 4, "nm": "gradient-c-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6171", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [65, 100, 100], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60, 100, 100], "t": 239}, {"s": [100, 100, 100], "t": 268}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [48.416, 354, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [48.416, 354, 0], "t": 117, "ti": [-23.042, 37.583, 0], "to": [23.042, -37.583, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [186.666, 128.5, 0], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [186.666, 128.5, 0], "t": 239, "ti": [1.319, -2.151, 0], "to": [-21.723, 35.432, 0]}, {"s": [48, 354, 0], "t": 268, "ti": [0, 0, 0], "to": [-23.042, 37.583, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 5}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 25, "parent": 22}, {"ty": 4, "nm": "c-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6170", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [223.5, 210, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [50], "t": 43}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 239}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [50], "t": 254}, {"s": [100], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 117}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, -12], [75.5, -12]]}], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, -5.333], [75.5, -5.333]]}], "t": 162}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, -5.333], [75.5, -5.333]]}], "t": 239}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 74], [75.5, 74]]}], "t": 268}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 283}]}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 26, "parent": 22}, {"ty": 4, "nm": "å¡d", "sr": 1, "st": 0, "op": 4380, "ip": 0, "hd": true, "ln": "6169", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [226.5, 210, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [346.5, 1130, 0], "t": 28, "ti": [-80.5, 76.833, 0], "to": [80.5, -76.833, 0]}, {"o": {"x": 0.449, "y": 0.449}, "i": {"x": 0.743, "y": 0.743}, "s": [829.5, 669, 0], "t": 59}, {"o": {"x": 0.343, "y": 0}, "i": {"x": 0.656, "y": 1}, "s": [829.5, 669, 0], "t": 119}, {"o": {"x": 0.226, "y": 0.226}, "i": {"x": 0.656, "y": 0.656}, "s": [344.75, 669, 0], "t": 147}, {"o": {"x": 0.226, "y": 0}, "i": {"x": 0.35, "y": 1}, "s": [344.75, 669, 0], "t": 239, "ti": [-0.292, -76.833, 0], "to": [0, 0, 0]}, {"s": [346.5, 1130, 0], "t": 268, "ti": [-80.5, 76.833, 0], "to": [0.292, 76.833, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [], "ind": 27}, {"ty": 4, "nm": "gradient-d-1", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6168", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [108, 108, 0], "t": 118, "ti": [0, 1.5, 0], "to": [0, -1.5, 0]}, {"s": [108, 99, 0], "t": 147, "ti": [0, 1.5, 0], "to": [0, -1.5, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3886", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [40, 40]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [20, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-20, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 28, "parent": 27}, {"ty": 4, "nm": "gradient-d-2", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6167", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "p": {"a": 0, "k": [48, 297, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 14]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 29, "parent": 27}, {"ty": 4, "nm": "gradient-d-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6166", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "p": {"a": 0, "k": [48, 354, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 30, "parent": 27}, {"ty": 4, "nm": "d-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6165", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [223.5, 210, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [50], "t": 43}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 57}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [70], "t": 132}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 0}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 118}]}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 99}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 31, "parent": 27}, {"ty": 4, "nm": "å¡e", "sr": 1, "st": 0, "op": 4380, "ip": 0, "hd": true, "ln": "6164", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [226.5, 210, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 1130, 0], "t": 28, "ti": [80.5, 0, 0], "to": [-80.5, 0, 0]}, {"o": {"x": 0.35, "y": 0.35}, "i": {"x": 0.35, "y": 0.35}, "s": [346.5, 1130, 0], "t": 59}, {"o": {"x": 0.35, "y": 0.687}, "i": {"x": 0.35, "y": 1}, "s": [346.5, 1130, 0], "t": 118, "ti": [-80.5, 76.833, 0], "to": [80.5, -76.833, 0]}, {"o": {"x": 0.35, "y": 0.35}, "i": {"x": 0.35, "y": 0.35}, "s": [829.5, 669, 0], "t": 147}, {"o": {"x": 0.35, "y": 0}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 669, 0], "t": 239, "ti": [0, -76.833, 0], "to": [0, 76.833, 0]}, {"s": [829.5, 1130, 0], "t": 268, "ti": [80.5, 0, 0], "to": [0, 76.833, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [], "ind": 32}, {"ty": 4, "nm": "gradient-e-1", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6163", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [108, 99, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3886", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [40, 40]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [20, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-20, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 33, "parent": 32}, {"ty": 4, "nm": "gradient-e-2", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6162", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "p": {"a": 0, "k": [48.5, 297.25, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 14]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 34, "parent": 32}, {"ty": 4, "nm": "gradient-e-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6161", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}, "p": {"a": 0, "k": [48.5, 356.25, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [5], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 35, "parent": 32}, {"ty": 4, "nm": "e-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6160", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [223.5, 210, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 28}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 118}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [50], "t": 132}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 147}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 239}, {"s": [0], "t": 268}]}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.833, 69.5], [75.167, 69.5]]}}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 36, "parent": 32}, {"ty": 4, "nm": "å¡f", "sr": 1, "st": 0, "op": 4380, "ip": 0, "hd": true, "ln": "6159", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [226.5, 210, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 1404, 0], "t": 27, "ti": [0, 45.667, 0], "to": [0, -45.667, 0]}, {"o": {"x": 0.35, "y": 0.35}, "i": {"x": 0.35, "y": 0.35}, "s": [829.5, 1130, 0], "t": 58}, {"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 1130, 0], "t": 118, "ti": [80.5, 0, 0], "to": [-80.5, 0, 0]}, {"o": {"x": 0.35, "y": 0.35}, "i": {"x": 0.35, "y": 0.35}, "s": [346.5, 1130, 0], "t": 147}, {"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [346.5, 1130, 0], "t": 239, "ti": [0, -45.5, 0], "to": [0, 45.5, 0]}, {"s": [346.5, 1403, 0], "t": 268, "ti": [0, 45.667, 0], "to": [0, 45.5, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [], "ind": 37}, {"ty": 4, "nm": "gradient-f-1", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6158", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [108, 99, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3886", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [40, 40]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [20, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-20, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 38, "parent": 37}, {"ty": 4, "nm": "gradient-f-2", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6157", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [56.863, 100, 100]}, "p": {"a": 0, "k": [193, 67, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 14]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 39, "parent": 37}, {"ty": 4, "nm": "gradient-f-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6156", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [56.863, 100, 100]}, "p": {"a": 0, "k": [193, 126, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 40, "parent": 37}, {"ty": 4, "nm": "f-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6155", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [223.5, 210, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 0}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, -3.667], [75.5, -3.667]]}], "t": 147}]}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 41, "parent": 37}, {"ty": 4, "nm": "å¡g", "sr": 1, "st": 0, "op": 4380, "ip": 0, "hd": true, "ln": "6154", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [226.5, 210, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 1403, 0], "t": 118, "ti": [0, 45.5, 0], "to": [0, -45.5, 0]}, {"o": {"x": 0.35, "y": 0.35}, "i": {"x": 0.35, "y": 0.35}, "s": [829.5, 1130, 0], "t": 147}, {"o": {"x": 0.35, "y": 0.85}, "i": {"x": 0.35, "y": 1}, "s": [829.5, 1130, 0], "t": 239, "ti": [0, -45.5, 0], "to": [0, 45.5, 0]}, {"s": [829.5, 1403, 0], "t": 268}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [], "ind": 42}, {"ty": 4, "nm": "gradient-g-1", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6153", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [108, 99, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3886", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [40, 40]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [20, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-20, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 43, "parent": 42}, {"ty": 4, "nm": "gradient-g-2", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6152", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [56.863, 100, 100]}, "p": {"a": 0, "k": [193, 67, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3888", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 14]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.7, 1, 0.4]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 44, "parent": 42}, {"ty": 4, "nm": "gradient-g-3", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6151", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-178.5, 0, 0]}, "s": {"a": 0, "k": [56.863, 100, 100]}, "p": {"a": 0, "k": [193, 126, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "shapes": [{"ty": "gr", "nm": "Rectangle 3889", "it": [{"ty": "rc", "nm": "ç©å½¢è·¯å¾ 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 4}, "s": {"a": 0, "k": [119, 12]}}, {"ty": "gf", "nm": "æ¸åå¡«å 1", "e": {"a": 0, "k": [60, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0, 0, 0.5, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0.5, 0.75, 1, 0.5]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [-60, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 45, "parent": 42}, {"ty": 4, "nm": "g-bg", "sr": 1, "st": 0, "op": 4380, "ip": 0, "ln": "6150", "hasMask": true, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [223.5, 210, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}}, "masksProperties": [{"nm": "èç 1", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-226.5, -210], [-226.5, -11], [226.5, -11], [226.5, -210]]}}}], "shapes": [{"ty": "gr", "nm": "Rectangle 3890", "it": [{"ty": "sh", "nm": "è·¯å¾ 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, 70], [75.5, 70]]}], "t": 0}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.5, -70], [-75.5, -70], [-75.5, -3.667], [75.5, -3.667]]}], "t": 147}]}}, {"ty": "rd", "nm": "åè§ 1", "r": {"a": 0, "k": 22}}, {"ty": "fl", "nm": "å¡«å 1", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 46, "parent": 42}], "v": "5.7.0", "fr": 60, "op": 332, "ip": 0, "assets": [{"id": "1", "e": 1, "w": 249, "h": 40, "p": "data:image/png;base64,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", "u": ""}, {"id": "2", "e": 1, "w": 247, "h": 40, "p": "data:image/png;base64,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", "u": ""}, {"id": "3", "e": 1, "w": 250, "h": 40, "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPoAAAAoCAYAAADXGucZAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAA6uSURBVHgB7V1dWtRKE66RHwUvPr4VnLACxxU4rABcgeOFvzfCChhWANyoyIVhBeAKjCsQV2DOCs6cCxAR5dQbqseaNv/pZIL<PERSON>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", "u": ""}, {"id": "4", "e": 1, "w": 870, "h": 114, "p": "data:image/png;base64,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", "u": ""}, {"id": "5", "e": 1, "w": 120, "h": 120, "p": "data:image/png;base64,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", "u": ""}], "markers": [{"cm": "1over\r", "tm": 48, "dr": 0}, {"cm": "2\r", "tm": 118, "dr": 0}, {"cm": "3", "tm": 239, "dr": 0}]}